import { CapacitorConfig } from '@capacitor/cli';

let config: CapacitorConfig;

const basePlugins: CapacitorConfig['plugins'] = {
  PushNotifications: {
    presentationOptions: ['sound', 'alert'],
  },
  SplashScreen: {
    launchShowDuration: 1250,
    backgroundColor: '#FFFFFF',
  },
};

const baseConfig: CapacitorConfig = {
  appId: 'com.sudshare.sudster',
  appName: 'Poplin for Laundry Pros',
  webDir: 'www',
  bundledWebRuntime: false,
  backgroundColor: '#FFFFFF',
};

switch (process.env.SUDSHARE_ENV) {
  case 'prod':
    config = {
      ...baseConfig,
      cordova: {
        preferences: {
          'com.forter.mobile.cordova.site_id': '079aa61d41c2',
        },
      },
      ios: {
        scheme: 'Prod',
      },
      android: {
        flavor: 'prod',
        useLegacyBridge: false,
      },
      plugins: {
        ...basePlugins,
        CapacitorUpdater: {
          autoUpdate: true,
          defaultChannel: 'production',
        },
      },
    };
    break;
  default:
    config = {
      ...baseConfig,
      cordova: {
        preferences: {
          'com.forter.mobile.cordova.site_id': '57ee6d0f6e5b',
        },
      },
      ios: {
        scheme: 'Test',
      },
      android: {
        flavor: 'qa',
        useLegacyBridge: false,
      },
      plugins: {
        ...basePlugins,
        CapacitorUpdater: {
          autoUpdate: true,
          defaultChannel: 'dev',
        },
      },
    };
    break;
}

export default config;
