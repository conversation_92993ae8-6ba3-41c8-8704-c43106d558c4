{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "defaultProject": "app", "newProjectRoot": "projects", "projects": {"app": {"root": "", "sourceRoot": "src", "projectType": "application", "prefix": "", "schematics": {}, "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"stylePreprocessorOptions": {"includePaths": ["src/theme"]}, "outputPath": "www", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.app.json", "allowedCommonJsDependencies": ["moment-timezone", "@firebase/app-check", "chart.js", "statsig-js"], "assets": [{"glob": "**/*", "input": "src/assets", "output": "assets"}, {"glob": "**/*.svg", "input": "node_modules/ionicons/dist/ionicons/svg", "output": "./svg"}], "styles": [{"input": "src/theme/variables.scss"}, {"input": "src/global.scss"}, {"input": "src/app/app.scss"}, "src/theme/poplin-styles.scss", "src/theme/tokens.scss", "src/theme/ionic/index.scss"], "scripts": [], "vendorChunk": true, "sourceMap": true, "namedChunks": true}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "index": {"input": "src/index.prod.html", "output": "index.html"}, "optimization": true, "sourceMap": {"hidden": false, "scripts": true, "styles": true, "vendor": true}, "namedChunks": false, "aot": true, "extractLicenses": false, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "3mb", "maximumError": "5mb"}]}, "soft-prod": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.soft-prod.ts"}], "optimization": true, "sourceMap": {"hidden": false, "scripts": true, "styles": true, "vendor": true}, "namedChunks": false, "aot": true, "extractLicenses": false, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "3mb", "maximumError": "5mb"}]}, "emulator": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.emulator.ts"}]}, "local": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.local.ts"}]}, "ci": {"progress": false}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "app:build"}, "configurations": {"production": {"browserTarget": "app:build:production"}, "emulator": {"browserTarget": "app:build:emulator"}, "local": {"browserTarget": "app:build:local"}, "ci": {}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "app:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "karmaConfig": "src/karma.conf.js", "styles": [], "scripts": ["node_modules/latlon-geohash/latlon-geohash.js"], "assets": [{"glob": "favicon.ico", "input": "src/", "output": "/"}, {"glob": "**/*", "input": "src/assets", "output": "/assets"}]}, "configurations": {"ci": {"progress": false, "watch": false}}}, "ionic-cordova-build": {"builder": "@ionic/angular-toolkit:cordova-build", "options": {"browserTarget": "app:build"}, "configurations": {"production": {"browserTarget": "app:build:production"}}}, "ionic-cordova-serve": {"builder": "@ionic/angular-toolkit:cordova-serve", "options": {"cordovaBuildTarget": "app:ionic-cordova-build", "devServerTarget": "app:serve"}, "configurations": {"production": {"cordovaBuildTarget": "app:ionic-cordova-build:production", "devServerTarget": "app:serve:production"}}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"analytics": "fa6e1b68-6823-4200-938d-e0d1c2a23ab5", "schematicCollections": ["@angular-eslint/schematics", "@ionic/angular-toolkit"]}, "schematics": {"@ionic/angular-toolkit:component": {"styleext": "scss"}, "@ionic/angular-toolkit:page": {"styleext": "scss"}}}