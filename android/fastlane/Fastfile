# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
update_fastlane

default_platform(:android)

platform :android do
  desc "Test build for App Distribution"
  lane :beta do

    increment_version_code(app_project_dir: '**/app')

    gradle(
      task: "assemble",
      build_type: "Debug",
      flavor: "qa",
    )

    firebase_app_distribution(
      app: "1:************:android:413f2d0a8c87d3ddce016e",
      service_credentials_file: "../android/fastlane/serviceAccountKey.json",
      groups: "android-testers",
      release_notes: "Automated Release",
    )
  end
end

platform :android do
  desc "Production build for App Distribution"
  lane :betaProd do |options|

    bump_type = options[:bump_type]

    if bump_type == "build"
      increment_version_code(app_project_dir: '**/app')
    else
      increment_version_name(app_project_dir: '**/app', bump_type: bump_type)
      increment_version_code(app_project_dir: '**/app')
    end

    gradle(
      task: 'assemble',
      build_type: 'Debug',
      flavor: 'prod',
    )

    firebase_app_distribution(
      app: "1:***********:android:200e72e40f06b742",
      service_credentials_file: "../android/fastlane/serviceAccountKey.json",
      groups: "android-testers",
      release_notes: "Beta Release",
    )
  end
end

platform :android do
  desc "Deploy to Google Play"
  lane :deploy_to_play_store do
    supply(
      json_key_data: ENV["GOOGLE_SERVICE_ACCOUNT_JSON"],
      package_name: "com.sudshare.sudster",
      track: "production",
      aab_paths: ["./app/build/outputs/bundle/prodRelease/app-prod-release.aab"], # or aab_paths for Android App Bundles
      skip_upload_metadata: false,
      skip_upload_images: true,
      skip_upload_screenshots: true,
      release_status: "draft", # Set the release status so that it is not automatically sent for review, can be adjusted later
      metadata_path: "./metadata/android/en-US/changelogs" # Include Release Notes
    )
  end
end

lane :bump_minor do
  increment_version_name(app_project_dir: '**/app', bump_type: 'minor')

  version = get_version_name(app_project_dir: '**/app')

  increment_version_code(app_project_dir: '**/app')

  version = get_version_name(app_project_dir: '**/app')
  UI.message("New version name: #{version}")

  version_code = get_version_code(app_project_dir: '**/app')
  UI.message("New version code: #{version_code}")
end

lane :bump_patch do
  increment_version_name(app_project_dir: '**/app', bump_type: 'patch')
  increment_version_code(app_project_dir: '**/app')
end