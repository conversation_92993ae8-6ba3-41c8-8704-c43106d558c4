// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN

android {
  compileOptions {
      sourceCompatibility JavaVersion.VERSION_17
      targetCompatibility JavaVersion.VERSION_17
  }
}

apply from: "../capacitor-cordova-android-plugins/cordova.variables.gradle"
dependencies {
    implementation project(':capacitor-community-contacts')
    implementation project(':capacitor-community-fcm')
    implementation project(':capacitor-app')
    implementation project(':capacitor-browser')
    implementation project(':capacitor-camera')
    implementation project(':capacitor-clipboard')
    implementation project(':capacitor-device')
    implementation project(':capacitor-filesystem')
    implementation project(':capacitor-geolocation')
    implementation project(':capacitor-haptics')
    implementation project(':capacitor-keyboard')
    implementation project(':capacitor-network')
    implementation project(':capacitor-preferences')
    implementation project(':capacitor-push-notifications')
    implementation project(':capacitor-share')
    implementation project(':capacitor-splash-screen')
    implementation project(':capacitor-status-bar')
    implementation project(':capgo-capacitor-updater')
    implementation project(':sudshare-capacitor-zendesk')
    implementation project(':appsflyer-capacitor-plugin')
    implementation "com.google.android.gms:play-services-ads-identifier:17.0.+"
}
apply from: "../../node_modules/cordova-plugin-ionic/src/android/cordovapluginionic.gradle"
apply from: "../../node_modules/cordova-plugin-smartlook/src/android/plugin.gradle"
apply from: "../../node_modules/forter-cordova-plugin/src/android/sdk/forter.gradle"

if (hasProperty('postBuildExtras')) {
  postBuildExtras()
}
