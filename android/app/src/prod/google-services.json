{"project_info": {"project_number": "79996270453", "firebase_url": "https://laundry-llama.firebaseio.com", "project_id": "laundry-llama", "storage_bucket": "laundry-llama.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:79996270453:android:08438e84bab08aac", "android_client_info": {"package_name": "com.sudshare.sudshare"}}, "oauth_client": [{"client_id": "79996270453-30081rfrpe9ditsphurf8tpo6psqqcuo.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.sudshare.sudshare", "certificate_hash": "1e3e17b55261b70d19d573c5d110793362190c7a"}}, {"client_id": "79996270453-56mg87lr1s6ltchmnrhn87cmr6m9pr26.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.sudshare.sudshare", "certificate_hash": "d5020157d6f890b406d5882f0971671876a8dcad"}}, {"client_id": "79996270453-moj41l3luo46jao83rjp5a1bi3da6ljj.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAREMiDfJaDD3Wd29u-MeiPekdRdVzrzrA"}, {"current_key": "AIzaSyDVPnDYVzqwkvy1aIC_xJGuc4I2PJ3MyPc"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "79996270453-moj41l3luo46jao83rjp5a1bi3da6ljj.apps.googleusercontent.com", "client_type": 3}, {"client_id": "79996270453-0degch7sggmbuf9erku5pvj06ans5fp0.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.sudshare.SudShare", "app_store_id": "1333675995"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:79996270453:android:200e72e40f06b742", "android_client_info": {"package_name": "com.sudshare.sudster"}}, "oauth_client": [{"client_id": "79996270453-kecgdfqbqmvj6hldab60hmjgocd516p3.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.sudshare.sudster", "certificate_hash": "f9606d23e9b24d36ff6a17f85bfa2922837d6a75"}}, {"client_id": "79996270453-oi670sqpc6okvm1ucd3tf0hhcqce9sl5.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.sudshare.sudster", "certificate_hash": "204fbe549cdd35afad6d1aa6d8aa12765dd0ebc5"}}, {"client_id": "79996270453-moj41l3luo46jao83rjp5a1bi3da6ljj.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAREMiDfJaDD3Wd29u-MeiPekdRdVzrzrA"}, {"current_key": "AIzaSyDVPnDYVzqwkvy1aIC_xJGuc4I2PJ3MyPc"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "79996270453-moj41l3luo46jao83rjp5a1bi3da6ljj.apps.googleusercontent.com", "client_type": 3}, {"client_id": "79996270453-0degch7sggmbuf9erku5pvj06ans5fp0.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.sudshare.SudShare", "app_store_id": "1333675995"}}]}}}], "configuration_version": "1"}