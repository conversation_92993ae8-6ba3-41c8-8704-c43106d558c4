package com.sudshare.sudster.plugins;

import android.content.Context;
import android.net.Uri;
import android.util.Log;

import com.getcapacitor.JSObject;
import com.getcapacitor.Plugin;
import com.getcapacitor.PluginCall;
import com.getcapacitor.PluginMethod;
import com.getcapacitor.annotation.CapacitorPlugin;
import com.iterable.iterableapi.IterableActionContext;
import com.iterable.iterableapi.IterableApi;

@CapacitorPlugin(name = "Iterable")
public class IterablePlugin extends Plugin {
    private Context context;
    private IterableApi iterableApi;
    private static IterablePlugin currentInstance;

    @Override
    public void load() {
        super.load();
        this.context = this.getContext();
        this.iterableApi = IterableApi.getInstance();
        IterablePlugin.currentInstance = this;
    }

    @PluginMethod()
    public void registerUser(PluginCall call) {
        if (checkIsInitialized(call)) return;

        String userId = call.getString("userId");

        if (userId != null && !userId.isEmpty() && !userId.isBlank()) {
            this.iterableApi.setUserId(userId);
            this.iterableApi.registerForPush();
        }

        JSObject data = new JSObject();
        data.put("value", "ok");
        call.resolve(data);
    }

    @PluginMethod
    public void reset(PluginCall call) {
        if (checkIsInitialized(call)) return;

        this.iterableApi.setUserId(null);
        JSObject data = new JSObject();
        data.put("value", "ok");
        call.resolve(data);
    }

    private boolean checkIsInitialized(PluginCall call) {
        if (this.iterableApi == null) {
            call.reject("Sdk not initialized");
            return true;
        }
        return false;
    }

    @PluginMethod
    public void trackPush(PluginCall call) {
        if (checkIsInitialized(call)) return;

        Integer campaignId = call.getInt("campaignId", -1);
        Integer templateId = call.getInt("templateId", -1);

        JSObject data = new JSObject();
        if (campaignId > 0 && templateId > 0) {
            String msgId = call.getString("msgId", "");
            this.iterableApi.trackPushOpen(campaignId, templateId, msgId );
            data.put("result", "ok");
        } else {
            data.put("result", "no-track");
        }

        call.resolve(data);
    }

    public boolean handleIterableURL(Uri uri, IterableActionContext iterableActionContext) {
        try {
            JSObject data = new JSObject();
            data.put("url", uri.toString());
            this.notifyListeners("IterableInAppOnUrl", data);
        } catch (Exception e) {
            Log.e("IterablePlugin", "Failure notifying url" + uri.toString());
        }
        return true;
    }

    public static IterablePlugin getInstance() {
        return IterablePlugin.currentInstance;
    }
}