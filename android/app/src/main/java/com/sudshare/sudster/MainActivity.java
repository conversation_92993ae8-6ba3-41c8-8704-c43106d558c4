package com.sudshare.sudster;

import android.Manifest;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.getcapacitor.BridgeActivity;
import com.getcapacitor.JSObject;
import com.iterable.iterableapi.IterableActionContext;
import com.iterable.iterableapi.IterableApi;
import com.iterable.iterableapi.IterableConfig;
import com.iterable.iterableapi.IterableUrlHandler;
import com.segment.analytics.Analytics;
import com.sudshare.sudster.plugins.AppRequestReview;
import com.sudshare.sudster.plugins.IterablePlugin;
import com.sudshare.sudster.plugins.SudSharePlugin;
import com.sudshare.sudster.plugins.SegmentPlugin;


public class MainActivity extends BridgeActivity implements IterableUrlHandler {

    @Override
    public void onCreate(Bundle savedInstanceState) {
        registerPlugin(AppRequestReview.class);
        registerPlugin(SudSharePlugin.class);
        Context context = this.getApplicationContext();


        IterableConfig config = new IterableConfig.Builder()
                .setAllowedProtocols(new String[]{"http", "tel", "poplinlp"})
                .setPushIntegrationName(BuildConfig.PACKAGE_NAME)
                .setAutoPushRegistration(true)
                .setUrlHandler(this)
                .build();
        IterableApi.initialize(getApplicationContext(), BuildConfig.ITERABLE_KEY, config);
        registerPlugin(IterablePlugin.class);

        // Set the initialized instance as a globally accessible instance.
        Analytics.setSingletonInstance(new Analytics.Builder(this, BuildConfig.SEGMENT_KEY)
                .trackApplicationLifecycleEvents()
                .build());
        registerPlugin(SegmentPlugin.class);

        super.onCreate(savedInstanceState);
    }


    @Override
    protected void attachBaseContext(Context newBase) {
        super.attachBaseContext(newBase);
        final Configuration override = new Configuration(newBase.getResources().getConfiguration());
        override.fontScale = 1.0f;
        applyOverrideConfiguration(override);
    }


    @Override
    public boolean handleIterableURL(@NonNull Uri uri, @NonNull IterableActionContext iterableActionContext) {
        if (IterablePlugin.getInstance() == null) {
            return false;
        }

        return IterablePlugin.getInstance().handleIterableURL(uri, iterableActionContext);
    }

    /**
     * Overriding this method to comply with Android 34 security measures.
     * Reference:  https://web.archive.org/web/20240805232143/https://medium.com/@siddhantshelake/fixing-broadcastreceiver-crashes-and-metro-server-issues-in-android-14-3b7d05939a43
     *
     * @param receiver
     * @param filter
     * @return
     */
    @Override
    public Intent registerReceiver(@Nullable BroadcastReceiver receiver, IntentFilter filter) {
        if (Build.VERSION.SDK_INT >= 34 && getApplicationInfo().targetSdkVersion >= 34) {
            return super.registerReceiver(receiver, filter, Context.RECEIVER_EXPORTED);
        } else {
            return super.registerReceiver(receiver, filter);
        }
    }
}
