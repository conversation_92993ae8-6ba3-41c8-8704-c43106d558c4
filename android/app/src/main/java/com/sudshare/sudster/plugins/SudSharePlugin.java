package com.sudshare.sudster.plugins;

import com.getcapacitor.JSObject;
import com.getcapacitor.Plugin;
import com.getcapacitor.PluginCall;
import com.getcapacitor.PluginMethod;
import com.getcapacitor.annotation.CapacitorPlugin;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

@CapacitorPlugin(name = "SudSharePlugin")
public class SudSharePlugin extends Plugin {

    @Override
    public void load() {
    }

    @PluginMethod
    public void setup(PluginCall call) {
        String userId = call.getString("userId");

        FirebaseCrashlytics.getInstance().setUserId(userId != null ? userId : "Unknown");

        JSObject ret = new JSObject();
        ret.put("value", "ok");
        call.resolve(ret);
    }
}
