package com.sudshare.sudster.plugins;

import android.content.Context;

import com.getcapacitor.JSObject;
import com.getcapacitor.Plugin;
import com.getcapacitor.PluginCall;
import com.getcapacitor.PluginMethod;
import com.getcapacitor.annotation.CapacitorPlugin;
import com.segment.analytics.Analytics;
import com.segment.analytics.Properties;
import com.segment.analytics.Traits;

import java.util.Iterator;

@CapacitorPlugin(name = "Segment")
public class SegmentPlugin extends Plugin {
    private Context context;

    @Override
    public void load() {
        super.load();
        this.context = this.getContext();
    }

    @PluginMethod()
    public boolean identify(PluginCall call) {
        String userId = call.getString("userId");
        JSObject userTraitsInformation = call.getObject("traits");

        Traits userTraits = new Traits();
        for (Iterator<String> it = userTraitsInformation.keys(); it.hasNext(); ) {
            String key = it.next();
            userTraits.putValue(key, userTraitsInformation.getString(key));
        }

        Analytics.with(this.context).identify(userId, userTraits, null);
        return true;
    }

    @PluginMethod
    public boolean track(PluginCall call) {
        String eventName = call.getString("eventName");
        JSObject properties = call.getObject("properties");

        Properties eventProperties = new Properties();
        for (Iterator<String> it = properties.keys(); it.hasNext(); ) {
            String key = it.next();
            eventProperties.putValue(key, properties.getString(key));
        }

        Analytics.with(this.context).track(eventName, eventProperties, null);
        return true;
    }

    @PluginMethod
    public boolean reset(PluginCall call) {
        Analytics.with(this.context).reset();
        return true;
    }
}
