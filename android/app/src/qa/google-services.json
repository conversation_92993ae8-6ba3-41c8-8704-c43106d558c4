{"project_info": {"project_number": "119070405931", "firebase_url": "https://sudsharetest.firebaseio.com", "project_id": "sudsharetest", "storage_bucket": "sudsharetest.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:119070405931:android:4baee533ce0b9427ce016e", "android_client_info": {"package_name": "com.sudshare.sudshare.test"}}, "oauth_client": [{"client_id": "119070405931-4l2a1edra13p0v6u1u36poba52eudfb1.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAbZVcPBoLazdLIMNhcIiduFEmMXthAoQo"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "119070405931-4l2a1edra13p0v6u1u36poba52eudfb1.apps.googleusercontent.com", "client_type": 3}, {"client_id": "119070405931-5j0vegbl91pcndi1clae5tak0hf55hec.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.sudshare.Sudsters", "app_store_id": "1333729162"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:119070405931:android:413f2d0a8c87d3ddce016e", "android_client_info": {"package_name": "com.sudshare.sudster.test"}}, "oauth_client": [{"client_id": "119070405931-4l2a1edra13p0v6u1u36poba52eudfb1.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAbZVcPBoLazdLIMNhcIiduFEmMXthAoQo"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "119070405931-4l2a1edra13p0v6u1u36poba52eudfb1.apps.googleusercontent.com", "client_type": 3}, {"client_id": "119070405931-5j0vegbl91pcndi1clae5tak0hf55hec.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.sudshare.Sudsters", "app_store_id": "1333729162"}}]}}}], "configuration_version": "1"}