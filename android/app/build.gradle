apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'

android {
    namespace "com.sudshare.sudster"
    compileSdk 34
    defaultConfig {
        applicationId "com.sudshare.sudster"
        minSdk 22
        targetSdk 34
        versionCode 3657
        versionName "3.59.0"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    signingConfigs {
        release {
            keyAlias System.getenv("KEY_ALIAS")
            keyPassword System.getenv("KEY_PASSWORD")
            storeFile file(System.getenv("STORE_FILE") ?: '../certs/prod-release.keystore')
            storePassword System.getenv("STORE_PASSWORD")
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
        debug {
            debuggable true
        }
    }
    flavorDimensions "app"
    productFlavors {
        prod {
            dimension "app"
            manifestPlaceholders = [appName: "Laundry Pro", deepLink: "laundry-pro.poplin.co", oneLink: "appsflyer.poplin.co", customUri: 'poplinlp']
            buildConfigField "String", "SEGMENT_KEY", "\"XPRDDutOuvZ3ofk35K4EyCG9i97wfGyH\""
            buildConfigField "String", "ITERABLE_KEY", "\"5b917c9f0746455a821e160020c6cb07\""
            buildConfigField "String", "PACKAGE_NAME", "\"com.sudshare.sudster\""
        }

        qa {
            dimension "app"
            applicationIdSuffix ".test"
            manifestPlaceholders = [appName: "Laundry Pro Test", deepLink: "test-laundry-pro.poplin.co", oneLink: "nonprod-appsflyer.poplin.co", customUri: 'poplinlptest']
            buildConfigField "String", "SEGMENT_KEY", "\"N12ayva4kDqPKbggxFdGIWCGMW10vQ5q\""
            buildConfigField "String", "ITERABLE_KEY", "\"122354345243\""
            buildConfigField "String", "PACKAGE_NAME", "\"com.sudshare.sudster.test\""
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

}

repositories {
    flatDir {
        dirs '../capacitor-cordova-android-plugins/src/main/libs', 'libs'
    }
    mavenCentral()
}

dependencies {
    implementation "androidx.core:core-splashscreen:$coreSplashScreenVersion"
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation "androidx.appcompat:appcompat:1.6.1"
    implementation project(':capacitor-android')
    implementation 'androidx.coordinatorlayout:coordinatorlayout:1.1.0'
    testImplementation "junit:junit:$junitVersion"
    implementation platform('com.google.firebase:firebase-bom:29.1.0')
    androidTestImplementation "androidx.test.ext:junit:$androidxJunitVersion"
    androidTestImplementation "androidx.test.espresso:espresso-core:$androidxEspressoCoreVersion"
    implementation project(':capacitor-cordova-android-plugins')
    implementation 'com.google.firebase:firebase-crashlytics'
    implementation("com.google.firebase:firebase-iid")
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-messaging'
    implementation "androidx.core:core-ktx:1.2.0"
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation 'com.google.android.play:asset-delivery:2.2.2'
    implementation 'com.google.android.play:app-update:2.1.0'
    implementation 'com.google.android.gms:play-services-tasks:18.2.0'
    implementation("com.google.android.play:app-update:2.1.0")
    implementation("com.google.android.play:app-update-ktx:2.1.0")
    implementation 'com.segment.analytics.android:analytics:4.10.4'
    implementation 'com.iterable:iterableapi:3.5.2'
    implementation 'com.google.android.play:review:2.0.1'
}

apply from: 'capacitor.build.gradle'
