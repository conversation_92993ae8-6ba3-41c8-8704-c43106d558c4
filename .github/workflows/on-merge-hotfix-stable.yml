name: Hotfix Merge to Stable

on:
  push:
    branches:
      - stable

jobs:
  hotfix_merge:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout the code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0 # Needed to fetch the full history to cherry-pick the commit

      - name: Get the latest squash merge commit
        id: get_commit
        run: |
          COMMIT_HASH=$(git log -1 --format="%H")
          echo "Latest commit hash: $COMMIT_HASH"
          echo "::set-output name=commit_hash::$COMMIT_HASH"

      - name: Create a new branch from main
        id: create_branch
        run: |
          # Configure Git
           git config --global user.email "<EMAIL>"
          git config --global user.name "poplin-service-admin"

          # Fetch the latest main branch
          git fetch origin main

          # Create a new branch based off main
          NEW_BRANCH="ci/hotfix-sync-$(date +'%Y%m%d%H%M%S')"
          git checkout -b $NEW_BRANCH origin/main

          # Cherry-pick the squash merge commit from stable
          git cherry-pick ${{ steps.get_commit.outputs.commit_hash }} -m 1

          # Push the new branch
          git push origin $NEW_BRANCH

          # Set the new branch name as output
          echo "::set-output name=new_branch::$NEW_BRANCH"
          echo "::set-output name=commit_message::$COMMIT_MESSAGE"

      - name: Create a Pull Request
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.GH_ENG_ADMIN_PAT }}
          commit-message: 'Applying hotfix changes from stable to main'
          branch: ${{ steps.create_branch.outputs.new_branch }}
          base: main
          title: 'fix: automatic hotfix sync from stable'
          body: ${{ steps.create_branch.outputs.commit_message }}
