name: Deploy Prod iOS AppStore

on:
  workflow_dispatch:
    inputs:
      version-bump-type:
        description: 'Version Bump Type (major, minor, patch, build)'
        required: true
        default: 'build'

jobs:
  ios_prod_appstore_dist:
    runs-on: macos-14
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          ref: ${{ github.head_ref || github.ref_name }} # Use the branch that triggered the workflow
          fetch-depth: 0 # Ensures the entire history is checked out
          token: ${{ secrets.GH_ENG_ADMIN_PAT }}

      - name: Sync with remote
        run: |
          git fetch origin ${{ github.head_ref || github.ref_name }}
          git merge origin/${{ github.head_ref || github.ref_name }}

      - name: Determine version bump type
        id: determine-bump
        run: |
          if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
            echo "::set-output name=bump_type::${{ github.event.inputs.version-bump-type }}"
          else
            echo "Push event detected, defaulting to build"
            echo "::set-output name=bump_type::build"
          fi
        shell: bash

      - name: Validate Version Bump Type
        run: |
          VERSION_BUMP_TYPE="${{ steps.determine-bump.outputs.bump_type }}"
          if [[ ! "$VERSION_BUMP_TYPE" =~ ^(major|minor|patch|build)$ ]]; then
            echo "Error: Invalid version bump type '$VERSION_BUMP_TYPE'. Must be one of: major, minor, patch, build."
            exit 1
          fi
        shell: bash

      - name: Install GitHub CLI
        run: brew install gh

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: setup-cocoapods
        uses: maxim-lobanov/setup-cocoapods@v1
        with:
          podfile-path: ios/App/Podfile.lock

      - name: Authenticate NPM
        run: echo "//npm.pkg.github.com/:_authToken=${{ secrets.NPM_TOKEN }}" > ~/.npmrc

      - name: Cache
        id: node-modules
        uses: actions/cache@v3
        with:
          path: ~/.npm
          key: ${{ runner.OS }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Configure npm to use legacy peer dependencies
        run: npm config set legacy-peer-deps true

      - name: Install Dependencies
        uses: nick-invision/retry@v3
        with:
          timeout_minutes: 10
          max_attempts: 3
          command: npm ci

      - name: Build iOS App for Production
        run: |
          if npm run build:prod:syncIos; then
            echo "Build succeeded."
          else
            echo "Build failed."
            exit 1
          fi

      # Caching step for bundler dependencies (pointing to the subfolder Gemfile.lock)
      - name: Cache bundler dependencies
        id: cache-bundle
        uses: actions/cache@v3
        with:
          path: ios/App/vendor/bundle # Ensure this is the correct path for the bundled gems
          key: ${{ runner.os }}-gems-${{ hashFiles('ios/App/Gemfile.lock') }}
          restore-keys: |
            ${{ runner.os }}-gems-

      - name: Install Fastlane and Setup CI
        run: |
          set -e
          ruby_version=$(ruby -v | cut -d " " -f 2)
          required_ruby_version="2.7.0"
          if [ "$(printf '%s\n' "$required_ruby_version" "$ruby_version" | sort -V | head -n1)" = "$required_ruby_version" ]; then
            echo "Required Ruby version is already installed."
          else
            brew install ruby
          fi
          export GEM_HOME="$HOME/.gem"
          cd ios/App
          gem install bundler
          bundle install
          bundle exec fastlane install_plugins

      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: ${{ secrets.SECRET_GITHUB_SSH_PRIVATE_KEY }}

      - name: Increment and save build version to prevent blocking.
        if: ${{ steps.determine-bump.outputs.bump_type == 'build' }}
        env:
          APP_IDENTIFIER: 'com.sudshare.Sudsters'
          GITHUB_TOKEN: ${{ secrets.GH_ENG_ADMIN_PAT }}
        run: |
          export GEM_HOME="$HOME/.gem"
          cd ios/App/
          echo "Incrementing build version during appstore prod ios..."
          bundle exec fastlane run increment_build_number
          git config --global user.email "<EMAIL>"
          git config --global user.name "poplin-service-admin"

          echo "Status before push:"
          git status
          # Pull the latest changes from the branch
          git fetch origin
          git pull origin ${{ github.head_ref || github.ref_name }}

          echo "Local commits since last pull/rebase:"
          git log origin/${{ github.head_ref || github.ref_name }}..HEAD

          git add App.xcodeproj/project.pbxproj App/Info.plist Test-Info.plist
          git commit -m "chore: Increment build version [skip ci]"
          git push --verbose origin HEAD:${{ github.head_ref || github.ref_name }}

      - name: Upload to Testflight for Private Beta
        env:
          APP_IDENTIFIER: 'com.sudshare.Sudsters'
          APP_STORE_TEAM_ID: ${{ secrets.SECRET_APP_STORE_DEV_PORTAL_TEAM_ID }}
          APP_STORE_KEY_ID: ${{ secrets.APP_STORE_KEY_ID }}
          APP_STORE_ISSUER_ID: ${{ secrets.APP_STORE_ISSUER_ID }}
          MATCH_PASSWORD: ${{ secrets.MATCH_PASSWORD }}
          VERSION_BUMP_TYPE: ${{ steps.determine-bump.outputs.bump_type }}
        run: |
          export GEM_HOME="$HOME/.gem"
          cd ios/App/fastlane

          echo "${{ secrets.APP_STORE_AUTH_KEY }}" > AuthKey.p8

          echo "Running Fastlane for deploy to AppStore with version bump type $VERSION_BUMP_TYPE..."
          bundle exec fastlane appstore bump_type:$VERSION_BUMP_TYPE

      - name: Update environment files with iOS app version details
        run: |
          # Define paths
          INFO_PLIST_PATH="./ios/App/App/Info.plist"
          APP_ENV_FILE="./src/environments/app.version.ts"

          PACKAGE_JSON="./package.json"
          PACKAGE_LOCK_JSON="./package-lock.json"

          # Read version and build number from Info.plist
          APP_VERSION=$(/usr/libexec/PlistBuddy -c "Print CFBundleShortVersionString" "$INFO_PLIST_PATH")

           # Update iOS version and build number for all environment files
          sed -i '' "s|export const currentAppVersion = '[^']*';|export const currentAppVersion = '$APP_VERSION';|" "$APP_ENV_FILE"

          # Update version in package.json
          sed -i '' "3s/\"version\": \"[^\"]*\"/\"version\": \"$APP_VERSION\"/" $PACKAGE_JSON

          # Update version in package-lock.json, on line 3 and 9
          sed -i '' "3s/\"version\": \"[^\"]*\"/\"version\": \"$APP_VERSION\"/" $PACKAGE_LOCK_JSON
          sed -i '' "9s/\"version\": \"[^\"]*\"/\"version\": \"$APP_VERSION\"/" $PACKAGE_LOCK_JSON
        shell: bash

      - name: Commit Changes
        env:
          GITHUB_TOKEN: ${{ secrets.GH_ENG_ADMIN_PAT }}
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "poplin-service-admin"

          echo "Status before push:"
          git status
          # Pull the latest changes from the branch
          git fetch origin
          git pull origin ${{ github.head_ref || github.ref_name }}

          # fix any changes made by fastlane not using prettier rules. ie. capacitor.plugins.json
          npm run prettier:fix

          # Check if there are any changes to commit
          if [ -n "$(git status --porcelain)" ]; then
            echo "Changes detected, proceeding with new commit."
            git log origin/${{ github.head_ref || github.ref_name }}..HEAD

            git add .
            git commit -m "chore: auto-ci appstore prod ios action after release [skip ci]"
            git push https://${{ secrets.GH_ENG_ADMIN_PAT }}@github.com/${{ github.repository }} HEAD:${{ github.head_ref || github.ref_name }} --verbose

          else
            echo "No changes to commit."
          fi
