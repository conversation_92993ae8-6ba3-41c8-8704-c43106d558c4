name: Deploy Android Prod to App Distribution

on:
  workflow_dispatch:
    inputs:
      version-bump-type:
        description: 'Version Bump Type (major, minor, patch, build)'
        required: true
        default: 'build'
  push:
    branches:
      - 'release/*'

jobs:
  check_branch_sync:
    runs-on: ubuntu-latest
    if: ${{ github.ref_name != 'main' }}
    outputs:
      result: ${{ steps.branch_check.outputs.result }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          token: ${{ secrets.GH_ENG_ADMIN_PAT }}

      - name: Check if current branch and main are the same
        id: branch_check
        continue-on-error: true
        run: |
          git fetch origin main
          if [ "$(git rev-parse HEAD)" = "$(git rev-parse origin/main)" ]; then
            echo "Current branch is up-to-date with main. Skipping deployment."
            echo "result=false" >> "$GITHUB_OUTPUT"
            exit 1
          else
            echo "result=true" >> "$GITHUB_OUTPUT"
            echo "Current branch differs from main. Continuing beta build workflow."
          fi

  android_prod_app_dist:
    runs-on: ubuntu-latest
    needs: check_branch_sync
    if: ${{ github.ref_name == 'main' || needs.check_branch_sync.outputs.result == 'true' }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          token: ${{ secrets.GH_ENG_ADMIN_PAT }}

      - name: Check if current branch and main are the same
        if: ${{ github.event_name == 'push' }}
        run: |
          # Compare against main and exit if are the same, prevents running unnecessary when creating a release branch
          if [ "$(git rev-parse HEAD)" = "$(git rev-parse origin/main)" ]; then
            echo "Current branch is up-to-date with main. Ignoring workflow run, you can run manually if needed."
            exit 0
          else
            echo "Current branch differs from main. Continuing workflow."
          fi

      - name: Determine version bump type
        id: determine-bump
        run: |
          if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
            echo "bump_type=${{ github.event.inputs.version-bump-type }}" >> "$GITHUB_OUTPUT"
          else
            echo "Push event detected, defaulting to build"
            echo "bump_type=build" >> "$GITHUB_OUTPUT"
          fi
        shell: bash

      - name: Validate Version Bump Type
        run: |
          VERSION_BUMP_TYPE="${{ steps.determine-bump.outputs.bump_type }}"
          if [[ ! "$VERSION_BUMP_TYPE" =~ ^(major|minor|patch|build)$ ]]; then
            echo "Error: Invalid version bump type '$VERSION_BUMP_TYPE'. Must be one of: major, minor, patch, build."
            exit 1
          fi
        shell: bash

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Authenticate NPM
        run: echo "//npm.pkg.github.com/:_authToken=${{ secrets.NPM_TOKEN }}" > ~/.npmrc

      - name: Cache
        id: node-modules
        uses: actions/cache@v3
        env:
          cache-name: node-modules
        with:
          path: node_modules
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-build-${{ env.cache-name }}-
            ${{ runner.os }}-build-
            ${{ runner.os }}-

      - name: Install Dependencies
        if: steps.node-modules.outputs.cache-hit != 'true'
        run: |
          npm config set legacy-peer-deps true
          npm ci

      - name: Modify forter.gradle File
        run: |
          FILE="node_modules/forter-cordova-plugin/src/android/sdk/forter.gradle"
          if grep -q 'compile "com.forter.mobile:fortersdk:2.4.12@aar"' "$FILE"; then
            sed -i.bak 's/compile "com.forter.mobile:fortersdk:2.4.12@aar"/implementation "com.forter.mobile:fortersdk:2.4.12@aar"/' "$FILE"
            echo "Replaced 'compile' with 'implementation' in $FILE"
          else
            echo "No replacement made. Line not found or already updated."
          fi

      - name: Build Android App
        run: npm run build:prod:sync

      - name: Setup Java
        uses: actions/setup-java@v3
        with:
          distribution: 'temurin'
          java-version: '17'
          cache: 'gradle'

      - name: Setup Android SDK
        uses: android-actions/setup-android@v3

      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: '3.2.2'
          bundler-cache: true

      - name: Install Fastlane
        run: |
          cd android
          gem install bundler
          bundle install --verbose
          bundle exec fastlane install_plugins --verbose

      - name: Upload to Firebase App Distribution
        env:
          KEY_ALIAS: upload
          KEY_PASSWORD: ${{ secrets.ANDROID_KEY_PASSWORD }}
          STORE_PASSWORD: ${{ secrets.ANDROID_KEYSTORE_PASSWORD}}
          STORE_FILE: certs/google_play.keystore
          MY_GOOGLE_APPLICATION_CREDENTIALS: ${{ secrets.GCP_PROD }}
          APP_IDENTIFIER: 'com.sudshare.sudster'
          VERSION_BUMP_TYPE: ${{ steps.determine-bump.outputs.bump_type }}
        run: |
          cd android/fastlane
          echo "$MY_GOOGLE_APPLICATION_CREDENTIALS" > serviceAccountKey.json
          echo "Running fastlane ..."
          bundle exec fastlane betaProd bump_type:$VERSION_BUMP_TYPE --verbose

      - name: Commit Changes
        env:
          GITHUB_TOKEN: ${{ secrets.GH_ENG_ADMIN_PAT }}
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "poplin-service-admin"

          echo "Status before push:"
          git status
          # Pull the latest changes from the branch
          git fetch origin
          git pull origin ${{ github.head_ref || github.ref_name }}

          # fix any changes made by fastlane not using prettier rules. ie. capacitor.plugins.json
          npm run prettier:fix

          # Check if there are any changes to commit
          if [ -n "$(git status --porcelain)" ]; then
            echo "Changes detected, proceeding with new commit."
            git log origin/${{ github.head_ref || github.ref_name }}..HEAD

            git add .
            git commit -m "chore: auto-ci android prod bump build version [skip ci]"
            git push https://${{ secrets.GH_ENG_ADMIN_PAT }}@github.com/${{ github.repository }} HEAD:${{ github.head_ref || github.ref_name }} --verbose
          else
            echo "No changes to commit."
          fi
