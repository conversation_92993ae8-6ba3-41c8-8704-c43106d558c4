name: Deploy NonProd iOS to TestFlight

on:
  workflow_dispatch:
  push:
    branches:
      - main

jobs:
  ios_nonprod_app_dist:
    runs-on: macos-14
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          token: ${{ secrets.GH_ENG_ADMIN_PAT }}

      - name: Select Xcode 16.2
        uses: maxim-lobanov/setup-xcode@v1
        with:
          xcode-version: '16.2'

      - name: Show Xcode version
        run: xcodebuild -version

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Authenticate NPM
        run: echo "//npm.pkg.github.com/:_authToken=${{ secrets.NPM_TOKEN }}" > ~/.npmrc

      - name: Cache
        id: node-modules
        uses: actions/cache@v3
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}

      - name: Install Dependencies
        run: |
          npm config set legacy-peer-deps true
          npm ci

      - name: Build iOS App
        id: build_ios
        run: |
          if npm run test:actions:ios; then
            echo "status=success" >> "$GITHUB_OUTPUT"
          else
            echo "status=failure" >> "$GITHUB_OUTPUT"
            exit 1
          fi

      - name: Install Fastlane
        if: steps.build_ios.outputs.status == 'success'
        run: |
          set -e
          ruby_version=$(ruby -v | cut -d " " -f 2)
          required_ruby_version="2.7.0"
          if [ "$(printf '%s\n' "$required_ruby_version" "$ruby_version" | sort -V | head -n1)" = "$required_ruby_version" ]; then
            echo "Required Ruby version is already installed."
          else
            brew install ruby
          fi
          export GEM_HOME="$HOME/.gem"
          cd ios/App
          gem install bundler
          bundle install
          bundle exec fastlane install_plugins

      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: ${{ secrets.SECRET_GITHUB_SSH_PRIVATE_KEY }}

      - name: Increment and save build version to prevent blocking.
        env:
          APP_IDENTIFIER: 'com.sudshare.Sudsters.test'
          GITHUB_TOKEN: ${{ secrets.GH_ENG_ADMIN_PAT }}
        run: |
          export GEM_HOME="$HOME/.gem"
          cd ios/App/
          echo "Incrementing build version for nonProd test ios..."
          bundle exec fastlane run increment_build_number
          git config --global user.email "<EMAIL>"
          git config --global user.name "poplin-service-admin"

          echo "Status before push:"
          git status
          # Pull the latest changes from the branch
          git fetch origin
          git pull origin ${{ github.head_ref || github.ref_name }}

          echo "Local commits since last pull/rebase:"
          git log origin/${{ github.head_ref || github.ref_name }}..HEAD

          git add Test-Info.plist App/Info.plist App.xcodeproj/project.pbxproj
          git commit -m "chore: Increment build version nonProd action [skip ci]"
          git push --verbose origin HEAD:${{ github.head_ref || github.ref_name }}

      - name: Upload to Testflight for NonProd Test
        env:
          APP_IDENTIFIER: 'com.sudshare.Sudsters.test'
          APP_STORE_TEAM_ID: ${{ secrets.SECRET_APP_STORE_DEV_PORTAL_TEAM_ID }}
          APP_STORE_KEY_ID: ${{ secrets.APP_STORE_KEY_ID }}
          APP_STORE_ISSUER_ID: ${{ secrets.APP_STORE_ISSUER_ID }}
          MATCH_PASSWORD: ${{ secrets.MATCH_PASSWORD }}
        run: |
          export GEM_HOME="$HOME/.gem"
          cd ios/App/fastlane

          echo "${{ secrets.APP_STORE_AUTH_KEY }}" > AuthKey.p8

          echo "Running Fastlane for Non Prod Test"
          bundle exec fastlane test
          echo "Fastlane test lane completed successfully"

      - name: Commit any pending changes
        env:
          GITHUB_TOKEN: ${{ secrets.GH_ENG_ADMIN_PAT }}
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "poplin-service-admin"

          echo "Status before push:"
          git status
          # Pull the latest changes from the branch
          git fetch origin

          # fix any changes made by fastlane not using prettier rules. ie. capacitor.plugins.json
          npm run prettier:fix

          if [ -n "$(git status --porcelain)" ]; then
            echo "Changes detected, proceeding with new commit."
            git log origin/${{ github.head_ref || github.ref_name }}..HEAD

            git add .
            git commit -m "chore: auto-ci nonProd ios action after release [skip ci]"
            git push --verbose origin HEAD:${{ github.head_ref || github.ref_name }}
          else
            echo "No changes to commit."
          fi
