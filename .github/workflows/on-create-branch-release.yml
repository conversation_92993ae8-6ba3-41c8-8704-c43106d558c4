name: Initiate Release deployment

on:
  workflow_dispatch:
  create: # This event is triggered only once when the branch is created, not on subsequent pushes
    branches:
      - 'release/**'

jobs:
  check_is_release_branch_not_basic_branch:
    runs-on: ubuntu-latest
    outputs:
      proceed: ${{ steps.branch_check.outputs.proceed }}
    steps:
      - name: Check if branch starts with "release/"
        id: branch_check
        run: |
          branch_name="${GITHUB_REF#refs/heads/}"
          echo "Current branch: $branch_name"
          if [[ "$branch_name" == release/* ]]; then
            echo "Branch starts with release/. Continuing workflow."
            echo "proceed=true" >> "$GITHUB_OUTPUT"
          else
            echo "Branch does not start with release/. Exiting workflow."
            echo "proceed=false" >> "$GITHUB_OUTPUT"
            exit 1
          fi

  create_release_branch:
    runs-on: macos-14
    needs: check_is_release_branch_not_basic_branch
    if: needs.check_is_release_branch_not_basic_branch.outputs.proceed == 'true'
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          ref: ${{ github.head_ref || github.ref_name }}
          fetch-depth: 0 # Ensures the entire history is checked out
          token: ${{ secrets.GH_ENG_ADMIN_PAT }}

      - name: Sync with remote
        run: |
          git fetch origin ${{ github.head_ref || github.ref_name }}
          git merge origin/${{ github.head_ref || github.ref_name }}

      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: '3.0'

      # Caching step for bundler dependencies (pointing to the subfolder Gemfile.lock)
      - name: Cache bundler dependencies
        id: cache-bundle
        uses: actions/cache@v3
        with:
          path: ios/App/vendor/bundle # Check if correct path
          key: ${{ runner.os }}-gems-${{ hashFiles('ios/App/Gemfile.lock') }}
          restore-keys: |
            ${{ runner.os }}-gems-

      # Install Fastlane and required dependencies with caching
      - name: Install Fastlane
        run: |
          set -e
          ruby_version=$(ruby -v | cut -d " " -f 2)
          required_ruby_version="2.7.0"
          if [ "$(printf '%s\n' "$required_ruby_version" "$ruby_version" | sort -V | head -n1)" = "$required_ruby_version" ]; then
            echo "Required Ruby version is already installed."
          else
            brew install ruby
          fi
          export GEM_HOME="$HOME/.gem"
          cd ios/App
          gem install bundler
          bundle install
          bundle exec fastlane install_plugins

      - name: Increment minor version for prod test ios
        env:
          APP_IDENTIFIER: 'com.sudshare.Sudsters'
        run: |
          export GEM_HOME="$HOME/.gem"
          cd ios/App/
          echo "Incrementing minor version for prod test ios..."
          bundle exec fastlane run increment_version_number bump_type:minor
          bundle exec fastlane run increment_build_number build_number:0

      - name: Increment minor version for prod test android
        run: |
          echo "Incrementing minor version for prod test android..."
          export GEM_HOME="$HOME/.gem"
          cd ./android/fastlane
          gem install bundler
          bundle install
          bundle exec fastlane install_plugins
          bundle exec fastlane bump_minor

      - name: Update environment files with iOS app version details
        run: |
          # Define paths
          INFO_PLIST_PATH="./ios/App/App/Info.plist"
          APP_ENV_FILE="./src/environments/app.version.ts"

          PACKAGE_JSON="./package.json"
          PACKAGE_LOCK_JSON="./package-lock.json"

          # Read version and build number from Info.plist
          APP_VERSION=$(/usr/libexec/PlistBuddy -c "Print CFBundleShortVersionString" "$INFO_PLIST_PATH")

          # Update iOS version and build number for all environment files
          sed -i '' "s|export const currentAppVersion = '[^']*';|export const currentAppVersion = '$APP_VERSION';|" "$APP_ENV_FILE"

          # Update version in package.json
          sed -i '' "3s/\"version\": \"[^\"]*\"/\"version\": \"$APP_VERSION\"/" $PACKAGE_JSON

          # Update version in package-lock.json, on line 3 and 9
          sed -i '' "3s/\"version\": \"[^\"]*\"/\"version\": \"$APP_VERSION\"/" $PACKAGE_LOCK_JSON
          sed -i '' "9s/\"version\": \"[^\"]*\"/\"version\": \"$APP_VERSION\"/" $PACKAGE_LOCK_JSON

        shell: bash

      - name: Commit build number increment
        run: |
          echo "Committing build number increment..."
          git config --global user.email "<EMAIL>"
          git config --global user.name "poplin-service-admin"

          echo "Status before push:"
          git status
          echo "Local commits since last pull/rebase:"
          git log origin/${{ github.head_ref || github.ref_name }}..HEAD

          git add .
          git commit -m "chore: Increment minor version for prod release"
          git push https://${{ secrets.GH_ENG_ADMIN_PAT }}@github.com/${{ github.repository }} HEAD:${{ github.head_ref || github.ref_name }} --verbose

      - name: Authenticate with GCP
        uses: google-github-actions/auth@v1
        with:
          credentials_json: ${{ secrets.GCP_TEST }}

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v1

      - name: Update Remote Config version in Firestore
        run: |
          PACKAGE_JSON="./package.json"

          # Read version from package.json it should be updated in the previous step
          APP_VERSION=$(jq -r '.version' $PACKAGE_JSON)

          # Give execute permission to the script
          chmod +x ./scripts/update-remote-config-version.sh

          # Run on Prod
          ./scripts/update-remote-config-version.sh laundry-llama $APP_VERSION
