name: CapGo Bundle Upload

on:
  # Can be called manually:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        type: choice
        default: dev
        options:
          - dev
          # - soft-prod TODO: Add soft-prod channel when ready
          - production
      channel:
        description: 'CapGo channel'
        required: true
        type: choice
        default: dev
        options:
          - dev
          # - beta TODO: Add beta channel when softprod is ready
          - production
      commit_changes:
        description: 'Commit version changes back to repository'
        required: false
        type: boolean
        default: false
      custom_version:
        description: 'Override bundle version (optional)'
        required: false
        type: string
  # Can be called by other workflows:
  workflow_call:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        type: string
      channel:
        description: 'CapGo channel'
        required: true
        type: string
      commit_changes:
        description: 'Commit version changes back to repository'
        required: false
        type: boolean
        default: false
      custom_version:
        description: 'Override bundle version (optional)'
        required: false
        type: string

jobs:
  upload-bundle:
    if: ${{ !vars.DISABLE_CAPGO_UPDATES }}
    runs-on: ubuntu-latest
    env:
      CG_DEFAULT_ENVIRONMENT: ${{ inputs.environment }}
      CG_DEFAULT_CHANNEL: ${{ inputs.channel }}
      CG_DEFAULT_COMMIT_CHANGES: ${{ inputs.commit_changes }}
      CG_DEFAULT_CUSTOM_VERSION: ${{ inputs.custom_version }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Use HTTP authentication
        run: >
          git config --global url."https://github.com/".insteadOf
          ssh://**************/

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Authenticate NPM
        run: echo "//npm.pkg.github.com/:_authToken=${{ secrets.NPM_TOKEN }}" > ~/.npmrc

      - name: Cache
        id: node-modules
        uses: actions/cache@v3
        env:
          cache-name: node-modules
        with:
          path: node_modules
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-build-${{ env.cache-name }}-
            ${{ runner.os }}-build-
            ${{ runner.os }}-

      - name: Install Dependencies
        if: steps.node-modules.outputs.cache-hit != 'true'
        run: |
          npm config set legacy-peer-deps true
          npm ci

      # Build based on environment
      - name: Build Dev
        if: ${{ env.CG_DEFAULT_ENVIRONMENT == 'dev' }}
        run: npm run build

      - name: Build Soft Prod
        if: ${{ env.CG_DEFAULT_ENVIRONMENT == 'soft-prod' }}
        run: npm run build:softprod

      - name: Build Prod
        if: ${{ env.CG_DEFAULT_ENVIRONMENT == 'production' }}
        run: npm run build:prod

      - name: Set Version Variables
        id: set_version
        run: |
          # Set package version
          PACKAGE_VERSION=$(node -p "require('./package.json').version")
          echo "PACKAGE_VERSION=${PACKAGE_VERSION}" >> $GITHUB_ENV

          # Generate bundle version
          if [[ -n "${{ env.CG_DEFAULT_CUSTOM_VERSION }}" ]]; then
            BUNDLE_VERSION="${{ env.CG_DEFAULT_CUSTOM_VERSION }}"
            echo "Using custom version: ${BUNDLE_VERSION}"
          else
            TIMESTAMP=$(date +%Y%m%d%H%M%S)
            if [[ "${{ env.CG_DEFAULT_ENVIRONMENT }}" == "dev" ]]; then
              BUNDLE_VERSION="${PACKAGE_VERSION}-dev.${TIMESTAMP}"
            elif [[ "${{ env.CG_DEFAULT_ENVIRONMENT }}" == "soft-prod" ]]; then
              BUNDLE_VERSION="${PACKAGE_VERSION}-beta.${TIMESTAMP}"
            else
              BUNDLE_VERSION="${PACKAGE_VERSION}"
            fi
            echo "Using generated version: ${BUNDLE_VERSION}"
          fi
          echo "BUNDLE_VERSION=${BUNDLE_VERSION}" >> $GITHUB_ENV

          # Set outputs for use in other steps
          echo "package_version=${PACKAGE_VERSION}" >> $GITHUB_OUTPUT
          echo "bundle_version=${BUNDLE_VERSION}" >> $GITHUB_OUTPUT

      - name: Verify Versions
        run: |
          echo "Package Version: ${{ env.PACKAGE_VERSION }}"
          echo "Bundle Version: ${{ env.BUNDLE_VERSION }}"

      - name: Update app.version.ts
        run: |
          # Update currentAppVersion
          sed -i "s|export const currentAppVersion = '[^']*'|export const currentAppVersion = '${{ env.PACKAGE_VERSION }}'|" src/environments/app.version.ts

          # Check if bundleVersion exists and update or add it
          if grep -q "export const bundleVersion" src/environments/app.version.ts; then
            # Update existing bundleVersion
            sed -i "s|export const bundleVersion = '[^']*'|export const bundleVersion = '${{ env.BUNDLE_VERSION }}'|" src/environments/app.version.ts
          else
            # Add bundleVersion as a new line
            echo "export const bundleVersion = '${{ env.BUNDLE_VERSION }}';" >> src/environments/app.version.ts
          fi

      - name: Verify app.version.ts
        run: cat src/environments/app.version.ts

      - name: Verify Capgo CLI
        run: npx @capgo/cli --version

      - name: Upload to CapGo
        run: |
          npx @capgo/cli bundle upload \
            --apikey ${{ secrets.CAPGO_LP_API_KEY }} \
            --channel ${{ env.CG_DEFAULT_CHANNEL }} \
            --bundle ${{ env.BUNDLE_VERSION }}

      - name: Commit Changes
        if: ${{ env.CG_DEFAULT_COMMIT_CHANGES == 'true' }}
        env:
          GITHUB_TOKEN: ${{ secrets.GH_ENG_ADMIN_PAT }}
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "poplin-service-admin"

          echo "Current git status:"
          git status

          echo "Fetching latest changes..."
          git fetch origin

          BRANCH="${{ github.head_ref || github.ref_name }}"
          echo "Working on branch: $BRANCH"

          echo "Pulling latest changes..."
          git pull origin $BRANCH

          git add src/environments/app.version.ts
          git commit -m "chore: auto-ci capgo bundle upload [skip ci]"
          git push https://${{ secrets.GH_ENG_ADMIN_PAT }}@github.com/${{ github.repository }} HEAD:$BRANCH
