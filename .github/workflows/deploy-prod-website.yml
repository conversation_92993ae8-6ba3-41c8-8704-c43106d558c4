name: Deploy Website to Prod

'on':
  push:
    tags:
      - 'v[0-9]+.[0-9]+.[0-9]+'
  workflow_dispatch:

jobs:
  build_and_deploy_prod:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Use HTTP authentication
        run: >
          git config --global url."https://github.com/".insteadOf
          ssh://**************/

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: 16

      - name: Authenticate NPM
        run: echo "//npm.pkg.github.com/:_authToken=${{ secrets.NPM_TOKEN }}" > ~/.npmrc

      - name: Cache
        id: node-modules
        uses: actions/cache@v3
        env:
          cache-name: node-modules
        with:
          path: node_modules
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-build-${{ env.cache-name }}-
            ${{ runner.os }}-build-
            ${{ runner.os }}-

      - name: Install Dependencies
        if: steps.node-modules.outputs.cache-hit != 'true'
        run: |
          npm config set legacy-peer-deps true
          npm ci

      - name: Build Prod
        run: npm run build:prod

      - name: Upload Sourcemaps
        run: npm run upload:sourcemaps
        env:
          ROLLBAR_ACCESS_TOKEN: ${{ secrets.ROLLBAR_ACCESS_TOKEN }}
          MINIFIED_URL: ${{ secrets.MINIFIED_URL_PROD }}

      - name: Delete Sourcemaps
        run: npm run delete:sourcemaps

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: 16

      - name: Deploy Prod
        uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_LAUNDRY_LLAMA }}'
          projectId: laundry-llama
          target: prod
          channelId: live
          firebaseToolsVersion: ^9.20.0
