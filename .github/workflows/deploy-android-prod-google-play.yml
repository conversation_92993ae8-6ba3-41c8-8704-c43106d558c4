name: Deploy Prod Android to Google Play
on:
  workflow_dispatch:
  push:
    branches:
      - 'stable/*'

jobs:
  android_prod_google_play:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: 16

      - name: Authenticate NPM
        run: echo "//npm.pkg.github.com/:_authToken=${{ secrets.NPM_TOKEN }}" > ~/.npmrc

      - name: Cache
        id: node-modules
        uses: actions/cache@v3
        env:
          cache-name: node-modules
        with:
          path: node_modules
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-build-${{ env.cache-name }}-
            ${{ runner.os }}-build-
            ${{ runner.os }}-

      - name: Install Dependencies
        if: steps.node-modules.outputs.cache-hit != 'true'
        run: |
          npm config set legacy-peer-deps true
          npm ci

      - name: Modify forter.gradle File
        run: |
          FILE="node_modules/forter-cordova-plugin/src/android/sdk/forter.gradle"
          if grep -q 'compile "com.forter.mobile:fortersdk:2.4.12@aar"' "$FILE"; then
            sed -i.bak 's/compile "com.forter.mobile:fortersdk:2.4.12@aar"/implementation "com.forter.mobile:fortersdk:2.4.12@aar"/' "$FILE"
            echo "Replaced 'compile' with 'implementation' in $FILE"
          else
            echo "No replacement made. Line not found or already updated."
          fi

      - name: Build Android App
        run: npm run build:prod:sync

      - name: Setup Java
        uses: actions/setup-java@v3
        with:
          distribution: 'temurin'
          java-version: '17'

      - name: Setup Android SDK
        uses: android-actions/setup-android@v3

      - name: Build Android Release AAB
        env:
          KEY_ALIAS: ${{ secrets.GOOGLE_PLAY_KEY_ALIAS }}
          KEY_PASSWORD: ${{ secrets.GOOGLE_PLAY_KEY_PW}}
          STORE_PASSWORD: ${{ secrets.GOOGLE_PLAY_KEY_PW}}
          STORE_FILE: ../certs/prod-release.keystore
          MY_GOOGLE_APPLICATION_CREDENTIALS: ${{ secrets.GOOGLE_PLAY_UPLOAD_KEY }}
        run: |
          cd android
          ./gradlew bundleRelease

      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: '3.2.2'
          bundler-cache: true

      - name: Setup Google Play Store credentials
        run: |
          echo "${{ secrets.GOOGLE_PLAY_JSON_KEY }}" > /home/<USER>/work/sudshare-sudster/sudshare-sudster/android/app/src/prod/google_services.json
        env:
          GOOGLE_APPLICATION_CREDENTIALS: /home/<USER>/work/sudshare-sudster/sudshare-sudster/android/app/src/prod/google_services.json

      - name: Install Fastlane
        run: |
          cd android
          gem install bundler
          bundle install --verbose
          bundle exec fastlane install_plugins --verbose

      - name: Upload to Google Play
        env:
          KEY_ALIAS: ${{ secrets.GOOGLE_PLAY_KEY_ALIAS }}
          KEY_PASSWORD: ${{ secrets.GOOGLE_PLAY_KEY_PW}}
          STORE_PASSWORD: ${{ secrets.GOOGLE_PLAY_KEY_PW}}
          MY_GOOGLE_APPLICATION_CREDENTIALS: ${{ secrets.GCP_PROD }}
          GOOGLE_SERVICE_ACCOUNT_JSON: ${{secrets.GOOGLE_SERVICE_ACCOUNT_JSON}}
          APP_IDENTIFIER": 'com.sudshare.sudster'
        run: |
          cd android/fastlane
          echo "$MY_GOOGLE_APPLICATION_CREDENTIALS" > serviceAccountKey.json
          echo "Running fastlane for Google Play deployment..."
          bundle exec fastlane deploy_to_play_store --verbose
