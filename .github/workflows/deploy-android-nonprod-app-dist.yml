name: Deploy Android NonProd to App Distribution

on:
  push:
    branches:
      - 'main'
  workflow_dispatch:

jobs:
  android_nonprod_app_dist:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          token: ${{ secrets.GH_ENG_ADMIN_PAT }}

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: 16

      - name: Authenticate NPM
        run: echo "//npm.pkg.github.com/:_authToken=${{ secrets.NPM_TOKEN }}" > ~/.npmrc

      - name: Cache
        id: node-modules
        uses: actions/cache@v3
        env:
          cache-name: node-modules
        with:
          path: node_modules
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-build-${{ env.cache-name }}-
            ${{ runner.os }}-build-
            ${{ runner.os }}-

      - name: Install Dependencies
        if: steps.node-modules.outputs.cache-hit != 'true'
        run: |
          npm config set legacy-peer-deps true
          npm ci

      - name: Modify forter.gradle File
        run: |
          FILE="node_modules/forter-cordova-plugin/src/android/sdk/forter.gradle"
          if grep -q 'compile "com.forter.mobile:fortersdk:2.4.12@aar"' "$FILE"; then
            sed -i.bak 's/compile "com.forter.mobile:fortersdk:2.4.12@aar"/implementation "com.forter.mobile:fortersdk:2.4.12@aar"/' "$FILE"
            echo "Replaced 'compile' with 'implementation' in $FILE"
          else
            echo "No replacement made. Line not found or already updated."
          fi

      - name: Build Android App
        run: npm run build:sync

      - name: Setup Java
        uses: actions/setup-java@v3
        with:
          distribution: 'temurin'
          java-version: '17'
          cache: 'gradle'

      - name: Setup Android SDK
        uses: android-actions/setup-android@v3

      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: '3.2.2'
          bundler-cache: true

      - name: Install Fastlane
        run: |
          cd android
          gem install bundler
          bundle install --verbose
          bundle exec fastlane install_plugins

      - name: Upload to Firebase App Distribution
        env:
          MY_GOOGLE_APPLICATION_CREDENTIALS: ${{ secrets.GCP_TEST }}
          APP_IDENTIFIER": 'com.sudshare.sudster.test'
        run: |
          cd android/fastlane
          echo "$MY_GOOGLE_APPLICATION_CREDENTIALS" > serviceAccountKey.json
          echo "Running fastlane..."
          bundle exec fastlane beta --verbose

      - name: Commit build number increment
        env:
          GITHUB_TOKEN: ${{ secrets.GH_ENG_ADMIN_PAT }}
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "poplin-service-admin"

          echo "Status before push:"
          git status
          # Pull the latest changes from the branch
          git fetch origin
          git pull origin ${{ github.head_ref || github.ref_name }}

          # fix any changes made by fastlane not using prettier rules. ie. capacitor.plugins.json
          npm run prettier:fix

          # Check if there are any changes to commit
          if [ -n "$(git status --porcelain)" ]; then
            echo "Changes detected, proceeding with new commit."
            git log origin/${{ github.head_ref || github.ref_name }}..HEAD

            git add .
            git commit -m "chore: auto-ci android nonProd bump build version [skip ci]"
            git push --verbose origin HEAD:${{ github.head_ref || github.ref_name }}
          else
            echo "No changes to commit."
          fi
