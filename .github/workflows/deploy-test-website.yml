name: Deploy Website to Test

'on':
  push:
    branches:
      - 'release/**'
      - main
  workflow_dispatch:

jobs:
  build_and_deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Use HTTP authentication
        run: >
          git config --global url."https://github.com/".insteadOf
          ssh://**************/

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: 16

      - name: Authenticate NPM
        run: echo "//npm.pkg.github.com/:_authToken=${{ secrets.NPM_TOKEN }}" > ~/.npmrc

      - name: Cache
        id: node-modules
        uses: actions/cache@v3
        env:
          cache-name: node-modules
        with:
          path: node_modules
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-build-${{ env.cache-name }}-
            ${{ runner.os }}-build-
            ${{ runner.os }}-

      - name: Install Dependencies
        if: steps.node-modules.outputs.cache-hit != 'true'
        run: |
          npm config set legacy-peer-deps true
          npm ci

      - name: Build Test
        run: npm run build

      - name: Upload Sourcemaps
        run: npm run upload:sourcemaps
        env:
          ROLLBAR_ACCESS_TOKEN: ${{ secrets.ROLLBAR_ACCESS_TOKEN }}
          MINIFIED_URL: ${{ secrets.MINIFIED_URL_TEST }}

      - name: Delete Sourcemaps
        run: npm run delete:sourcemaps

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: 16

      - name: Deploy Preview
        id: deploy_preview
        uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_SUDSHARETEST }}'
          expires: 1h
          projectId: sudsharetest
          target: nonProd
          channelId: nonProdTest
          firebaseToolsVersion: ^9.20.0

      # TODO: Resolve this inconsist step
      # - name: Trigger Workflow and Wait
      #   uses: convictional/trigger-workflow-and-wait@v1.6.5
      #   with:
      #     owner: sudshare
      #     repo: sudshare-qa
      #     github_token: ${{ secrets.QA_TOKEN }}
      #     workflow_file_name: LP-NONPROD-browser.yml
      #     ref: master
      #     wait_interval: 10
      #     client_payload: '{"firestore_temp_url": "${{ fromJson(steps.deploy_preview.outputs.urls)[0] }}"}'

      - name: Deploy Test
        uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_SUDSHARETEST }}'
          projectId: sudsharetest
          target: nonProd
          channelId: live
          firebaseToolsVersion: ^9.20.0
