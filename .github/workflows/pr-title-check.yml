name: PR Title Check

on:
  pull_request:
    types: [opened, edited, synchronize]

jobs:
  check-pr-title:
    runs-on: ubuntu-latest
    steps:
      - name: Check PR title format
        env:
          PR_TITLE: ${{ github.event.pull_request.title }}
        run: |
          echo "Checking PR title: '$PR_TITLE'"

          if [[ ! "$PR_TITLE" =~ ^\[(feat|fix|hotfix|chore)\]\ POP-[0-9]{1,5}:\ .+$ ]] && [[ ! "$PR_TITLE" =~ ^Release\/[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
            echo "❌ PR title does not follow the required format!"
            echo "Expected format for JIRA tickets: '[<type>] POP-XXX: <description>' (e.g., '[feat] POP-123: fix login button')"
            echo "Or release format: 'Release/x.y.z' (e.g., 'Release/4.0.1')"
            exit 1
          fi

          echo "✅ PR title format is valid."
