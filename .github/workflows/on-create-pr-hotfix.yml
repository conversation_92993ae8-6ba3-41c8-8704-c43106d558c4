name: Hotfix Patch Bump

on:
  workflow_dispatch:
  pull_request:
    types: [opened]
    branches:
      - 'hotfix/**'

jobs:
  bump_patch:
    runs-on: macos-14

    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          ref: ${{ github.head_ref || github.ref_name }}
          fetch-depth: 0
          token: ${{ secrets.GH_ENG_ADMIN_PAT }}

      - name: Cache bundler dependencies
        id: cache-bundle
        uses: actions/cache@v3
        with:
          path: ios/App/vendor/bundle # check if correct path
          key: ${{ runner.os }}-gems-${{ hashFiles('ios/App/Gemfile.lock') }}
          restore-keys: |
            ${{ runner.os }}-gems-

      # Install Fastlane and required dependencies with caching
      - name: Install Fastlane
        if: steps.build_ios.outputs.status == 'success'
        run: |
          set -e
          ruby_version=$(ruby -v | cut -d " " -f 2)
          required_ruby_version="2.7.0"
          if [ "$(printf '%s\n' "$required_ruby_version" "$ruby_version" | sort -V | head -n1)" = "$required_ruby_version" ]; then
            echo "Required Ruby version is already installed."
          else
            brew install ruby
          fi
          export GEM_HOME="$HOME/.gem"
          cd ios/App
          gem install bundler
          bundle install
          bundle exec fastlane install_plugins

      - name: Increment patch number
        run: |
          echo "Incrementing patch number for hotfix...
          cd ios/App
          bundle exec fastlane run increment_version_number bump_type:patch

      - name: Increment patch version for prod test android
        run: |
          echo "Incrementing minor version for prod test android..."
          export GEM_HOME="$HOME/.gem"
          cd ./android/fastlane
          gem install bundler
          bundle install
          bundle exec fastlane install_plugins
          bundle exec fastlane bump_patch

      - name: Update environment files with iOS app version details
        run: |
          # Define paths
          INFO_PLIST_PATH="./ios/App/App/Info.plist"
          ENV_FILE="./src/environments/environment.ts"
          ENV_PROD_FILE="./src/environments/environment.prod.ts"

          # Read version and build number from Info.plist
          APP_VERSION=$(/usr/libexec/PlistBuddy -c "Print CFBundleShortVersionString" "$INFO_PLIST_PATH")
          BUILD_NUMBER=$(/usr/libexec/PlistBuddy -c "Print CFBundleVersion" "$INFO_PLIST_PATH")
        shell: bash

      - name: Commit build number increment
        run: |
          echo "Committing build number increment..."
          git config --global user.email "<EMAIL>"
          git config --global user.name "poplin-service-admin"

          echo "Status before push:"
          git status
          # Pull the latest changes from the branch
          git fetch origin
          git pull origin ${{ github.head_ref || github.ref_name }}
          echo "Local commits since last pull/rebase:"
          git log origin/${{ github.head_ref || github.ref_name }}..HEAD

          git add .
          git commit -m "chore: Increment patch version for hotfix release [skip ci]"
          git push https://${{ secrets.GH_ENG_ADMIN_PAT }}@github.com/${{ github.repository }} HEAD:${{ github.head_ref || github.ref_name }} --verbose
