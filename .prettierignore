*~
*.sw[mnpcod]
*.log
*.tmp
*.tmp.*
log.txt
*.sublime-project
*.sublime-workspace
npm-debug.log*

.firebase/

.idea/
*/.idea
*/.idea/
.ionic/
.sourcemaps/
.sass-cache/
.tmp/
.versions/
coverage/
www/
node_modules/
tmp/
temp/
platforms/
/plugins/
plugins/android.json
plugins/ios.json
$RECYCLE.BIN/

.DS_Store
Thumbs.db
UserInterfaceState.xcuserstate

dist/

ios/App/build
ios/App/Pods
ios/App/App/public
ios/App/Podfile.lock
ios/xcuserdata

ios/capacitor-cordova-ios-plugins

ios/App/App/capacitor.config.json
ios/App/App/config.xml

android/app/build/

android/*.apk
android/*.ap_
android/*.aab

android/*.dex

android/*.class

android/bin/
android/gen/
android/out/
android/release/

android/.gradle/
android/build/

android/local.properties

android/proguard/

android/*.log
android/.navigation/
android/captures/

android/*.iml
android/.idea/workspace.xml
android/.idea/tasks.xml
android/.idea/gradle.xml
android/.idea/assetWizardSettings.xml
android/.idea/dictionaries
android/.idea/libraries
android/.idea/caches
android/.idea/modules.xml
android/.idea/navEditor.xml

android/.externalNativeBuild

android/freeline.py
android/freeline/
android/freeline_project_description.json

android/fastlane/report.xml
android/fastlane/Preview.html
android/fastlane/screenshots
android/fastlane/test_output
android/fastlane/readme.md

android/vcs.xml

android/lint/intermediates/
android/lint/generated/
android/lint/outputs/
android/lint/tmp/

android/capacitor-cordova-android-plugins

android/app/src/main/assets/public

android/app/src/main/assets/capacitor.config.json
android/app/src/main/res/xml/config.xml
.angular