{"hosting": [{"target": "softProd", "public": "www", "cleanUrls": true, "trailingSlash": false, "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "**", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}, {"source": "*.[0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f].+(css|js)", "headers": [{"key": "Cache-Control", "value": "public,max-age=31536000,immutable"}]}]}, {"target": "nonProd", "public": "www", "cleanUrls": true, "trailingSlash": false, "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "**", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}, {"source": "*.[0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f].+(css|js)", "headers": [{"key": "Cache-Control", "value": "public,max-age=31536000,immutable"}]}]}, {"target": "prod", "public": "www", "cleanUrls": true, "trailingSlash": false, "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "**", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}, {"source": "*.[0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f][0-9a-f].+(css|js)", "headers": [{"key": "Cache-Control", "value": "public,max-age=31536000,immutable"}]}]}]}