{"compileOnSave": false, "compilerOptions": {"baseUrl": "./", "downlevelIteration": true, "importHelpers": true, "outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "module": "esnext", "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "target": "es2020", "typeRoots": ["node_modules/@types"], "lib": ["es2018", "dom", "ES2021.String"], "esModuleInterop": true, "skipLibCheck": true, "noImplicitAny": false, "resolveJsonModule": true}}