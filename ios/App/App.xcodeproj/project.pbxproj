// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 48;
	objects = {

/* Begin PBXBuildFile section */
		0C2E35AE937EE27A08E5BC74 /* Pods_Test.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B1C82D96E0D9F09A45FF6DF8 /* Pods_Test.framework */; };
		1AAC48521BCA87C2ED5C290A /* Pods_Prod.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 12DDE91DE012FD4BF22FBE1A /* Pods_Prod.framework */; };
		2FAD9763203C412B000D30F8 /* config.xml in Resources */ = {isa = PBXBuildFile; fileRef = 2FAD9762203C412B000D30F8 /* config.xml */; };
		4540EA23292577B900A3C42A /* SudSharePlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = 4540EA22292577B900A3C42A /* SudSharePlugin.m */; };
		4540EA24292577B900A3C42A /* SudSharePlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = 4540EA22292577B900A3C42A /* SudSharePlugin.m */; };
		4540EA26292577C400A3C42A /* SudSharePlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4540EA25292577C400A3C42A /* SudSharePlugin.swift */; };
		4540EA27292577C400A3C42A /* SudSharePlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4540EA25292577C400A3C42A /* SudSharePlugin.swift */; };
		45CC8F972908D406006033F9 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 504EC3071FED79650016851F /* AppDelegate.swift */; };
		45CC8F982908D406006033F9 /* AppRequestReview.m in Sources */ = {isa = PBXBuildFile; fileRef = 5C5D9D51254369CD00D48722 /* AppRequestReview.m */; };
		45CC8F992908D406006033F9 /* AppRequestReview.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5C5D9D4D2543697800D48722 /* AppRequestReview.swift */; };
		45CC8FA02908D406006033F9 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 504EC3101FED79650016851F /* LaunchScreen.storyboard */; };
		45CC8FA12908D406006033F9 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 504EC30E1FED79650016851F /* Assets.xcassets */; };
		45CC8FA22908D406006033F9 /* ordernotificationsound.wav in Resources */ = {isa = PBXBuildFile; fileRef = A775905C27205740000E5BB9 /* ordernotificationsound.wav */; };
		45CC8FA32908D406006033F9 /* capacitor.config.json in Resources */ = {isa = PBXBuildFile; fileRef = 50379B222058CBB4000EE86E /* capacitor.config.json */; };
		45CC8FA42908D406006033F9 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 504EC30B1FED79650016851F /* Main.storyboard */; };
		45CC8FA52908D406006033F9 /* config.xml in Resources */ = {isa = PBXBuildFile; fileRef = 2FAD9762203C412B000D30F8 /* config.xml */; };
		45CC8FA72908D406006033F9 /* public in Resources */ = {isa = PBXBuildFile; fileRef = 5CB5674526ADD1E600094728 /* public */; };
		50379B232058CBB4000EE86E /* capacitor.config.json in Resources */ = {isa = PBXBuildFile; fileRef = 50379B222058CBB4000EE86E /* capacitor.config.json */; };
		504EC3081FED79650016851F /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 504EC3071FED79650016851F /* AppDelegate.swift */; };
		504EC30D1FED79650016851F /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 504EC30B1FED79650016851F /* Main.storyboard */; };
		504EC30F1FED79650016851F /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 504EC30E1FED79650016851F /* Assets.xcassets */; };
		504EC3121FED79650016851F /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 504EC3101FED79650016851F /* LaunchScreen.storyboard */; };
		5C5D9D4E2543697800D48722 /* AppRequestReview.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5C5D9D4D2543697800D48722 /* AppRequestReview.swift */; };
		5C5D9D52254369CD00D48722 /* AppRequestReview.m in Sources */ = {isa = PBXBuildFile; fileRef = 5C5D9D51254369CD00D48722 /* AppRequestReview.m */; };
		5CB5674626ADD1E600094728 /* public in Resources */ = {isa = PBXBuildFile; fileRef = 5CB5674526ADD1E600094728 /* public */; };
		A70B57AF29C3505800B4B261 /* SegmentPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = A70B57AE29C3505800B4B261 /* SegmentPlugin.swift */; };
		A70B57B029C3505800B4B261 /* SegmentPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = A70B57AE29C3505800B4B261 /* SegmentPlugin.swift */; };
		A70B57B229C3513A00B4B261 /* SegmentPlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = A70B57B129C3513A00B4B261 /* SegmentPlugin.m */; };
		A70B57B329C3513A00B4B261 /* SegmentPlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = A70B57B129C3513A00B4B261 /* SegmentPlugin.m */; };
		A70B57B529C3528E00B4B261 /* SudshareConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = A70B57B429C3528E00B4B261 /* SudshareConfig.swift */; };
		A70B57B629C3528E00B4B261 /* SudshareConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = A70B57B429C3528E00B4B261 /* SudshareConfig.swift */; };
		A763B7322C2BD10400069437 /* IterablePlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = A763B7312C2BD10400069437 /* IterablePlugin.m */; };
		A763B7332C2BD10400069437 /* IterablePlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = A763B7312C2BD10400069437 /* IterablePlugin.m */; };
		A763B7352C2BD13100069437 /* IterablePlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = A763B7342C2BD13100069437 /* IterablePlugin.swift */; };
		A763B7362C2BD13100069437 /* IterablePlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = A763B7342C2BD13100069437 /* IterablePlugin.swift */; };
		A775905D27205740000E5BB9 /* ordernotificationsound.wav in Resources */ = {isa = PBXBuildFile; fileRef = A775905C27205740000E5BB9 /* ordernotificationsound.wav */; };
		A7CBC4B22AFEABB0000525C2 /* poplindefault.wav in Resources */ = {isa = PBXBuildFile; fileRef = A7CBC4B12AFEABB0000525C2 /* poplindefault.wav */; };
		A7CBC4B32AFEABB0000525C2 /* poplindefault.wav in Resources */ = {isa = PBXBuildFile; fileRef = A7CBC4B12AFEABB0000525C2 /* poplindefault.wav */; };
		A7D006102CEE3A960061CA20 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 5CDD43C62524F905004E6414 /* GoogleService-Info.plist */; };
		A7D006112CEE3A9B0061CA20 /* SudshareConfig.plist in Resources */ = {isa = PBXBuildFile; fileRef = A70B57B729C3535400B4B261 /* SudshareConfig.plist */; };
		A7D006122CEE3B690061CA20 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = 45B8A40C29096C6900D5107D /* GoogleService-Info.plist */; };
		A7D006132CEE3B6E0061CA20 /* SudshareConfig.plist in Resources */ = {isa = PBXBuildFile; fileRef = A70B57BA29C3536F00B4B261 /* SudshareConfig.plist */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		12DDE91DE012FD4BF22FBE1A /* Pods_Prod.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Prod.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		2FAD9762203C412B000D30F8 /* config.xml */ = {isa = PBXFileReference; lastKnownFileType = text.xml; path = config.xml; sourceTree = "<group>"; };
		3B2A2FE5F0102C89F634AB33 /* Pods-Prod.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Prod.debug.xcconfig"; path = "Pods/Target Support Files/Pods-Prod/Pods-Prod.debug.xcconfig"; sourceTree = "<group>"; };
		3B69277A412F9A70F1B4E7A8 /* Pods-Prod.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Prod.release.xcconfig"; path = "Pods/Target Support Files/Pods-Prod/Pods-Prod.release.xcconfig"; sourceTree = "<group>"; };
		4540EA22292577B900A3C42A /* SudSharePlugin.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SudSharePlugin.m; sourceTree = "<group>"; };
		4540EA25292577C400A3C42A /* SudSharePlugin.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SudSharePlugin.swift; sourceTree = "<group>"; };
		45B8A40C29096C6900D5107D /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		45CC8FAC2908D406006033F9 /* Test.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Test.app; sourceTree = BUILT_PRODUCTS_DIR; };
		45CC8FAD2908D406006033F9 /* Test-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "Test-Info.plist"; path = "/Users/<USER>/Documents/workspace/sudster-app-v3/ios/App/Test-Info.plist"; sourceTree = "<absolute>"; };
		50379B222058CBB4000EE86E /* capacitor.config.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; path = capacitor.config.json; sourceTree = "<group>"; };
		504EC3041FED79650016851F /* Prod.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Prod.app; sourceTree = BUILT_PRODUCTS_DIR; };
		504EC3071FED79650016851F /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		504EC30C1FED79650016851F /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		504EC30E1FED79650016851F /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		504EC3111FED79650016851F /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		504EC3131FED79650016851F /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		5C5D9D4D2543697800D48722 /* AppRequestReview.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppRequestReview.swift; sourceTree = "<group>"; };
		5C5D9D50254369CD00D48722 /* App-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "App-Bridging-Header.h"; sourceTree = "<group>"; };
		5C5D9D51254369CD00D48722 /* AppRequestReview.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppRequestReview.m; sourceTree = "<group>"; };
		5C8D6A4A102FC9964418A687 /* Pods-Test.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Test.release.xcconfig"; path = "Pods/Target Support Files/Pods-Test/Pods-Test.release.xcconfig"; sourceTree = "<group>"; };
		5CB5674526ADD1E600094728 /* public */ = {isa = PBXFileReference; lastKnownFileType = folder; path = public; sourceTree = "<group>"; };
		5CB6801522DCFFDD00B37E07 /* App.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = App.entitlements; sourceTree = "<group>"; };
		5CDD43C62524F905004E6414 /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		637E4F5A2E06188C92153133 /* Pods-Test.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Test.debug.xcconfig"; path = "Pods/Target Support Files/Pods-Test/Pods-Test.debug.xcconfig"; sourceTree = "<group>"; };
		A70B57AE29C3505800B4B261 /* SegmentPlugin.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SegmentPlugin.swift; sourceTree = "<group>"; };
		A70B57B129C3513A00B4B261 /* SegmentPlugin.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SegmentPlugin.m; sourceTree = "<group>"; };
		A70B57B429C3528E00B4B261 /* SudshareConfig.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SudshareConfig.swift; sourceTree = "<group>"; };
		A70B57B729C3535400B4B261 /* SudshareConfig.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = SudshareConfig.plist; sourceTree = "<group>"; };
		A70B57BA29C3536F00B4B261 /* SudshareConfig.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = SudshareConfig.plist; sourceTree = "<group>"; };
		A763B7312C2BD10400069437 /* IterablePlugin.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = IterablePlugin.m; sourceTree = "<group>"; };
		A763B7342C2BD13100069437 /* IterablePlugin.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IterablePlugin.swift; sourceTree = "<group>"; };
		A763B7382C36E1B400069437 /* ProdDebug.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = ProdDebug.entitlements; sourceTree = "<group>"; };
		A763B7392C36E20500069437 /* TestDebug.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = TestDebug.entitlements; sourceTree = "<group>"; };
		A775905C27205740000E5BB9 /* ordernotificationsound.wav */ = {isa = PBXFileReference; lastKnownFileType = audio.wav; path = ordernotificationsound.wav; sourceTree = "<group>"; };
		A7CBC4B12AFEABB0000525C2 /* poplindefault.wav */ = {isa = PBXFileReference; lastKnownFileType = audio.wav; path = poplindefault.wav; sourceTree = "<group>"; };
		B1C82D96E0D9F09A45FF6DF8 /* Pods_Test.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Test.framework; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		45CC8F9D2908D406006033F9 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0C2E35AE937EE27A08E5BC74 /* Pods_Test.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		504EC3011FED79650016851F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1AAC48521BCA87C2ED5C290A /* Pods_Prod.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		27E2DDA53C4D2A4D1A88CE4A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				12DDE91DE012FD4BF22FBE1A /* Pods_Prod.framework */,
				B1C82D96E0D9F09A45FF6DF8 /* Pods_Test.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		45B8A40B29096C6900D5107D /* Firebase */ = {
			isa = PBXGroup;
			children = (
				45B8A40E29096CA000D5107D /* prod */,
				45B8A40D29096C8E00D5107D /* test */,
			);
			path = Firebase;
			sourceTree = "<group>";
		};
		45B8A40D29096C8E00D5107D /* test */ = {
			isa = PBXGroup;
			children = (
				45B8A40C29096C6900D5107D /* GoogleService-Info.plist */,
				A70B57BA29C3536F00B4B261 /* SudshareConfig.plist */,
			);
			path = test;
			sourceTree = "<group>";
		};
		45B8A40E29096CA000D5107D /* prod */ = {
			isa = PBXGroup;
			children = (
				5CDD43C62524F905004E6414 /* GoogleService-Info.plist */,
				A70B57B729C3535400B4B261 /* SudshareConfig.plist */,
			);
			path = prod;
			sourceTree = "<group>";
		};
		504EC2FB1FED79650016851F = {
			isa = PBXGroup;
			children = (
				A763B7392C36E20500069437 /* TestDebug.entitlements */,
				A763B7382C36E1B400069437 /* ProdDebug.entitlements */,
				504EC3061FED79650016851F /* App */,
				504EC3051FED79650016851F /* Products */,
				7F8756D8B27F46E3366F6CEA /* Pods */,
				27E2DDA53C4D2A4D1A88CE4A /* Frameworks */,
				45CC8FAD2908D406006033F9 /* Test-Info.plist */,
			);
			sourceTree = "<group>";
		};
		504EC3051FED79650016851F /* Products */ = {
			isa = PBXGroup;
			children = (
				504EC3041FED79650016851F /* Prod.app */,
				45CC8FAC2908D406006033F9 /* Test.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		504EC3061FED79650016851F /* App */ = {
			isa = PBXGroup;
			children = (
				A775905E27231248000E5BB9 /* plugins */,
				5CB5674526ADD1E600094728 /* public */,
				45B8A40B29096C6900D5107D /* Firebase */,
				5CB6801522DCFFDD00B37E07 /* App.entitlements */,
				50379B222058CBB4000EE86E /* capacitor.config.json */,
				504EC3071FED79650016851F /* AppDelegate.swift */,
				A775905C27205740000E5BB9 /* ordernotificationsound.wav */,
				A7CBC4B12AFEABB0000525C2 /* poplindefault.wav */,
				504EC30B1FED79650016851F /* Main.storyboard */,
				504EC30E1FED79650016851F /* Assets.xcassets */,
				504EC3101FED79650016851F /* LaunchScreen.storyboard */,
				504EC3131FED79650016851F /* Info.plist */,
				2FAD9762203C412B000D30F8 /* config.xml */,
				5C5D9D50254369CD00D48722 /* App-Bridging-Header.h */,
				A70B57B429C3528E00B4B261 /* SudshareConfig.swift */,
			);
			path = App;
			sourceTree = "<group>";
		};
		7F8756D8B27F46E3366F6CEA /* Pods */ = {
			isa = PBXGroup;
			children = (
				3B2A2FE5F0102C89F634AB33 /* Pods-Prod.debug.xcconfig */,
				3B69277A412F9A70F1B4E7A8 /* Pods-Prod.release.xcconfig */,
				637E4F5A2E06188C92153133 /* Pods-Test.debug.xcconfig */,
				5C8D6A4A102FC9964418A687 /* Pods-Test.release.xcconfig */,
			);
			name = Pods;
			sourceTree = "<group>";
		};
		A775905E27231248000E5BB9 /* plugins */ = {
			isa = PBXGroup;
			children = (
				5C5D9D4D2543697800D48722 /* AppRequestReview.swift */,
				5C5D9D51254369CD00D48722 /* AppRequestReview.m */,
				4540EA22292577B900A3C42A /* SudSharePlugin.m */,
				4540EA25292577C400A3C42A /* SudSharePlugin.swift */,
				A70B57AE29C3505800B4B261 /* SegmentPlugin.swift */,
				A70B57B129C3513A00B4B261 /* SegmentPlugin.m */,
				A763B7312C2BD10400069437 /* IterablePlugin.m */,
				A763B7342C2BD13100069437 /* IterablePlugin.swift */,
			);
			path = plugins;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		45CC8F922908D406006033F9 /* Test */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 45CC8FA92908D406006033F9 /* Build configuration list for PBXNativeTarget "Test" */;
			buildPhases = (
				45CC8F932908D406006033F9 /* [CP] Check Pods Manifest.lock */,
				45CC8F942908D406006033F9 /* Sources */,
				45CC8F9D2908D406006033F9 /* Frameworks */,
				45B8A41029096D6400D5107D /* Copy Configs */,
				45CC8F9F2908D406006033F9 /* Resources */,
				45CC8FA82908D406006033F9 /* [CP] Embed Pods Frameworks */,
				45EFA2EC292416DE00E84A8D /* Firebase Crashlytics */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Test;
			productName = App;
			productReference = 45CC8FAC2908D406006033F9 /* Test.app */;
			productType = "com.apple.product-type.application";
		};
		504EC3031FED79650016851F /* Prod */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 504EC3161FED79650016851F /* Build configuration list for PBXNativeTarget "Prod" */;
			buildPhases = (
				6634F4EFEBD30273BCE97C65 /* [CP] Check Pods Manifest.lock */,
				504EC3001FED79650016851F /* Sources */,
				504EC3011FED79650016851F /* Frameworks */,
				504EC3021FED79650016851F /* Resources */,
				9592DBEFFC6D2A0C8D5DEB22 /* [CP] Embed Pods Frameworks */,
				45EFA2ED2924171800E84A8D /* Firebase Crashlytics */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Prod;
			productName = App;
			productReference = 504EC3041FED79650016851F /* Prod.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		504EC2FC1FED79650016851F /* Project object */ = {
			isa = PBXProject;
			attributes = {
				KnownAssetTags = (
					New,
				);
				LastSwiftUpdateCheck = 920;
				LastUpgradeCheck = 920;
				TargetAttributes = {
					45CC8F922908D406006033F9 = {
						ProvisioningStyle = Manual;
					};
					504EC3031FED79650016851F = {
						CreatedOnToolsVersion = 9.2;
						LastSwiftMigration = 1210;
						ProvisioningStyle = Manual;
						SystemCapabilities = {
							com.apple.BackgroundModes = {
								enabled = 1;
							};
							com.apple.ClassKit = {
								enabled = 0;
							};
							com.apple.Push = {
								enabled = 1;
							};
						};
					};
				};
			};
			buildConfigurationList = 504EC2FF1FED79650016851F /* Build configuration list for PBXProject "App" */;
			compatibilityVersion = "Xcode 8.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 504EC2FB1FED79650016851F;
			productRefGroup = 504EC3051FED79650016851F /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				504EC3031FED79650016851F /* Prod */,
				45CC8F922908D406006033F9 /* Test */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		45CC8F9F2908D406006033F9 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				45CC8FA02908D406006033F9 /* LaunchScreen.storyboard in Resources */,
				45CC8FA12908D406006033F9 /* Assets.xcassets in Resources */,
				A7D006122CEE3B690061CA20 /* GoogleService-Info.plist in Resources */,
				45CC8FA22908D406006033F9 /* ordernotificationsound.wav in Resources */,
				45CC8FA32908D406006033F9 /* capacitor.config.json in Resources */,
				A7D006132CEE3B6E0061CA20 /* SudshareConfig.plist in Resources */,
				A7CBC4B32AFEABB0000525C2 /* poplindefault.wav in Resources */,
				45CC8FA42908D406006033F9 /* Main.storyboard in Resources */,
				45CC8FA52908D406006033F9 /* config.xml in Resources */,
				45CC8FA72908D406006033F9 /* public in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		504EC3021FED79650016851F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				504EC3121FED79650016851F /* LaunchScreen.storyboard in Resources */,
				504EC30F1FED79650016851F /* Assets.xcassets in Resources */,
				A7D006102CEE3A960061CA20 /* GoogleService-Info.plist in Resources */,
				A775905D27205740000E5BB9 /* ordernotificationsound.wav in Resources */,
				50379B232058CBB4000EE86E /* capacitor.config.json in Resources */,
				A7D006112CEE3A9B0061CA20 /* SudshareConfig.plist in Resources */,
				A7CBC4B22AFEABB0000525C2 /* poplindefault.wav in Resources */,
				504EC30D1FED79650016851F /* Main.storyboard in Resources */,
				2FAD9763203C412B000D30F8 /* config.xml in Resources */,
				5CB5674626ADD1E600094728 /* public in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		45B8A41029096D6400D5107D /* Copy Configs */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Copy Configs";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "GOOGLESERVICE_INFO_PLIST=GoogleService-Info.plist\nSUDSHARE_INFO_PLIST=SudshareConfig.plist\n\nGOOGLESERVICE_INFO_TEST=${PROJECT_DIR}/App/Firebase/Test/${GOOGLESERVICE_INFO_PLIST}\nGOOGLESERVICE_INFO_PROD=${PROJECT_DIR}/App/Firebase/Prod/${GOOGLESERVICE_INFO_PLIST}\n\nSUDSHARE_INFO_TEST=${PROJECT_DIR}/App/Firebase/Test/${SUDSHARE_INFO_PLIST}\nSUDSHARE_INFO_PROD=${PROJECT_DIR}/App/Firebase/Prod/${SUDSHARE_INFO_PLIST}\n\nif [ ! -f $GOOGLESERVICE_INFO_TEST || SUDSHARE_INFO_TEST ]\nthen\n    echo \"No Testing configs found.\"\n    exit 1\nfi\n\nif [ ! -f $GOOGLESERVICE_INFO_PROD || SUDSHARE_INFO_PROD ]\nthen\n    echo \"No Production configs found.\"\n    exit 1\nfi\n\nPLIST_DESTINATION=${BUILT_PRODUCTS_DIR}/${PRODUCT_NAME}.app\n\nif [ \"${PRODUCT_NAME}\" == \"Prod\" ]\nthen\n    echo \"Copying Production Configs\"\n    cp \"${SUDSHARE_INFO_PROD}\" \"${PLIST_DESTINATION}\"\n    cp \"${GOOGLESERVICE_INFO_PROD}\" \"${PLIST_DESTINATION}\"\nelse\n    echo \"Copying Testing Configs\"\n    cp \"${SUDSHARE_INFO_TEST}\" \"${PLIST_DESTINATION}\"\n    cp \"${GOOGLESERVICE_INFO_TEST}\" \"${PLIST_DESTINATION}\"\nfi\n";
		};
		45CC8F932908D406006033F9 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Test-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		45CC8FA82908D406006033F9 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Test/Pods-Test-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		45EFA2EC292416DE00E84A8D /* Firebase Crashlytics */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Resources/DWARF/${TARGET_NAME}",
				"$(SRCROOT)/$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "Firebase Crashlytics";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/FirebaseCrashlytics/run\"\n";
		};
		45EFA2ED2924171800E84A8D /* Firebase Crashlytics */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Resources/DWARF/${TARGET_NAME}",
				"$(SRCROOT)/$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "Firebase Crashlytics";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/FirebaseCrashlytics/run\"\n";
		};
		6634F4EFEBD30273BCE97C65 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Prod-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		9592DBEFFC6D2A0C8D5DEB22 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Prod/Pods-Prod-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		45CC8F942908D406006033F9 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A763B7362C2BD13100069437 /* IterablePlugin.swift in Sources */,
				A70B57B029C3505800B4B261 /* SegmentPlugin.swift in Sources */,
				4540EA24292577B900A3C42A /* SudSharePlugin.m in Sources */,
				45CC8F972908D406006033F9 /* AppDelegate.swift in Sources */,
				A70B57B329C3513A00B4B261 /* SegmentPlugin.m in Sources */,
				A70B57B629C3528E00B4B261 /* SudshareConfig.swift in Sources */,
				45CC8F982908D406006033F9 /* AppRequestReview.m in Sources */,
				A763B7332C2BD10400069437 /* IterablePlugin.m in Sources */,
				45CC8F992908D406006033F9 /* AppRequestReview.swift in Sources */,
				4540EA27292577C400A3C42A /* SudSharePlugin.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		504EC3001FED79650016851F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A763B7352C2BD13100069437 /* IterablePlugin.swift in Sources */,
				A70B57AF29C3505800B4B261 /* SegmentPlugin.swift in Sources */,
				4540EA23292577B900A3C42A /* SudSharePlugin.m in Sources */,
				504EC3081FED79650016851F /* AppDelegate.swift in Sources */,
				A70B57B229C3513A00B4B261 /* SegmentPlugin.m in Sources */,
				A70B57B529C3528E00B4B261 /* SudshareConfig.swift in Sources */,
				5C5D9D52254369CD00D48722 /* AppRequestReview.m in Sources */,
				A763B7322C2BD10400069437 /* IterablePlugin.m in Sources */,
				5C5D9D4E2543697800D48722 /* AppRequestReview.swift in Sources */,
				4540EA26292577C400A3C42A /* SudSharePlugin.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		504EC30B1FED79650016851F /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				504EC30C1FED79650016851F /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		504EC3101FED79650016851F /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				504EC3111FED79650016851F /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		45CC8FAA2908D406006033F9 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 637E4F5A2E06188C92153133 /* Pods-Test.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIconTest;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = TestDebug.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 2;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 2J3CZ4C5D8;
				INFOPLIST_FILE = "Test-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "Laundry Pro Test";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.lifestyle";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				MARKETING_VERSION = 3.53.0;
				OTHER_SWIFT_FLAGS = "$(inherited) \"-D\" \"COCOAPODS\" \"-DDEBUG\"";
				PRODUCT_BUNDLE_IDENTIFIER = com.sudshare.Sudsters.test;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "match AppStore com.sudshare.Sudsters.test 1721692762";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OBJC_BRIDGING_HEADER = "App/App-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		45CC8FAB2908D406006033F9 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5C8D6A4A102FC9964418A687 /* Pods-Test.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIconTest;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = App/App.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 2;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 2J3CZ4C5D8;
				INFOPLIST_FILE = "Test-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = "Laundry Pro Test";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.lifestyle";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				MARKETING_VERSION = 3.53.0;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.sudshare.Sudsters.test;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "match AppStore com.sudshare.Sudsters.test 1721692762";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "";
				SWIFT_OBJC_BRIDGING_HEADER = "App/App-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		504EC3141FED79650016851F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		504EC3151FED79650016851F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SWIFT_OPTIMIZATION_LEVEL = "-Owholemodule";
				SWIFT_VERSION = 5.0;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		504EC3171FED79650016851F /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3B2A2FE5F0102C89F634AB33 /* Pods-Prod.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = ProdDebug.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 2;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 2J3CZ4C5D8;
				INFOPLIST_FILE = App/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Laundry Pro";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.lifestyle";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				MARKETING_VERSION = 3.53.0;
				OTHER_SWIFT_FLAGS = "$(inherited) \"-D\" \"COCOAPODS\" \"-DDEBUG\"";
				PRODUCT_BUNDLE_IDENTIFIER = com.sudshare.Sudsters;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "match AppStore com.sudshare.Sudsters";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG USE_PUSH";
				SWIFT_OBJC_BRIDGING_HEADER = "App/App-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		504EC3181FED79650016851F /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3B69277A412F9A70F1B4E7A8 /* Pods-Prod.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = App/App.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 2;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 2J3CZ4C5D8;
				INFOPLIST_FILE = App/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Laundry Pro";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.lifestyle";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				MARKETING_VERSION = 3.53.0;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.sudshare.Sudsters;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = "match AppStore com.sudshare.Sudsters";
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = USE_PUSH;
				SWIFT_OBJC_BRIDGING_HEADER = "App/App-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		45CC8FA92908D406006033F9 /* Build configuration list for PBXNativeTarget "Test" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				45CC8FAA2908D406006033F9 /* Debug */,
				45CC8FAB2908D406006033F9 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		504EC2FF1FED79650016851F /* Build configuration list for PBXProject "App" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				504EC3141FED79650016851F /* Debug */,
				504EC3151FED79650016851F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		504EC3161FED79650016851F /* Build configuration list for PBXNativeTarget "Prod" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				504EC3171FED79650016851F /* Debug */,
				504EC3181FED79650016851F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 504EC2FC1FED79650016851F /* Project object */;
}
