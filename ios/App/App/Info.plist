<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Laundry Pro</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>3.58.0</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.sudshare.Sudsters</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.sudshare.Sudsters</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLName</key>
			<string>com.getcapacitor.capacitor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>capacitor</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fb1689845974551597</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLIconFile</key>
			<string></string>
			<key>CFBundleURLName</key>
			<string>com.sudshare.Sudsters</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>poplinlp</string>
			</array>
		</dict>
		<dict/>
	</array>
	<key>CFBundleVersion</key>
	<string>33</string>
	<key>FirebaseAppDelegateProxyEnabled</key>
	<string>NO</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>IonApi</key>
	<string>$UPDATE_API</string>
	<key>IonAppId</key>
	<string>$APP_ID</string>
	<key>IonChannelName</key>
	<string>$CHANNEL_NAME</string>
	<key>IonMaxVersions</key>
	<string>$MAX_STORE</string>
	<key>IonMinBackgroundDuration</key>
	<string>$MIN_BACKGROUND_DURATION</string>
	<key>IonUpdateMethod</key>
	<string>$UPDATE_METHOD</string>
	<key>LSApplicationCategoryType</key>
	<string></string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>akamaihd.net</key>
			<dict>
				<key>NSIncludesSubdomains</key>
				<true/>
				<key>NSThirdPartyExceptionRequiresForwardSecrecy</key>
				<false/>
			</dict>
			<key>capacitor://sudsterapp</key>
			<true/>
			<key>laundry-llama.firebaseapp.com</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSIncludesSubdomains</key>
				<true/>
			</dict>
		</dict>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>For use with verification of pickup, detergent, and drop off location.</string>
	<key>NSContactsUsageDescription</key>
	<string>Needed to refer the app only to your selected contacts. This app WILL NOT send any phone number, email or contact names to our servers</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>For calculating drive times and current location on order map.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>For calculating drive times and current location on order map.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>For use with videos recorded and customer support.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>To store pictures related to verification of pickup, detergent, and drop off location. </string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>To upload pictures related to verification of pickup, detergent, and drop off location. </string>
	<key>NSUserTrackingUsageDescription</key>
	<string>This will be used to track marketing statistics in your area.</string>
	<key>UIApplicationSceneManifest</key>
	<dict>
		<key>UIApplicationSupportsMultipleScenes</key>
		<false/>
	</dict>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UIStatusBarHidden</key>
	<false/>
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleDarkContent</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<true/>
</dict>
</plist>
