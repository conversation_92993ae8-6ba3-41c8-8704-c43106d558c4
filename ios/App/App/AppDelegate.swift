import Capacitor
import Firebase
import FirebaseAuth
import FirebaseCore
import FirebaseFirestore
import FirebaseMessaging
import FirebasePerformance
import Segment
import UIKit
import IterableSDK
import SudshareCapacitorZendesk


@UIApplicationMain
class AppDelegate: UIResponder, UIApplicationDelegate, IterableURLDelegate, UNUserNotificationCenterDelegate {
    var window: UIWindow?

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        // Override point for customization after application launch.
        FirebaseApp.configure()
        #if DEBUG
        if #available(iOS 16.4, *) {
            DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
                if let vc = self.window?.rootViewController as? CAPBridgeViewController {
                    vc.bridge?.webView?.isInspectable = true
                }
            }
        }
        #endif
        // loads all configurations depending on the build environment
        var settings: SudshareConfig?
        if let settingsURL = Bundle.main.path(forResource: "SudshareConfig", ofType: "plist") {
            do {
                let data = try Data(contentsOf: URL(fileURLWithPath: settingsURL))
                let decoder = PropertyListDecoder()
                settings = try decoder.decode(SudshareConfig.self, from: data)
            } catch {
                print(error)
            }
        }
        let configuration = AnalyticsConfiguration(writeKey: (settings?.segmentConfig?.apiKey)!)
               configuration.trackApplicationLifecycleEvents = true // Enable this to record certain application events automatically!
               configuration.recordScreenViews = false // Enable this to record screen views automatically!
               Analytics.setup(with: configuration)

        let config = IterableConfig()
        config.allowedProtocols = ["http", "tel", "capacitor"]
        config.pushIntegrationName = settings?.segmentConfig?.appId
        config.useInMemoryStorageForInApps = true
        config.autoPushRegistration = true
        config.urlDelegate = self

        IterableAPI.initialize(apiKey: (settings?.iterableConfig?.apiKey)!, launchOptions: launchOptions, config: config)

        registerForPushNotifications()

        return true
    }

    private func registerForPushNotifications() {
        let notificationCenter = UNUserNotificationCenter.current()
        notificationCenter.delegate = self
        notificationCenter.requestAuthorization(options: [.alert, .sound, .badge]) { allowed, _ in
            guard allowed else { return }

            DispatchQueue.main.async {
                UIApplication.shared.registerForRemoteNotifications()
            }
        }
    }

    public func handle(iterableURL url: URL, inContext _: IterableActionContext) -> Bool {
        guard let instance = IterablePlugin.getInstance() else {
            return true
        }

        return instance.notifyInAppUrl(iterableURL: url)
    }

    func applicationWillResignActive(_ application: UIApplication) {
        // Sent when the application is about to move from active to inactive state. This can occur for certain types of temporary interruptions (such as an incoming phone call or SMS message) or when the user quits the application and it begins the transition to the background state.
        // Use this method to pause ongoing tasks, disable timers, and invalidate graphics rendering callbacks. Games should use this method to pause the game.
    }

    func applicationDidEnterBackground(_ application: UIApplication) {
        // Use this method to release shared resources, save user data, invalidate timers, and store enough application state information to restore your application to its current state in case it is terminated later.
        // If your application supports background execution, this method is called instead of applicationWillTerminate: when the user quits.
    }

    func applicationWillEnterForeground(_ application: UIApplication) {
        // Called as part of the transition from the background to the active state; here you can undo many of the changes made on entering the background.
    }

    func applicationDidBecomeActive(_ application: UIApplication) {
        // Restart any tasks that were paused (or not yet started) while the application was inactive. If the application was previously in the background, optionally refresh the user interface.
        // resets the badge number when becoming active.
        application.applicationIconBadgeNumber = 0
    }

    func applicationWillTerminate(_ application: UIApplication) {
        // Called when the application is about to terminate. Save data if appropriate. See also applicationDidEnterBackground:.
    }

    func application(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey: Any] = [:]) -> Bool {
        // Called when the app was launched with a url. Feel free to add additional processing here,
        // but if you want the App API to support tracking app url opens, make sure to keep this call
        return ApplicationDelegateProxy.shared.application(app, open: url, options: options)
    }

    func application(_ application: UIApplication, continue userActivity: NSUserActivity, restorationHandler: @escaping ([UIUserActivityRestoring]?) -> Void) -> Bool {
        // Called when the app was launched with an activity, including Universal Links.
        // Feel free to add additional processing here, but if you want the App API to support
        // tracking app url opens, make sure to keep this call
        return ApplicationDelegateProxy.shared.application(application, continue: userActivity, restorationHandler: restorationHandler)
    }

    func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        Messaging.messaging().apnsToken = deviceToken
        Messaging.messaging().token(completion: { token, error in
            let trace = Performance.startTrace(name: "Registered_For_Push_Notifications")

            if let error = error {
                trace?.incrementMetric("FAILED_TO_REGISTER", by: 1)
                NotificationCenter.default.post(name: .capacitorDidFailToRegisterForRemoteNotifications, object: error)

            } else if let token = token {
                trace?.incrementMetric("SUCCESSFULLY_REGISTERED", by: 1)
                NotificationCenter.default.post(name: .capacitorDidRegisterForRemoteNotifications, object: token)
            }
            trace?.stop()
        })
        IterableAPI.register(token: deviceToken)

        ZendeskMessagingPlugin.registerToken(deviceToken)
    }

    func application(_ application: UIApplication, didReceiveRemoteNotification userInfo: [AnyHashable: Any], fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
        let trace = Performance.startTrace(name: "PUSH_NOTIFICATIONS")
        IterableAppIntegration.application(application, didReceiveRemoteNotification: userInfo, fetchCompletionHandler: completionHandler)

        trace?.incrementMetric("NOTIFICATION_RECEIVED", by: 1)
        NotificationCenter.default.post(name: Notification.Name("didReceiveRemoteNotification"), object: completionHandler, userInfo: userInfo)
        trace?.stop()
    }

}

extension Data {
    var hexString: String {
        return map { String(format: "%02.2hhx", arguments: [$0]) }.joined()
    }
}
