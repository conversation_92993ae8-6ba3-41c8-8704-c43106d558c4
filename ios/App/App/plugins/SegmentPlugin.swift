//
//  SegmentPlugin.swift
//  App
//
//  Created by <PERSON> on 16/13/23.
//

import Foundation
import Capacitor
import Segment
import CryptoKit

@objc(SegmentPlugin)
public class SegmentPlugin: CAPPlugin {
    
    @objc func track(_ call: CAPPluginCall){
        let eventName = call.getString("eventName") ?? ""
        let eventPayload = call.getObject("properties") ?? nil
        let eventProperties = getEventValuesDic(params: eventPayload)
        Analytics.shared().track(eventName, properties: eventProperties)
        call.resolve([
            "value": "ok"
        ])
    }
    
    @objc func identify(_ call: CAPPluginCall){
        let userId = call.getString("userId") ?? ""
        let userTraits = call.getObject("traits") ?? nil
        let userTraitsInfo = getEventValuesDic(params: userTraits)
        Analytics.shared().identify(userId, traits: userTraitsInfo)
        call.resolve([
            "result": true
        ])
    }
    
    @objc func reset(_ call: CAPPluginCall){
        Analytics.shared().reset()
        call.resolve([
            "result": true
        ])
    }
    
    private func getEventValuesDic(params: JSObject?) -> Dictionary<String, Any> {
        guard let keys = params?.keys else {
            return [:]
        }
        var eventProps: Dictionary<String, Any> = [:]
        for prop in keys {
            eventProps[prop] = params?[prop]
        }
        return eventProps
    }
}
