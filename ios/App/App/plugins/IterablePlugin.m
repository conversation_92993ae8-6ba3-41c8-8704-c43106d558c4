//
//  IterablePlugin.m
//  App
//
//  Created by <PERSON> on 25/06/24.
//

#import <Foundation/Foundation.h>
#import <Capacitor/Capacitor.h>
// Define the plugin using the CAP_PLUGIN Macro, and
// each method the plugin supports using the CAP_PLUGIN_METHOD macro.
CAP_PLUGIN(IterablePlugin, "Iterable",
           CAP_PLUGIN_METHOD(registerUser, CAPPluginReturnPromise);
           CAP_PLUGIN_METHOD(trackPush, CAPPluginReturnPromise);
)
