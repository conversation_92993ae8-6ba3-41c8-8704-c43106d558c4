//
//  IterablePlugin.swift
//  App
//
//

import Foundation
import Capacitor
import IterableSDK

@objc(IterablePlugin)
public class IterablePlugin: CAPPlugin {

    static var instance: IterablePlugin?

    public override func load() {
        IterablePlugin.instance = self
    }

    @objc func registerUser(_ call: CAPPluginCall) {
        let userId = call.getString("userId") ?? nil

        if userId != nil {
            IterableAPI.userId = userId
            call.resolve(
                ["result": "ok"]
            )
        } else {
            call.resolve(["result": "error"])
        }
    }

    @objc func trackPush(_ call: CAPPluginCall) {
        let campaignId = call.getInt("campaignId")
        let templateId = call.getInt("templateId")
        let msgId = call.getString("msgId")
        let appWasRunning = call.getBool("appWasRunning") ?? false

        if (campaignId != nil && templateId != nil && msgId != nil) {
            IterableAPI.track(pushOpen: campaignId! as NSNumber, templateId: templateId! as NSNumber, messageId: msgId!, appAlreadyRunning: appWasRunning, dataFields: [:])
            call.resolve(["result": "ok"])
        } else {
            call.resolve(["result": "no-track"])
        }
    }

    public func notifyInAppUrl(iterableURL url: URL) -> Bool {
        DispatchQueue.main.asyncAfter(deadline: .now()) {
            self.notifyListeners("IterableInAppOnUrl", data: ["url": url.absoluteString])
        }

        return true
    }

    public static func getInstance() -> IterablePlugin? {
        return instance
    }
}
