import Foundation
import Capacitor
import FirebaseCrashlytics

/**
 * Please read the Capacitor iOS Plugin Development Guide
 * here: https://capacitorjs.com/docs/plugins/ios
 */
@objc(SudSharePlugin)
public class SudSharePlugin: CAPPlugin {

    @objc func setup(_ call: CAPPluginCall) {
        let userId = call.getString("userId") ?? "Unknown"
        
        Crashlytics.crashlytics().setUserID(userId)
        
        call.resolve([
            "value": "ok"
        ])
    }
}
