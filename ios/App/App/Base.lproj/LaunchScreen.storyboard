<?xml version="1.0" encoding="UTF-8" ?>
<document
  type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB"
  version="3.0"
  toolsVersion="17156"
  targetRuntime="iOS.CocoaTouch"
  propertyAccessControl="none"
  useAutolayout="YES"
  launchScreen="YES"
  useTraitCollections="YES"
  useSafeAreas="YES"
  colorMatched="YES"
  initialViewController="01J-lp-oVM"
>
    <device id="retina4_7" orientation="portrait" appearance="light" />
    <dependencies>
        <deployment identifier="iOS" />
        <plugIn
      identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin"
      version="17125"
    />
        <capability
      name="documents saved in the Xcode 8 format"
      minToolsVersion="8.0"
    />
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <imageView
            key="view"
            userInteractionEnabled="NO"
            contentMode="scaleAspectFill"
            horizontalHuggingPriority="251"
            verticalHuggingPriority="251"
            image="Splash"
            id="snD-IY-ifK"
          >
                        <rect
              key="frame"
              x="0.0"
              y="0.0"
              width="375"
              height="667"
            />
                        <autoresizingMask key="autoresizingMask" />
                    </imageView>
                </viewController>
                <placeholder
          placeholderIdentifier="IBFirstResponder"
          id="iYj-Kq-Ea1"
          userLabel="First Responder"
          sceneMemberID="firstResponder"
        />
            </objects>
            <point key="canvasLocation" x="53" y="375" />
        </scene>
    </scenes>
    <resources>
        <image name="Splash" width="1366" height="1366" />
    </resources>
</document>
