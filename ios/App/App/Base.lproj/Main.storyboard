<?xml version="1.0" encoding="UTF-8" ?>
<document
  type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB"
  version="3.0"
  toolsVersion="14111"
  targetRuntime="iOS.CocoaTouch"
  propertyAccessControl="none"
  useAutolayout="YES"
  useTraitCollections="YES"
  colorMatched="YES"
  initialViewController="BYZ-38-t0r"
>
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen" />
    </device>
    <dependencies>
        <deployment identifier="iOS" />
        <plugIn
      identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin"
      version="14088"
    />
    </dependencies>
    <scenes>
        <!--Bridge View Controller-->
        <scene sceneID="tne-QT-ifu">
            <objects>
                <viewController
          id="BYZ-38-t0r"
          customClass="CAPBridgeViewController"
          customModule="Capacitor"
          sceneMemberID="viewController"
        />
                <placeholder
          placeholderIdentifier="IBFirstResponder"
          id="dkx-z0-nzr"
          sceneMemberID="firstResponder"
        />
            </objects>
        </scene>
    </scenes>
</document>
