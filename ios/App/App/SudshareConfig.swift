//
//  SudshareConfig.swift
//  App
//
//  Created by <PERSON> on 16/03/23.
//

import Foundation

struct SudshareConfig: Codable {
    var segmentConfig: CommonApiConfig?
    var iterableConfig: CommonApiConfig?

    private enum RootKeys: String, CodingKey {
        case segmentConfig = "SegmentConfig"
        case iterableConfig = "IterableConfig"
    }

    // common struct to reuse, can add more fields
    struct CommonApiConfig: Codable {
        var apiKey: String?
        var appId: String?
        var secret: String?

        init(from decoder: Decoder) throws {
            let values = try decoder.container(keyedBy: CodingKeys.self)
            apiKey = try values.decodeIfPresent(String.self, forKey: .apiKey)
            appId = try values.decodeIfPresent(String.self, forKey: .appId)
            secret = try values.decodeIfPresent(String.self, forKey: .secret)
        }
    }

    init(from decoder: Decoder) throws {
        let values = try decoder.container(keyedBy: RootKeys.self)
        segmentConfig = try values.decodeIfPresent(CommonApiConfig.self, forKey: .segmentConfig)
        iterableConfig = try values.decodeIfPresent(CommonApiConfig.self, forKey: .iterableConfig)
    }
}
