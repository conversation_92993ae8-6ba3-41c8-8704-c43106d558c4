# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
update_fastlane

default_platform(:ios)

platform :ios do
  desc "Push a new test build to TestFlight"
  lane :test do

    setup_ci(force: true)
    match(
      git_url: "**************:SudShare/apple-code-signing2.git",
      storage_mode: "git",
      type: "appstore",
      app_identifier: "com.sudshare.Sudsters.test",
      username: "<EMAIL>",
      clone_branch_directly: true,
      readonly: true,
      keychain_name: "fastlane_tmp_keychain",
    )

    build_app(
      workspace: "App.xcworkspace",
      output_name: "laundryPro-test.ipa",
      scheme: "Test",
    )

    api_key = app_store_connect_api_key(
      key_id: ENV["APP_STORE_KEY_ID"],
      issuer_id: ENV["APP_STORE_ISSUER_ID"],
      key_filepath: "./fastlane/AuthKey.p8",
    )

    upload_to_testflight(
      beta_app_review_info: {
        contact_email: "<EMAIL>",
        contact_first_name: "Ezequiel",
        contact_last_name: "Matalon",
        demo_account_name: "<EMAIL>",
        demo_account_password: "connectapi",
        notes: "this is review note for the reviewer <3 thank you for reviewing"
      },
      skip_waiting_for_build_processing: true,
      api_key: api_key,
      ipa: "laundryPro-test.ipa",
      groups: ["SudShare Engineers"],
    )

    clean_build_artifacts
    sh "rm -f ./AuthKey.p8"
  end


  desc "Push a new production beta build to Testflight"
  lane :private_beta do |options|
    setup_ci(force: true)

    match(
      git_url: "**************:SudShare/apple-code-signing2.git",
      storage_mode: "git",
      type: "appstore", # Ensure this is set for production
      app_identifier: "com.sudshare.Sudsters", # Use the production app identifier
      username: "<EMAIL>",
      readonly: true,
      keychain_name: "fastlane_tmp_keychain",
    )

    build_app(
      workspace: "App.xcworkspace",
      # output_name: "laundryPro-prod.ipa",
      scheme: "Prod", # Use the production scheme
      export_options: {
        provisioningProfiles: {
          "com.sudshare.Sudsters" => "match AppStore com.sudshare.Sudsters",
          "com.sudshare.Sudsters.test" => "match AppStore com.sudshare.Sudsters.test 1721692762",
        }
      }
    )

    api_key = app_store_connect_api_key(
      key_id: ENV["APP_STORE_KEY_ID"],
      issuer_id: ENV["APP_STORE_ISSUER_ID"],
      key_filepath: "./fastlane/AuthKey.p8",
    )

    upload_to_testflight(
      api_key: api_key,
      ipa: "Prod.ipa",
      groups: ["SudShare Engineers"],
      distribute_external: true,
      notify_external_testers: false,
      app_identifier: "com.sudshare.Sudsters",
      changelog: "Poplin for Laundry Pros Beta",
      skip_waiting_for_build_processing: true,
      beta_app_review_info: {
        contact_email: "<EMAIL>",
        contact_first_name: "Ezequiel",
        contact_last_name: "Matalon",
        demo_account_name: "<EMAIL>",
        demo_account_password: "connectapi",
        notes: "this is review note for the reviewer <3 thank you for reviewing"
      },
    )

    clean_build_artifacts
    sh "rm -f ./AuthKey.p8"
  end

  desc "Push a new production beta build to AppStore for review"
  lane :appstore do |options|
      bump_type = "build"

    # Determine the action based on bump_type
    if bump_type != "build"
      increment_version_number(bump_type: bump_type) # Increments the version number (major, minor, patch)
      increment_build_number(build_number: 1)
    end

    # get current version number
    current_version_number = get_version_number(
      xcodeproj: "App.xcodeproj",
      target: "Prod"
    )

    setup_ci(force: true)
    match(
      git_url: "**************:SudShare/apple-code-signing2.git",
      storage_mode: "git",
      type: "appstore", # Ensure this is set for production
      app_identifier: "com.sudshare.Sudsters", # Use the production app identifier
      username: "<EMAIL>",
      readonly: true,
      keychain_name: "fastlane_tmp_keychain",
    )

    build_app(
      workspace: "App.xcworkspace",
      scheme: "Prod", # Use the production scheme
    )

    api_key = app_store_connect_api_key(
      key_id: ENV["APP_STORE_KEY_ID"],
      issuer_id: ENV["APP_STORE_ISSUER_ID"],
      key_filepath: "../../ios/App/fastlane/AuthKey.p8",
    )

    deliver(
      submit_for_review: false,
      force: false,
      #metadata_path: "../../ios/App/fastlane/metadata",
      screenshots_path: "../../ios/App/fastlane/screenshots",
      app_version: current_version_number,
      ipa: "Prod.ipa",
      skip_screenshots: false,
      skip_metadata: true,
      automatic_release: false,
      phased_release: true, # Set to true if you want to enable phased releases for the app
      precheck_include_in_app_purchases: false, # Set to true to check in-app purchases during precheck
      languages: ["en-US"],
      # skip_app_version_update: true, # Don't create or update the app version that is being prepared for submission
    )

    clean_build_artifacts
    sh "rm -f ./AuthKey.p8"
    sh "git checkout -- ../Gemfile.lock"

  end

  desc "Bump the version based on the bump type selected"
  lane :increase_build_by_bump_type do |options|
    bump_type = options[:bump_type]

    if bump_type != "build"
      increment_version_number(bump_type: bump_type) # Increments the version number (major, minor, patch)
      increment_build_number(build_number: 1)
    else
      increment_build_number
    end

  end

end
