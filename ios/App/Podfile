require_relative '../../node_modules/@capacitor/ios/scripts/pods_helpers'

platform :ios, '13.0'
use_frameworks!

$AppsFlyerStrictMode=true

# workaround to avoid Xcode 10 caching of Pods that requires
# Product -> Clean Build Folder after new Cordova plugins installed
# Requires CocoaPods 1.6 or newer
install! 'cocoapods', :disable_input_output_paths => true

def capacitor_pods
  pod 'Capacitor', :path => '../../node_modules/@capacitor/ios'
  pod 'CapacitorCordova', :path => '../../node_modules/@capacitor/ios'
  pod 'CapacitorCommunityContacts', :path => '../../node_modules/@capacitor-community/contacts'
  pod 'CapacitorCommunityFcm', :path => '../../node_modules/@capacitor-community/fcm'
  pod 'CapacitorApp', :path => '../../node_modules/@capacitor/app'
  pod 'CapacitorBrowser', :path => '../../node_modules/@capacitor/browser'
  pod 'CapacitorCamera', :path => '../../node_modules/@capacitor/camera'
  pod 'CapacitorClipboard', :path => '../../node_modules/@capacitor/clipboard'
  pod 'CapacitorDevice', :path => '../../node_modules/@capacitor/device'
  pod 'CapacitorFilesystem', :path => '../../node_modules/@capacitor/filesystem'
  pod 'CapacitorGeolocation', :path => '../../node_modules/@capacitor/geolocation'
  pod 'CapacitorHaptics', :path => '../../node_modules/@capacitor/haptics'
  pod 'CapacitorKeyboard', :path => '../../node_modules/@capacitor/keyboard'
  pod 'CapacitorNetwork', :path => '../../node_modules/@capacitor/network'
  pod 'CapacitorPreferences', :path => '../../node_modules/@capacitor/preferences'
  pod 'CapacitorPushNotifications', :path => '../../node_modules/@capacitor/push-notifications'
  pod 'CapacitorShare', :path => '../../node_modules/@capacitor/share'
  pod 'CapacitorSplashScreen', :path => '../../node_modules/@capacitor/splash-screen'
  pod 'CapacitorStatusBar', :path => '../../node_modules/@capacitor/status-bar'
  pod 'CapgoCapacitorUpdater', :path => '../../node_modules/@capgo/capacitor-updater'
  pod 'SudshareCapacitorZendesk', :path => '../../node_modules/@sudshare/capacitor-zendesk'
  pod 'AppsflyerCapacitorPlugin', :path => '../../node_modules/appsflyer-capacitor-plugin'
  pod 'CordovaPlugins', :path => '../capacitor-cordova-ios-plugins'
  pod 'CordovaPluginsStatic', :path => '../capacitor-cordova-ios-plugins'
end

target 'Prod' do
  capacitor_pods
  pod 'Firebase/Messaging'
  pod 'Firebase/Core'
  pod 'Firebase/Performance'
  pod 'Firebase/Auth'
  pod 'Firebase/Firestore'
  pod 'Firebase/Analytics'
  pod 'Firebase/Crashlytics'
  pod 'ForterSDK', :git => 'https://forter-mobile-git:<EMAIL>/forter-mobile/forter-ios-sdk.git', :commit => 'e1ded25e5004c8c4e410315e8ef3f41c7a75932f'
  pod 'Analytics', '~> 4.1'
  pod 'Iterable-iOS-SDK'
end

target 'Test' do
  capacitor_pods
  pod 'Firebase/Messaging'
  pod 'Firebase/Core'
  pod 'Firebase/Performance'
  pod 'Firebase/Auth'
  pod 'Firebase/Firestore'
  pod 'Firebase/Analytics'
  pod 'Firebase/Crashlytics'
  pod 'ForterSDK', :git => 'https://forter-mobile-git:<EMAIL>/forter-mobile/forter-ios-sdk.git', :commit => 'e1ded25e5004c8c4e410315e8ef3f41c7a75932f'
  pod 'Analytics', '~> 4.1'
  pod 'Iterable-iOS-SDK'
end


post_install do |installer|
  assertDeploymentTarget(installer)
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
        if target.respond_to?(:product_type) and target.product_type == "com.apple.product-type.bundle"
            target.build_configurations.each do |config|
                config.build_settings['CODE_SIGNING_ALLOWED'] = 'NO'
            end
        end
    xcconfig_path = config.base_configuration_reference.real_path
    xcconfig = File.read(xcconfig_path)
    xcconfig_mod = xcconfig.gsub(/DT_TOOLCHAIN_DIR/, "TOOLCHAIN_DIR")
    File.open(xcconfig_path, "w") { |file| file << xcconfig_mod }
    end
  end
end