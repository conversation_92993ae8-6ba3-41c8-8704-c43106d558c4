{"workbench.colorCustomizations": {"titleBar.activeBackground": "#b71717"}, "files.exclude": {"**/node_modules/**": true, "**/.git/": true, "**/lib/**": true, "**/.nyc_output/**": true, "**/functions/*.log": true, "out/": true, "typings/": true, ".firebase/": true, "www/": true}, "json.format.enable": true, "files.trimTrailingWhitespace": true, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "ionic.externalAddress": false, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}