import { enableProdMode } from '@angular/core';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';

import { AppModule } from './app/app.module';
import { environment } from './environments/environment';

import { CapacitorUpdater } from '@capgo/capacitor-updater';
import { defineCustomElements } from '@ionic/pwa-elements/loader';

CapacitorUpdater.notifyAppReady();

if (environment.production) {
  enableProdMode();
}

declare const Stripe: any;

platformBrowserDynamic()
  .bootstrapModule(AppModule)
  .catch((err) => console.error('Platform Error', { err }));

defineCustomElements(window);
