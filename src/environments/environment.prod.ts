import { currentAppVersion } from './app.version';

// ! update firebase configuration here because it is used inside the environment object
const firebase = {
  apiKey: 'AIzaSyCL_QcXwq2sY0ZiLBT7rBlMpVQ9mcMpGks',
  authDomain: 'laundry-llama.firebaseapp.com',
  databaseURL: 'https://laundry-llama.firebaseio.com',
  projectId: 'laundry-llama',
  storageBucket: 'laundry-llama.appspot.com',
  messagingSenderId: '79996270453',
  appId: '1:79996270453:web:5118054ea85265a0e4853c',
  measurementId: 'G-VNSFDWR4TY',
};

export const environment = {
  production: true,
  emulator: false,
  local: false,
  version: currentAppVersion,
  baseEndpoint: `https://us-central1-${firebase.projectId}.cloudfunctions.net/`,
  apiGateway: 'https://prod-10qzppbp.uc.gateway.dev',
  apiToken: 'AIzaSyDLzsJhxN_p_DA0TwOYef8UUjdpjVeDA8U',
  firebase,
  GoogleMaps: 'AIzaSyCL_QcXwq2sY0ZiLBT7rBlMpVQ9mcMpGks',
  Stripe: 'pk_live_mcfmfRdNyDVRB3MLrwvJPODh',
  webApp: 'https://laundry-pro.poplin.co',
  apiPath: 'https://prod-10qzppbp.uc.gateway.dev',
  apiPathV2: 'https://prod-api-gateway-10qzppbp.uc.gateway.dev',
  statsig: {
    clients: [
      {
        version: 'default',
        clientKey: 'client-h5PVxeThS1aQEnjy4ONKDIUATpLaVWFUBf1G5iOWlX1',
      },
      {
        version: 'v4',
        clientKey: 'client-LK4XE3GlENbkG5gN9sAfAC8J3qa4x4p7JgHAG8N0hOc',
      },
    ],
    flags: {
      testFlag: 'testflag',
      forterLoginApi: 'forterloginapi',
      orderExtension: 'orderextension',
      stripeIdv: 'stripeidv',
      checkrVerification: 'checkrverification',
      showOrderDataPreacceptance: 'showorderdatapreacceptance',
      ForceUpdate: 'sudsterappupdate',
      SudsterReferralCode: 'sudsterreferralcode',
      overweightOrderExtension: 'overweightorderextension',
      smartlookRecordings: 'smartlookrecording',
      ExpressService: 'expressservice',
      LPFirstOrderPickUpDeadline: 'lpfirstorderpickupdeadline',
      newOrderCardsEnabled: 'newordercardsenabled',
      lpFirstOrderRecommendedTraining: 'lpfirstorderrecommendedtraining',
      showNewPayoutPage: 'shownewpayoutpage',
      californiaPayoutsRestrictions: 'californiapayoutsrestrictions',
    },
    dynamic: {
      orderExtensionBagCount: 'orderextensionbagcount',
      referralLpABTest: 'lp-referral-ab-test',
    },
  },
  checkr: {
    endpoint: 'https://api.checkr.com/v1',
    apiKey: '9448fd274bc9a701448985c79301353550d72ac7',
    package: 'pro_criminal',
  },
  zendesk: {
    iosKey:
      'eyJzZXR0aW5nc191cmwiOiJodHRwczovL3BvcGxpbi56ZW5kZXNrLmNvbS9tb2JpbGVfc2RrX2FwaS9zZXR0aW5ncy8wMUg3R0hBQjRBODg2VjlOTUhXRzlLNkJTOS5qc29uIn0=',
    androidKey:
      'eyJzZXR0aW5nc191cmwiOiJodHRwczovL3BvcGxpbi56ZW5kZXNrLmNvbS9tb2JpbGVfc2RrX2FwaS9zZXR0aW5ncy8wMUg3R0g5UDRQUFY4MTNQQVlOTVA1WDlYMS5qc29uIn0=',
    webKey: '2d51a9d5-994f-4529-8a8b-664e60999006',
  },
  smartlook: {
    key: '10e9841826649b4350256f29dd7be21579b416c6',
  },
  appsflyer: {
    appId: 'id1333729162',
    devKey: 'qSCKCrqKsKGTZLLR2ijtk',
    oneLink: 'appsflyer.poplin.co',
    emailLinkWrapper: 'click.poplin.co',
  },
  rollbar: {
    accessToken: '974f21abbc654a4e91f89fdf099c3f13',
  },
};
