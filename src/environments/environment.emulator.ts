import { currentAppVersion } from './app.version';

// ! update firebase configuration here because it is used inside the environment object
const firebase = {
  apiKey: 'AIzaSyBKHhl6I2xPGNjFaxv-gFZuFOSSTae3q2Y',
  authDomain: 'sudsharetest.firebaseapp.com',
  databaseURL: 'https://sudsharetest.firebaseio.com',
  projectId: 'sudsharetest',
  storageBucket: 'sudsharetest.appspot.com',
  messagingSenderId: '119070405931',
  appId: '1:119070405931:web:ff33db9af7d936d3ce016e',
  measurementId: 'G-XJ5SR9E3S1',
};

export const environment = {
  production: false,
  emulator: true,
  local: true,
  version: currentAppVersion,
  baseEndpoint: `http://localhost:5001/sudsharetest/us-central1/`,
  apiGateway: 'https://non-prod-1ip7g17v.uc.gateway.dev',
  apiToken: 'AIzaSyDh1zGEIVsu0MJKzzIU3n6wC3fmfqKu_mE',
  firebase,
  apiPath: 'http://localhost:5002/sudsharetest/us-central1/GeneralAPI',
  apiPathV2: 'http://localhost:8080',
  GoogleMaps: 'AIzaSyCL_QcXwq2sY0ZiLBT7rBlMpVQ9mcMpGks',
  Stripe: 'pk_test_UN2Hmj2I2XyW9cSoQvuYriao',
  webApp: 'https://nonprod-laundry-pro.poplin.co',
  statsig: {
    clients: [
      {
        version: 'default',
        clientKey: 'client-m7UNwYRjZPJJU9wEH9KqVlEGBs5OTicQaAGP0uqbrlv',
      },
      {
        version: 'v4',
        clientKey: 'client-M1yVHQM5FlWiQT4ZV7WcIFl8OA0OFiArc27c78huvat',
      },
    ],
    flags: {
      testFlag: 'testflag',
      forterLoginApi: 'forterloginapi',
      orderExtension: 'orderextension',
      stripeIdv: 'stripeidv',
      checkrVerification: 'checkrverification',
      showOrderDataPreacceptance: 'showorderdatapreacceptance',
      ForceUpdate: 'sudsterappupdate',
      SudsterReferralCode: 'sudsterreferralcode',
      overweightOrderExtension: 'overweightorderextension',
      smartlookRecordings: 'smartlookrecording',
      ExpressService: 'expressservice',
      LPFirstOrderPickUpDeadline: 'lpfirstorderpickupdeadline',
      newOrderCardsEnabled: 'newordercardsenabled',
      lpFirstOrderRecommendedTraining: 'lpfirstorderrecommendedtraining',
      showNewPayoutPage: 'shownewpayoutpage',
      californiaPayoutsRestrictions: 'californiapayoutsrestrictions',
    },
    dynamic: {
      orderExtensionBagCount: 'orderextensionbagcount',
      referralLpABTest: 'lp-referral-ab-test',
    },
  },
  checkr: {
    endpoint: 'https://api.checkr-staging.com/v1',
    apiKey: 'c66506832d504e12f1bf9991ec67b72dcbed6803',
    package: 'test_pro_criminal',
  },
  zendesk: {
    iosKey:
      'eyJzZXR0aW5nc191cmwiOiJodHRwczovL3BvcGxpbi56ZW5kZXNrLmNvbS9tb2JpbGVfc2RrX2FwaS9zZXR0aW5ncy8wMUg3VDUzMURTNjI3WUFINVlTWlZZMjlQSC5qc29uIn0=',
    androidKey:
      'eyJzZXR0aW5nc191cmwiOiJodHRwczovL3BvcGxpbi56ZW5kZXNrLmNvbS9tb2JpbGVfc2RrX2FwaS9zZXR0aW5ncy8wMUg3VDUxMDZINVlHNDgyRzcxODhLUVFTRC5qc29uIn0=',
    webKey: '2d51a9d5-994f-4529-8a8b-664e60999006',
  },
  smartlook: {
    key: 'cfd9d9ab7d4834f745509d206172340d0c2ce088',
  },
  appsflyer: {
    appId: 'id1333000000',
    devKey: 'qSjkjhkjsiiiiiiiii',
    oneLink: 'appsflyer.poplin.co',
    emailLinkWrapper: 'click.poplin.co',
  },
  rollbar: {
    accessToken: '',
  },
};
