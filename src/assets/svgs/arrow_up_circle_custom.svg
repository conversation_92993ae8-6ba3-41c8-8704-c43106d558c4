<svg width="240" height="240" viewBox="0 0 240 240" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_503_129)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M120 170C153.137 170 180 143.137 180 110C180 76.8629 153.137 50 120 50C86.8629 50 60 76.8629 60 110C60 143.137 86.8629 170 120 170ZM117.786 131.99L122.624 132L122.55 98.2591L124.787 98.2639L135.073 108.415L138.487 105.046L120.201 87.0001L116.788 90.369L116.799 90.3806L102 104.986L105.429 108.37L115.688 98.2443L117.711 98.2487L117.786 131.99Z" fill="currentColor"/>
</g>
<defs>
<filter id="filter0_d_503_129" x="0" y="0" width="240" height="240" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="30"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_503_129"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_503_129" result="shape"/>
</filter>
</defs>
</svg>
