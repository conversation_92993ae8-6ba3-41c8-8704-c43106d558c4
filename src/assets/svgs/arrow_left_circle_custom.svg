<svg width="240" height="240" viewBox="0 0 240 240" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_503_128)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M180 110C180 76.8629 153.137 50 120 50C86.8629 50 60 76.8629 60 110C60 143.137 86.8629 170 120 170C153.137 170 180 143.137 180 110ZM141.99 112.214L142 107.376L108.259 107.45L108.264 105.213L118.415 94.927L115.046 91.5134L97.0001 109.799L100.369 113.212L100.381 113.201L114.986 128L118.37 124.571L108.244 114.312L108.249 112.289L141.99 112.214Z" fill="currentColor"/>
</g>
<defs>
<filter id="filter0_d_503_128" x="0" y="0" width="240" height="240" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="30"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_503_128"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_503_128" result="shape"/>
</filter>
</defs>
</svg>
