<svg width="240" height="240" viewBox="0 0 240 240" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_503_127)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M120 50C86.8629 50 60 76.8629 60 110C60 143.137 86.8629 170 120 170C153.137 170 180 143.137 180 110C180 76.8629 153.137 50 120 50ZM122.214 88.0103L117.376 87.9999L117.45 121.741L115.213 121.736L104.927 111.585L101.513 114.954L119.799 133L123.212 129.631L123.201 129.619L138 115.014L134.571 111.63L124.312 121.756L122.289 121.751L122.214 88.0103Z" fill="currentColor"/>
</g>
<defs>
<filter id="filter0_d_503_127" x="0" y="0" width="240" height="240" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="30"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_503_127"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_503_127" result="shape"/>
</filter>
</defs>
</svg>
