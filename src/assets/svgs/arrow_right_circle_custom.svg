<svg width="240" height="240" viewBox="50 40 140 140" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_503_126)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M60 110C60 143.137 86.8629 170 120 170C153.137 170 180 143.137 180 110C180 76.8629 153.137 50 120 50C86.8629 50 60 76.8629 60 110ZM98.0103 107.786L97.9999 112.624L131.741 112.55L131.736 114.787L121.585 125.073L124.954 128.487L143 110.201L139.631 106.788L139.619 106.799L125.014 92.0001L121.63 95.4286L131.756 105.688L131.751 107.711L98.0103 107.786Z" fill="currentColor"/>
</g>
<defs>
<filter id="filter0_d_503_126" x="0" y="0" width="240" height="240" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="10"/>
<feGaussianBlur stdDeviation="30"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_503_126"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_503_126" result="shape"/>
</filter>
</defs>
</svg>
