#refer-code {
  &-page {
    background: var(--white);
  }
  &-header {
    width: 100%;
  }
  &-back-button {
    color: var(--ion-color-base);
  }
  &-title {
    margin-right: 50px;
  }
  &-toggle {
    position: relative;
    width: 90%;
  }
  &-showshare-card {
    box-shadow: none;
  }
  &-border {
    height: 300px;
    width: 300px;
    box-shadow: 0 5px 40px -10px rgba(0, 0, 0, 0.2);
    margin: 35px auto;
    position: relative;
    overflow: hidden;
    border-radius: 50%;
  }

  &-image {
    position: absolute;
    z-index: 2;
    top: 5px;
    left: 5px;
    right: 5px;
    bottom: 5px;
    height: calc(100% - 10px);
    width: calc(100% - 10px);
    border-radius: 50%;
    text-align: center;
    background: var(--white);
    justify-content: center;
  }

  &-howitworks {
    &-card {
      box-shadow: none;
      @media only screen and (min-width: 600px) {
        margin: 0 25%;
      }
    }
    &-content-header {
      h4 {
        margin: 0;
      }
    }
    &-paragraph {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;
      li {
        padding: 2%;
        color: var(--ion-color-dark-shade);
        font-size: large;

        p {
          font-size: large;
          color: var(--ion-color-dark-shade);
        }
      }
      &-learnmore {
        padding-top: 0;
        color: var(--ion-color-dark-shade);
      }
    }
    &-link {
      &-one,
      &-two {
        color: var(--ion-color-dark-shade);
      }
      &-three {
        display: flex;
        align-items: center;
        color: white;
      }
    }
    &-feedback {
      display: flex;
      align-items: center;
      justify-content: center;
      padding-bottom: 0;
      &-text {
        border: 1px solid var(--ion-color-secondary);
        border-radius: 0.5em;
        background: var(--viridian-825);
        color: white;
        font-size: medium;
        padding-bottom: 0;
      }
      p {
        display: flex;
        flex-direction: row-reverse;
        padding: 10px;
      }
    }
    &-arrow-icon {
      font-size: 0.875em;
    }
  }
  &-link-button {
    &::part(native) {
      background-color: var(--viridian-825);
    }
  }
}

ion-segment {
  width: 90%;
  margin: 20px auto;
}
