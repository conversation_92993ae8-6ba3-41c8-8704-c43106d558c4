import { Component } from '@angular/core';
import { Clipboard } from '@capacitor/clipboard';
import { Device } from '@capacitor/device';
import { Share } from '@capacitor/share';
import { ToastController } from '@ionic/angular';
import { untilDestroyed } from '@ngneat/until-destroy';
import { Options } from 'ngx-qrcode-styling';
import { takeWhile } from 'rxjs/operators';
import { SudsterData } from 'src/app/_interfaces/sudster-data.interface';
import { AnalyticsTrackEventType } from 'src/app/_services/analytics/analytics-event.entity';
import { AuthidService } from 'src/app/_services/authid.service';
import { GetSudsterDataService } from 'src/app/_services/get-sudster-data.service';
import { LegacyApiService } from 'src/app/_services/legacy-api.service';
import { StatsigFactoryService } from 'src/app/_services/statsig-factory.service';
import { StatsigService } from 'src/app/_services/statsig.service';
import { trackEvent } from 'src/app/_utils/track-event';
import { environment } from 'src/environments/environment';

export enum ReferralType {
  LpInPersonQR = 'LpInPersonQR',
  LpDigitalRefCode = 'LpDigitalRefCode',
}

@Component({
  selector: 'app-refercode',
  templateUrl: './refercode.page.html',
  styleUrls: ['./refercode.page.scss'],
})
export class ReferCodePage {
  ShowOrShare = true;
  sudster: SudsterData;
  url: string;
  urlDigital: string;
  gotValue = false;
  platformType: string;

  benefitCredits: number;
  benefitPoints: number;
  userId: string;

  private uniqueCall = false;

  public config: Options = {
    width: 200,
    height: 200,
    data: '',
    image:
      'data:image/svg+xml;base64,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',
    margin: 0,
    qrOptions: {
      typeNumber: 0,
      mode: 'Byte',
      errorCorrectionLevel: 'Q',
    },
    imageOptions: { hideBackgroundDots: true, imageSize: 0.5, margin: 0 },
    dotsOptions: {
      type: 'dots',
      color: '#000000',
    },
    backgroundOptions: { color: '#ffffff' },
    cornersSquareOptions: {
      type: 'square',
      color: '#square',
    },
    cornersDotOptions: {
      type: 'square',
      color: '#square',
    },
  };

  private statsigService: StatsigService;

  constructor(
    sudsterData: GetSudsterDataService,
    private apiService: LegacyApiService,
    public toastController: ToastController,
    private statsigFactoryService: StatsigFactoryService,
    private AuthID: AuthidService
  ) {
    this.statsigService = this.statsigFactoryService.getInstance();
    const config = this.statsigService.getConfig(
      environment.statsig.dynamic.referralLpABTest
    );
    this.benefitCredits = config.get('credits', 20);
    this.benefitPoints = config.get('points', 30);
    this.userId = this.AuthID.getID();

    sudsterData
      .listenForSudster()
      .pipe(
        takeWhile((user) => !this.sudster && !!user), // close the subscription when we have all required data
        untilDestroyed(this)
      )
      .subscribe(async (user) => {
        this.sudster = user;
        const hasQRRef =
          user?.Referrals && user?.Referrals[ReferralType.LpInPersonQR];
        const hasDigitalReferral =
          user?.Referrals && user?.Referrals[ReferralType.LpDigitalRefCode];

        if (!this.uniqueCall && this.userId) {
          this.uniqueCall = true;
          trackEvent({
            eventData: {
              event: 'LPReferralOpened',
              userId: this.userId,
            },
          });
        }

        if (!hasQRRef) {
          await apiService
            .post('ReferralService/v1/NewReferral', {
              ReferralSource: 'SudsterApp',
              ReferralType: ReferralType.LpInPersonQR,
            })
            .subscribe(async (res) => {
              const data = await res.data;
              this.url = data['refLink'];
              this.config.data = this.url;
            });
        } else {
          this.url = user?.Referrals[ReferralType.LpInPersonQR];
          this.config.data = this.url;
        }

        if (!hasDigitalReferral) {
          await apiService
            .post('ReferralService/v1/NewReferral', {
              ReferralSource: 'SudsterApp',
              ReferralType: ReferralType.LpDigitalRefCode,
            })
            .subscribe(async (res) => {
              const data = await res.data;
              this.urlDigital = data['refLink'];
            });
        } else {
          this.urlDigital = user?.Referrals[ReferralType.LpDigitalRefCode];
        }
      });
  }

  async OpenShare() {
    this.platformType = await Device.getInfo().then((deviceInfo) => {
      return deviceInfo.platform;
    });

    if (this.platformType === 'web') {
      Clipboard.write({
        string: this.urlDigital,
      });

      this.toastController
        .create({
          message: 'Link copied to clipboard. Paste and send to your friends.',
          duration: 2500,
        })
        .then((toast) => {
          toast.present();
        });
    } else {
      const shareCont: { [k: string]: string } = {
        title: 'Give yourself a break',
        dialogTitle: 'Pick a Friend',
        text: `Use my Poplin link today to get $${this.benefitCredits} off your first order of fresh, perfectly folded laundry delivered right to your door. I would love to serve you! And if I’m not in your area or available another incredible Laundry Pro will be at your service. Give it a try!`,
      };

      if (this.platformType === 'ios') {
        shareCont.text = `${shareCont.text} ${this.urlDigital}`;
      } else {
        shareCont.url = this.urlDigital;
      }

      Share.share(shareCont)
        .then(() => {
          return Promise.resolve();
        })
        .catch((error) => {
          console.error('Share Capacitor Error ', { error });
        });
    }

    this.apiService
      .post('SegmentServiceApi/v1/TrackEvent', {
        source: 'Laundry Pro',
        eventCategory: 'Referral',
        eventData: {
          eventType: AnalyticsTrackEventType.ReferralShared,
          referralLink: this.urlDigital,
        },
      })
      .subscribe();

    const referralId = this.getReferralId(this.urlDigital);

    trackEvent({
      eventData: {
        event: 'LPReferralShared',
        referralId: referralId,
        userId: this.userId,
      },
    });
  }

  getReferralId(refUrl: string) {
    const url = new URL(refUrl);
    return url.searchParams.get('referralId');
  }
}
