import { Component, OnInit } from '@angular/core';
import { AngularFirestore } from '@angular/fire/firestore';
import { AngularFireFunctions } from '@angular/fire/functions';
import { LoadingController } from '@ionic/angular';

@Component({
  selector: 'app-feedback',
  templateUrl: './feedback.page.html',
  styleUrls: ['./feedback.page.scss'],
})
export class FeedbackPage implements OnInit {
  Source = '';

  constructor(
    public firestore: AngularFirestore,
    private cloudFunctions: AngularFireFunctions,
    private loadingController: LoadingController
  ) {}

  ngOnInit() {
    this.presentLoading().then((loading) => {
      this.cloudFunctions
        .httpsCallable('SudsterV3_GenerateCannyToken')({})
        .toPromise()
        .then((res) => {
          this.Source = `https://webview.canny.io?boardToken=${res.BoardToken}&ssoToken=${res.SSOToken}`;
          setTimeout(() => loading.dismiss(), 3000);
        })
        .catch((err) => {
          loading.dismiss();
        });
    });
  }

  async presentLoading() {
    const loading = await this.loadingController.create({});
    await loading.present();
    return loading;
  }
}
