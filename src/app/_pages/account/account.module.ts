import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { IonicModule } from '@ionic/angular';
import { PoplinInformationBoxComponent } from 'src/app/shared/components/poplin-information-box/poplin-information-box.component';
import { EditAddressPage } from './edit-address/edit-address.page';

const routes: Routes = [
  {
    path: '',
    component: EditAddressPage,
  },
];

@NgModule({
  declarations: [EditAddressPage],
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ReactiveFormsModule,
    RouterModule.forChild(routes),
    PoplinInformationBoxComponent,
  ],
})
export class AccountModule {}
