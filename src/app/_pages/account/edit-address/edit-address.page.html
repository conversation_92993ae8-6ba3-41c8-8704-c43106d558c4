<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button
        defaultHref="availability"
        icon="arrow-back"
        color="black"
      ></ion-back-button>
    </ion-buttons>
    <ion-title>Home Address</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <form [formGroup]="addressForm">
    <ion-list>
      <ion-item>
        <ion-label position="floating">Street Address</ion-label>
        <ion-input required="true" formControlName="StreetAddress"></ion-input>
      </ion-item>

      <ion-item>
        <ion-label position="floating">Apt or Unit Number</ion-label>
        <ion-input formControlName="AddressLine2"></ion-input>
      </ion-item>

      <ion-item>
        <ion-label position="floating">City</ion-label>
        <ion-input required="true" formControlName="City"></ion-input>
      </ion-item>

      <ion-item>
        <ion-label position="floating">State</ion-label>
        <ion-input required="true" formControlName="State"></ion-input>
      </ion-item>

      <ion-item>
        <ion-label position="floating">Zipcode</ion-label>
        <ion-input
          pattern="^[0-9]{5}(?:-[0-9]{4})?$"
          required="true"
          formControlName="Zipcode"
        >
        </ion-input>
      </ion-item>

      <ion-button
        id="save-button"
        style="margin-top: 20px"
        [disabled]="!addressForm.valid"
        (click)="saveChanges()"
        expand="block"
        fill="solid"
        >Save Changes
      </ion-button>
    </ion-list>
  </form>
</ion-content>
