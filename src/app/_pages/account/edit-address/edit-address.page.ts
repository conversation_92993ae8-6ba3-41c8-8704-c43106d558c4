import { Component, OnInit } from '@angular/core';
import { AngularFirestore } from '@angular/fire/firestore';
import {
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import {
  Alert<PERSON>ontroller,
  LoadingController,
  NavController,
  ToastController,
} from '@ionic/angular';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import firebase from 'firebase/app';
import 'firebase/firestore';
import Geohash from 'latlon-geohash';
import { of } from 'rxjs';
import { catchError, finalize, first } from 'rxjs/operators';
import { AnalyticsLogService } from 'src/app/_services/analytics/analytics-log.service';
import { AuthidService } from 'src/app/_services/authid.service';
import { LegacyApiService } from 'src/app/_services/legacy-api.service';
import { MapService } from 'src/app/_services/map.service';
import { environment } from 'src/environments/environment';

declare const google;
@UntilDestroy()
@Component({
  selector: 'app-edit-address',
  templateUrl: './edit-address.page.html',
  styleUrls: ['./edit-address.page.scss'],
})
export class EditAddressPage implements OnInit {
  private loading: any;
  private userID: string;

  public addressForm: UntypedFormGroup;
  private geocoder;

  ContactEmail: string;

  constructor(
    public loadingController: LoadingController,
    public firestore: AngularFirestore,
    private authService: AuthidService,
    private alertController: AlertController,
    public navCtrl: NavController,
    public toastController: ToastController,
    private fb: UntypedFormBuilder,
    private analyticsService: AnalyticsLogService,
    private apiService: LegacyApiService
  ) {
    this.geocoder = new google.maps.Geocoder();
    this.userID = this.authService.getID();

    this.addressForm = this.fb.group({
      StreetAddress: ['', Validators.required],
      AddressLine2: '',
      City: ['', Validators.required],
      State: ['', Validators.required],
      Zipcode: [
        '',
        [Validators.required, Validators.pattern('^[0-9]{5}(?:-[0-9]{4})?$')],
      ],
    });
  }

  ngOnInit() {
    this.presentLoading().then(() => {
      this.firestore
        .doc<any>('Sudsters/' + this.userID)
        .valueChanges()
        .pipe(untilDestroyed(this))
        .subscribe((doc) => {
          this.addressForm.patchValue(doc);
          this.ContactEmail = doc.ContactEmail;
          setTimeout(() => {
            this.loading.dismiss();
          }, 300);
        });
    });
  }

  async presentLoading() {
    this.loading = await this.loadingController.create({});
    await this.loading.present();
  }

  public saveChanges() {
    this.presentLoading();
    this.firestore
      .doc<any>(`Code/WorkArea`)
      .valueChanges()
      .pipe(first())
      .subscribe((doc) => {
        const availableWorkArea = doc.Circles;

        this.geocoder.geocode(
          {
            address:
              this.addressForm.value.StreetAddress +
              ' ' +
              this.addressForm.value.City +
              ', ' +
              this.addressForm.value.State +
              ', ' +
              this.addressForm.value.Zipcode +
              ', USA',
          },
          (data, status) => {
            if (status == 'ZERO_RESULTS') {
              this.presentAlert(
                'There is problem',
                'Your home address could not be found. Please check it and try again.'
              );
              return;
            }
            // else if (data[0].geometry.location_type != 'ROOFTOP') {
            //   this.presentAlert(
            //     'There is problem',
            //     'Your home address seems to be incomplete or inaccurate. Please enter your full home address and try again.'
            //   );
            //   return;
            // }
            else if (data.length > 2) {
              this.presentAlert(
                'There is problem',
                'Your home address seems to be incomplete. Please check and try again.'
              );
              return;
            }

            let setLocality = false;
            let setNeighborhood = false;
            let HouseNumber;
            let StreetAddress;
            let City;
            let State;
            let Zipcode;
            for (const component of data[0].address_components) {
              for (const type of component.types) {
                if (type === 'street_number') {
                  HouseNumber = component.long_name;
                }
                if (type === 'route') {
                  StreetAddress = component.short_name;
                }
                if (type === 'locality') {
                  City = component.long_name;
                  setLocality = true;
                }
                if (type === 'neighborhood' && !setLocality) {
                  City = component.long_name;
                  setNeighborhood = true;
                }
                if (
                  (type === 'sublocality_level_1' || type === 'sublocality') &&
                  !setLocality &&
                  !setNeighborhood
                ) {
                  City = component.long_name;
                }
                if (type === 'administrative_area_level_1') {
                  State = component.short_name;
                }
                if (type === 'postal_code') {
                  Zipcode = component.long_name;
                }
              }
            }

            this.addressForm.patchValue({
              StreetAddress: `${HouseNumber} ${StreetAddress}`,
              City: City,
              State: State,
              Zipcode: Zipcode,
            });

            let inArea = false;
            for (let i = 0; i < availableWorkArea.length; i++) {
              const dist = MapService.getDistanceinMiles(
                data[0].geometry.location.lat(),
                data[0].geometry.location.lng(),
                availableWorkArea[i].Center.latitude,
                availableWorkArea[i].Center.longitude
              );
              if (dist <= availableWorkArea[i].Radius + 5) {
                inArea = true;
              }
            }

            if (inArea) {
              const GeoHashCenter = Geohash.encode(
                data[0].geometry.location.lat(),
                data[0].geometry.location.lng(),
                4
              );
              const GeoHashNeighbours = Geohash.neighbours(GeoHashCenter);
              const GeoHashArray = Object.keys(GeoHashNeighbours).map(function (
                n
              ) {
                return GeoHashNeighbours[n];
              });
              GeoHashArray.push(GeoHashCenter);

              this.firestore
                .doc('Sudsters/' + this.userID)
                .set(
                  {
                    WorkAreaCenter: new firebase.firestore.GeoPoint(
                      data[0].geometry.location.lat(),
                      data[0].geometry.location.lng()
                    ),
                    AvgWorkAreaLat: data[0].geometry.location.lat(),
                    WorkAreaRadius: 24140,
                    GeoHash: GeoHashCenter,
                    ...this.addressForm.value,
                  },
                  { merge: true }
                )
                .then(() => {
                  this.apiService
                    .post(
                      '/payments/payouts/check-schedule',
                      {},
                      {
                        baseUrl: `${environment.apiPathV2}`,
                      }
                    )
                    .pipe(
                      finalize(() => {
                        this.loading.dismiss();
                      }),
                      catchError((error) => {
                        return of(null);
                      })
                    )
                    .subscribe(() => {
                      this.navCtrl.pop().then(() => {
                        this.toastController
                          .create({
                            message: 'Address Updated',
                            duration: 2000,
                          })
                          .then((toast) => toast.present());
                      });
                    });
                });
              this.analyticsService.logAddressChangeEvent(
                this.userID,
                this.ContactEmail
              );
            } else {
              //Not in service area
              this.presentAlert(
                'Error',
                "Sorry, we don't service that area yet. But we'll be there soon!"
              );
            }
          }
        );
      });
  }

  async presentAlert(header: string, message: string) {
    if (this.loading) {
      this.loading.dismiss();
    }

    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK'],
    });
    await alert.present();
  }
}
