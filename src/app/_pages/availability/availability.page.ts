import { CommonModule, KeyValue } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import {
  ReactiveFormsModule,
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { Router } from '@angular/router';
import {
  IonicModule,
  LoadingController,
  ModalController,
} from '@ionic/angular';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import moment from 'moment-timezone';
import { timer } from 'rxjs';
import { finalize, switchMap, tap } from 'rxjs/operators';
import { PreferredWorkingDays } from '../../_interfaces/preferred-work-days.service';
import { GenericAlertsService } from '../../_services/generic-alerts.service';
import { IsoDatePipe } from '../../_services/pipes/iso-date.pipe';
import { ToastService } from '../../_services/toast.service';
import { VacationModeService } from '../../_services/vacation-mode.service';
import { getAuthId } from '../../_utils/authId.injector';
import { IconComponent } from '../../shared/components/icon/icon.component';
import { AvailabilitySettings, DateRange } from './availability.model';
import { AvailabilityService } from './availability.service';
import { VacationsModalComponent } from './vacations-modal/vacations-modal.component';

@UntilDestroy()
@Component({
  selector: 'app-availability',
  standalone: true,
  imports: [
    CommonModule,
    IonicModule,
    ReactiveFormsModule,
    IsoDatePipe,
    IconComponent,
  ],
  templateUrl: './availability.page.html',
  styleUrls: ['./availability.page.scss'],
})
export class AvailabilityPage implements OnInit {
  service = inject(AvailabilityService);
  vacationModeService = inject(VacationModeService);
  fb = inject(UntypedFormBuilder);
  toasterService = inject(ToastService);
  modalCtrl = inject(ModalController);
  genericAlertService = inject(GenericAlertsService);
  loadingController = inject(LoadingController);
  router = inject(Router);

  public form: UntypedFormGroup;
  originalData: AvailabilitySettings;
  readonly userID = getAuthId();
  readonly weekDays = {
    Sunday: 'S',
    Monday: 'M',
    Tuesday: 'T',
    Wednesday: 'W',
    Thursday: 'T',
    Friday: 'F',
    Saturday: 'S',
  };
  private loading: any;
  formIsDisabled = false;

  get isVacationEnabled() {
    return this.form.get('VacationMode').get('isVacationEnabled');
  }

  get alertsOnLp() {
    return this.form.get('AlertsOnLp');
  }

  get startDate() {
    return this.form.get('VacationMode').get('startDate');
  }

  get endDate() {
    return this.form.get('VacationMode').get('endDate');
  }

  get preferredWorkingDays() {
    return this.form.get('PreferredWorkingDays');
  }

  get workRadius() {
    return this.form.get('WorkRadius');
  }

  async ngOnInit(): Promise<void> {
    await this.presentLoading();
    this.service
      .doesVacationModeStartSaving()
      .pipe(untilDestroyed(this))
      .subscribe();
  }

  // check every 1h if the vacation has ended
  data$ = timer(0, 3600000).pipe(
    untilDestroyed(this),
    switchMap(() =>
      this.service.getSettings().pipe(
        tap(async (data) => {
          const week = data?.preferredDays
            ? Object.keys(data?.preferredDays)
            : Object.keys(this.weekDays);

          const convertedWeek = week.reduce(
            (acc, key) => ({
              ...acc,
              [key]: data?.preferredDays ? data?.preferredDays[key] : true,
            }),
            {}
          );

          this.form = this.fb.group({
            AlertsOnLp: !!data.doc.AlertsOnLp,
            WorkRadius: [
              data.radius || 50,
              [Validators.required, Validators.pattern(/^[0-9]\d*$/)],
            ],
            VacationMode: this.fb.group({
              isVacationEnabled: !!data.vacations?.isVacationEnabled,
              startDate: data.vacations?.startDate,
              endDate: data.vacations?.endDate,
            }),
            PreferredWorkingDays: this.fb.group(convertedWeek),
          });

          // Store original data for comparison
          this.originalData = {
            AlertsOnLp: !!data.doc.AlertsOnLp,
            WorkRadius: data.radius || 50,
            VacationMode: {
              isVacationEnabled: !!data.vacations?.isVacationEnabled,
              startDate: data.vacations?.startDate,
              endDate: data.vacations?.endDate,
            },
            PreferredWorkingDays: convertedWeek as PreferredWorkingDays,
          };

          if (!this.alertsOnLp.value) {
            await this.alertsOnLpChanged();
          } else {
            this.vacationModeChanged();
          }

          this.setupVacationEndMonitor();

          // ensure the form state properly reflects logic based on enable/disable criteria
          this.initializeFormState();
        })
      )
    ),
    tap(async () => await this.dismissLoading())
  );

  setupVacationEndMonitor() {
    if (
      this.isVacationEnabled?.value &&
      this.startDate?.value &&
      this.endDate?.value
    ) {
      const now = moment();
      const end = moment(this.endDate.value);

      if (now.isAfter(end)) {
        this.turnOffVacationMode();
      }
    }

    // initial check
    if (moment().isAfter(moment(this.endDate.value))) {
      this.turnOffVacationMode();
    }
  }

  turnOffVacationMode() {
    if (this.isVacationEnabled?.value) {
      this.isVacationEnabled.setValue(false);
      this.form.markAsDirty();

      // GIVEN an LP had vacation mode on and it is between the set vacation dates
      // WHEN vacation dates end THEN vacation mode toggle should automatically turn OFF AND
      // * Preferred daily schedule is enabled
      // * New Order Alerts toggle is enabled
      this.alertsOnLp.setValue(true);
      this.alertsOnLp.enable();
      this.preferredWorkingDays.enable();

      this.saveSettings();
    }
  }

  async alertsOnLpChanged() {
    if (this.alertsOnLp.value) {
      this.formIsDisabled = false;
      // GIVEN an LP has New Order Alerts toggled ON AND Vacation mode if toggled OFF
      // OR it is before the set vacation dates THEN
      // * Preferred daily schedule is enabled
      // * Vacation Mode is enabled
      this.isVacationEnabled.enable();
      this.workRadius.enable();
      this.preferredWorkingDays.enable();

      // Check if we're on vacation mode and within date range
      const now = moment(new Date());
      const isWithinRange =
        this.startDate.value &&
        this.endDate.value &&
        now.isBetween(this.startDate.value, this.endDate.value, null, '[]');

      if (this.isVacationEnabled.value && isWithinRange) {
        // If vacation mode is on, and we're within the date range,
        // alerts should be disabled
        this.alertsOnLp.setValue(false);
        this.preferredWorkingDays.disable();
      }
    } else {
      // GIVEN an LP has New Order Alerts toggled OFF
      // * Preferred daily schedule is disabled
      // * Vacation Mode is disabled
      this.preferredWorkingDays.disable();
      this.isVacationEnabled.setValue(false);
      this.isVacationEnabled.disable();
      this.workRadius.disable();
      this.service.saveVacationButtonStatus(false);
      this.formIsDisabled = true;
    }

    if (this.form.dirty) {
      const state = this.alertsOnLp.value ? 'ON' : 'OFF';
      const message = this.alertsOnLp.value
        ? "You're back online and ready to take orders — alerts are on and your availability is active."
        : "New order alerts are off — you're no longer available to take orders.";

      await this.presentLoading('Saving settings...');

      this.service
        .updateSettings(this.form.value, this.originalData)
        .pipe(
          finalize(async () => {
            this.toasterService
              .successToast('NEW ORDER ALERTS ' + state, message)
              .subscribe();
          })
        )
        .subscribe(async () => await this.dismissLoading());
    }

    this.form.markAsPristine();
  }

  vacationModeChanged() {
    const now = moment(new Date());
    const isWithinRange = now.isBetween(
      this.startDate.value,
      this.endDate.value,
      null,
      '[]' // start and end dates in the range check
    );

    // handle vacation mode based on whether it's enabled and date ranges
    if (this.isVacationEnabled.value) {
      if (isWithinRange) {
        // GIVEN an LP has vacation mode toggled ON WHEN it is between the set vacation dates
        // * Preferred daily schedule is disabled
        // * New Order Alerts toggle is disabled
        this.alertsOnLp.setValue(false);
        this.alertsOnLp.disable();
        this.preferredWorkingDays.disable();
        this.workRadius.disable();
        this.formIsDisabled = true;
      } else {
        // GIVEN an LP has vacation mode toggled ON WHEN it is before the set vacation dates
        // * Preferred daily schedule is enabled
        // * New Order Alerts toggle is enabled
        this.alertsOnLp.enable();
        this.preferredWorkingDays.enable();
        this.formIsDisabled = false;
      }
    } else {
      this.formIsDisabled = false;
      this.form.enable();
      this.alertsOnLp.setValue(true);

      this.startDate.setValue(null);
      this.endDate.setValue(null);
      this.vacationModeService.vacationModeModel = { from: null, to: null };
    }

    // monitor vacation endDate
    this.setupVacationEndMonitor();
  }

  saveSettings() {
    this.form.markAsPristine();

    const formValues = { ...this.form.value };

    if (!this.isVacationEnabled.value) {
      formValues.VacationMode = {
        ...formValues.VacationMode,
        startDate: null,
        endDate: null,
      };
    }

    const updates: Partial<AvailabilitySettings> = {};

    if (formValues.AlertsOnLp !== this.originalData.AlertsOnLp) {
      updates.AlertsOnLp = formValues.AlertsOnLp;
    }

    if (formValues.WorkRadius !== this.originalData.WorkRadius) {
      updates.WorkRadius = formValues.WorkRadius;
    }

    const normalizeValue = (val) => {
      return val === null || val === undefined ? null : val;
    };

    const vacationEnabled =
      formValues.VacationMode.isVacationEnabled !==
      this.originalData.VacationMode.isVacationEnabled;
    const startDateChanged =
      normalizeValue(formValues.VacationMode.startDate) !==
      normalizeValue(this.originalData.VacationMode.startDate);
    const endDateChanged =
      normalizeValue(formValues.VacationMode.endDate) !==
      normalizeValue(this.originalData.VacationMode.endDate);

    const vacationModeChanged =
      vacationEnabled || startDateChanged || endDateChanged;

    if (vacationModeChanged) {
      updates.VacationMode = formValues.VacationMode;
    }

    const originalDays = this.originalData.PreferredWorkingDays;
    const currentDays = formValues.PreferredWorkingDays;
    const daysChanged =
      currentDays &&
      Object.keys(currentDays).some(
        (day) => currentDays[day] !== originalDays[day]
      );

    if (daysChanged) {
      updates.PreferredWorkingDays = currentDays;
    }

    if (Object.keys(updates).length > 0) {
      this.service
        .updateSettings(updates as AvailabilitySettings, this.originalData)
        .pipe(
          switchMap(() => this.presentLoading('Saving settings...')),
          finalize(async () => await this.dismissLoading())
        )
        .subscribe({
          next: () => {
            this.originalData = {
              ...this.originalData,
              ...updates,
            };

            this.toasterService
              .successToast(
                'Availability Settings',
                'Changes saved successfully'
              )
              .subscribe();
          },
          error: () =>
            this.toasterService
              .errorToast(
                'Error Saving',
                'Something went wrong while saving your changes. Please try again.'
              )
              .subscribe(),
        });
    }
  }

  goToPath(path = '/') {
    if (this.form.dirty && this.form.touched) {
      this.genericAlertService
        .showUnsavedChangesAlert(this.form.value)
        .subscribe(({ data: { role } }) => {
          if (role === 'submit') {
            this.saveSettings();
          }

          this.router.navigate([path]);
        });
    } else {
      this.router.navigate([path]);
    }
  }

  async openVacationsModal() {
    this.vacationModeChanged();

    if (this.isVacationEnabled.value) {
      const modal = await this.modalCtrl.create({
        component: VacationsModalComponent,
        cssClass: 'datepicker-modal',
        componentProps: {
          date: {
            from: null,
            to: null,
          },
        },
      });

      await modal.present();

      const result = (await modal.onDidDismiss()) as { data: DateRange };

      if (result.data) {
        this.startDate.setValue(result.data.from);
        this.endDate.setValue(result.data.to);
        this.form.markAsDirty();
      } else {
        this.isVacationEnabled.setValue(false);
      }

      this.vacationModeChanged();
    }
  }

  originalOrder = (
    a: KeyValue<string, string>,
    b: KeyValue<string, string>
  ): number => {
    return 0;
  };

  private async presentLoading(message = 'Loading settings...') {
    this.loading = await this.loadingController?.create({
      spinner: 'circular',
      message,
      cssClass: 'circular-spinner',
    });
    await this.loading?.present();
  }

  private async dismissLoading() {
    if (!(await this.loadingController.getTop())) {
      await this.presentLoading('Saving settings...');
    }

    if (this.loadingController && this.loading) {
      this.loading = await this.loadingController?.dismiss({ dismissed: true });
    }
  }

  private initializeFormState() {
    const now = moment(new Date());
    const isWithinRange =
      this.startDate.value &&
      this.endDate.value &&
      now.isBetween(this.startDate.value, this.endDate.value, null, '[]');

    // check if vacation mode active and within dates
    if (this.isVacationEnabled.value && isWithinRange) {
      this.alertsOnLp.setValue(false);
      this.alertsOnLp.disable();
      this.preferredWorkingDays.disable();
    }
    // handle new order alerts OFF state
    else if (!this.alertsOnLp.value) {
      this.preferredWorkingDays.disable();
      this.isVacationEnabled.disable();
    }
  }
}
