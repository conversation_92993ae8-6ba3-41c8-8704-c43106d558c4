<ion-content class="content">
  <ion-title class="content__title"> When’s your vacation? </ion-title>

  <div class="content__inputs">
    <div [class.content__inputs__disabled]="!date?.from">
      <ion-label>Starts</ion-label>
      <strong *ngIf="date?.from">{{ date.from | isoDate }}</strong>
    </div>
    <div [class.content__inputs__disabled]="!date?.to">
      <ion-label>Ends</ion-label>
      <strong *ngIf="date?.to">{{ date.to | isoDate }}</strong>
    </div>
  </div>
  <ion-range-calendar
    [(ngModel)]="model"
    (selectStart)="startDateChange($event)"
    (selectEnd)="endDateChange($event)"
    (change)="calendarChange($event)"
    (select)="selectionCounter()"
    [type]="type"
    [format]="calendarFormat"
    [options]="optionsRange"
  >
  </ion-range-calendar>
</ion-content>

<ion-footer class="footer">
  <div class="footer__actions">
    <div>
      <ion-button class="poplin-theme" color="light" (click)="cancel()"
        >CLOSE</ion-button
      >
      <ion-button
        class="poplin-theme"
        (click)="save()"
        color="primary"
        [disabled]="!(model?.to && model?.from)"
        >SAVE</ion-button
      >
    </div>
  </div>
</ion-footer>
