import { CommonModule } from '@angular/common';
import {
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  inject,
  OnInit,
} from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import {
  CalendarComponentOptions,
  CalendarDay,
  IonRangeCalendarModule,
} from '@googlproxer/ion-range-calendar';
import { IonicModule, ModalController } from '@ionic/angular';
import moment from 'moment-timezone';
import { isOnVacation } from 'src/app/_utils/utils';
import { IsoDatePipe } from '../../../_services/pipes/iso-date.pipe';
import { ToastService } from '../../../_services/toast.service';
import { VacationModeService } from '../../../_services/vacation-mode.service';
import { DATE_FORMAT } from '../../../_utils/enum';
import { DateRange } from '../availability.model';

@Component({
  selector: 'app-vacations-modal',
  standalone: true,
  imports: [
    CommonModule,
    IonicModule,
    IonRangeCalendarModule,
    FormsModule,
    IsoDatePipe,
    ReactiveFormsModule,
  ],
  templateUrl: './vacations-modal.component.html',
  styleUrls: ['./vacations-modal.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class VacationsModalComponent implements OnInit {
  modalCtrl = inject(ModalController);
  vacationService = inject(VacationModeService);
  toasterService = inject(ToastService);

  readonly calendarFormat = DATE_FORMAT;
  readonly dbDateFormat = 'yyyy-MM-DD';
  counter = 0;
  model: DateRange;
  date: DateRange;
  type: 'js-date';
  optionsRange: CalendarComponentOptions = {
    pickMode: 'range',
    from: new Date(),
    showMonthPicker: false,
    to: moment(new Date()).add(30, 'days').toDate(),
  };

  ngOnInit() {
    const { vacationModeModel: model } = this.vacationService;

    if (model.from && model.to) {
      this.date = this.setModel(model);
      this.model = this.date;
    } else {
      this.model = this.date;
      this.saveModelState();
    }
  }

  async save() {
    this.vacationService.notifyVacationDataStartSaving$.next(true);
    const model = this.setModel(this.model, DATE_FORMAT);
    this.vacationService
      .updateVacations({
        isVacationEnabled: true,
        startDate: this.model.from,
        endDate: this.model.to,
        isVacationModeActive: isOnVacation(this.model.from, this.model.to),
      })
      .subscribe(() =>
        this.toasterService
          .successToast(
            'Vacation Mode Enabled',
            `Enjoy your vacation from ${model.from} to ${model.to}!`
          )
          .subscribe()
      );
    this.saveModelState();
    await this.modalCtrl?.dismiss(this.setModel(this.model));
  }

  calendarChange(evt: any) {
    const MAX_CALENDAR_CLICKS = 3;

    this.model = evt;
    this.date = this.setModel(evt);

    if (this.counter === MAX_CALENDAR_CLICKS) {
      this.date = { from: null, to: null };
      this.model = undefined;
      this.counter = 0;
    }
  }

  async cancel() {
    this.saveModelState();

    await this.modalCtrl.dismiss();
  }

  startDateChange(evt: CalendarDay) {
    this.date = {
      from: moment(evt.time).format(this.dbDateFormat),
      to: null,
    };
  }

  endDateChange(evt: CalendarDay) {
    this.date = {
      from: this.date.from,
      to: moment(evt.time).format(this.dbDateFormat),
    };
  }

  selectionCounter() {
    this.counter++;
  }

  private saveModelState() {
    const { vacationModeModel: model } = this.vacationService;
    this.vacationService.vacationModeModel =
      model?.to && model?.from ? this.date : this.model;

    if (this.model?.from && this.model?.to) {
      this.vacationService.vacationModeModel = this.model;
    }
  }

  private setModel(model: DateRange, format = this.dbDateFormat) {
    return {
      from: moment(model.from).utc(false).format(format),
      to: moment(model.to).utc(false).format(format),
    };
  }
}
