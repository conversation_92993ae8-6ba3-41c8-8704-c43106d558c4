@import 'type-mixins';

:host {
  padding: 36px 24px;

  .content {
    padding: 0;

    &__inputs {
      margin-block-end: 40px;
      margin-block-start: 24px;
      display: flex;
      justify-content: space-between;
      gap: 24px;

      &__disabled {
        background-color: var(--gray-150);
      }

      div {
        width: 100%;
        border-radius: var(--border-radius-default);
        border: 1px solid var(--gray-250);
        min-height: 64px;
        padding: 11px 12px;
        align-items: flex-start;
        display: flex;
        flex-direction: column;
        justify-content: center;

        ion-label {
          @include r-body;
        }

        strong {
          @include h2-subhead;
        }
      }
    }

    &__title {
      padding-inline-start: 0;
      display: inherit;
      @include ion-header;
    }
  }

  .footer {
    &__actions {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding-right: 16px;
    }
  }

  ion-range-calendar {
    padding: 0;
    margin-left: -8px;
    width: 103%;
  }

  ion-content {
    --padding-start: 0;
    --padding-end: 0;
    --padding-bottom: 0;
    --padding-top: 0;
  }
}

// Override calendar UI
ion-range-calendar ::ng-deep {
  .title .button.switch-btn {
    color: var(--ion-color-primary) !important;
    text-transform: uppercase;
    @include title-btn;
  }

  button.today.days-btn {
    background: var(--viridian-150);
  }
}
