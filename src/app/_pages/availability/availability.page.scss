@import 'type-mixins';

:host {
  max-width: 440px;
  margin: 0 auto;

  ion-title {
    @include ion-container-header;
    padding-inline-end: 60px;
  }

  .disable-section {
    color: var(--gray-700);
  }

  a {
    color: var(--blue-750);
    text-decoration: underline;

    &:visited {
      color: var(--blue-750);
    }
  }

  .datepicker-preview {
    display: flex;
    background: var(--Form-Fields-Background-default);
    padding: 11px 20px;
    flex-direction: column;
    border-radius: var(--border-radius-default);
    border: 1px solid var(--Form-Fields-Border-default);
    box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.04);

    label {
      color: var(--gray-800);
    }

    > * {
      @include r-body;
    }
  }

  .content {
    padding: 24px 24px 0;
    height: 100%;

    p {
      color: var(--gray-800);
      margin-bottom: 0;
    }

    h2 {
      font-family: 'Fakt-Bold', sans-serif;
      @include h2-subhead;
    }

    ion-text {
      margin-block-end: 36px;
      display: inline-block;
      @include r-body;
    }

    &__block {
      margin-block-end: 36px;
    }

    &__columns {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    &__week {
      margin-block-start: 16px;
      display: flex;
      justify-content: space-between;

      div {
        display: flex;
        flex-direction: column;
        gap: 8px;
        align-items: center;
      }
    }

    ion-chip {
      --background: var(--blue-300);
      margin: 0;
      text-transform: uppercase;
      @include ion-chip;
    }

    .content-info {
      margin-block-end: 36px;
    }

    &__item {
      margin-block-end: 36px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 20px;

      span {
        color: var(--gray-800);
      }

      &__error {
        display: flex;
        align-items: center;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;

        poplin-icon {
          height: 20px !important;
          width: 20px !important;
        }
      }

      .radius-error {
        display: flex;
        align-items: center;
        color: var(--gray-800);
        @include r-body;

        ion-icon {
          margin-right: 4px;
        }
      }

      ion-label {
        display: flex;
        flex-direction: column;
      }

      ion-button {
        margin: unset;
      }

      input[type='number'] {
        min-width: 110px;
        text-align: center;
      }

      &__next {
        margin-block-end: 16px;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        display: grid;
        align-items: center;
      }

      &.address {
        align-items: flex-start;
      }
    }

    &__vacation {
      margin-block-end: 16px;
    }
  }

  .actions {
    display: flex;
    flex-direction: column;
    align-items: center;

    ion-button:first-child {
      margin-block-start: 0;
      width: 100%;
    }
  }

  section {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;

    ion-spinner {
      width: 75px;
      height: 75px;
    }
  }

  .footer {
    border-top: 1px solid var(--color-border-alt);
    padding: 24px 32px;
  }
}
