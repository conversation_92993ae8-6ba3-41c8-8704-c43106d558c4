import { inject, Injectable } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/auth';
import { AngularFirestore } from '@angular/fire/firestore';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { combineLatest, distinctUntilChanged, Observable, of } from 'rxjs';
import { catchError, debounceTime, switchMap } from 'rxjs/operators';
import { PreferredWorkingDays } from '../../_interfaces/preferred-work-days.service';
import { SudsterData } from '../../_interfaces/sudster-data.interface';
import { VacationUpdateData } from '../../_interfaces/vacation-mode.interface';
import { WorkRadiusData } from '../../_interfaces/work-radius.interface';
import { ApiService } from '../../_services/api.service';
import { PreferredWorkDaysService } from '../../_services/preferred-work-days.service';
import { ToastService } from '../../_services/toast.service';
import { VacationModeService } from '../../_services/vacation-mode.service';
import { WorkRadiusService } from '../../_services/work-radius.service';
import { getAuthId } from '../../_utils/authId.injector';
import { isOnVacation } from '../../_utils/utils';
import { AvailabilitySettings } from './availability.model';

@UntilDestroy()
@Injectable({
  providedIn: 'root',
})
export class AvailabilityService {
  firestore = inject(AngularFirestore);
  afAuth = inject(AngularFireAuth);
  vacationService = inject(VacationModeService);
  toasterService = inject(ToastService);
  workRadiusService = inject(WorkRadiusService);
  apiService = inject(ApiService);
  preferredWorkdaysService = inject(PreferredWorkDaysService);

  private readonly alertsLPOnUrl = 'AlertsOnLp/v1/sudster/alerts-on-lp';
  readonly userID = getAuthId();

  getLegacyData(): Observable<SudsterData> {
    return this.firestore
      .doc<SudsterData>(`Sudsters/${this.userID}`)
      .valueChanges()
      .pipe(untilDestroyed(this), distinctUntilChanged(), debounceTime(300));
  }

  getVacationsSettings() {
    return this.vacationService.getVacations();
  }

  getPreferredDays() {
    return this.preferredWorkdaysService.getPreferredWorkDays();
  }

  getWorkRadius() {
    return this.workRadiusService.getWorkRadius();
  }

  getSettings() {
    return this.getLegacyData().pipe(
      switchMap((doc) =>
        combineLatest({
          radius: this.getWorkRadius(),
          preferredDays: this.getPreferredDays(),
          vacations: this.getVacationsSettings(),
          doc: of(doc),
        })
      )
    );
  }

  updateSettings(
    data: AvailabilitySettings,
    originalData: AvailabilitySettings
  ) {
    const vacationData: VacationUpdateData = data.VacationMode
      ?.isVacationEnabled
      ? {
          ...data.VacationMode,
          isVacationModeActive: isOnVacation(
            data.VacationMode.startDate,
            data.VacationMode.endDate
          ),
        }
      : null;

    const updateVacations$ =
      data.VacationMode?.isVacationEnabled !== undefined
        ? this.updateVacation(data.VacationMode.isVacationEnabled, vacationData)
        : of(null);

    const alerts =
      data.AlertsOnLp === undefined
        ? of(null)
        : this.updateAlertsOnLp(data.AlertsOnLp);

    const radius =
      data.WorkRadius === undefined
        ? of(null)
        : this.updateRadius({
            WorkRadius: data.WorkRadius,
            OriginalWorkRadius: originalData?.WorkRadius,
          });

    const preferredDays =
      data.PreferredWorkingDays === undefined
        ? of(null)
        : this.updatePreferredDays(data.PreferredWorkingDays);

    const vacations =
      data.VacationMode?.isVacationEnabled === undefined
        ? of(null)
        : updateVacations$;

    return combineLatest({
      alerts,
      radius,
      preferredDays,
      vacations,
    });
  }

  updateVacation(isVacationEnabled: boolean, vacationData: VacationUpdateData) {
    this.vacationService.notifyVacationDataStartSaving$.next(true);
    return isVacationEnabled
      ? this.updateVacationsSettings(vacationData)
      : this.updateVacationsMode(isVacationEnabled);
  }

  updateVacationsSettings(data: VacationUpdateData) {
    return this.vacationService.updateVacations(data);
  }

  updateVacationsMode(isVacationEnabled: boolean) {
    return this.vacationService.updateVacationsMode(isVacationEnabled);
  }

  updateRadius(data: WorkRadiusData) {
    return this.workRadiusService.updateWorkRadius(data);
  }

  updatePreferredDays(data: PreferredWorkingDays) {
    return this.preferredWorkdaysService.updatePreferredWorkDays(data);
  }

  updateAlertsOnLp(AlertsOnLp: boolean) {
    return this.apiService.patch(this.alertsLPOnUrl, { AlertsOnLp }).pipe(
      // just in case
      catchError((_) =>
        of(this.firestore.doc(`Sudsters/${this.userID}`).update({ AlertsOnLp }))
      )
    );
  }

  doesVacationModeStartSaving = (): Observable<boolean> =>
    this.vacationService.notifyVacationDataStartSaving$.pipe(
      untilDestroyed(this)
    );

  saveVacationButtonStatus = (value: boolean): void => {
    this.vacationService.notifyVacationDataStartSaving$.next(value);
  };

  throwErrorToast() {
    return this.toasterService.errorToast(
      'Error Saving',
      'Something went wrong while saving your changes. Please try again.'
    );
  }
}
