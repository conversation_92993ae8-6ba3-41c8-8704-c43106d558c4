<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button (click)="goToPath()" color="black">
        <ion-icon name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title> Availability </ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ng-container *ngIf="data$ | async as data">
    <form [formGroup]="form" class="content">
      <div class="content__columns">
        <ion-text>
          Customize your available days, set your travel distance, and toggle
          Vacation Mode for a well-deserved break whenever you need it.
        </ion-text>
      </div>

      <div class="content__item">
        <ion-label
          [class.disable-section]="isVacationEnabled.value && !alertsOnLp.enabled"
        >
          <h2>New Order Alerts</h2>
          <span *ngIf="alertsOnLp.value" class="content__item__error"
            >Get notified when new orders are available in your work
            radius.</span
          >
          <span *ngIf="!alertsOnLp.value" class="content__item__error">
            <poplin-icon
              color="--Form-Fields-Border-error"
              name="close_sm_filled"
              [size]="48"
            ></poplin-icon
            >You will not receive alerts</span
          >
        </ion-label>
        <ion-toggle
          (ionChange)="alertsOnLpChanged()"
          formControlName="AlertsOnLp"
        ></ion-toggle>
      </div>

      <div class="content__block">
        <ion-label>
          <h2 [class.disable-section]="formIsDisabled">
            Preferred Daily Schedule
          </h2>
          <p>
            Select the days you’d like to accept orders. This will guide your
            customers’ requests.
          </p>
        </ion-label>
        <div class="content__week" formGroupName="PreferredWorkingDays">
          <div *ngFor="let control of weekDays | keyvalue : originalOrder">
            <ion-label [class.disable-section]="formIsDisabled"
              >{{ control.value }}</ion-label
            >
            <ion-checkbox class="poplin-theme" [formControlName]="control.key">
            </ion-checkbox>
          </div>
        </div>
      </div>

      <div class="content__item">
        <ion-label>
          <h2 [class.disable-section]="formIsDisabled">Work Radius</h2>
          <span
            *ngIf="!(workRadius.value > 50 || workRadius.value < 0 || workRadius.invalid)"
            >50-miles max</span
          >
          <span
            class="radius-error"
            *ngIf="workRadius.value > 50 || workRadius.value < 0 || workRadius.invalid"
          >
            <ion-icon color="danger" name="alert-circle"></ion-icon>
            Enter a value within 50 miles</span
          >
        </ion-label>
        <input
          formControlName="WorkRadius"
          class="poplin-theme"
          type="number"
          min="1"
          max="50"
          placeholder="## miles"
        />
      </div>

      <div formGroupName="VacationMode">
        <div class="content__item content__vacation">
          <ion-label>
            <h2
              [class.disable-section]="!alertsOnLp.value && formIsDisabled && !isVacationEnabled.value"
            >
              Vacation Mode
            </h2>
            <p>
              Set Vacation Mode up to 30 days in advance.
              <a
                href="https://poplin-laundry-pro.zendesk.com/hc/en-us/articles/32543043305115-Availability-Settings-Beta"
                target="_blank"
                >Learn more</a
              >
            </p>
          </ion-label>
          <ion-toggle
            formControlName="isVacationEnabled"
            (ionChange)="openVacationsModal()"
          ></ion-toggle>
        </div>
        <div *ngIf="isVacationEnabled.value" (click)="openVacationsModal()">
          <ng-container *ngIf="startDate.value && endDate.value">
            <div class="content__item__next">
              <div class="datepicker-preview">
                <label>Starts</label>
                <span>{{startDate.value | isoDate }}</span>
              </div>
              <div class="datepicker-preview">
                <label>Ends</label>
                <span>{{endDate.value | isoDate }}</span>
              </div>
            </div>

            <div class="content-info bg-info">
              You will not receive new order notifications during your vacation.
            </div>
          </ng-container>
        </div>
      </div>

      <div class="content__item address">
        <ion-label>
          <h2>Home Address</h2>
          <p>
            {{ data.doc.StreetAddress }}<br />
            {{ data.doc.AddressLine2 }}<br *ngIf="data.doc.AddressLine2" />
            <span
              >{{ data.doc.City }}, {{ data.doc.State }}, {{ data.doc.Zipcode
              }}</span
            >
          </p>
        </ion-label>
        <ion-button
          class="poplin-theme"
          color="light"
          (click)="goToPath('/account')"
          >UPDATE</ion-button
        >
      </div>
    </form>
  </ng-container>
</ion-content>

<ion-footer class="footer">
  <div class="actions">
    <ion-button
      class="poplin-theme"
      (click)="saveSettings()"
      color="primary"
      [disabled]="form && (form.pristine || (workRadius.value > 50 || workRadius.value < 0))"
      >SAVE SETTINGS</ion-button
    >
  </div>
</ion-footer>
