@import 'src/theme/type-mixins.scss';

swiper {
  height: calc((100vh - 45px) - constant(safe-area-inset-bottom));
  height: calc((100vh - 45px) - env(safe-area-inset-bottom));
  position: relative;

  .swiper-pagination-bullet {
    background-color: var(--BlueGray4);
  }

  .swiper-pagination-bullet-active {
    background-color: var(--BlueGray4);
  }
}

#SignupButton {
  margin: 0 auto;
  --height: 45px;
  height: 45px;
  margin-bottom: constant(safe-area-inset-bottom); /* iOS 11.0 */
  margin-bottom: env(safe-area-inset-bottom); /* iOS 11.2 */
}

.SlideInnerDiv {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  text-align: center;
}

.SlideInnerDiv img {
  width: 90%;
  max-width: 500px;
  box-shadow: 0px 3px 5px rgba(0, 0, 50, 0.1);
  border-radius: 15px;
}

.SlideInnerDiv ion-button {
  margin-top: 15px;
}

.SlideInnerDiv h1 {
  margin-top: 50px;
  @include m-head;
}

.SlideInnerDiv h2 {
  @include m-body;
  color: var(--BlueGray4);
  max-width: 250px;
  margin: 10px auto;
}

.Frame6Button {
  --background: none;
}

#get-started-button {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  height: 45px;
  --height: 45px;
  margin-bottom: constant(safe-area-inset-bottom);
  margin-bottom: env(safe-area-inset-bottom);
}
