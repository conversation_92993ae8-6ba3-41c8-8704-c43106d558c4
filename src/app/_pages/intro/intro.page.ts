import { Component, OnInit } from '@angular/core';
import { <PERSON>ert<PERSON>ontroller, ModalController } from '@ionic/angular';
import { StatsigFactoryService } from 'src/app/_services/statsig-factory.service';
import { StatsigService } from 'src/app/_services/statsig.service';
import { environment } from 'src/environments/environment';
import SwiperCore, { Pagination } from 'swiper';

SwiperCore.use([Pagination]);

@Component({
  selector: 'app-intro',
  templateUrl: './intro.page.html',
  styleUrls: ['./intro.page.scss'],
})
export class IntroPage implements OnInit {
  isExpressServiceEnabled = false;

  swiperConfig: any = {
    pagination: { clickable: true },
    autoHeight: true, // Enable autoHeight here
  };
  private statsigService: StatsigService;
  constructor(
    public modalCtrl: ModalController,
    public alertController: AlertController,
    private statsigFactoryService: StatsigFactoryService
  ) {
    this.statsigService = this.statsigFactoryService.getInstance();
  }

  ngOnInit() {
    this.statsigService
      .checkGate(environment.statsig.flags.ExpressService)
      .subscribe((expressservice) => {
        this.isExpressServiceEnabled = expressservice;
      });
  }

  async OpenFAQModal() {
    const alert = await this.alertController.create({
      header: 'FAQ',
      message: `<h4>What are my hours and work area?</h4>
                <p>
                  That's up to you. You're the boss. You set your hours and your area. Accept orders when you want; take off when you feel like it.
                </p>
                <h4>How quickly does the laundry have to be done?</h4>
                <p>
                  Customers are guaranteed next day delivery, or same day on some Express orders.
                </p>
                <h4>What do I need to start?</h4>
                <ul>
                  <li>U.S. Social Security Number (for background check)</li>
                  <li>Access to a washer &amp; dryer</li>
                  <li>Access to a drying rack, clothesline, or something to air-dry clothes</li>
                  <li>Laundry Detergent</li>
                  <li>A car or some way to pickup and deliver</li>
                  <li>A Basic Bathroom Scale</li>
                  <li>Plastic Bags</li>
                </ul>
                <h4>How much money can I make?</h4>
                <p>
                  The pay for Standard (next-day) service starts at $.75/LB of laundry completed plus tips and boosts.
                  The pay for Express (same-day or overnight) service starts $1.50/LB of laundry completed plus tips and boosts.
                  Top Laundry Pro earns over $6000/month.The average for the top 100 Laundry Pros is about $3500/month.
                  Many Laundry Pros gig part time and earn to save for a vacation, pay a particular bill, or contribute to a college fund.
                  There is a $22.50 minimum per order, so you'll always make at least $22.50 (plus tips) per order
                </p>`,
      buttons: ['OK'],
    });

    await alert.present();
  }

  async OpenHIW() {
    const alert = await this.alertController.create({
      header: 'How It Works',
      message: `<b>1. Signup</b><br>Simply fill out the form and watch a short basic training video.<br><br><b>2. Accept Jobs</b><br>We send you orders in your area. Accept only the orders you want.<br><br><b>3. Wash-Dry-Fold</b><br>Pick up your customer's laundry, wash-dry-fold, and return it by the deadline.<br><br><b>4. Get Paid</b><br>Weekly payouts with opportunities to unlock faster options.`,
      buttons: ['OK'],
    });

    await alert.present();
  }

  async OpenRequirements() {
    const alert = await this.alertController.create({
      header: 'Getting Started',
      message: `<b>Signup</b>
                <ul
                  style="
                    text-align: left;
                    font-size: 12px;
                    line-height: 120%;
                    margin-top: 15px;
                  "
                >
                  <li><span>Full Social Security Number (for background check)</span></li>
                  <li><span>Bank Account or Debit Card (for Direct Deposit of YourEarnings)</span></li>
                  <li><span>At least 18 years old</span></li>
                </ul>
                <b>Recommended Supplies</b>
                <ul style="text-align: left; font-size: 12px; line-height: 120%">
                  <li><span>Laundry Detergent</span></li>
                  <li><span>Clear trash bags to pack folded laundry</span></li>
                  <li><span>Stickers for labeling laundry bags</span></li>
                  <li><span>Scale to weigh clothes</span></li>
                  <li><span>Laundry Baskets</span></li>
                  <li><span>Access to a washer &amp; dryer</span></li>
                  <li><span>A car or some way to pick-up and deliver</span></li>
                </ul>`,
      buttons: ['OK'],
    });

    await alert.present();
  }
}
