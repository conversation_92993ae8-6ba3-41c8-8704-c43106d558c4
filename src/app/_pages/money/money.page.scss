@import 'type-mixins.scss';

#OverviewSec {
  width: 90%;
  margin: 20px auto;
  max-width: 400px;
  border-radius: 15px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  text-align: center;
  overflow: hidden;
  position: relative;

  #OverviewSecLine {
    position: absolute;
    width: 5px;
    height: 100%;
    left: 15px;
    top: 0px;
    bottom: 0px;
    background: var(--Green);
  }

  #BalanceTotalDiv {
    float: left;
    width: 50%;
    padding: 10px;
    padding-left: 30px;

    small {
      font-weight: 700;
      text-transform: uppercase;
      font-size: 10px;
      letter-spacing: 1px;
      color: var(--BlueGray4);
    }

    h1 {
      font-size: 1.25em;
      margin: 10px 0px;
      color: var(--BlueGray5);
    }

    label {
      font-weight: 500;
      font-size: 10px;
      text-transform: uppercase;
      display: block;
      color: var(--BlueGray4);
    }
  }

  #BalanceBreakdownDiv {
    float: right;
    width: 50%;
    overflow: hidden;
    padding-top: 5px;

    .BalanceItem {
      display: block;
      padding: 5px 0px;
      text-align: left;
      color: var(--BlueGray5);

      i {
        vertical-align: -5px;
        font-size: 22px;
        padding-right: 5px;
      }

      label {
        font-weight: 700;
      }

      small {
        font-size: 10px;
        font-weight: 700;
        color: var(--BlueGray4);
        padding-left: 5px;
        vertical-align: 1px;
      }
    }
  }

  #PayoutDiv {
    display: block;
    clear: both;
    padding: 10px 0px;

    label {
      text-transform: uppercase;
      font-size: 12px;
      font-weight: 400;
      letter-spacing: 1px;
      background: var(--BlueGray1);
      display: block;
      padding: 4px;
      color: var(--BlueGray5);
      box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
    }

    button {
      width: 90%;
      padding: 5px;
      font-weight: 600;
      background: var(--Green);
      color: white;
      letter-spacing: 1px;
      font-size: 15px;
      border-top-right-radius: 20px;
      border-bottom-right-radius: 20px;
      box-shadow: 2px 0px 4px rgba(0, 0, 0, 0.2);
    }
  }

  #PayoutVideoButton {
    margin-bottom: 5px;
    margin-top: -5px;
    font-size: 12px;
  }
}

#ListSec {
  .header {
    font-weight: 800;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 13px;
    color: var(--BlueGray4);
    display: block;
    padding-left: 15px;
    padding-bottom: 5px;
  }

  ion-item {
    .Date {
      text-align: center;
      font-size: 18px;
      color: var(--BlueGray4);
      padding-right: 15px;
      padding-left: 5px;

      small {
        display: block;
        font-size: 10px;
        font-weight: 500;
      }
    }

    .Amount {
      font-weight: 500;
    }

    ion-chip {
      --color: white;
      font-weight: 700;
      letter-spacing: 1px;
      font-size: 12px;
      height: 22px;
      margin-left: 10px;
      text-transform: uppercase;
    }
  }

  ion-item.pending {
    font-style: italic;

    ion-label {
      color: var(--BlueGray4);
      font-weight: 500;

      small {
        background: var(--BlueGray1);
        padding: 2px 8px;
        display: inline-block;
        border-radius: 15px;
        font-style: normal;
        font-weight: 500;
        margin-left: 5px;
        vertical-align: 1px;
        box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
        text-transform: uppercase;
      }
    }

    .Amount {
      color: var(--BlueGray4);
      padding-right: 3px;
    }
  }

  ion-label.Title {
    padding-right: 1px;
    white-space: normal;
  }

  .info-chip {
    margin: 2px 0 0 6px;
  }
}

#SendPayoutButton {
  @include aux-text;
  margin: 4px 13px 5px 32px;
  width: auto;
  max-width: unset;
  letter-spacing: 1.3px;
}
