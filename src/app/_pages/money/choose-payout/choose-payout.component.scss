.SideDiv {
  width: 50%;
  float: left;
  text-align: center;
  padding: 20px 10px;

  i {
    font-size: 50px;
    color: var(--Blue);
  }

  h1 {
    color: var(--BlueGray5);
  }

  label {
    font-weight: 700;
    letter-spacing: 1px;
    color: var(--<PERSON>Gray3);
  }
  p {
    line-height: 140%;
    color: var(--BlueGray5);
  }
}

#ChooseTitle {
  text-align: center;
  margin: 0;
  margin-top: 15px;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: var(--BlueGray5);
}

.disabled {
  opacity: 0.3;
  filter: blur(1px) grayscale(1);
}

.disabledLabel {
  text-align: center;
  display: block;
  font-size: 13px;
  font-weight: 600;
  font-style: italic;
  color: var(--BlueGray3);
}
