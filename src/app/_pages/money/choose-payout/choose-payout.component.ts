import { Component, Input, OnInit } from '@angular/core';
import { AngularFireFunctions } from '@angular/fire/functions';
import {
  AlertController,
  LoadingController,
  ModalController,
} from '@ionic/angular';
import { LegacyApiService } from 'src/app/_services/legacy-api.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-choose-payout',
  templateUrl: './choose-payout.component.html',
  styleUrls: ['./choose-payout.component.scss'],
})
export class ChoosePayoutComponent implements OnInit {
  @Input() level: number;
  @Input() available: string;
  @Input() instant_available: string;
  @Input() PayoutDest: string;

  ChooseInstant = false;

  constructor(
    private cloudFunctions: AngularFireFunctions,
    private alertController: AlertController,
    private loadingController: LoadingController,
    private modalCtrl: ModalController,
    private readonly apiService: LegacyApiService
  ) {}

  ngOnInit() {}

  async ConfirmPayout(instant) {
    if (instant) {
      if (this.level >= 4) {
        if (
          (this.level == 4 && this.convertStringToInt(this.available) < 5) ||
          (this.level > 4 &&
            this.convertStringToInt(this.instant_available) < 5)
        ) {
          this.presentAlert(
            '$5 Minimum',
            'There is a $5 minimum for manual payouts.'
          );
          return;
        }
        if (!this.PayoutDest.includes('Debit')) {
          this.presentAlert(
            'Debit Card Required',
            'Instant Payouts required you to use a debit card as your payout method. Please update your payout settings to a debit card.'
          );
          return;
        }
        const alert = await this.alertController.create({
          header: 'Confirm Instant Payout',
          message: `Payout Balance: $${
            this.level == 4 ? this.available : this.instant_available
          }<br><br>Payout Fee: 1.8% (or 75¢ min.)<br><br>Arrives Instantly (Guaranteed within 30 minutes).`,
          buttons: [
            {
              text: 'Cancel',
              role: 'cancel',
              cssClass: 'secondary',
            },
            {
              text: 'CONFIRM',
              handler: () => {
                this.ChooseInstant = true;
                this.SendPayout();
              },
            },
          ],
        });
        await alert.present();
      }
    } else {
      if (this.convertStringToInt(this.available) < 5) {
        this.presentAlert(
          '$5 Minimum',
          'There is a $5 minimum for manual payouts.'
        );
        return;
      }
      const alert = await this.alertController.create({
        header: 'Confirm Standard Payout',
        message: `Payout Balance: $${this.available}<br><br>Payout Fee: FREE<br><br>Arrives within 2 business days.`,
        buttons: [
          {
            text: 'Cancel',
            role: 'cancel',
            cssClass: 'secondary',
          },
          {
            text: 'CONFIRM',
            handler: () => {
              this.ChooseInstant = false;
              this.SendPayout();
            },
          },
        ],
      });
      await alert.present();
    }
  }

  SendPayout() {
    const loading = this.presentLoading();
    this.apiService
      .post(
        '/payments/payouts',
        {
          instant: this.ChooseInstant,
        },
        {
          baseUrl: `${environment.apiPathV2}`,
        }
      )
      .subscribe({
        next: (res) => {
          loading.then(function (ld) {
            ld.dismiss();
          });
          this.modalCtrl.dismiss({
            reload: true,
          });
        },
        error: (err) => {
          console.log('instant payout error', err);
          loading.then(function (ld) {
            ld.dismiss();
          });
          this.presentAlert(
            'Payout Error!',
            err?.message || 'An error occurred while processing your request.'
          );
        },
      });
  }

  convertStringToInt(amount: string) {
    return parseFloat(amount.replace(',', ''));
  }

  async presentLoading() {
    const loading = await this.loadingController.create({});
    await loading.present();
    return loading;
  }

  async presentAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK'],
    });
    await alert.present();
  }
}
