<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button
        defaultHref="home"
        text=""
        icon="arrow-back"
        color="black"
      ></ion-back-button>
    </ion-buttons>
    <ion-title>My Money</ion-title>
    <ion-buttons slot="end">
      <ion-button
        id="open-payout-button"
        (click)="OpenPayoutSettings($event)"
        *ngIf="!PayoutSettings.PayoutsBlocked"
      >
        <ion-icon slot="icon-only" name="settings-sharp"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content *ngIf="loadingDone">
  <section id="OverviewSec">
    <b id="OverviewSecLine"></b>
    <div id="BalanceTotalDiv">
      <small>Balance</small>
      <h1>${{totalBalance}}</h1>
      <label
        ><b>${{convertNumberToDollar(Balance.LifetimeTotal)}}</b>
        all-time</label
      >
    </div>
    <div id="BalanceBreakdownDiv" (click)="ShowAboutBalance()">
      <div class="BalanceItem">
        <i class="material-icons">rotate_right</i>
        <label>${{convertNumberToDollar(Balance.pending)}}</label>
        <small>PROCESSING</small>
      </div>
      <div class="BalanceItem">
        <i class="material-icons">monetization_on</i>
        <label>${{convertNumberToDollar(Balance.available)}}</label>
        <small>AVAILABLE</small>
      </div>
      <div class="BalanceItem">
        <i class="material-icons">account_balance</i>
        <label>${{convertNumberToDollar(Balance.in_transit)}}</label>
        <small>IN-TRANSIT</small>
      </div>
    </div>
    <div id="PayoutDiv">
      <label
        id="ScheduledText"
        *ngIf="PayoutSettings.interval !== 'manual' && !PayoutSettings.PayoutsBlocked"
        >Payout Schedule:
        <b
          >{{PayoutSettings.interval}}
          <span *ngIf="PayoutSettings.interval !== 'daily'"
            >({{GetAnchorString()}})</span
          ></b
        ></label
      >

      <ion-button
        *ngIf="PayoutSettings.interval === 'manual' && !PayoutSettings.PayoutsBlocked"
        id="SendPayoutButton"
        expand="block"
        class="poplin-theme"
        [disabled]="PayoutSettings.currentCount >= PayoutSettings.maxCount"
        (click)="OpenPayoutModal()"
      >
        {{ payoutButtonText }}
      </ion-button>
    </div>
    <ion-button
      (click)="PayoutVideoClick()"
      id="PayoutVideoButton"
      size="small"
      expand="full"
      fill="clear"
      >How Payouts Work <ion-icon name="play-circle-outline"> </ion-icon>
    </ion-button>
  </section>

  <section id="ListSec">
    <ion-list mode="md">
      <ion-label *ngIf="pendingTransactionList.length > 0" class="header"
        >Processing</ion-label
      >
      <ion-item
        class="pending"
        *ngFor="let transaction of pendingTransactionList"
      >
        <label class="Date"
          >{{transaction.day}}<small>{{transaction.month}}</small></label
        >
        <ion-label class="Title"
          >{{transaction.title}}<small class="info-chip"
            >{{transaction.availableOn}}</small
          ></ion-label
        >
        <label class="Amount"
          >${{convertNumberToDollar(transaction.amount)}}</label
        >
      </ion-item>

      <ion-label
        *ngIf="pendingTransactionList.length > 0"
        class="header"
        style="padding-top: 25px"
        >Completed</ion-label
      >
      <ion-item *ngFor="let transaction of TransactionList">
        <label class="Date"
          >{{transaction.day}}<small>{{transaction.month}}</small></label
        >
        <ion-label class="Title"
          >{{transaction.title}}
          <ion-chip
            class="info-chip"
            [style.--background]="getPayoutColor(transaction.payoutStatus)"
            *ngIf="transaction.payoutStatus != null"
          >
            <ion-label>{{transaction.payoutStatus}}</ion-label>
          </ion-chip>
        </ion-label>
        <label
          class="Amount"
          [style.color]="getAmountColor(transaction.amount, transaction.payoutStatus)"
          >${{convertNumberToDollar(transaction.amount)}}</label
        >
      </ion-item>
      <ion-item *ngIf="TransactionList.length >= 100">
        <ion-label class="ion-text-wrap"
          >Transaction history is limited to 100. Contact us if you would like a
          full report sent to you.
        </ion-label>
      </ion-item>
    </ion-list>

    <p *ngIf="TransactionList.length == 0" style="text-align: center">
      No transactions from the last 90 days.
    </p>
  </section>
</ion-content>
