import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { MoneyPageRoutingModule } from './money-routing.module';

import { ChoosePayoutComponent } from './choose-payout/choose-payout.component';
import { MoneyPage } from './money.page';
import { PayoutSettingsComponent } from './payout-settings/payout-settings.component';
import { PayoutUpdateComponent } from './payout-update/payout-update.component';

@NgModule({
  imports: [CommonModule, FormsModule, IonicModule, MoneyPageRoutingModule],
  declarations: [
    MoneyPage,
    PayoutSettingsComponent,
    ChoosePayoutComponent,
    PayoutUpdateComponent,
  ],
})
export class MoneyPageModule {}
