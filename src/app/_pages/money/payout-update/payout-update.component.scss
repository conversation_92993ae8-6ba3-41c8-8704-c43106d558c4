.payout-header,
h6 {
  margin-left: 39px;
  margin-top: 37px;
  /**Layout*/
  display: flex;
  width: 285px;
  height: 19px;
  flex-direction: column;
  flex-shrink: 0;
  /**Style*/
  color: var(--themes-light-content, #000);

  /* Aux Text/S */
  font-family: 'PitchSans-Medium', 'Helvetica Neue', Helvetica, Arial,
    sans-serif;
  font-size: 13px;
  font-style: normal;
  font-weight: 700;
  line-height: 16px;
  letter-spacing: 1.3px;
  text-transform: uppercase;
}
.payout-update-desc,
.payout-update-input {
  /**Layout*/
  display: flex;
  width: 289px;
  height: 82px;
  flex-direction: column;
  flex-shrink: 0;
  margin-left: 39px;
  /**Style*/
  color: var(--themes-light-content, #000);

  /* Body Text/Small */
  font-family: 'Fakt Normal' sans-serif;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 140%;
}
.payout-update-input {
  border-radius: var(--button-radius-radius-square, 8px);
  border: 1px solid var(--form-fields-border-default, #e6e6e6);
  background: var(--form-fields-background-default, #fff);

  /* Light/Basic Drop Shadow */
  box-shadow: 0px 4px 24px 0px rgba(0, 0, 0, 0.04);
  height: 48px;
  position: relative;
  bottom: 26px;
}

.phone-number {
  position: relative;
  bottom: 30px;
  p {
    margin-left: 39px;
    /**Layout */
    display: flex;
    width: 262px;
    height: 24px;
    flex-direction: column;
    flex-shrink: 0;
    /**Style * /
    color: var(--themes-light-content, #000);

/* Subhead/Medium */
    font-family: 'PitchSans-Medium' sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 120%;
    letter-spacing: 2.4px;
    text-transform: uppercase;
  }
}
ion-button {
  &::part(native) {
    /** Layout */
    position: relative;
    bottom: 50px;
    display: flex;
    width: 285px;
    min-height: 48px;
    padding: var(--button-padding-vertical-default, 6px)
      var(--button-padding-horizontal-default, 8px);
    justify-content: center;
    align-items: center;

    /** Style */
    border-radius: var(--button-radius-radius-square, 8px);
    background: var(--pink-primary-pink-core, #ff6289);

    /* Light/Basic Drop Shadow */
    box-shadow: 0px 4px 24px 0px rgba(0, 0, 0, 0.04);
    font-family: 'PitchSans-Medium', sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 120%;
    letter-spacing: 2.4px;
    text-transform: uppercase;
  }
}
.resend-code-btn {
  /** Layout*/
  bottom: 88px;
  font-size: 13px;
  width: 285px;
  display: flex;
  padding: 0px var(--button-padding-inner-small, 12px);
  flex-direction: column;
  justify-content: center;
  align-items: center;
  align-self: stretch;
  margin: auto;
  bottom: 1;
  position: relative;
  background: white;
  font-family: 'PitchSans-Medium';
  font-weight: 700;
  text-transform: uppercase;
}
