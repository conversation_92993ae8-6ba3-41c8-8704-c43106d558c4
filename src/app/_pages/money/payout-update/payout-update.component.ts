import { Component, OnInit } from '@angular/core';
import { AngularFireFunctions } from '@angular/fire/functions';
import {
  <PERSON>ert<PERSON>ontroller,
  LoadingController,
  ModalController,
} from '@ionic/angular';
import { SudsterData } from 'src/app/_interfaces/sudster-data.interface';
import { GetSudsterDataService } from 'src/app/_services/get-sudster-data.service';

@Component({
  selector: 'app-payout-update',
  templateUrl: './payout-update.component.html',
  styleUrls: ['./payout-update.component.scss'],
})
export class PayoutUpdateComponent implements OnInit {
  constructor(
    private cloudFunctions: AngularFireFunctions,
    private alertController: AlertController,
    private loadingController: LoadingController,
    private modalController: ModalController,
    private getSudsterData: GetSudsterDataService
  ) {}
  verificationStep: number;
  lpData: SudsterData;
  last4Phone: string;
  requestType: string = 'payout-update';
  payoutCode: string;

  async ngOnInit() {
    this.verificationStep = 1;
    this.lpData = await this.getSudsterData.getOnce();
    this.last4Phone = this.formatPhone(this.lpData.Phone);
  }

  async sendVerificationCode() {
    const loading = this.presentLoading();
    return await this.cloudFunctions
      .httpsCallable('SudsterV3_VerifyPhone')({
        phone: this.lpData.Phone,
        requestType: this.requestType,
      })
      .toPromise()
      .then((res) => {
        loading.then((ld) => ld.dismiss());
        this.verificationStep = 2;
        return res;
      })
      .catch((err) => {
        loading.then((ld) => ld.dismiss());
        return this.presentAlert(
          'Error',
          err?.message || 'An error occurred while processing your request.'
        );
      });
  }
  async verifyCode(code: string) {
    const loading = this.presentLoading();
    return await this.cloudFunctions
      .httpsCallable('SudsterV3_VerifyPhone')({
        phone: this.lpData.Phone,
        requestType: this.requestType,
        code: code.replace('-', ''),
      })
      .toPromise()
      .then((res) => {
        loading.then((ld) => ld.dismiss());

        if (res === 'Verification code is not correct. Please try again.') {
          this.presentAlert('Error', res);
        } else {
          this.modalController.dismiss({ result: res });
        }
        return res;
      })
      .catch((err) => {
        loading.then((ld) => ld.dismiss());
        return this.presentAlert(
          'Error',
          err?.message || 'An error occurred while processing your request.'
        );
      });
  }

  async presentAlert(
    Title: string,
    Message: string
  ): Promise<HTMLIonAlertElement> {
    const alert = await this.alertController.create({
      header: Title,
      message: Message,
      buttons: ['OK'],
    });
    await alert.present();
    return alert;
  }
  async presentLoading() {
    const loading = await this.loadingController.create({});
    await loading.present();
    return loading;
  }
  formatPhone(phoneNumber: string): string {
    const last4Phone = phoneNumber
      .slice(2)
      .replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
    const formattedPhone = last4Phone.replace(
      /^(\D*\d\D*){6}/gm,
      function (match) {
        return match.replace(/\d/g, '*');
      }
    );

    return formattedPhone;
  }
  onChange(ev: string) {
    this.payoutCode = ev;
  }
}
