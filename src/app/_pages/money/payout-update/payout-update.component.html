<ion-label class="payout-header">{{
  verificationStep === 1 ? 'Identity Verification' : 'Enter Verification Code'
}}</ion-label>
<section class="payout-update-desc" id="payout" *ngIf="verificationStep === 1">
  For your account's security, each time you update your payout method, we will
  send a one-time verification code to your mobile number.
</section>
<section class="payout-update-input" *ngIf="verificationStep === 2">
  <ion-input
    label="Verification Code"
    label-placement="floating"
    fill="outline"
    maxLength="7"
    placeholder="Enter six-digit code"
    [value]="inputModel"
    (ionChange)="onChange($event.detail.value)"
  ></ion-input>
</section>
<section class="phone-number" *ngIf="verificationStep === 1">
  <h6>Phone Number</h6>
  <p>{{ last4Phone }}</p>
</section>
<ion-button *ngIf="verificationStep === 1" (click)="sendVerificationCode()">
  Send Code
</ion-button>
<ion-button *ngIf="verificationStep === 2" (click)="verifyCode(payoutCode)">
  Verify Code
</ion-button>
<div>
  <button
    *ngIf="verificationStep === 2"
    class="resend-code-btn"
    (click)="sendVerificationCode()"
  >
    Resend Code
  </button>
</div>
