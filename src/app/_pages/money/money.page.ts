import { Component, OnInit } from '@angular/core';
import { AngularFireFunctions } from '@angular/fire/functions';
import { Router } from '@angular/router';
import {
  Alert<PERSON>ontroller,
  LoadingController,
  ModalController,
} from '@ionic/angular';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import {
  catchError,
  combineLatest,
  filter,
  finalize,
  iif,
  map,
  of,
} from 'rxjs';
import { VIDEOREFS } from 'src/app/_interfaces/videoRefs';
import {
  LegacyApiService,
  extractData,
} from 'src/app/_services/legacy-api.service';
import { PlayVideoService } from 'src/app/_services/play-video.service';
import { StatsigFactoryService } from 'src/app/_services/statsig-factory.service';
import { ToastService } from 'src/app/_services/toast.service';
import { environment } from 'src/environments/environment';
import {
  PAYOUT_SCHEDULE,
  PayoutUpdateService,
} from '../payout/payout-update.service';
import { ChoosePayoutComponent } from './choose-payout/choose-payout.component';
import { PayoutSettingsComponent } from './payout-settings/payout-settings.component';

const CA_MAX_MONTHLY_PAYOUTS_NUMBER = 2;
const MONTH_NAMES = [
  'JAN',
  'FEB',
  'MAR',
  'APR',
  'MAY',
  'JUN',
  'JUL',
  'AUG',
  'SEP',
  'OCT',
  'NOV',
  'DEC',
] as const;

interface Balance {
  pending: number;
  available: number;
  in_transit: number;
  instant_available: number;
  LifetimeTotal: number;
}

interface PayoutSettings {
  PayoutDest: string;
  PayoutsBlocked: boolean;
  anchor: string;
  level: number;
  interval: string;
  currentCount: number;
  maxCount: number;
}

interface Transaction {
  day: string;
  month: string;
  title: string;
  amount: number;
  payoutStatus?: string;
  payoutETA?: string;
}

interface PendingTransaction {
  day: string;
  month: string;
  title: string;
  amount: number;
  availableOn: string;
}

@UntilDestroy()
@Component({
  selector: 'app-money',
  templateUrl: './money.page.html',
  styleUrls: ['./money.page.scss'],
})
export class MoneyPage implements OnInit {
  loadingDone = false;
  Balance: Balance = {
    pending: 0,
    available: 0,
    in_transit: 0,
    instant_available: 0,
    LifetimeTotal: 0,
  };

  PayoutSettings: PayoutSettings = {
    PayoutDest: '',
    PayoutsBlocked: true,
    anchor: '',
    level: 1,
    interval: '',
    currentCount: 0,
    maxCount: CA_MAX_MONTHLY_PAYOUTS_NUMBER,
  };

  TransactionList: Transaction[] = [];
  pendingTransactionList: PendingTransaction[] = [];

  private payoutModalOpen = false;
  private showNewPayoutPage = false;
  californiaPayoutsRestrictions = false;

  constructor(
    private readonly cloudFunctions: AngularFireFunctions,
    private readonly loadingController: LoadingController,
    private readonly modalController: ModalController,
    private readonly alertController: AlertController,
    private readonly PlayVideo: PlayVideoService,
    private readonly statsigFactoryService: StatsigFactoryService,
    private readonly router: Router,
    private readonly payoutUpdateService: PayoutUpdateService,
    private readonly apiService: LegacyApiService,
    private readonly toasterService: ToastService
  ) {
    this.initializeStatsig();
  }

  private initializeStatsig(): void {
    const statsigService = this.statsigFactoryService.getInstance();
    combineLatest([
      statsigService.checkGate(environment.statsig.flags.showNewPayoutPage),
      statsigService.checkGate(
        environment.statsig.flags.californiaPayoutsRestrictions
      ),
    ]).subscribe(([showNewPayoutPage, californiaPayoutsRestrictions]) => {
      this.showNewPayoutPage = showNewPayoutPage;
      this.californiaPayoutsRestrictions = californiaPayoutsRestrictions;
      this.GetMoneyData(this.showNewPayoutPage);
    });
  }

  ngOnInit(): void {
    this.initializePayoutUpdates();
  }

  private initializePayoutUpdates(): void {
    this.payoutUpdateService.payoutUpdate$
      .pipe(
        untilDestroyed(this),
        filter((update) => !!update)
      )
      .subscribe((update) => {
        this.updatePayoutSettings(update);
      });
  }

  private updatePayoutSettings(update: any): void {
    this.PayoutSettings.interval = update.type;
    this.PayoutSettings.anchor = this.getAnchorForSchedule(update);
  }

  private getAnchorForSchedule(update: any): string {
    switch (update.type) {
      case PAYOUT_SCHEDULE.MONTHLY:
        return update.monthDay;
      case PAYOUT_SCHEDULE.WEEKLY:
        return update.weekDay;
      case PAYOUT_SCHEDULE.BIWEEKLY:
        return update.biweeklyDay;
      case PAYOUT_SCHEDULE.MANUAL:
        return '';
      default:
        return this.PayoutSettings.anchor;
    }
  }

  get totalBalance(): string {
    return this.convertNumberToDollar(
      this.Balance.pending + this.Balance.available + this.Balance.in_transit
    );
  }

  get payoutButtonText(): string {
    if (!this.californiaPayoutsRestrictions) {
      return 'SEND PAYOUT';
    }

    const { currentCount, maxCount } = this.PayoutSettings;
    return currentCount >= maxCount
      ? `PAYOUT LIMIT REACHED ${currentCount}/${maxCount}`
      : `SEND PAYOUT ${currentCount}/${maxCount}`;
  }

  get anchorString(): string {
    if (this.PayoutSettings.anchor.includes('-')) {
      return this.formatBiweeklyAnchor();
    }
    return this.formatSingleDayAnchor();
  }

  private formatBiweeklyAnchor(): string {
    const [firstDay, secondDay] = this.PayoutSettings.anchor.split('-');
    const firstDayString = firstDay === '1' ? '1st' : `${firstDay}th`;
    return `${firstDayString} & ${secondDay}th`;
  }

  private formatSingleDayAnchor(): string {
    const day = parseInt(this.PayoutSettings.anchor);
    if (day === 1) return '1st';
    if (day <= 30) return `${day}th`;
    return this.PayoutSettings.anchor;
  }

  convertNumberToDollar(amount: number): string {
    return (amount / 100).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }

  getAmountColor(amount: number, payout: string | null): string {
    if (payout != null) return 'var(--Blue)';
    return amount > 0 ? 'var(--Green)' : 'var(--Red)';
  }

  getPayoutColor(status: string): string {
    switch (status) {
      case 'IN-TRANSIT':
        return 'var(--Orange)';
      case 'ARRIVED':
        return 'var(--Green)';
      default:
        return 'var(--Red)';
    }
  }

  GetMoneyData(showNewPayoutPage: boolean = false) {
    const loading = this.presentLoading('Generating Financial Report...');
    this.TransactionList = [];

    iif(
      () => showNewPayoutPage,
      combineLatest([
        this.apiService.get(`/payments/payouts/report`, {
          baseUrl: `${environment.apiPathV2}`,
        }),
        this.apiService.get(`/payments/payouts/schedule`, {
          baseUrl: `${environment.apiPathV2}`,
        }),
      ]),
      this.cloudFunctions
        .httpsCallable('SudsterV3_GetStripeMoneyData')({})
        .pipe(map((data) => [data]))
    )
      .pipe(
        extractData<Record<string, any>>(),
        catchError((err) => {
          return of(null);
        }),
        finalize(() => {
          this.loadingDone = true;
          loading.then((ld) => ld.dismiss());
        })
      )
      .subscribe((resultData) => {
        if (!resultData) {
          this.router.navigate(['/']);

          this.toasterService
            .errorToast(
              'Error Loading Money Information',
              'Something went wrong while loading your balance information. Please try again in a few minutes.'
            )
            .subscribe();

          return;
        }
        const [reportData, scheduleData] = resultData as Record<string, any>[];
        this.initViewData(reportData, scheduleData);
      });
  }

  private initViewData(
    reportData: Record<string, any>,
    scheduleData?: Record<string, any>
  ) {
    this.Balance = {
      pending: reportData.balance.pending || 0,
      available: reportData.balance.available || 0,
      in_transit:
        reportData.balance.in_transit || reportData.balance.inTransit || 0,
      instant_available:
        reportData.balance.instant_available ||
        reportData.balance.instantAvailable ||
        0,
      LifetimeTotal:
        reportData.balance.LifetimeTotal ||
        reportData.balance.lifetimeTotal ||
        0,
    };

    this.PayoutSettings = reportData.payoutSettings;

    this.PayoutSettings.currentCount = scheduleData?.payoutCount ?? 0;
    this.PayoutSettings.maxCount = CA_MAX_MONTHLY_PAYOUTS_NUMBER;

    this.pendingTransactionList = [];
    this.TransactionList = [];

    reportData.processingTransactions.forEach((item) => {
      const itemDate = new Date(item.unixdate * 1000);
      this.pendingTransactionList.push({
        day: itemDate.getDate().toString(),
        month: MONTH_NAMES[itemDate.getMonth()],
        title: item.title ? item.title.replace('Bonus', 'Boost') : 'Payment',
        amount: item.amount,
        availableOn: item.availableOn,
      });
    });

    reportData.transactions.forEach((item) => {
      const itemDate = new Date(item.unixdate * 1000);
      let PayoutStatus = '';
      if (item.payoutStatus == 'paid') {
        PayoutStatus = 'ARRIVED';
      } else if (
        item.payoutStatus == 'pending' ||
        item.payoutStatus == 'in_transit'
      ) {
        PayoutStatus = 'IN-TRANSIT';
      } else {
        PayoutStatus = item.payoutStatus;
      }
      this.TransactionList.push({
        day: itemDate.getDate().toString(),
        month: MONTH_NAMES[itemDate.getMonth()],
        title: item.title ? item.title.replace('Bonus', 'Boost') : 'Payment',
        amount: item.amount,
        payoutStatus: PayoutStatus,
        payoutETA: '',
      });
    });
  }

  async presentLoading(message?) {
    const loading = await this.loadingController.create({ message: message });
    await loading.present();
    return loading;
  }

  GetAnchorString() {
    if (this.PayoutSettings.anchor.indexOf('-') > 0) {
      const biweeklyValues = this.PayoutSettings.anchor.split('-');
      const firstDayString =
        biweeklyValues[0] === '1' ? '1st' : biweeklyValues[0] + 'th';
      return `${firstDayString} & ${biweeklyValues[1]}th`;
    }
    if (parseInt(this.PayoutSettings.anchor) == 1) {
      return this.PayoutSettings.anchor + 'st';
    } else if (parseInt(this.PayoutSettings.anchor) <= 30) {
      return this.PayoutSettings.anchor + 'th';
    } else {
      return this.PayoutSettings.anchor;
    }
  }

  async OpenPayoutSettings() {
    if (this.showNewPayoutPage) {
      this.router.navigate(['payout']);
      return;
    }

    const modal = await this.modalController.create({
      component: PayoutSettingsComponent,
      componentProps: this.PayoutSettings,
      canDismiss: true,
      showBackdrop: true,
      backdropDismiss: true,
      cssClass: 'SmallPopupModal',
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    if (data != null && data.reload) {
      this.loadingDone = false;
      this.GetMoneyData(this.showNewPayoutPage);
    }
  }

  async OpenPayoutModal() {
    if (this.payoutModalOpen) {
      return;
    }

    this.payoutModalOpen = true;
    const modal = await this.modalController.create({
      component: ChoosePayoutComponent,
      cssClass: 'SmallPopupModal',
      componentProps: {
        level: this.PayoutSettings.level,
        available: this.convertNumberToDollar(this.Balance.available),
        instant_available: this.convertNumberToDollar(
          this.Balance.instant_available
        ),
        PayoutDest: this.PayoutSettings.PayoutDest,
      },
    });
    await modal.present();
    this.payoutModalOpen = false;

    const { data } = await modal.onWillDismiss();
    if (data != null && data.reload) {
      this.loadingDone = false;
      this.GetMoneyData(this.showNewPayoutPage);
    }
  }

  ShowAboutBalance() {
    this.presentAlert(
      'About Balances',
      '<b>Processing - </b>After you have completed an order, your earnings will be sent to your balance for processing. The average processing time for each transaction is 2 business days; however, some may take longer while others could take only a few minutes.<br><br><b>Available - </b>After a transaction finishes processing, it will be moved to your available balance where it will be sent to you on your next payout.<br><br><b>In Transit - </b>This balance shows all your payouts that have been sent out to your bank, but have not arrived yet.'
    );
  }

  async presentAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK'],
    });
    await alert.present();
  }

  PayoutVideoClick() {
    this.PlayVideo.open(VIDEOREFS.moneyPage.ref);
  }
}
