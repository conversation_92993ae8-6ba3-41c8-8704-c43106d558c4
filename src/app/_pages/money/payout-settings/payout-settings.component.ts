import { Component, Input, OnInit } from '@angular/core';
import {
  <PERSON>adingController,
  ModalController,
  PopoverController,
  AlertController,
} from '@ionic/angular';
import { Until<PERSON><PERSON>roy } from '@ngneat/until-destroy';
import { firstValueFrom } from 'rxjs';
import { ErrorHandlingService } from 'src/app/_services/error-handling/error-handling.service';
import { PayoutsetupComponent } from '../../start/payoutsetup/payoutsetup.component';
import { PayoutUpdateComponent } from '../payout-update/payout-update.component';
import { UpdatePayoutSettingsService } from '../../../_services/update-payout-settings/update-payout-settings.service';
import { UpdatePayoutSettingsData } from '../../../_interfaces/update-payout-settings.interface';

@UntilDestroy()
@Component({
  selector: 'app-payout-settings',
  templateUrl: './payout-settings.component.html',
  styleUrls: ['./payout-settings.component.scss'],
})
export class PayoutSettingsComponent implements OnInit {
  PayoutSchedule = 'weekly';
  PayoutWeekday = 'wednesday';
  PayoutMonthday = '1';
  payoutUpdateData: string;

  @Input() PayoutDest: string = '';
  @Input() PayoutsBlocked: boolean = false;
  @Input() anchor: string = '';
  @Input() level: number = 1;
  @Input() interval: string = '';

  constructor(
    private modalController: ModalController,
    private loadingController: LoadingController,
    private popoverController: PopoverController,
    private alertController: AlertController,
    private updatePayoutSettingsService: UpdatePayoutSettingsService,
    private errorHandlingService: ErrorHandlingService
  ) {}

  ngOnInit() {
    this.PayoutSchedule = this.interval;

    if (this.anchor == '' || this.anchor == null || this.anchor == 'manual') {
      this.PayoutWeekday = 'wednesday';
      this.PayoutMonthday = '1';
    } else {
      if (parseInt(this.anchor) > 0) {
        this.PayoutWeekday = 'wednesday';
        this.PayoutMonthday = this.anchor;
      } else {
        this.PayoutWeekday = this.anchor;
        this.PayoutMonthday = '1';
      }
    }
  }

  async UpdatePayoutMethod() {
    const modal = await this.modalController.create({
      component: PayoutsetupComponent,
      cssClass: 'SmallPopupModal',
      componentProps: { ShowPayoutOptions: true },
    });
    return await modal.present();
  }

  async authenticatePayoutUpdate() {
    const modal = await this.modalController.create({
      component: PayoutUpdateComponent,
      cssClass: 'SmallPopupModal',
    });
    await modal.present();
    await modal.onDidDismiss().then((data) => {
      this.payoutUpdateData = data.data.result;
      if (this.payoutUpdateData === 'Payout update initiated successfully') {
        this.UpdatePayoutMethod();
      }
    });
    return modal;
  }

  async payoutHandler(payoutUpdate: string) {
    if (payoutUpdate === 'Payout update initiated successfully') {
      this.UpdatePayoutMethod();
    } else {
      this.authenticatePayoutUpdate();
    }
  }

  async SaveSettings() {
    const loading = await this.presentLoading();
    try {
      const payload = {
        PayoutSchedule: this.PayoutSchedule,
        PayoutWeekday:
          this.PayoutSchedule === 'weekly' ? this.PayoutWeekday : undefined,
        PayoutMonthday:
          this.PayoutSchedule === 'monthly' ? this.PayoutMonthday : undefined,
      } as UpdatePayoutSettingsData;

      await firstValueFrom(
        this.updatePayoutSettingsService.updatePayoutSettings(payload)
      );
      await loading.dismiss();
      this.modalController.dismiss({ reload: true });
    } catch (err: any) {
      await loading.dismiss();
      this.errorHandlingService.handleError(err);
      this.presentAlert('Error', err.message);
    }
  }

  async presentLoading() {
    const loading = await this.loadingController.create({});
    await loading.present();
    return loading;
  }

  async presentAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK'],
    });
    return alert.present();
  }

  GetInitDate() {
    if (this.PayoutSchedule === 'weekly') {
      if (this.PayoutWeekday === 'monday') {
        return 'Sent Thursday. Arrives Monday.';
      } else if (this.PayoutWeekday === 'tuesday') {
        return 'Sent Friday. Arrives Tuesday.';
      } else if (this.PayoutWeekday === 'wednesday') {
        return 'Sent Monday. Arrives Wednesday.';
      } else if (this.PayoutWeekday === 'thursday') {
        return 'Sent Tuesday. Arrives Thursday.';
      } else if (this.PayoutWeekday === 'friday') {
        return 'Sent Wednesday. Arrives Friday.';
      }
    }
    return;
  }

  setPayoutSchedule(ev: string) {
    this.PayoutSchedule = ev;
    return this.PayoutSchedule;
  }
}
