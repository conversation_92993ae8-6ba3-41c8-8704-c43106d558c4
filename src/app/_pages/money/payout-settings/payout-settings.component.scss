ion-select {
  --padding-start: 0px;
  flex-basis: 200px;
  max-width: 200px;
}

ion-item {
  ion-label {
    flex-basis: 140px;
    margin-left: 23px;
    font-family: 'Fakt-Normal' sans-serif;
  }
}
.payout-schedule-label {
  margin-left: 39px;
  color: var(--themes-light-content, #000);

  /* Aux Text/S */
  font-family: 'PitchSans-Regular', 'Helvetica Neue', Helvetica, Arial;
  font-size: 13px;
  font-style: normal;
  font-weight: 700;
  line-height: 16px;
  letter-spacing: 1.3px;
  text-transform: uppercase;
  padding-top: 12px;
}
.payout-dest {
  margin-top: 8px;
  color: var(--themes-light-content, #000);

  /* Aux Text/S */
  font-family: 'PitchSans-Regular', sans-serif;
  font-size: 13px;
  font-style: normal;
  font-weight: 700;
  line-height: 16px;
  letter-spacing: 1.3px;
  text-transform: uppercase;
  margin-left: 39px;
}
ion-button.update-payout-btn {
  &::part(native) {
    /**Layout */
    color: black;
    display: flex;
    width: 285px;
    padding: 6px 4px;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    border: 1px solid var(--poplin-black, #000);
    text-align: center;
    margin-right: 8px;
    margin-bottom: 12px;
    /** Font Style*/
    font-family: 'PitchSans-Medium', 'Helvetica Neue', Helvetica, sans-serif;
    font-size: 13px;
    font-style: normal;
    font-weight: 700;
    line-height: 16px;
    letter-spacing: 1.3px;
    text-transform: uppercase;
  }
}
ion-button.save-btn {
  &::part(native) {
    /**Layout */
    display: flex;
    width: 285px;
    min-height: 48px;
    padding: var(--button-padding-vertical-default, 6px)
      var(--button-padding-horizontal-default, 8px);
    justify-content: center;
    align-items: center;
    margin: auto;
    bottom: 31px;
    /** Style */
    border-radius: var(--button-radius-radius-square, 8px);
    background: var(--button-color-primary-main, #ff6289);

    /* Light/Basic Drop Shadow */
    box-shadow: 0px 4px 24px 0px rgba(0, 0, 0, 0.04);

    /** Style */
    color: var(--button-color-primary-alt, #fff);
    text-align: center;

    /* Aux Text/M */
    font-family: 'PitchSans-Regular', sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 20px;
    letter-spacing: 1.6px;
    text-transform: uppercase;
  }
}
ion-note {
  margin-bottom: 28px;
}

/* ion-note styles */
.ion-note-custom {
  text-align: center;
  display: block;
  padding: 5px;
}
