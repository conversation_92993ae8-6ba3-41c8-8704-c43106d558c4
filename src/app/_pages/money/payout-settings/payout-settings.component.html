<ion-label class="payout-dest">{{ PayoutDest }}</ion-label>
<ion-button
  id="update-payout-button"
  class="update-payout-btn"
  (click)="payoutHandler(payoutUpdateData)"
  fill="outline"
  size="small"
  expand="block"
  >Update Payout Method
</ion-button>
<ion-label class="payout-schedule-label">Payout Schedule</ion-label>
<ion-item [disabled]="level < 2">
  <ion-label>Schedule:</ion-label>
  <ion-select
    (ionChange)="setPayoutSchedule($event.srcElement.value)"
    [value]="PayoutSchedule"
  >
    <ion-select-option value="weekly">Weekly</ion-select-option>
    <ion-select-option value="monthly">Monthly</ion-select-option>
    <ion-select-option value="manual" [disabled]="level < 3"
      >Manual/Instant</ion-select-option
    >
  </ion-select>
</ion-item>

<ion-item *ngIf="PayoutSchedule === 'weekly'" [disabled]="level < 2">
  <ion-label>Payout Day:</ion-label>
  <ion-select
    (ionChange)="PayoutWeekday = $event.srcElement.value"
    [value]="PayoutWeekday"
  >
    <ion-select-option value="monday">Monday</ion-select-option>
    <ion-select-option value="tuesday">Tuesday</ion-select-option>
    <ion-select-option value="wednesday">Wednesday</ion-select-option>
    <ion-select-option value="thursday">Thursday</ion-select-option>
    <ion-select-option value="friday">Friday</ion-select-option>
  </ion-select>
</ion-item>

<ion-item *ngIf="PayoutSchedule === 'monthly'" [disabled]="level < 2">
  <ion-label>Payout Day:</ion-label>
  <ion-select
    (ionChange)="PayoutMonthday = $event.srcElement.value"
    [value]="PayoutMonthday"
  >
    <ion-select-option value="1">1st of month</ion-select-option>
    <ion-select-option value="15">15th of month</ion-select-option>
    <ion-select-option value="28">28th of month</ion-select-option>
  </ion-select>
</ion-item>

<ion-note *ngIf="GetInitDate() !== null" class="ion-note-custom">
  {{ GetInitDate() }}
</ion-note>

<ion-button
  id="save-button"
  class="save-btn"
  (click)="SaveSettings()"
  expand="full"
>
  Save Settings
</ion-button>
