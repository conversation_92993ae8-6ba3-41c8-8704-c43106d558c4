import { Component, OnInit } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/auth';
import { AngularFirestore } from '@angular/fire/firestore';
import { AngularFireFunctions } from '@angular/fire/functions';
import {
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { AlertController, LoadingController } from '@ionic/angular';
import { SudsterData } from 'src/app/_interfaces/sudster-data.interface';
import { environment } from './../../../environments/environment';

import { AngularFireRemoteConfig } from '@angular/fire/remote-config';
import { AngularFireStorage } from '@angular/fire/storage';
import { Camera, CameraResultType } from '@capacitor/camera';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { AuthidService } from 'src/app/_services/authid.service';

declare const Stripe: any;
const stripe = Stripe(environment.Stripe);

@UntilDestroy()
@Component({
  selector: 'app-unverified',
  templateUrl: './unverified.page.html',
  styleUrls: ['./unverified.page.scss'],
})
export class UnverifiedPage implements OnInit {
  IDForm: UntypedFormGroup;
  dlFrontImg: SafeUrl = '';
  dlBackImg: SafeUrl = '';
  dlFront = '';
  dlBack = '';

  IDStatus = '';
  StripeAccountID = '';
  faceRecognition = '';
  standardVerification = '';
  userFaceVerified = false;
  verifying = false;
  faceVerificationPlaceholder = 'Biometric Face Verification';
  prevIDStatus = '';
  faceVerification = false;

  constructor(
    private fb: UntypedFormBuilder,
    private sanitizer: DomSanitizer,
    private cloudFunctions: AngularFireFunctions,
    public loadingController: LoadingController,
    public alertController: AlertController,
    public afAuth: AngularFireAuth,
    public firestore: AngularFirestore,
    private storage: AngularFireStorage,
    private AuthID: AuthidService,
    remoteConfig: AngularFireRemoteConfig
  ) {
    remoteConfig.booleans.FaceVerification.subscribe((value) => {
      this.faceVerification = value;
    });
  }

  UserID = this.AuthID.getID();

  ngOnInit() {
    this.IDForm = this.fb.group({
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      dob: ['', [Validators.required, Validators.minLength(8)]],
      ssn: ['', [Validators.required, Validators.minLength(9)]],
      gender: ['', Validators.required],
      addressLine1: ['', Validators.required],
      addressLine2: '',
      city: ['', Validators.required],
      state: ['', Validators.required],
      zipcode: ['', Validators.required],
    });

    this.firestore
      .doc<SudsterData>(`Sudsters/${this.UserID}`)
      .valueChanges()
      .pipe(untilDestroyed(this))
      .subscribe((doc) => {
        this.IDStatus = doc.IDStatus;
        this.StripeAccountID = doc.StripeAccountID;
        this.faceRecognition = doc.faceRecognition;
        this.standardVerification = doc.standardVerification;
      });
  }

  async takePicture(front) {
    const image = await Camera.getPhoto({
      quality: 80,
      allowEditing: true,
      resultType: CameraResultType.DataUrl,
      correctOrientation: true,
      saveToGallery: false,
      height: 400,
      width: 550,
    });
    // image.webPath will contain a path that can be set as an image src.
    // You can access the original file using image.path, which can be
    // passed to the Filesystem API to read the raw data of the image,
    // if desired (or pass resultType: CameraResultType.Base64 to getPhoto)
    if (front) {
      this.dlFrontImg = this.sanitizer.bypassSecurityTrustUrl(image.dataUrl);
      this.dlFront = image.dataUrl;
    } else {
      this.dlBackImg = this.sanitizer.bypassSecurityTrustUrl(image.dataUrl);
      this.dlBack = image.dataUrl;
    }
  }

  Submit() {
    if (this.standardVerification === 'verified' && this.faceVerification) {
      // We only need to verify biometric, so we just wait for it to complete
      this.IDStatus = 'pending';
    } else {
      const loading = this.presentLoading();
      this.cloudFunctions
        .httpsCallable('SudsterSubmitAdditonalID')({
          ...this.IDForm.value,
          dlBack: this.dlBack,
          dlFront: this.dlFront,
        })
        .toPromise()
        .then((res) => {
          this.storage
            .ref(`SudsterIDs/${this.UserID}/FrontID.jpeg`)
            .putString(this.dlFront, 'data_url')
            .then(() => {
              return this.storage
                .ref(`SudsterIDs/${this.UserID}/BackID.jpeg`)
                .putString(this.dlBack, 'data_url');
            })
            .then(() => {
              return this.storage
                .ref(`SudsterIDs/${this.UserID}/IdentityNumber.txt`)
                .putString(this.IDForm.value.ssn);
            })
            .then(() => {
              loading.then(function (ld) {
                ld.dismiss();
              });
              this.IDStatus = 'pending';
            })
            .catch((err) => {
              loading.then(function (ld) {
                ld.dismiss();
              });
              this.presentAlert('Error', err.message);
            });
        })
        .catch((err) => {
          loading.then(function (ld) {
            ld.dismiss();
          });
          this.presentAlert('Error', err);
        });
    }
  }

  RefreshVerification() {
    const loading = this.presentLoading();
    this.cloudFunctions
      .httpsCallable('SudsterV3_RefreshStripeVerification')({
        StripeAccountID: this.StripeAccountID,
      })
      .toPromise()
      .then((res) => {
        loading.then(function (ld) {
          ld.dismiss();
        });
      })
      .catch((err) => {
        loading.then(function (ld) {
          ld.dismiss();
        });
        this.presentAlert('Error', err.message);
      });
  }

  async presentLoading() {
    const loading = await this.loadingController.create({});
    await loading.present();
    return loading;
  }

  async presentAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK'],
    });
    await alert.present();
  }

  verifyIdentity() {
    this.verifying = true;
    this.cloudFunctions
      .httpsCallable('SudsterV3_VerifyID')({})
      .toPromise()
      .then((res) => {
        if (this.standardVerification === 'verified') {
          setTimeout(() => {
            this.prevIDStatus = this.IDStatus;
            this.firestore.doc<SudsterData>(`Sudsters/${this.UserID}`).update({
              IDStatus: 'pending',
            });
          }, 2000);
        } else {
          this.userFaceVerified = true;
        }
        stripe.verifyIdentity(res).then((res) => {
          const error = res.error;
          if (!error) {
            this.firestore
              .doc<SudsterData>(`Sudsters/${this.UserID}`)
              .valueChanges()
              .pipe(untilDestroyed(this))
              .subscribe((doc) => {
                const newFaceVerification = doc.faceRecognition;
                if (newFaceVerification === 'verified') {
                  this.faceVerificationPlaceholder = 'Complete';
                  this.userFaceVerified = true;
                }
              });
          }
          if (error && error.code) {
            if (error.code === 'session_cancelled') {
              this.firestore
                .doc<SudsterData>(`Sudsters/${this.UserID}`)
                .update({
                  IDStatus: this.prevIDStatus,
                });
            } else {
              this.presentAlert('Error', error.code);
            }
            this.userFaceVerified = false;
          }
          this.verifying = false;
        });
      });
  }
}
