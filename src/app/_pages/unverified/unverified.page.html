<ion-content>
  <i id="MainIcon" class="material-icons"> security </i>

  <div *ngIf="IDStatus === 'verified'">
    <h1>ID Verified</h1>

    <ion-button
      id="go-to-app-button"
      size="large"
      expand="block"
      [routerLink]="['/home']"
      >Go To App</ion-button
    >
  </div>

  <div *ngIf="IDStatus === 'pending'">
    <h1>ID Pending</h1>
    <p class="maxwidth">
      Thank you. Your ID verification is pending. You will receive a
      notification once it has finished processing. If you still see this after
      1 hour, please
      <a
        (click)="RefreshVerification()"
        style="color: inherit; text-decoration: underline"
        >tap here to refresh.</a
      >
    </p>
  </div>

  <div *ngIf="IDStatus === 'unverified'">
    <h1>ID Verification</h1>
    <p class="maxwidth">
      We could not verify your ID with the information you provided. Please
      enter your accurate information below and try again. Information provided
      must match your driver's license and will be used to verify your identity,
      which is required to process payouts. All information provided is secure
      and confidential. Your privacy is important to us.
    </p>

    <form [formGroup]="IDForm" style="margin-top: 20px">
      <div *ngIf="standardVerification !== 'verified'">
        <ion-segment class="InputSegment" mode="ios" formControlName="gender">
          <ion-segment-button mode="ios" value="male" id="select-male-button">
            <ion-label>Male</ion-label>
          </ion-segment-button>
          <ion-segment-button
            mode="ios"
            value="female"
            id="select-female-button"
          >
            <ion-label>Female</ion-label>
          </ion-segment-button>
        </ion-segment>

        <div class="InputDiv" id="first-name-div">
          <i class="material-icons">account_circle</i>
          <input
            id="first-name-input"
            type="text"
            placeholder="Full Legal First Name"
            formControlName="firstName"
          />
        </div>

        <div class="InputDiv" id="last-name-div">
          <i class="material-icons">people</i>
          <input
            id="last-name-input"
            type="text"
            placeholder="Full Legal Last Name"
            formControlName="lastName"
          />
        </div>

        <div class="InputDiv" id="dob-div">
          <i class="material-icons">date_range</i>
          <input
            id="dob-input"
            type="text"
            [dropSpecialCharacters]="false"
            placeholder="Date of Birth (mm/dd/yyyy)"
            mask="00/00/0000"
            formControlName="dob"
          />
        </div>

        <div class="InputDiv" id="ssn-div">
          <i class="material-icons">lock</i>
          <input
            id="ssn-input"
            type="text"
            placeholder="Social Security Number"
            mask="***********"
            formControlName="ssn"
          />
        </div>

        <div class="InputDiv" id="address-div">
          <i class="material-icons">home</i>
          <input
            id="address-line-1-input"
            formControlName="addressLine1"
            type="text"
            placeholder="Address Line 1"
          />
          <input
            id="address-line-2-input"
            formControlName="addressLine2"
            type="text"
            placeholder="Address Line 2"
          />
          <input
            id="city-input"
            formControlName="city"
            type="text"
            placeholder="City"
          />
          <input
            id="state-input"
            formControlName="state"
            type="text"
            placeholder="State"
          />
          <input
            id="zipcode-input"
            formControlName="zipcode"
            type="text"
            placeholder="Zipcode"
          />
        </div>

        <div class="InputDiv" id="drivers-license-front-div">
          <i class="material-icons">directions_car</i>
          <img
            *ngIf="dlFrontImg !== ''"
            (click)="takePicture(true)"
            [src]="dlFrontImg"
          />
          <input
            id="dl-front-input"
            [hidden]="dlFront !== ''"
            type="text"
            [value]="dlFront"
            (click)="takePicture(true)"
            placeholder="Driver's License (Front)"
          />
        </div>

        <div class="InputDiv" id="drivers-license-back-div">
          <i class="material-icons">directions_car</i>
          <img
            *ngIf="dlBackImg !== ''"
            (click)="takePicture(false)"
            [src]="dlBackImg"
          />
          <input
            id="dl-back-input"
            [hidden]="dlBack !== ''"
            type="text"
            [value]="dlBack"
            (click)="takePicture(false)"
            placeholder="Driver's License (Back)"
          />
        </div>
      </div>

      <div class="InputDiv" *ngIf="faceVerification" id="face-verification-div">
        <i class="material-icons">perm_identity</i>
        <input
          id="face-verification-input"
          *ngIf="!verifying"
          [disabled]="userFaceVerified || verifying"
          type="text"
          (click)="verifyIdentity()"
          placeholder="{{faceVerificationPlaceholder}}"
        />
        <ion-spinner *ngIf="verifying" name="crescent"></ion-spinner>
      </div>

      <ion-button
        id="next-button"
        *ngIf="!standardVerification && faceVerification"
        (click)="Submit()"
        [disabled]="IDForm.invalid || dlBackImg === '' || dlFrontImg === '' || !userFaceVerified"
        expand="block"
      >
        Next <ion-icon name="arrow-forward"> </ion-icon>
      </ion-button>
      <ion-button
        id="submit-button"
        *ngIf="standardVerification && faceVerification"
        (click)="Submit()"
        [disabled]="!userFaceVerified"
        expand="block"
      >
        Next <ion-icon name="arrow-forward"> </ion-icon>
      </ion-button>

      <ion-button
        id="submit-button"
        *ngIf="!faceVerification"
        (click)="Submit()"
        [disabled]="IDForm.invalid || dlBackImg === '' || dlFrontImg === ''"
        expand="block"
      >
        Next <ion-icon name="arrow-forward"> </ion-icon>
      </ion-button>
    </form>
  </div>
  <br />
  <ion-button
    id="help-button"
    [routerLink]="['/help']"
    size="small"
    fill="clear"
    expand="block"
    >Help</ion-button
  >
  <br />
</ion-content>
