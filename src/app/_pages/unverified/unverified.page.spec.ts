import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { UnverifiedPage } from './unverified.page';

describe('UnverifiedPage', () => {
  let component: UnverifiedPage;
  let fixture: ComponentFixture<UnverifiedPage>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [UnverifiedPage],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(UnverifiedPage);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
