import { ContactPayload } from '@capacitor-community/contacts';

const clearNumberRegex = /[+-\s]/gi;

export async function getContactHash(contacts: ContactPayload[]) {
  const nHash = new Set<string>();

  for (const contact of contacts) {
    for (const n of contact.phones) {
      let number = n.number.replace(clearNumberRegex, '');
      // hash for only last 10 numbers,
      number =
        number.length > 10 ? number.substring(number.length - 10) : number;
      const hash = await createHash(n.number);
      nHash.add(hash);
    }
  }

  return nHash;
}

// https://developer.mozilla.org/es/docs/Web/API/SubtleCrypto/digest
async function createHash(message): Promise<string> {
  const msgUint8 = new TextEncoder().encode(message); // encode as (utf-8) Uint8Array
  const hashBuffer = await crypto.subtle.digest('SHA-256', msgUint8); // hash the message
  const hashArray = Array.from(new Uint8Array(hashBuffer)); // convert buffer to byte array
  const hashHex = hashArray
    .map((b) => b.toString(16).padStart(2, '0'))
    .join(''); // convert bytes to hex string

  return hashHex;
}
