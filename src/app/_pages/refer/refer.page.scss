:host {
  .mock-list-wrapper {
    height: 90%;
    margin: 0 5%;
    position: relative;

    ion-button {
      position: absolute;
      top: 23px;
      left: 0;
      right: 0;
      width: 180px;
      --ion-color-base: var(--ion-color-medium-shade) !important;
    }
  }

  .mock-list {
    background-image: url(/assets/img/mock_item.png);
    width: 100%;
    height: 100%;
    background-size: contain;
  }

  .btn-share {
    width: 100%;
    padding: 0 20px;
  }
}
.font-override {
  font-size: 1.2em;
}
