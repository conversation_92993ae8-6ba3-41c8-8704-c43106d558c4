<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button
        defaultHref="home"
        text=""
        icon="arrow-back"
        color="black"
      ></ion-back-button>
    </ion-buttons>
    <ion-title class="ion-text-wrap font-override"
      >Share with your Friends</ion-title
    >
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-item>
    <p style="font-size: 18px; text-align: center; width: 100%">
      Share this app with 2 friends<br />
      and get +30 points instantly.
    </p>
  </ion-item>

  <ng-container *ngIf="!requestPermission; else showContacts">
    <div class="mock-list-wrapper">
      <ion-button color="medium" (click)="allowContacts()"
        >ALLOW CONTACTS</ion-button
      >
    </div>
  </ng-container>
</ion-content>

<ion-footer *ngIf="requestPermission" class="ion-no-padding ion-no-margin">
  <ion-button
    class="btn-share"
    color="primary"
    [disabled]="!shareBtnEnabled || sharingApp"
    (click)="sharingApp = true; shareContacts()"
    >SHARE
    <ion-spinner *ngIf="sharingApp" name="crescent"></ion-spinner>
  </ion-button>
</ion-footer>

<ng-template #showContacts>
  <ion-list>
    <ion-item *ngFor="let contact of contacts">
      <ion-checkbox
        slot="start"
        [(ngModel)]="contact.isChecked"
        (ionChange)="onCheckboxChange(contact)"
      >
      </ion-checkbox>
      <ion-label
        >{{ contact.displayName || contact.phoneNumbers[0].number }}</ion-label
      >
    </ion-item>
  </ion-list>
</ng-template>
