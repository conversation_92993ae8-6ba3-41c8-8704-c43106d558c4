import { Component, OnInit } from '@angular/core';
import { AngularFirestore } from '@angular/fire/firestore';
import { AngularFireFunctions } from '@angular/fire/functions';
import { SMS } from '@awesome-cordova-plugins/sms/ngx';
import { ContactPayload, Contacts } from '@capacitor-community/contacts';
import { Alert<PERSON>ontroller, NavController } from '@ionic/angular';
import { AuthidService } from 'src/app/_services/authid.service';

@Component({
  selector: 'app-refer',
  templateUrl: './refer.page.html',
  styleUrls: ['./refer.page.scss'],
})
export class ReferPage implements OnInit {
  mocks = [1, 2, 3, 4, 5, 6, 7, 8, 9];
  contacts: ContactPayload[] = [];
  requestPermission = false;

  sudsterId = this.AuthID.getID();
  shareBtnEnabled: boolean;
  selectedContacts: Set<string> = new Set();
  sharingApp = false;

  private clearNumberRegex = /[+-\s]/gi;

  constructor(
    private sms: SMS,
    public firestore: AngularFirestore,
    private AuthID: AuthidService,
    private alertController: AlertController,
    private navCtrl: NavController,
    private cloudFunctions: AngularFireFunctions
  ) {}

  ngOnInit(): void {}

  allowContacts() {
    this.loadContacts();
  }

  onCheckboxChange(contact: ContactPayload) {
    if (contact['isChecked']) {
      contact.phones.forEach((n) => this.selectedContacts.add(n.number));
    } else {
      contact.phones.forEach((n) => this.selectedContacts.delete(n.number));
    }

    this.shareBtnEnabled = this.selectedContacts.size >= 2;
  }

  async shareContacts() {
    const options = {
      replaceLineBreaks: false,
      android: {
        intent: 'INTENT',
      },
    };

    const contacts = Array.from(this.selectedContacts) as [];
    const nHash = new Set<string>();

    // TODO: may improve speed generating hashes just after loaded.
    for (const contact of this.contacts) {
      for (const n of contact.phones) {
        let number = n.number.replace(this.clearNumberRegex, '');
        // hash for only last 10 numbers,
        number =
          number.length > 10 ? number.substring(number.length - 10) : number;
        const hash = await this.createHash(n.number);
        nHash.add(hash);
      }
    }

    this.cloudFunctions
      .httpsCallable('SudsterV3_ReferSudsterApp')({ hashes: Array.from(nHash) })
      .subscribe(
        (res) => {
          this.sms.send(
            contacts,
            "Hey! Check out this cool app I just signed up for! You can work from home doing laundry. The best part is I'm my own boss! https://poplin.co/join-the-team",
            options
          );
          this.sharingApp = false;

          const nav = this.navCtrl;
          const btns = [
            {
              text: 'OK',
              role: 'confirm-button',
              handler: () => {
                nav.pop();
              },
            },
          ];
          this.showAlert(
            '+30 Points granted',
            'Keep the good work and lets go to the top!',
            btns
          );
        },
        (err) => {
          this.sharingApp = false;
          this.showAlert(
            'Internal Error: ',
            err.message ||
              'Please try again in a few minutes or contact support if the problem persist'
          );
        }
      );
  }

  private async loadContacts() {
    Contacts.requestPermissions()
      .then(
        async (res) => {
          this.requestPermission = true;
          // some phones or impl returns res.contacts = 'granted'
          if (res.contacts === 'granted') {
            const result = await Contacts.getContacts({
              projection: { phones: true },
            });
            this.contacts = result.contacts.filter((c) => c.phones.length > 0);
          } else {
            this.showContactErrors(res);
          }
        },
        (err) => {
          this.showContactErrors(err);
        }
      )
      .catch((err) => {
        this.showContactErrors(err);
      });
  }

  private async showContactErrors(err) {
    const nav = this.navCtrl;
    const btns = [
      {
        text: 'OK',
        role: 'confirm-button',
        handler: () => {
          nav.pop();
        },
      },
    ];

    return this.showAlert(
      "Can't share app",
      'We are unable to display you contacts. Please allow contact access in your phone settings.',
      btns
    );
  }

  // https://developer.mozilla.org/es/docs/Web/API/SubtleCrypto/digest
  private async createHash(message): Promise<string> {
    const msgUint8 = new TextEncoder().encode(message); // encode as (utf-8) Uint8Array
    const hashBuffer = await crypto.subtle.digest('SHA-256', msgUint8); // hash the message
    const hashArray = Array.from(new Uint8Array(hashBuffer)); // convert buffer to byte array
    const hashHex = hashArray
      .map((b) => b.toString(16).padStart(2, '0'))
      .join(''); // convert bytes to hex string
    return hashHex;
  }

  private async showAlert(title: string, msg: string, btns: any = ['OK']) {
    const alert = await this.alertController.create({
      header: title,
      message: msg,
      buttons: btns,
    });

    return alert.present();
  }
}
