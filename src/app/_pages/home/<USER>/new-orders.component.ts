import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { AngularFireAuth } from '@angular/fire/auth';
import { AngularFirestore } from '@angular/fire/firestore';
import { App } from '@capacitor/app';
import { PluginListenerHandle } from '@capacitor/core';
import {
  AlertController,
  LoadingController,
  ModalController,
  Platform,
} from '@ionic/angular';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Subscription } from 'rxjs';
import {
  AvailableOrder,
  AvailableOrderData,
  NewOrders,
  PreferredPickup,
} from 'src/app/_interfaces/available-orders-interfaces';
import { SudsterData } from 'src/app/_interfaces/sudster-data.interface';
import { AnalyticsLogService } from 'src/app/_services/analytics/analytics-log.service';
import { AuthidService } from 'src/app/_services/authid.service';
import { GetSudsterDataService } from 'src/app/_services/get-sudster-data.service';
import { OrderExtensionService } from 'src/app/_services/order-extension.service';
import { StatsigFactoryService } from 'src/app/_services/statsig-factory.service';
import { StatsigService } from 'src/app/_services/statsig.service';
import { trackEvent } from 'src/app/_utils/track-event';
import { GetNewOrdersService } from 'src/app/service/new-order-listener';
import { environment } from 'src/environments/environment';
import { Detergent } from '../current-orders/adapters/active-order.adapter';
import { DeclineOrderModalComponent } from '../decline-order-modal/decline-order-modal.component';
import { GreetingComponent } from '../greeting/greeting.component';
import { OrderContractModalComponent } from '../order-contract-modal/order-contract-modal.component';
import { MoreInfoAlert } from './no-orders-info';

declare let google;

interface responseJson {
  errors: string[];
}

export const calcBreakpoint = (minValue: number): number => {
  const value = Math.ceil((minValue / window.innerHeight) * 20) / 20;
  return Math.min(value, 1);
};
export const breakpoints = [0, 0.75, 0.8, 0.85, 0.9, 1];

const LOCALSTORAGE_KEY_UNIQUE_TRACKEVENTS = 'UniqueEvents';
@UntilDestroy()
@Component({
  selector: 'app-new-orders',
  templateUrl: './new-orders.component.html',
  styleUrls: ['./new-orders.component.scss'],
})
export class NewOrdersComponent implements OnInit, OnDestroy {
  NewOrders: NewOrders[] = [];
  FullOrderDataArray = [];
  OutsideAreaCounter = 0;
  SudsterFirstOrder = false;
  LoadingOrders = true;
  SameDayService: boolean;
  flexibleExtension = false; // flips to true if preferred sudster want's a extension
  UserID = this.AuthID.getID();
  DistanceService: any;
  preAcceptanceFlag: boolean;
  Sudster: SudsterData;
  isExpressServiceEnabled = false;
  private currentOrdersSubscriptions: Subscription;
  private stateListener: PluginListenerHandle;
  private statsigService: StatsigService;
  breakpoints = breakpoints;
  initialBreakpoint = 0.75;

  @ViewChild('ScrollDiv') private ScrollDiv;
  @Output() AvailableCount = new EventEmitter();
  @Output() GoToActive = new EventEmitter();
  @Input() AvailableCountNum: number;
  @Input() IdVerification: string;
  @Input() CheckrVerification: string;

  constructor(
    public firestore: AngularFirestore,
    public afAuth: AngularFireAuth,
    private GetNewOrders: GetNewOrdersService,
    private modalController: ModalController,
    private SudsterDocService: GetSudsterDataService,
    private alertController: AlertController,
    private AuthID: AuthidService,
    public platform: Platform,
    private loadingController: LoadingController,
    private statsigFactoryService: StatsigFactoryService,
    private logService: AnalyticsLogService,
    private orderExtensionService: OrderExtensionService
  ) {
    this.DistanceService = new google.maps.DistanceMatrixService();
    this.stateListener = App.addListener('appStateChange', (data) => {
      // state.isActive contains the active state
      if (data.isActive) {
        this.LoadOrderList();
      } else if (this.currentOrdersSubscriptions) {
        this.currentOrdersSubscriptions.unsubscribe();
      }
    });
    this.statsigService = this.statsigFactoryService.getInstance();
  }

  ngOnInit() {
    this.statsigService
      .checkGate(environment.statsig.flags.ExpressService)
      .pipe(untilDestroyed(this))
      .subscribe((expressservice) => {
        this.isExpressServiceEnabled = expressservice;
      });

    this.LoadOrderList();
  }

  ngOnDestroy(): void {
    if (this.stateListener) {
      this.stateListener.remove();
    }
  }

  ScrollRight() {
    this.ScrollDiv.nativeElement.scrollLeft += 50;
  }

  async RefreshOrderList(event) {
    const StartTime = new Date().getTime();

    await this.handleOrderLoading();

    if (event != null && event?.target) {
      let waitTime = 0;
      if (new Date().getTime() - 1000 < StartTime) {
        waitTime = 1000;
      }
      setTimeout(() => {
        if (typeof event.target?.complete === 'function') {
          event.target.complete();
        }
      }, waitTime);
    }
  }

  private async handleOrderLoading() {
    this.NewOrders = [];
    this.LoadingOrders = true;

    if (!this.currentOrdersSubscriptions?.closed) {
      this.currentOrdersSubscriptions?.unsubscribe();
    }

    this.currentOrdersSubscriptions = this.GetNewOrders.currentOrders
      .pipe(untilDestroyed(this))
      .subscribe(
        (data: { AvailableOrders: AvailableOrder[]; Sudster: SudsterData }) => {
          if (!data.AvailableOrders || !data.AvailableOrders.length) {
            this.LoadingOrders = false;
            this.NewOrders = [];
            this.AvailableCount.emit(this.NewOrders.length);
            return;
          }
          const AvailableOrders = data.AvailableOrders;
          this.Sudster = data.Sudster;

          if (this.Sudster != null) {
            const SudsterWorkRadius = this.Sudster.WorkRadius || 20;
            this.SudsterFirstOrder = (this.Sudster.OrderCount || 0) == 0;

            this.OutsideAreaCounter = 0;
            this.NewOrders = [];
            this.FullOrderDataArray = [];
            AvailableOrders.forEach(async (doc: AvailableOrder) => {
              const order = doc.Order as AvailableOrderData;
              const priceBoost =
                order.PriceSchedule?.ExpressRate > 2 ||
                order.PriceSchedule?.StandardRate > 1 ||
                false;
              const pricePerPound =
                order.Delivery === 'SameDay'
                  ? order.PriceSchedule?.ExpressRate || 2
                  : order.PriceSchedule?.StandardRate || 1;

              const sudsterRate = pricePerPound * 0.75;

              if (
                getDistanceInMi(
                  this.Sudster.WorkAreaCenter.latitude,
                  this.Sudster.WorkAreaCenter.longitude,
                  order.Address.Lat,
                  order.Address.Long
                ) <= SudsterWorkRadius
              ) {
                this.FullOrderDataArray.push(doc);
                this.NewOrders.push({
                  UserID: order.UserID,
                  OrderNumber: doc.OrderNumber.startsWith('S')
                    ? `#${doc.OrderNumber}`
                    : `#${order.OrderId}`,
                  Name: `${order.CustomerFirstName} ${
                    order.CustomerLastName ? order.CustomerLastName : ''
                  }`,
                  PrivateOrderNumber: !doc.OrderNumber.startsWith('S')
                    ? doc.OrderNumber
                    : '',
                  City: order.Address.City,
                  BagCount: order.BagCount,
                  BagSize: order.BagSize,
                  Earnings: order.EstimatedEarnings,
                  Bonus: order.Bonus,
                  BonusPoints: order.BonusPoints,
                  BonusPointsMultiplier: order.BonusPointsMultiplier,
                  LocationName: `${order.Address.City}, ${order.Address.State} ${order.Address.ZipCode}`,
                  FirstOrder: order.FirstOrder,
                  PreferredPickup: order.Pickup,
                  Preferred: doc.PrioritySudsters.includes(this.UserID),
                  FromDualReferral: doc.ReferralSudster === this.UserID,
                  SameDayService: order.Delivery === 'SameDay',
                  Detergent: order.Detergent,
                  PriceBoost: priceBoost,
                  SudsterRate: sudsterRate,
                });
              } else {
                this.OutsideAreaCounter++;
              }
            });
          }
          this.AvailableCount.emit(this.NewOrders.length);
          this.LoadingOrders = false;
        }
      );

    await this.GetNewOrders.LoadOrderList();

    function getDistanceInMi(lat1, lon1, lat2, lon2) {
      function deg2rad(deg) {
        return deg * (Math.PI / 180);
      }
      const R = 6371; // Radius of the earth in km
      const dLat = deg2rad(lat2 - lat1); // deg2rad below
      const dLon = deg2rad(lon2 - lon1);
      const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(deg2rad(lat1)) *
          Math.cos(deg2rad(lat2)) *
          Math.sin(dLon / 2) *
          Math.sin(dLon / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      const d = R * c; // Distance in km
      const dm = d * 0.62; // Distance in miles
      return dm;
    }
  }

  async LoadOrderList() {
    await this.handleOrderLoading();
  }

  GetAreaName(Address) {
    if (typeof Address === 'object') {
      return `${Address.City}, ${Address.State} ${Address.ZipCode}`;
    } else if (Address.split(', ').length < 2) {
      return Address.split(' ').splice(-3).toString().replaceAll(',', ', ');
    } else {
      return `${Address.split(', ')[1]}, ${this.abbrState(
        Address.split(', ')[2]
      )}`;
    }
  }

  abbrState(input) {
    if (input == null) {
      return '';
    }

    const Zip = (input.match(/\d+/) || [''])[0];
    input = input.replace(/[0-9]/g, '').trim();

    const states = [
      ['Arizona', 'AZ'],
      ['Alabama', 'AL'],
      ['Alaska', 'AK'],
      ['Arkansas', 'AR'],
      ['California', 'CA'],
      ['Colorado', 'CO'],
      ['Connecticut', 'CT'],
      ['Delaware', 'DE'],
      ['Florida', 'FL'],
      ['Georgia', 'GA'],
      ['Hawaii', 'HI'],
      ['Idaho', 'ID'],
      ['Illinois', 'IL'],
      ['Indiana', 'IN'],
      ['Iowa', 'IA'],
      ['Kansas', 'KS'],
      ['Kentucky', 'KY'],
      ['Louisiana', 'LA'],
      ['Maine', 'ME'],
      ['Maryland', 'MD'],
      ['Massachusetts', 'MA'],
      ['Michigan', 'MI'],
      ['Minnesota', 'MN'],
      ['Mississippi', 'MS'],
      ['Missouri', 'MO'],
      ['Montana', 'MT'],
      ['Nebraska', 'NE'],
      ['Nevada', 'NV'],
      ['New Hampshire', 'NH'],
      ['New Jersey', 'NJ'],
      ['New Mexico', 'NM'],
      ['New York', 'NY'],
      ['North Carolina', 'NC'],
      ['North Dakota', 'ND'],
      ['Ohio', 'OH'],
      ['Oklahoma', 'OK'],
      ['Oregon', 'OR'],
      ['Pennsylvania', 'PA'],
      ['Rhode Island', 'RI'],
      ['South Carolina', 'SC'],
      ['South Dakota', 'SD'],
      ['Tennessee', 'TN'],
      ['Texas', 'TX'],
      ['Utah', 'UT'],
      ['Vermont', 'VT'],
      ['Virginia', 'VA'],
      ['Washington', 'WA'],
      ['West Virginia', 'WV'],
      ['Wisconsin', 'WI'],
      ['Wyoming', 'WY'],
    ];

    let returnValue = input;
    for (let i = 0; i < states.length; i++) {
      if (states[i][0] == input) {
        returnValue = states[i][1];
      }
    }

    return returnValue + ' ' + Zip;
  }

  round(num, decimalPlaces) {
    const p = Math.pow(10, decimalPlaces);
    const e = Number.EPSILON * num * p;
    return Math.round(num * p + e) / p;
  }

  async OpenOrderContract(orderIndex) {
    const orderItem = this.FullOrderDataArray[orderIndex]
      .Order as AvailableOrderData;
    orderItem.OrderNumber = this.FullOrderDataArray[orderIndex].OrderNumber;
    const pricePerPound =
      orderItem.Delivery === 'SameDay'
        ? orderItem.PriceSchedule?.ExpressRate || 2
        : orderItem.PriceSchedule?.StandardRate || 1;
    const sudsterRate = pricePerPound * 0.75;

    let isOverweightOrder = false;
    await this.statsigService
      .checkGate(environment.statsig.flags.showOrderDataPreacceptance)
      .pipe(untilDestroyed(this))
      .subscribe((val) => {
        this.preAcceptanceFlag = val;

        this.orderExtensionService
          .canExtendAvailableOrder(
            orderItem.BagSize,
            orderItem.BagCount,
            orderItem.Delivery == 'SameDay'
          )
          .then((val) => {
            isOverweightOrder = val;
          });
      });

    let minHeight = 420;
    if (
      orderItem.Delivery === 'SameDay' &&
      (orderItem.Detergent === Detergent.Hypoallergenic ||
        orderItem.Detergent === Detergent.UnscentedHypoallergenic)
    ) {
      minHeight = 570;
    } else if (
      this.SudsterFirstOrder &&
      (orderItem.Detergent === Detergent.Hypoallergenic ||
        orderItem.Detergent === Detergent.UnscentedHypoallergenic)
    ) {
      minHeight = 650;
    } else if (this.SudsterFirstOrder) {
      minHeight = 570;
    } else if (
      orderItem.Delivery === 'SameDay' ||
      orderItem.Detergent === Detergent.Hypoallergenic ||
      orderItem.Detergent === Detergent.UnscentedHypoallergenic
    ) {
      minHeight = 490;
    }
    const min = minHeight / this.platform.height();
    let minHeightBP = min;
    let breakpoints = [0, minHeightBP, 0.65];
    if (this.preAcceptanceFlag) {
      minHeightBP = this.updateModalBreakpoints(
        orderItem,
        min,
        isOverweightOrder
      );
      breakpoints = [0, minHeightBP, 1];
    }
    const modal = await this.modalController.create({
      component: OrderContractModalComponent,
      breakpoints: breakpoints,
      initialBreakpoint: minHeightBP,
      handle: false,
      showBackdrop: true,
      componentProps: {
        order: orderItem,
        SudsterFirstOrder: this.SudsterFirstOrder,
        flexibleExtension: this.flexibleExtension,
        IdVerification: this.IdVerification,
        CheckrVerification: this.CheckrVerification,
        sudsterRate: sudsterRate,
      },
    });
    await modal.present();

    this.trackOnceOrderView(orderItem, this.UserID);

    this.logService.logOrderViewedEvent(
      this.UserID,
      this.Sudster.ContactEmail,
      this.Sudster.Phone,
      orderItem.OrderNumber,
      this.preAcceptanceFlag
    );
    const { data } = await modal.onWillDismiss();

    if (data != null) {
      this.AvailableCount.emit(this.AvailableCountNum - 1);
      const SudsterDoc = await this.SudsterDocService.getOnce();
      const modal2 = await this.modalController.create({
        component: GreetingComponent,
        cssClass: 'PopupModal',
        componentProps: {
          CustomerFirstName: data.CustomerFirstName,
          SudsterFirstName: SudsterDoc.FirstName,
          OrderNumber: orderItem.OrderNumber,
          OrderTimezone: orderItem.Timezone,
        },
        backdropDismiss: false,
      });
      await modal2.present();
      await modal2.onDidDismiss();
      this.GoToActive.emit();
    }
  }

  trackOnceOrderView(orderItem: AvailableOrderData, sudsterId: string) {
    const LPOrderViewedKeyName = 'LPOrderViewed';
    const resultArrayStr = localStorage.getItem(
      LOCALSTORAGE_KEY_UNIQUE_TRACKEVENTS
    );
    let uniqueEventsArr = [];
    if (resultArrayStr) {
      uniqueEventsArr = JSON.parse(resultArrayStr);
    }
    const alreadyTracked = uniqueEventsArr.find(
      (entry) =>
        entry.id === orderItem.OrderNumber &&
        entry.evtName === LPOrderViewedKeyName
    );
    if (alreadyTracked) {
      return;
    }

    trackEvent({
      eventData: {
        event: LPOrderViewedKeyName,
        userId: sudsterId,
        orderId: orderItem.OrderNumber,
      },
    });

    const trackEntry = {
      id: orderItem.OrderNumber,
      time: new Date().getTime(),
      evtName: LPOrderViewedKeyName,
    };
    uniqueEventsArr.push(trackEntry);

    // delete old entries more than 15 days old
    const now = new Date().getTime();
    uniqueEventsArr = uniqueEventsArr.filter(
      (entry) => now - entry.time < 15 * 24 * 60 * 60 * 1000
    );
    localStorage.setItem(
      LOCALSTORAGE_KEY_UNIQUE_TRACKEVENTS,
      JSON.stringify(uniqueEventsArr)
    );
  }

  private async handleSudsterDecline(order: NewOrders) {
    const modal = await this.modalController.create({
      component: DeclineOrderModalComponent,
      breakpoints: this.breakpoints,
      initialBreakpoint: this.initialBreakpoint,
      cssClass: 'poplin-theme',
      handle: false,
      showBackdrop: true,
      componentProps: {
        internalOrderId: order.PrivateOrderNumber,
      },
    });
    await modal.present();
  }

  async PreferredDeclineAlert(order: NewOrders, orderIdx: number) {
    this.flexibleExtension = false;
    if (order.PreferredPickup === PreferredPickup.ASAP) {
      this.handleSudsterDecline(order);
      return;
    }

    const alert = await this.alertController.create({
      header: 'Need an extra day?',
      message:
        'The customer has indicated that they are flexible with an extra day if necessary. Would you like to accept this order with an extra day?',
      buttons: [
        {
          text: 'Accept Extension',
          handler: () => {
            this.flexibleExtension = true;
            this.OpenOrderContract(orderIdx);
          },
        },
        {
          text: 'Decline',
          handler: () => {
            this.alertController.dismiss();
            this.handleSudsterDecline(order);
          },
        },
      ],
    });
    await alert.present();
  }

  noOrdersInfoAlert() {
    this.presentAlert(MoreInfoAlert.title, MoreInfoAlert.body).then();
  }

  async presentAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK'],
    });
    await alert.present();
  }

  async presentLoading() {
    const loading = await this.loadingController.create({});
    await loading.present();
    return loading;
  }

  updateModalBreakpoints(
    order: AvailableOrderData,
    min: number,
    isOverweightOrder: boolean
  ): number {
    const arr = [
      order.IsCommercial,
      order.IsCustomerFirstOrder,
      order.Preferences.Delicates,
      order.Detergent.includes('I Will Provide'),
      order.Preferences.HangDry,
      order.Preferences.Instructions !== '',
      isOverweightOrder,
    ];

    let count = arr.filter(Boolean).length;

    if (count === 0) {
      return min;
    }
    count += order.Preferences.Instructions.length / 150;
    const size = (count * 35) / this.platform.height() + 0.08;
    let minHeight = min + size;
    if (minHeight > 0.9) {
      minHeight = 0.95;
    }
    return minHeight;
  }
}
