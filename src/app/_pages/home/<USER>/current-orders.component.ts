import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { Router } from '@angular/router';
import { firstValueFrom } from 'rxjs';
import { OrderData } from 'src/app/_interfaces/order-data.interface';
import { SudsterData } from 'src/app/_interfaces/sudster-data.interface';
import { GetActiveOrdersService } from 'src/app/_services/get-active-orders.service';
import { StatsigFactoryService } from 'src/app/_services/statsig-factory.service';
import { StatsigService } from 'src/app/_services/statsig.service';
import { environment } from 'src/environments/environment';
import { AuthidService } from '../../../_services/authid.service';

import { ContractComponent, OrderType } from '../contract/contract.component';
import { ContractService } from '../contract/contract.service';
import { ORDER_CARD_ADAPTER } from '../order-card/order-card.adapter';
import { OrderCardData } from '../order-card/order-card.model';
import { OrderCardService } from '../order-card/order-card.service';
import { OrderContractService } from '../order-contract-modal/order-contract.service';
import { ActiveOrderAdapter } from './adapters/active-order.adapter';
@Component({
  selector: 'app-current-orders',
  templateUrl: './current-orders.component.html',
  styleUrls: ['./current-orders.component.scss'],
  providers: [
    { provide: ORDER_CARD_ADAPTER, useClass: ActiveOrderAdapter },
    { provide: OrderCardService, useClass: OrderCardService },
  ],
})
export class CurrentOrdersComponent implements OnInit {
  @ViewChild(ContractComponent) contractComponent: ContractComponent;

  noActiveOrders = true;
  isExpressServiceEnabled = false;

  activeOrderList: Array<OrderData> = [];
  orderCardDataList: OrderCardData[] = [];
  sameDayService: boolean;
  sudster: SudsterData;
  userID = this.authID.getID();
  private statsigService: StatsigService;
  @Input() idVerification: string;
  @Input() checkrVerification: string;

  @Output() activeCount = new EventEmitter();

  constructor(
    private getActiveOrders: GetActiveOrdersService,
    private router: Router,
    private authID: AuthidService,
    private orderCardService: OrderCardService<{
      order: OrderData;
      expressService: boolean;
    }>,
    private orderContractService: OrderContractService,
    private contractService: ContractService,
    private statsigFactoryService: StatsigFactoryService
  ) {
    this.statsigService = this.statsigFactoryService.getInstance();
  }

  async ngOnInit() {
    this.isExpressServiceEnabled = await firstValueFrom(
      this.statsigService.checkGate(environment.statsig.flags.ExpressService)
    );

    this.getActiveOrders.currentOrders.subscribe(
      (data: {
        Orders: (OrderData & { OrderNumber: string })[];
        Sudster: SudsterData;
      }) => {
        const orders = data.Orders;

        if (orders != null && orders.length != 0) {
          this.activeOrderList = [];
          this.noActiveOrders = false;
          this.orderCardDataList = [];

          this.sudster = data.Sudster;

          orders.forEach((doc) => {
            this.activeOrderList.push(doc);
          });

          this.orderCardDataList = this.orderCardService.getCardDataList(
            this.activeOrderList.map((order) => ({
              order: order,
              expressService: this.isExpressServiceEnabled,
            }))
          );

          this.orderCardDataList.forEach((orderCardData) => {
            orderCardData.getAction('contract').actionHandler = (e: Event) => {
              e.stopPropagation();

              this.openOrderContract(orderCardData.id);
            };

            orderCardData.getAction('chat').actionHandler = (e: Event) => {
              e.stopPropagation();

              this.openChat(orderCardData.id);
            };
          });
        } else {
          this.noActiveOrders = true;
        }
        this.activeCount.emit(this.activeOrderList.length);
      }
    );
  }

  openOrderContract(id: string) {
    const orderData: OrderData = this.activeOrderList.find(
      (o) => o.OrderNumber === id
    );

    if (!this.contractService.useNewContractComponentFlag()) {
      this.orderContractService.openOrderContractFromOrderData(
        orderData,
        this.sudster,
        (this.sudster.OrderCount || 0) == 0,
        null,
        this.idVerification,
        this.checkrVerification
      );

      return;
    }

    this.contractComponent.openContract(
      this.sudster,
      orderData,
      OrderType.Active
    );
  }

  async openChat(id: string) {
    this.router.navigate(['/active', { id: id, showChat: true }]);
  }

  async clickActiveOrder(id: string) {
    this.router.navigate(['/active', { id: id }]);
  }
}
