@import 'src/theme/type-mixins.scss';

ion-content {
  --background: var(--gray-50);
  --padding-top: constant(safe-area-inset-top);
  --padding-top: env(safe-area-inset-top);

  #home {
    &-menu-button {
      position: absolute;
      background: none;
      top: 0;
      left: 0;
      padding-left: 18px;
      padding-right: 30px;
      padding-bottom: 15px;
      padding-top: calc(env(safe-area-inset-top) + 20px);
      b {
        width: 5px;
        height: 5px;
        margin-top: 3px;
        border-radius: 50%;
        display: block;
      }
    }
    &-configs {
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      margin: 15px;
    }
    &-settings-icon {
      right: 10px;
      font-size: 20px;
      color: var(--BlueGray3);
      top: calc(env(safe-area-inset-top) + 20px);
      margin: 4px;
    }
    &-sudster-month {
      top: calc(env(safe-area-inset-top) + 20px);
      right: 40px;
      text-transform: uppercase;
      text-align: right;
      letter-spacing: 1px;
      color: var(--BlueGray3);
      font-weight: 700;
      font-size: 10px;
    }
  }
  ion-row {
    &#home {
      &-header {
        display: flex;
        justify-content: space-between;
        padding: 5px;
      }
    }
    ion-col {
      h1 {
        &#home {
          &-sudster-name {
            @include l-head;
            align-self: center;
            margin: auto;
          }
        }
      }
      #refer-code {
        &-button {
          display: flex;
          background-color: var(--viridian-825);
          border-radius: 5px;
          padding: 5px;
          align-self: self-end;
          @include l-head;
        }
        &-text {
          display: flex;
          color: white;
          font-size: 0.313em;
          margin: 0 4px 0 0;
          i {
            font-size: medium;
            padding-right: 2px;
          }
        }
      }
    }
  }
  .MapFullscreen {
    height: 100vh;
    position: fixed;
    z-index: 10;
    width: 100vw;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }
  ion-segment {
    width: 90%;
    margin: 15px auto;
  }
  ion-footer {
    background-color: rgb(248, 252, 255) !important;
  }

  .tooltip {
    position: absolute;
    background-color: var(--blue-150);
    border-radius: 8px;
    color: var(--blue-850);
    padding: 12px 16px;
    font-size: 14px;
    left: -10px; /* Change right to left */
    top: 30px;
    width: 235px;
    z-index: 200;
    margin-top: 140px;
    margin-left: 20px; /* Change margin-right to margin-left */
    @supports (-webkit-touch-callout: none) {
      margin-top: 190px;
    }
    &::before {
      content: '';
      width: 0;
      height: 0;
      border-left: 5px solid transparent;
      border-right: 5px solid transparent;
      border-bottom: 5px solid var(--blue-150);
      position: absolute;
      left: calc(12% + 1px); /* Adjust the left value as needed */
      top: -5px;
    }
    div {
      text-transform: uppercase;
      font-family: 'PitchSans-Medium', Helvetica, sans-serif;
      font-weight: 700;
      line-height: 120%;
      letter-spacing: 2.1px;
      text-align: left;
    }
    p {
      margin-top: 5px;
      text-align: left;
    }
    a {
      float: right;
      text-decoration: underline;
      color: var(--blue-850);
    }
  }
}
