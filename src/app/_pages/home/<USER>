import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { ActiveOrdersComponent } from './active-orders/active-orders.component';

import { IonicModule } from '@ionic/angular';

import { DailyDeliveryPhotoComponent } from 'src/app/_components/daily-delivery-photo/daily-delivery-photo.component';
import { MenuPopoverComponent } from 'src/app/_components/menu-popover/menu-popover.component';
import { ShortDaySelectorComponent } from 'src/app/_components/short-day-selector/short-day-selector.component';
import { BannerComponent } from 'src/app/shared/components/banner/banner.component';
import { BannerService } from 'src/app/shared/components/banner/banner.service';
import { VerificationConfirmComponent } from '../verification/verification-confirm/verification-confirm.component';
import { AvailableOrdersComponent } from './available-orders/available-orders.component';
import { ActiveOrderAdapterStrategy } from './contract/adapters/active-order-adapter.strategy';
import { AvailableOrderAdapterStrategy } from './contract/adapters/available-order-adapter.strategy';
import { OrderAdapterFactory } from './contract/adapters/order-adapter.factory';
import { ContractComponent } from './contract/contract.component';
import { CurrentOrdersComponent } from './current-orders/current-orders.component';
import { DeclineOrderModalComponent } from './decline-order-modal/decline-order-modal.component';
import { DeclineOrderComponent } from './decline-order/decline-order.component';
import { GreetingComponent } from './greeting/greeting.component';
import { HomePage } from './home.page';
import { MapComponent } from './map/map.component';
import { NewOrdersComponent } from './new-orders/new-orders.component';
import { OrderCardComponent } from './order-card/order-card.component';
import { OrderContractModalComponent } from './order-contract-modal/order-contract-modal.component';
import { SettingsComponent } from './settings/settings.component';

const routes: Routes = [
  {
    path: '',
    component: HomePage,
  },
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes),
    OrderCardComponent,
    BannerComponent,
    ContractComponent,
  ],
  declarations: [
    HomePage,
    NewOrdersComponent,
    AvailableOrdersComponent,
    ActiveOrdersComponent,
    CurrentOrdersComponent,
    MapComponent,
    MenuPopoverComponent,
    OrderContractModalComponent,
    GreetingComponent,
    SettingsComponent,
    DailyDeliveryPhotoComponent,
    DeclineOrderComponent,
    VerificationConfirmComponent,
    DeclineOrderModalComponent,
    ShortDaySelectorComponent,
  ],
  providers: [
    NewOrdersComponent,
    AvailableOrdersComponent,
    BannerService,
    OrderAdapterFactory,
    ActiveOrderAdapterStrategy,
    AvailableOrderAdapterStrategy,
  ],
})
export class HomePageModule {}
