import { Component, OnInit } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/auth';
import { AngularFirestore } from '@angular/fire/firestore';
import { AngularFireFunctions } from '@angular/fire/functions';
import { Router } from '@angular/router';
import { Smartlook } from '@awesome-cordova-plugins/smartlook/ngx';
import { App } from '@capacitor/app';
import { Capacitor } from '@capacitor/core';
import { Device } from '@capacitor/device';
import {
  Alert<PERSON>ontroller,
  ModalController,
  NavController,
  PopoverController,
  ToastController,
} from '@ionic/angular';
import { LoadingController } from '@ionic/angular/standalone';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import firebase from 'firebase/app';
import 'firebase/firestore';
import moment from 'moment-timezone';
import { BehaviorSubject } from 'rxjs';
import { filter, first, tap } from 'rxjs/operators';
import { MenuPopoverComponent } from 'src/app/_components/menu-popover/menu-popover.component';
import { TermsAndPoliciesComponent } from 'src/app/_components/terms-and-policies/terms-and-policies.component';
import {
  BANNER_CONFIG,
  BannerTypeEnum,
} from 'src/app/_constants/banner.constant';
import { OrderData } from 'src/app/_interfaces/order-data.interface';
import {
  CheckrInformation,
  SudsterData,
} from 'src/app/_interfaces/sudster-data.interface';
import { AnalyticsTrackEventType } from 'src/app/_services/analytics/analytics-event.entity';
import { LaundryProService } from 'src/app/_services/api/laundry-pro.service';
import { AuthidService } from 'src/app/_services/authid.service';
import { GetActiveOrdersService } from 'src/app/_services/get-active-orders.service'; // Import the service
import { GetSudsterDataService } from 'src/app/_services/get-sudster-data.service';
import { InboxMessageService } from 'src/app/_services/inbox-message.service';
import { LegacyApiService } from 'src/app/_services/legacy-api.service';
import { NotificationService } from 'src/app/_services/notification-service';
import { SmartLookWebService } from 'src/app/_services/smartlook/smartlook-web-service';
import { StatsigFactoryService } from 'src/app/_services/statsig-factory.service';
import { StatsigService } from 'src/app/_services/statsig.service';
import { selectDistinctBy } from 'src/app/_utils/operators';
import { isOnVacation, parseDate } from 'src/app/_utils/utils';
import { BannerService } from 'src/app/shared/components/banner/banner.service';
import { environment } from 'src/environments/environment';
@UntilDestroy()
@Component({
  selector: 'app-home',
  templateUrl: './home.page.html',
  styleUrls: ['./home.page.scss'],
  providers: [InboxMessageService],
})
export class HomePage implements OnInit {
  OrdersToTransport: any;
  MapFullscreen = false;
  OrderViewValue = 'Available';
  SudsterFirstName = '';
  SignupMonth = '';
  SudsterData: SudsterData;
  AvailableCount = 0;
  ActiveCount = 0;
  ActiveOrderList = [];
  UserID = this.AuthID.getID();
  FirstRun = true;
  AccountRejectedForterReason: string;
  AccountRejected: boolean;
  PrevTOSResult: object;
  PlatformType: 'android' | 'ios' | 'web';
  MobileDeviceId: string;
  MobileAppVersion: string;
  statsigInitialized = false;
  ReferralFeatureFlag = false;
  CheckrInformation: CheckrInformation;
  ContactEmail: string;
  Name: string;
  showedExtensionTooltip = false;
  requireTermsOfServiceOldLP = undefined;
  newOrderCardsEnabled = new BehaviorSubject(false);
  newOrderCardsFlagLoaded = new BehaviorSubject(false);

  private statsigService: StatsigService;
  private statsigServiceV4: StatsigService;

  constructor(
    public firestore: AngularFirestore,
    public afAuth: AngularFireAuth,
    private popoverController: PopoverController,
    private alertController: AlertController,
    private toastController: ToastController,
    private navCtrl: NavController,
    private AuthID: AuthidService,
    private cloudFunctions: AngularFireFunctions,
    private sudsterDataService: GetSudsterDataService,
    private statsigFactoryService: StatsigFactoryService,
    private notificationService: NotificationService,
    private apiService: LegacyApiService,
    private router: Router,
    private smartlookMobile: Smartlook,
    private smartlookWeb: SmartLookWebService,
    private GetActiveOrders: GetActiveOrdersService,
    private modalController: ModalController,
    private laundryProService: LaundryProService,
    private loadingController: LoadingController,
    private bannerService: BannerService,
    private inboxMessageService: InboxMessageService
  ) {
    this.statsigService = this.statsigFactoryService.getInstance();
    this.statsigServiceV4 = this.statsigFactoryService.getInstance('v4');

    this.showedExtensionTooltip = !!localStorage.getItem(
      'showedExtensionTooltip'
    );

    Device.getId().then((deviceId) => {
      this.MobileDeviceId = deviceId.identifier;
    });

    Device.getInfo().then((deviceInfo) => {
      this.PlatformType = deviceInfo.platform;
      if (deviceInfo.platform !== 'web') {
        App.getInfo().then((res) => {
          this.MobileAppVersion = res.version;
        });
      }
    });

    this.checkNewOrderCards();
  }

  async presentTOSAlert(lastTOSAcceptanceDate) {
    this.getLatestTOSDate().then(async (x) => {
      const latestTOSDate = x['Date'];
      if (
        !lastTOSAcceptanceDate ||
        lastTOSAcceptanceDate?.seconds < latestTOSDate?.seconds
      ) {
        const alert = await this.alertController.create({
          header: 'Terms of Service',
          message:
            'We have updated our <a href="https://poplin.co/terms-of-service">Terms of Service</a> and <a href="https://poplin.co/privacy-policy">Privacy Policy</a>. By tapping “I agree” I am accepting the updated terms.',
          backdropDismiss: false,
          buttons: [
            {
              text: 'I Accept',
              handler: () => {
                this.acceptTOS();
              },
            },
          ],
        });
        await alert.present();
      } else {
        return;
      }
    });
  }

  async getLatestTOSDate() {
    if (!this.PrevTOSResult) {
      return await this.firestore
        .doc('Code/TermsUpdate')
        .get()
        .toPromise()
        .then((x) => {
          return (this.PrevTOSResult = x.data() as object);
        })
        .catch((e) => console.error('Failed to get TOS', e));
    } else {
      return this.PrevTOSResult;
    }
  }

  acceptTOS() {
    this.firestore.doc<any>(`Sudsters/${this.UserID}`).set(
      {
        LastTOSAcceptanceDate: new Date(),
      },
      { merge: true }
    );
  }

  ngOnInit() {
    this.loadActiveOrders();

    this.loadTermsAndPoliciesFeatureFlag();

    // test send segment event if gate is true
    this.statsigService
      .checkGate(environment.statsig.flags.testFlag)
      .pipe(untilDestroyed(this))
      .subscribe((value) => {
        this.statsigInitialized = value;
      });

    // QR gate
    this.statsigService
      .checkGate(environment.statsig.flags.SudsterReferralCode)
      .pipe(untilDestroyed(this))
      .subscribe((value) => {
        this.ReferralFeatureFlag = value;
      });

    // consider adding a statsig flag to remove in a controlled way
    // trigger multiple unnecessary snapshots update events, dont think is needed anymore
    setTimeout(() => {
      // Fixes strange android issue that causes orders not to load first time
      this.firestore
        .doc('Sudsters/' + this.UserID)
        .set({ LastOpened: new Date().getTime() }, { merge: true });
    }, 500);

    this.firestore
      .doc<SudsterData>(`Sudsters/${this.UserID}`)
      .valueChanges()
      .pipe(
        untilDestroyed(this),
        tap((doc) => {
          this.SudsterData = doc;
        }),
        filter((doc): doc is SudsterData => !!doc),
        selectDistinctBy((doc) => ({
          ContactEmail: doc.ContactEmail,
          FirstName: doc.FirstName,
          LastName: doc.LastName,
          LastTOSAcceptanceDate: doc.LastTOSAcceptanceDate,
          SinceMonth: doc.SinceMonth,
          AccountRejectedForterReason: doc.AccountRejectedForterReason,
          AccountRejected: doc.AccountRejected,
          SignupStepNumber: doc.SignupStepNumber,
          IDStatus: doc.IDStatus,
          IdVerification: doc.IdVerification,
          CheckrVerification: doc.CheckrVerification,
          AlertsOnLp: doc.AlertsOnLp,
          Vacation: doc.Vacation,
          InboxData: doc.InboxData,
        }))
      )
      .subscribe((doc) => {
        this.ContactEmail = doc.ContactEmail;
        this.Name = `${doc.FirstName} ${doc.LastName}`;

        this.presentTOSAlert(doc.LastTOSAcceptanceDate).then(() => {
          this.SudsterFirstName = doc.FirstName;
          this.SignupMonth = doc.SinceMonth;
          this.AccountRejectedForterReason = doc.AccountRejectedForterReason;
          this.AccountRejected = doc.AccountRejected;
          if (
            this.AccountRejected &&
            this.AccountRejectedForterReason == 'Rejected by Forter'
          ) {
            this.navCtrl.navigateRoot('/rejected');
            return;
          }
          if (this.FirstRun) {
            this.FirstRun = false;
            this.CheckAcademyInbox();
            this.CheckChatCredits();
          }

          if (this.SudsterData.RequireAcceptTOS === true) {
            this.showAcceptTOS();
          }
        });
        if (doc.SignupStepNumber == 0 && doc.IDStatus != 'verified') {
          // Move to ID page
          this.router.navigate(['unverified'], { replaceUrl: true });
          return;
        }

        if (
          doc.IdVerification === 'unverified' ||
          doc.IdVerification === 'pending'
        ) {
          this.router.navigate(['id-verification'], { replaceUrl: true });

          return;
        }
        if (
          doc.CheckrVerification !== null &&
          typeof doc.CheckrVerification !== 'undefined' &&
          doc.CheckrVerification !== 'verified'
        ) {
          this.router.navigate(['background'], { replaceUrl: true });
          return;
        }

        if (!this.confirmedVerification(this.SudsterData)) {
          this.router.navigate(['verification-success'], {
            replaceUrl: true,
          });
          return;
        }
        if (isOnVacation(doc.Vacation?.startDate, doc.Vacation?.endDate)) {
          /*
           * WARNING: an assumption was made by @Karina that the LP will use the same timezone
           * than the browser they are in. If this assumption changes, timezone should be considered
           * when using parseDate() to reflect the date accurately, retrieving it from
           * the profile of the LP
           */
          //End date is set on the last day of vacation. We intend to show the day after
          const endDate = this.getTomorrowDate(
            parseDate(doc.Vacation?.endDate)
          );
          this.showVacation(endDate);
        } else if (!doc.AlertsOnLp) {
          this.showAlert();
        } else {
          this.bannerService.hideBanner();
        }

        this.inboxMessageService.updateInboxMessage(doc.InboxData);
      });
    this.statsigService
      .checkGate(environment.statsig.flags.smartlookRecordings)
      .pipe(untilDestroyed(this))
      .subscribe((value) => {
        if (value) {
          if (Capacitor.isNativePlatform()) {
            this.smartlookMobile.setUserIdentifier({ identifier: this.UserID });
            this.smartlookMobile.setUserEmail({ email: this.ContactEmail });
            this.smartlookMobile.setUserName({
              name: this.Name,
            });
          } else {
            this.smartlookWeb.userSetup(
              this.UserID,
              this.Name,
              this.ContactEmail
            );
          }
        }
      });
    setTimeout(() => {
      this.handleAppVersion();

      if (this.PlatformType !== 'web') {
        this.notificationService.setVariables(
          this.PlatformType,
          this.MobileAppVersion,
          this.MobileDeviceId,
          this.UserID,
          this.SudsterData.FCM2
        );
        this.notificationService.init();
      }
    }, 3000);

    this.termsAndPoliciesCheck();
  }

  async checkNewOrderCards() {
    setTimeout(() => {
      this.newOrderCardsFlagLoaded.next(true);
    }, 1500);

    this.statsigService
      .checkGate(environment.statsig.flags.newOrderCardsEnabled)
      .pipe(untilDestroyed(this))
      .subscribe((value) => {
        this.newOrderCardsFlagLoaded.next(true);
        this.newOrderCardsEnabled.next(value);
      });
  }

  async loadTermsAndPoliciesFeatureFlag() {
    const config = await this.statsigServiceV4.getConfigPromise(
      'require-laundrypro-terms-and-policies'
    );

    this.requireTermsOfServiceOldLP = config.get(
      'explicityRequiredOldLP',
      false
    );
  }

  async termsAndPoliciesCheck() {
    if (this.requireTermsOfServiceOldLP === undefined) {
      await this.loadTermsAndPoliciesFeatureFlag();
    }

    if (this.requireTermsOfServiceOldLP) {
      const termsToAccept =
        await this.laundryProService.getTermsAndPoliciesToAccept(
          this.AuthID.UserID
        );

      if (termsToAccept.policies.needAccept || termsToAccept.terms.needAccept) {
        const termsAndPoliciesModal = await this.modalController.create({
          component: TermsAndPoliciesComponent,
          cssClass: 'modal-fullscreen',
          backdropDismiss: false,
          componentProps: {
            termsAndPolicies: {
              terms: termsToAccept.terms.term,
              policies: termsToAccept.policies.privacyPolicy,
            },
          },
        });
        await termsAndPoliciesModal.present();
        const confirmTermsAndPolicies =
          await termsAndPoliciesModal.onDidDismiss();

        if (
          confirmTermsAndPolicies.role === 'confirm' &&
          confirmTermsAndPolicies.data.accepted
        ) {
          const loading = await this.loadingController.create({
            message: 'Saving terms and policies accepted',
          });
          await loading.present();
          await this.laundryProService.saveNewTermsAndPoliciesAccepted(
            this.AuthID.UserID,
            {
              policyVersion: confirmTermsAndPolicies.data.policyVersion,
              termsVersion: confirmTermsAndPolicies.data.termsVersion,
            }
          );
          loading.dismiss();
        }
      }
    }
  }

  loadActiveOrders() {
    // Fetch active orders
    this.GetActiveOrders.LoadOrderList();
    this.GetActiveOrders.currentOrders
      .pipe(untilDestroyed(this))
      .subscribe((data: { Orders: OrderData[] }) => {
        this.ActiveCount = data.Orders?.length || 0;
      });
  }

  confirmedVerification(doc: SudsterData): boolean {
    return typeof doc.ConfirmedVerification !== 'undefined' &&
      !doc.ConfirmedVerification &&
      this.isVerified(doc)
      ? false
      : true;
  }

  isVerified(doc: SudsterData): boolean {
    if (doc.IdVerification && doc.CheckrVerification) {
      return (
        doc.IdVerification === 'verified' &&
        doc.CheckrVerification === 'verified'
      );
    }
    if (doc.IdVerification) {
      return doc.IdVerification === 'verified';
    }
    if (doc.CheckrVerification) {
      return doc.CheckrVerification === 'verified';
    }

    return true;
  }
  async presentAlertMessage(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK'],
    });

    await alert.present();
  }

  // Indicates most recently used AppType and AppVersion
  async handleAppVersion() {
    const sudsterDoc = `Sudsters/${this.UserID}`;
    const now = moment().utc();
    const nowNumber = now.valueOf();
    const nowDate = now.toDate();

    if (this.PlatformType === 'ios' || this.PlatformType === 'android') {
      this.firestore.doc(sudsterDoc).update({
        appInfo: {
          appVersion: this.MobileAppVersion,
          platform: this.PlatformType,
          timestamp: nowNumber,
          date: nowDate,
        },
      });
    } else {
      this.firestore.doc(sudsterDoc).update({
        appInfo: {
          appVersion: environment.version,
          platform: this.PlatformType,
          timestamp: nowNumber,
          date: nowDate,
        },
      });
    }
  }

  CheckChatCredits() {
    this.sudsterDataService.checkChatCredits();
  }

  CheckAcademyInbox() {
    const NewArticleToast = async () => {
      const toast = await this.toastController.create({
        header: 'Check out today’s Pro Tip',
        message:
          'Learn and earn 10 pts <ion-icon name="star-outline"></ion-icon>',
        position: 'bottom',
        buttons: [
          {
            side: 'end',
            text: 'VIEW',
            handler: () => {
              this.navCtrl.navigateForward('academy');
            },
          },
          {
            icon: 'close-outline',
            role: 'cancel',
          },
        ],
      });
      toast.present();
    };

    const HoursSinceLastUpdate =
      Math.abs(
        this.SudsterData.Academy?.LastUpdated?.toDate().getTime() -
          new Date().getTime()
      ) / 36e5;

    this.firestore
      .doc<any>(`Code/Sudster`)
      .valueChanges()
      .pipe(first())
      .subscribe((CodeDoc) => {
        const AcademyCount = this.SudsterData.Academy?.Count || 1000;
        const AcademyCompletedLength =
          this.SudsterData.Academy?.Completed?.length;
        if (
          this.SudsterData.Academy == null ||
          ((HoursSinceLastUpdate > 24 || isNaN(HoursSinceLastUpdate)) &&
            AcademyCount - AcademyCompletedLength < 3 &&
            AcademyCount < CodeDoc.AcademyArticles.length)
        ) {
          this.firestore.doc('Sudsters/' + this.UserID).set(
            {
              Academy: {
                Count: firebase.firestore.FieldValue.increment(1),
                Completed: this.SudsterData.Academy?.Completed || [],
                LastUpdated: new Date(),
              },
            },
            { merge: true }
          );
          setTimeout(() => {
            NewArticleToast();
          }, 3000);
        } else if (AcademyCount > CodeDoc.AcademyArticles.length) {
          // Fix for strange bug causing Count to go over limit
          this.firestore.doc('Sudsters/' + this.UserID).set(
            {
              Academy: {
                Count: CodeDoc.AcademyArticles.length,
              },
            },
            { merge: true }
          );
        }
      });
  }

  openSettings() {
    this.router.navigate(['/availability']);
  }

  async presentPopover(ev: any) {
    const popover = await this.popoverController.create({
      component: MenuPopoverComponent,
      event: ev,
      translucent: true,
      cssClass: 'MenuPopover',
      componentProps: {
        AcademyInboxCount:
          this.SudsterData.Academy?.Count -
            this.SudsterData.Academy?.Completed?.length || 0,
      },
    });
    return await popover.present();
  }

  async presentAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK'],
    });
    await alert.present();
  }

  async showAcceptTOS() {
    const alert = await this.alertController.create({
      header: 'Terms of service update',
      message:
        'To keep payouts enabled, please accept Stripe\'s <a href="https://stripe.com/connect-account/legal/recipient" target="_blank" rel="noopener noreferrer">terms of service.</a>',
      backdropDismiss: false,
      buttons: [
        {
          text: 'I Accept',
          handler: () => {
            this.cloudFunctions
              .httpsCallable('SudsterV3_Automation')({ process: 'accept-tos' })
              .subscribe((res) => {});
          },
        },
      ],
    });
    await alert.present();
  }
  referralOpened() {
    this.apiService
      .post('SegmentServiceApi/v1/TrackEvent', {
        source: 'Laundry Pro',
        eventCategory: 'Referral',
        eventData: {
          eventType: AnalyticsTrackEventType.ReferrealOpened,
          accessSource: 'home page',
        },
      })
      .subscribe();
  }
  dismissTooltip() {
    this.showedExtensionTooltip = true;
    localStorage.setItem('showedExtensionTooltip', 'true');
  }

  onBannerAction(type: BannerTypeEnum) {
    if (type === BannerTypeEnum.ALERT) {
      this.openSettings();
    }
  }

  private showAlert() {
    const type = BannerTypeEnum.ALERT;
    const config = BANNER_CONFIG[BannerTypeEnum.ALERT];
    this.bannerService.showBanner({ type, ...config });
  }

  private showVacation(endDate: string) {
    const type = BannerTypeEnum.VACATION;
    const config = BANNER_CONFIG[type];
    const message = `${config.message} ${endDate}`;
    this.bannerService.showBanner({ type, ...config, message });
  }

  private getTomorrowDate(date: Date): string {
    date.setDate(date.getDate() + 1);
    const nextDayFormatted = date.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
    });
    return nextDayFormatted;
  }
}
