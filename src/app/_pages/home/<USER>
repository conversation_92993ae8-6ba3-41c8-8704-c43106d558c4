<ion-content>
  <div id="home-configs">
    <button id="home-menu-button" (click)="presentPopover($event)">
      <b style="background: var(--pink-core)"></b>
      <b style="background: var(--pink-core)"></b>
      <b style="background: var(--pink-core)"></b>
    </button>

    <small id="home-sudster-month" class="col"
      >Laundry Pro<br />Since {{SignupMonth}}</small
    >
    <i
      id="home-settings-icon"
      class="material-icons col"
      (click)="openSettings()"
      >settings</i
    >
  </div>
  <ion-row id="home-header">
    <ion-col id="home-header-col-one">
      <h1 id="home-sudster-name" class="trunc">Hi {{SudsterFirstName}},</h1>
      <button
        *ngIf="ReferralFeatureFlag"
        (click)="referralOpened()"
        [routerLink]="['/refercode']"
        id="refer-code-button"
        alt="referral code button"
      >
        <div class="ion-text-right">
          <p id="refer-code-text">
            <i id="refer-code-icon" class="material-icons">qr_code</i>&nbsp;MY
            REFERRALS
          </p>
        </div>
      </button>
    </ion-col>
    <div class="tooltip" *ngIf="!showedExtensionTooltip && ReferralFeatureFlag">
      <div>GROW YOUR BUSINESS</div>
      <p>Want more orders?<br />Share a $20 referral today!</p>
      <a id="TooltipDismiss" (click)="dismissTooltip()"> Got it, thanks</a>
    </div>
  </ion-row>

  <app-map
    (Fullscreen)="MapFullscreen = !MapFullscreen"
    [class.MapFullscreen]="MapFullscreen"
    [IdVerification]="SudsterData?.IdVerification"
    [CheckrVerification]="SudsterData?.CheckrVerification"
  ></app-map>

  <ion-segment
    id="active-orders-tab"
    mode="ios"
    (ionChange)="OrderViewValue = $event.srcElement.value"
    [value]="OrderViewValue"
  >
    <ion-segment-button id="active-orders-tab-available" value="Available">
      <ion-label id="available-orders-tab-label">
        Available{{AvailableCount > 0 ? ' (' + AvailableCount + ')' : ''}}
      </ion-label>
    </ion-segment-button>
    <ion-segment-button id="active-orders-tab-active" value="Active">
      <ion-label id="active-orders-tab-active-label">
        Active{{ActiveCount > 0 ? ' (' + ActiveCount + ')' : ''}}
      </ion-label>
    </ion-segment-button>
  </ion-segment>

  <poplin-banner (bannerClicked)="onBannerAction($event)"></poplin-banner>

  <ng-container *ngIf="newOrderCardsFlagLoaded | async">
    <!-- New Cards -->
    <ng-container *ngIf="newOrderCardsEnabled | async">
      <app-available-orders
        *ngIf="OrderViewValue === 'Available'"
        (goToActive)="OrderViewValue = 'Active'"
        (availableCount)="AvailableCount = $event"
        [availableCountNum]="AvailableCount"
        [idVerification]="SudsterData?.IdVerification"
        [checkrVerification]="SudsterData?.CheckrVerification"
      ></app-available-orders>
      <app-current-orders
        *ngIf="OrderViewValue === 'Active'"
        [idVerification]="SudsterData?.IdVerification"
        [checkrVerification]="SudsterData?.CheckrVerification"
        (activeCount)="activeCount = $event"
      ></app-current-orders>
    </ng-container>

    <!-- Legacy Cards -->
    <ng-container *ngIf="!(newOrderCardsEnabled | async)">
      <app-new-orders
        *ngIf="OrderViewValue === 'Available'"
        (GoToActive)="OrderViewValue = 'Active'"
        (AvailableCount)="AvailableCount = $event"
        [AvailableCountNum]="AvailableCount"
        [IdVerification]="SudsterData?.IdVerification"
        [CheckrVerification]="SudsterData?.CheckrVerification"
      ></app-new-orders>
      <app-active-orders
        *ngIf="OrderViewValue === 'Active'"
        (ActiveCount)="ActiveCount = $event"
      ></app-active-orders>
    </ng-container>
  </ng-container>
</ion-content>
