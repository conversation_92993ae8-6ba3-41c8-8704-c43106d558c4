@import 'src/theme/type-mixins.scss';

:host {
  $finish-animation-length: 1.5s;
  $slider-button-size: 39px;

  $biggest-font-size: 20px;
  $big-font-size: 17px;

  $medium-font-size: 15px;
  $small-font-size: 13px;

  $smallest-font-size: 11px;

  @-webkit-keyframes bg-pan-right {
    from {
      background-position: 100%;
    }

    to {
      background-position: 0%;
    }
  }

  @keyframes bg-pan-right {
    from {
      background-position: 100%;
    }

    to {
      background-position: 0%;
    }
  }

  @-webkit-keyframes disappear {
    0% {
      opacity: 1;
    }

    100% {
      opacity: 0;
    }
  }

  @keyframes disappear {
    0% {
      opacity: 1;
    }

    100% {
      opacity: 0;
    }
  }

  @-webkit-keyframes condense {
    0% {
      width: '*';
      height: '*';
      border-radius: '*';
    }

    100% {
      width: 46px;
      height: 46px;
      border-radius: 50px;
    }
  }

  @keyframes condense {
    0% {
      width: '*';
      height: '*';
      border-radius: '*';
    }

    100% {
      width: 46px;
      height: 46px;
      border-radius: 50px;
    }
  }

  @-webkit-keyframes finish-background {
    0% {
      background: '*';
      --background: '*';
    }

    100% {
      background: var(--Blue);
      --background: var(--Blue);
    }
  }

  @keyframes finish-background {
    0% {
      background: '*';
      --background: '*';
    }

    100% {
      background: var(--Blue);
      --background: var(--Blue);
    }
  }

  @-webkit-keyframes finish-background-error {
    0% {
      background: var(--Blue);
      --background: var(--Blue);
    }

    100% {
      background: var(--Orange);
      --background: var(--Orange);
    }
  }

  @keyframes finish-background-error {
    0% {
      background: var(--Blue);
      --background: var(--Blue);
    }

    100% {
      background: var(--Orange);
      --background: var(--Orange);
    }
  }

  @-webkit-keyframes finish-background-success {
    0% {
      background: var(--Blue);
      --background: var(--Blue);
    }

    100% {
      background: var(--GreenTint);
      --background: var(--GreenTint);
    }
  }

  @keyframes finish-background-success {
    0% {
      background: var(--Blue);
      --background: var(--Blue);
    }

    100% {
      background: var(--GreenTint);
      --background: var(--GreenTint);
    }
  }

  @-webkit-keyframes finish-slider {
    0% {
      opacity: 1;
    }

    100% {
      opacity: 0;
    }
  }

  @keyframes finish-slider {
    0% {
      opacity: 1;
    }

    100% {
      opacity: 0;
    }
  }

  @-webkit-keyframes show-inner {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }

  @keyframes show-inner {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }

  @-webkit-keyframes hide-icon {
    0% {
      opacity: 1;
    }

    100% {
      opacity: 0;
    }
  }

  @keyframes hide-icon {
    0% {
      opacity: 1;
    }

    100% {
      opacity: 0;
    }
  }

  @-webkit-keyframes show-icon {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }

  @keyframes show-icon {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }

  .container {
    display: flex;
    flex-direction: column;

    height: 100% !important;
  }

  .header {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--BlueGray2);
    color: var(--BlueGray4);
    margin-left: 6%;
    margin-right: 6%;
    // min-height: 16%;

    &__order-number {
      font-size: $smallest-font-size;
      font-weight: bolder;
      border-radius: 0 0 200px 200px;
      background-color: var(--BlueGray1);
      padding: 2px 24px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
      letter-spacing: 2px;
    }

    &__title {
      margin-bottom: 2.5%;
      margin-top: 7px;
      @include s-head;
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    position: relative;

    height: 100%;

    color: var(--BlueGray4);

    &__top {
      font-weight: bolder;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      margin-top: 9px;
      // min-height: 23%;

      .fullWidthName {
        width: 90% !important;
        text-align: center;
      }

      &__name {
        @include s-head;
        color: var(--black);
        width: 50%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      &__bonus {
        background-color: var(--Pink);

        color: white;
        padding: 2px 12px;
        border-radius: 15px;
        box-shadow: -3px 3px 5px rgba(0, 30, 50, 0.15);
        margin-top: 7px;
        margin-left: 20px;
      }
    }

    &__details {
      font-weight: bolder;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      align-content: center;

      color: var(--BlueGray4);

      width: 100%;
      margin: 12px 0;

      $description-height: 15px;
      $side-div-width: 45%;

      &__left {
        display: flex;
        justify-content: space-evenly;
        align-items: baseline;
        align-content: center;
        padding-top: 8px;

        min-width: $side-div-width;
        width: $side-div-width;

        &__bags {
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          align-items: center;

          * {
            text-align: center;
          }

          &__count {
            font-size: 27px;

            & span {
              font-size: 15px;
            }
          }

          &__description {
            height: $description-height;
            min-height: $description-height;
            font-size: $smallest-font-size;
          }

          &__bagsize {
            font-size: 10px;
            text-transform: uppercase;
            font-weight: 800;
            font-style: italic;
          }
        }

        &__estimate {
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          align-items: center;

          * {
            text-align: center;
          }

          &__amount {
            font-size: 27px;
          }

          &__description {
            display: flex;
            justify-content: center;
            align-items: center;
            align-content: center;

            font-size: $smallest-font-size;

            height: $description-height;
            min-height: $description-height;

            &__text {
            }

            &__icon {
              font-size: 10px;
              width: 10px;
              height: 10px;

              margin-top: 1px;
              margin-left: 4px;
              border-radius: 50px;

              color: white;
              background-color: var(--BlueGray4);
            }
          }
        }
      }

      &__divider {
        border-left: 1px solid var(--BlueGray5);
        min-width: 2px;
        max-width: 2px;
        height: 100%;
      }

      &__location {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        align-items: space-around;

        min-width: $side-div-width;
        width: $side-div-width;

        &__place {
          font-size: 18px;
          max-height: 19vw;

          // white-space: nowrap;
          white-space: pre-wrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        &__distance {
          height: $description-height;
          min-height: $description-height;
          font-size: 13px;
        }
      }
    }

    &__deadline {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      align-items: center;

      font-weight: 400;
      background: var(--BlueGray1);

      width: 100%;
      padding: 8px 0;

      &__pickup {
        display: flex;
        justify-content: center;
        align-items: center;
        align-content: center;

        margin-bottom: 6px;

        &__description {
        }

        &__deadline {
          margin-left: 6px;
          font-weight: 800;
        }
      }

      &__delivery {
        display: flex;
        justify-content: center;
        align-items: center;
        align-content: center;

        &__description {
        }

        &__deadline {
          margin-left: 6px;
          font-weight: 800;
        }
      }
    }

    #FirstOrderDeadlineNotice {
      padding-bottom: 7px;
      padding-top: 5px;
      font-weight: 600;
      color: red;
      font-size: 13px;
      letter-spacing: -0.3px;

      ion-icon {
        font-size: 20px;
        vertical-align: -5px;
        padding-right: 5px;
      }
    }

    &__same-day-warning {
      position: relative;

      display: flex;
      flex-direction: column;
      justify-content: space-around;
      align-items: center;

      color: var(--Red);

      font-size: 20px;
      font-weight: bolder;

      border: 1px solid var(--Red);
      border-radius: 10px;
      padding: 8px 16px;
      margin-top: 24px;

      &__title {
      }

      &__radio {
        margin-top: 12px;
        font-size: $small-font-size;
        color: var(--BlueGray5);

        display: flex;
        justify-content: center;
        align-items: center;
        align-content: center;

        &__checkmark {
          margin-top: 1px;
          margin-right: 6px;

          border: 2px solid var(--BlueGray5);
          border-radius: 3px;

          width: 16px;
          height: 16px;

          ion-icon {
          }
        }
      }

      &__info {
        position: absolute;
        top: -11px;
        right: -11px;
        background: white;
        padding: 1px;
        border-radius: 50px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        color: red;
        font-size: 25px;
        font-weight: bolder;
        width: 26px;
        height: 26px;
      }
    }

    &__accept {
      min-width: 90%;
      margin-top: 1vh;
      margin-bottom: 5vh;

      &__slide {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: relative;

        $button-radius: 30px;
        $slider-width: 90%;
        $slider-height: 47px;

        height: 46px !important;
        width: 100% !important;

        &__background,
        &__background__isLoading,
        &__text,
        &__pulse {
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
        }

        &__background {
          width: 46px !important;
          height: 46px !important;
          border-radius: 50px !important;
        }

        &__background__isLoading,
        &__pulse {
          width: $slider-width;
          height: $slider-height;
          border-radius: $button-radius !important;
        }

        &__pulse {
          background-size: 250% !important;

          background: -webkit-gradient(
            linear,
            left top,
            right top,
            color-stop(0, transparent),
            color-stop(0.35, transparent),
            color-stop(0.5, white),
            color-stop(0.65, transparent),
            color-stop(1, transparent)
          );
          background: -moz-gradient(
            linear,
            left top,
            right top,
            color-stop(0, transparent),
            color-stop(0.35, transparent),
            color-stop(0.5, white),
            color-stop(0.65, transparent),
            color-stop(1, transparent)
          );
          background: gradient(
            linear,
            left top,
            right top,
            color-stop(0, transparent),
            color-stop(0.35, transparent),
            color-stop(0.5, white),
            color-stop(0.65, transparent),
            color-stop(1, transparent)
          );

          opacity: 0.35;
          filter: blur(8px);
          -webkit-filter: blur(8px);

          -webkit-animation: bg-pan-right 1.5s ease infinite;
          animation: bg-pan-right 1.5s ease infinite;
        }

        &__background {
          background: var(--Blue);

          width: 46px;
          height: 46px;

          display: flex;
          align-content: center;
          align-items: center;
          justify-content: center;

          &__inner {
            width: 40px;
            height: 40px;
            opacity: 0;

            position: absolute;
            right: 3px;

            border-radius: 50px;
            background: white;

            display: flex;
            align-content: center;
            align-items: center;
            justify-content: center;

            &__error-icon,
            &__success-icon {
              border-radius: 50px;

              width: 40px;
              height: 40px;

              display: block;
            }

            &__error-icon {
              opacity: 0;
              color: var(--Orange);
            }

            &__success-icon {
              opacity: 0;
              color: var(--GreenTint);
            }
          }
        }

        &__background__isLoading {
          background: -moz-linear-gradient(
            90deg,
            var(--yellow-core) 0%,
            var(--pink-core) 100%
          );
          background: -webkit-linear-gradient(
            90deg,
            var(--yellow-core) 0%,
            var(--pink-core) 100%
          );
          background: linear-gradient(
            90deg,
            var(--yellow-core) 0%,
            var(--pink-core) 100%
          );

          filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#FFC945", endColorstr="#FF6289", GradientType=1);

          display: flex;
          align-content: center;
          align-items: center;
          justify-content: center;

          &__inner {
            width: 40px;
            height: 40px;
            opacity: 0;

            position: absolute;
            right: 3px;

            border-radius: 50px;
            background: white;

            display: flex;
            align-content: center;
            align-items: center;
            justify-content: center;

            &__loading-icon {
              border-radius: 50px;

              width: 40px;
              height: 40px;

              display: block;

              background: url('../../../../../src/assets/img/loading.gif'),
                white;
              background-position: 50% 50% !important;

              background-repeat: no-repeat;
              background-position: 60% 50%;
              background-size: 24px;
            }
          }
        }

        &__background__isLoading__SameDay {
          background: -moz-radial-gradient(
            (100% 4181.78% at 100% 50%, #8fd0ff 0%, #37be6f 100%, #37be6f 100%)
          );
          background: -webkit-radial-gradient(
            (100% 4181.78% at 100% 50%, #8fd0ff 0%, #37be6f 100%, #37be6f 100%)
          );
          background: radial-gradient(
            (100% 4181.78% at 100% 50%, #8fd0ff 0%, #37be6f 100%, #37be6f 100%)
          );

          filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#8FD0FF", endColorstr="#37BE6F", GradientType=1);

          display: flex;
          align-content: center;
          align-items: center;
          justify-content: center;

          &__inner {
            width: 40px;
            height: 40px;
            opacity: 0;

            position: absolute;
            right: 3px;

            border-radius: 50px;
            background: white;

            display: flex;
            align-content: center;
            align-items: center;
            justify-content: center;

            &__loading-icon {
              border-radius: 50px;

              width: 40px;
              height: 40px;

              display: block;

              background: url('../../../../../src/assets/img/loading.gif'),
                white;
              background-position: 50% 50% !important;

              background-repeat: no-repeat;
              background-position: 60% 50%;
              background-size: 24px;
            }
          }
        }

        &__text {
          color: white;
          font-weight: 400;
          font-size: 20px;
          margin-top: 1px;
          width: 90%;
          text-align: center;
        }

        .range-pressed {
          transform: scale(1.1);
          width: 77.26% !important;
          cursor: -webkit-grabbing;
          cursor: -moz-grabbing;
        }

        &__slider {
          width: 80%;
          margin-top: 0px;

          --bar-background: transparent;
          --bar-background-active: transparent;
          --knob-size: 40px;
          --bar-height: 0px;

          --knob-background: no-repeat 60% 50%/60%
              url('../../../../../src/assets/img/chevron-forward-outline.svg'),
            white;

          transform-origin: 50% 50%;
          transform: scale(1);
          transition: transform ease-out 100ms;
          cursor: -webkit-grab;
          cursor: -moz-grab;

          display: flex;
          align-items: center;
        }
      }

      &__warning {
        color: var(--Red);
        display: flex;
        justify-content: center;
        align-items: center;
        align-content: center;

        font-size: 13px;
        font-weight: 500;

        margin-top: 8px;

        ion-icon {
          margin-top: 2px;
          margin-right: 6px;
        }

        span {
          b {
            font-weight: 900;
          }
        }
      }
    }

    .hasFinished {
      .content__accept__slide__pulse,
      .content__accept__slide__text {
        animation: disappear $finish-animation-length ease 1 forwards;
        -webkit-animation: disappear $finish-animation-length ease 1 forwards;
      }

      .content__accept__slide__background__isLoading {
        animation-name: condense, finish-background;
        animation-duration: $finish-animation-length;
        animation-iteration-count: 1;
        animation-fill-mode: forwards;

        -webkit-animation-name: condense, finish-background;
        -webkit-animation-duration: $finish-animation-length;
        -webkit-animation-iteration-count: 1;
        -webkit-animation-fill-mode: forwards;

        &__inner {
          animation-name: show-inner;
          animation-duration: 0.1s;
          animation-iteration-count: 1;
          animation-fill-mode: forwards;
          -webkit-animation-name: show-inner;
          -webkit-animation-duration: 0.1s;
          -webkit-animation-iteration-count: 1;
          -webkit-animation-fill-mode: forwards;

          &__loading-icon {
            animation-name: show-inner;
            animation-duration: 0.65s;
            animation-iteration-count: 1;
            animation-fill-mode: forwards;

            -webkit-animation-name: show-inner;
            -webkit-animation-duration: 0.65s;
            -webkit-animation-iteration-count: 1;
            -webkit-animation-fill-mode: forwards;
          }
        }
      }

      .content__accept__slide__slider {
        margin-left: 1px !important;

        animation-name: condense, finish-slider;
        animation-duration: $finish-animation-length, 0.5s;
        animation-iteration-count: 1;
        animation-fill-mode: forwards;

        -webkit-animation-name: condense, finish-slider;
        -webkit-animation-duration: $finish-animation-length, 0.5s;
        -webkit-animation-iteration-count: 1;
        -webkit-animation-fill-mode: forwards;
      }
    }

    .isSuccess,
    .isError {
      .content__accept__slide__background__inner {
        &__loading-icon {
          animation-name: hide-icon;
          animation-duration: $finish-animation-length;
          animation-iteration-count: 1;
          animation-fill-mode: forwards;

          -webkit-animation-name: hide-icon;
          -webkit-animation-duration: $finish-animation-length;
          -webkit-animation-iteration-count: 1;
          -webkit-animation-fill-mode: forwards;
        }
      }
    }

    .isSuccess {
      animation-name: finish-background-success;
      animation-duration: $finish-animation-length;
      animation-iteration-count: 1;
      animation-fill-mode: forwards;

      -webkit-animation-name: finish-background-success;
      -webkit-animation-duration: $finish-animation-length;
      -webkit-animation-iteration-count: 1;
      -webkit-animation-fill-mode: forwards;

      .content__accept__slide__background__inner {
        opacity: 1;

        &__success-icon {
          animation-name: show-icon;
          animation-duration: $finish-animation-length;
          animation-iteration-count: 1;
          animation-fill-mode: forwards;

          -webkit-animation-name: show-icon;
          -webkit-animation-duration: $finish-animation-length;
          -webkit-animation-iteration-count: 1;
          -webkit-animation-fill-mode: forwards;
        }
      }
    }

    .isError {
      animation-name: finish-background-error;
      animation-duration: $finish-animation-length;
      animation-iteration-count: 1;
      animation-fill-mode: forwards;

      -webkit-animation-name: finish-background-error;
      -webkit-animation-duration: $finish-animation-length;
      -webkit-animation-iteration-count: 1;
      -webkit-animation-fill-mode: forwards;

      .content__accept__slide__background__inner {
        opacity: 1;

        &__error-icon {
          animation-name: show-icon;
          animation-duration: $finish-animation-length;
          animation-iteration-count: 1;
          animation-fill-mode: forwards;

          -webkit-animation-name: show-icon;
          -webkit-animation-duration: $finish-animation-length;
          -webkit-animation-iteration-count: 1;
          -webkit-animation-fill-mode: forwards;
        }
      }
    }
  }

  .WarnDiv {
    border-top: solid 4px red;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    text-align: center;
    position: relative;
    width: 90%;
    max-width: 400px;
    margin-top: 2vh;
    margin-bottom: 0.5vh;

    ion-icon {
      position: absolute;
      top: -10px;
      right: -10px;
      display: block;
      height: 20px;
      width: 20px;
      background: white;
      border: solid 2px red;
      border-radius: 50px;
      color: red;
    }

    h1 {
      font-size: 17px;
      font-weight: 700;
      margin: 8px 0px;
      color: var(--BlueGray5);
    }

    label {
      font-size: 13px;
      font-weight: 500;
      font-variant: var(--BlueGray4);
      margin-bottom: 10px;
      display: block;

      ion-checkbox {
        margin-right: 5px;
        --size: 17px;
        position: relative;
        bottom: -3px;
      }
    }
  }

  .InformDiv {
    border-top: solid 4px var(--Blue);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    text-align: center;
    position: relative;
    width: 90%;
    max-width: 400px;
    margin-top: 15px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    h1 {
      font-size: 16px;
      font-weight: 700;
      margin: 8px 0px;
      color: var(--BlueGray5);
      margin-left: 20px;
    }

    p {
      font-size: 13px;
      font-weight: 500;
      color: var(--BlueGray5);
      margin-top: 0px;
      margin-bottom: 10px;
      margin-left: 20px;
      display: block;
      text-align: left;
      width: 85%;
    }

    label {
      font-size: 13px;
      font-weight: 500;
      font-variant: var(--BlueGray4);
      margin-bottom: 10px;
      display: block;

      ion-checkbox {
        margin-right: 5px;
        --size: 17px;
        position: relative;
        bottom: -3px;
      }
    }
  }

  #EstAgreement {
    position: absolute;
    background: var(--BlueGray5);
    z-index: 20;
    width: 330px;
    box-shadow: 0 3px 20px rgba(0, 0, 0, 0.4);
    left: 30px;
    top: 190px;
    color: white;
    padding: 15px;
    animation: ZoomIn;
    animation-duration: 0.3s;
    animation-delay: 0.5s;
    animation-iteration-count: 1;
    animation-timing-function: ease;
    animation-fill-mode: forwards;

    transform-origin: top;
    transform: scale(0);
    outline: solid 0px rgba(0, 0, 0, 0.15);

    h1 {
      font-size: 20px;
      text-align: center;
      font-weight: 700;
      margin: 0;
    }

    p {
      font-size: 13px;
      text-align: justify;
    }

    #Arrow {
      position: absolute;
      top: -20px;
      border: solid 10px transparent;
      border-bottom-color: var(--BlueGray5);
      left: 90px;
    }

    #understand-button {
      color: white;
      --color: white;
      --border-color: white;
    }
  }

  @keyframes ZoomIn {
    from {
      transform: scale(0);
    }

    to {
      transform: scale(1);
      outline-width: 250px;
    }
  }

  .info-icon {
    margin-right: 2%;
    margin-top: 6px;
  }

  .same-day-service-pill {
    background-color: #22ecab;
    border: 1px solid #fff;
    width: 114px;
    height: 32px;
    border-radius: 15px;
    text-align: center;
    padding-top: 6px;
    padding-left: 3px;
    font-weight: 600;
    font-size: 13px;
    color: #000;
    top: 15px;
    margin-right: 5vw;
    box-shadow: 2px 2px 3px 0px #d9d9d9;
    -webkit-box-shadow: 2px 2px 3px 0px #d9d9d9;
    -moz-box-shadow: 2px 2px 3px 0px #d9d9d9;

    span.same-day-icon {
      background-color: #fff;
      border-radius: 100%;
      padding: 2px;
    }
  }

  .double-points-pill {
    background-color: #f9b011;
    border: 1px solid #fff;
    height: 30px;
    border-radius: 15px;
    text-align: center;
    padding: 6px 10px 6px 3px;
    margin-right: 5px;
    font-weight: 600;
    font-size: 13px;
    flex-shrink: 0;
    z-index: 2;
    box-shadow: 2px 2px 3px 0px #d9d9d9;
    -webkit-box-shadow: 2px 2px 3px 0px #d9d9d9;
    -moz-box-shadow: 2px 2px 3px 0px #d9d9d9;
    color: #000;

    span {
      background-color: #fff;
      border-radius: 100%;
      padding: 3px;
      margin-left: 2px;
      margin-right: 1px;
      font-size: 12px;
    }
  }

  .content__details__left__estimate {
    margin-left: 10px;
  }

  .content__details__same__day__service__estimate {
    color: #909090;
    text-align: center;
    font-family: 'Fakt-Normal' sans-serif;
    font-size: 11px;
    font-style: normal;
    font-weight: 700;
    line-height: 20px; /* 200% */
  }

  .pill-container {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    margin-top: 10px;
  }

  .same__day__info {
    display: inline-flex;
  }

  .same__day__service__deadlines {
    color: #019d8a;
  }

  .same__day__checkbox {
    ion-checkbox {
      vertical-align: -4px;
    }

    span {
      padding-left: 5px;
    }

    font-size: 12px;
    font-weight: 600;
    margin-top: 1vh;
    text-align: center;
  }

  #ccIcon {
    font-family: 'Material Icons Outlined';
  }

  .material-icons-outlined {
    font-family: 'Material Icons Outlined' !important;
    padding-right: 10px;
  }

  .material-symbols-outlined {
    font-family: 'Material Symbols Outlined' !important;
    padding-right: 10px;
  }

  .specialInstructionsList {
    width: 90%;
    align-self: flex-start;
  }

  .list-helper-text {
    margin-left: 10%;
    padding: 0px;
    font-size: 13px;
  }

  .specialInstructions {
    font-size: small;
  }

  .overflow-scroll {
    overflow-y: scroll;
  }

  .height-35 {
    height: 35px;
  }

  ion-label {
    height: 22px;
  }

  .margin-bottom-overflow {
    margin-bottom: 5vh;
  }

  #OverweightAlert {
    width: 95%;
    margin-top: 1rem;
    font-size: 1em;
    background-color: rgba(0, 0, 0, 0.075);
    border-radius: 8px;
    overflow: hidden;

    p {
      display: flex;
      align-items: center;
      font-weight: 475;

      span {
        margin-left: 11px;
        margin-right: 5px;
      }

      i {
        border-radius: 100%;
        background-color: #fff;
        color: rgb(255, 199, 58);
        // height: 18px;
        margin-left: 12px;
      }
    }
  }
}
