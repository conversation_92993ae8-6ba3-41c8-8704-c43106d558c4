<ion-content>
  <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
    <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
    <path
      class="checkmark__check"
      fill="none"
      d="M14.1 27.2l7.1 7.2 16.7-16.8"
    />
  </svg>

  <h1>Order Accepted!</h1>

  <ion-item>
    <ion-label position="floating">Enter your estimated arrival time</ion-label>
    <ion-input
      autocapitalize="off"
      [(ngModel)]="ETAText"
      (ngModelChange)="TextChange($event)"
    ></ion-input>
  </ion-item>

  <div
    [hidden]="ETAText === ''"
    #MessageDiv
    [innerHTML]="GetGreetingText()"
  ></div>

  <ion-button
    id="send-message-button"
    [disabled]="ETAText === ''"
    size="large"
    expand="block"
    (click)="SendMessage()"
    >Send Message</ion-button
  >
</ion-content>
