import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { App } from '@capacitor/app';
import { PluginListenerHandle } from '@capacitor/core';
import { <PERSON>ert<PERSON>ontroller } from '@ionic/angular';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { firstValueFrom, Subscription } from 'rxjs';
import {
  AvailableOrder,
  AvailableOrderData,
} from 'src/app/_interfaces/available-orders-interfaces';
import { SudsterData } from 'src/app/_interfaces/sudster-data.interface';
import { StatsigFactoryService } from 'src/app/_services/statsig-factory.service';
import { getDistanceMi } from 'src/app/_utils/geolocation.util';
import { GetNewOrdersService } from 'src/app/service/new-order-listener';
import { environment } from 'src/environments/environment';
import { AuthidService } from '../../../_services/authid.service';
import { StatsigService } from '../../../_services/statsig.service';
import { ContractComponent, OrderType } from '../contract/contract.component';
import { ContractService } from '../contract/contract.service';
import { MoreInfoAlert } from '../new-orders/no-orders-info';
import { ORDER_CARD_ADAPTER } from '../order-card/order-card.adapter';
import { OrderCardData } from '../order-card/order-card.model';
import { OrderCardService } from '../order-card/order-card.service';
import { OrderContractService } from '../order-contract-modal/order-contract.service';
import { AvailableOrderAdapter } from './adapters/available-order.adapter';

export const calcBreakpoint = (minValue: number): number => {
  const value = Math.ceil((minValue / window.innerHeight) * 20) / 20;
  return Math.min(value, 1);
};

@UntilDestroy()
@Component({
  selector: 'app-available-orders',
  templateUrl: './available-orders.component.html',
  styleUrls: ['./available-orders.component.scss'],
  providers: [
    { provide: ORDER_CARD_ADAPTER, useClass: AvailableOrderAdapter },
    { provide: OrderCardService, useClass: OrderCardService },
  ],
})
export class AvailableOrdersComponent implements OnInit, OnDestroy {
  public orderCardDataList: OrderCardData[] = [];
  public fullOrderDataArray: Array<AvailableOrder> = [];
  public outsideAreaCounter = 0;
  public sudsterFirstOrder = false;
  public loadingOrders = true;
  public userID = this.authID.getID();
  public flexibleExtension = false;
  public sudster: SudsterData;
  public isExpressServiceEnabled: boolean;

  private currentOrdersSubscriptions: Subscription;
  private stateListener: PluginListenerHandle;
  private statsigService: StatsigService;

  @ViewChild('ScrollDiv') private scrollDiv;
  @ViewChild(ContractComponent) contractComponent: ContractComponent;

  @Output() goToActive = new EventEmitter();
  @Output() availableCount = new EventEmitter();

  @Input() availableCountNum: number;
  @Input() idVerification: string;
  @Input() checkrVerification: string;

  constructor(
    private getNewOrders: GetNewOrdersService,
    private authID: AuthidService,
    private alertController: AlertController,
    private orderCardService: OrderCardService<{
      availableOrder: AvailableOrder;
      sudsterData: SudsterData;
      expressService: boolean;
    }>,
    private orderContractService: OrderContractService,
    private contractService: ContractService,
    private statsigFactoryService: StatsigFactoryService
  ) {
    this.stateListener = App.addListener('appStateChange', (data) => {
      if (data.isActive) {
        this.loadOrderList();
      } else if (this.currentOrdersSubscriptions) {
        this.currentOrdersSubscriptions.unsubscribe();
      }
    });
    this.statsigService = this.statsigFactoryService.getInstance();
  }

  async ngOnInit() {
    this.isExpressServiceEnabled = await firstValueFrom(
      this.statsigService.checkGate(environment.statsig.flags.ExpressService)
    );

    this.loadOrderList();
  }

  ngOnDestroy(): void {
    if (this.stateListener) {
      this.stateListener.remove();
    }
  }

  scrollRight() {
    this.scrollDiv.nativeElement.scrollLeft += 50;
  }

  async refreshOrderList(event) {
    const startTime = new Date().getTime();

    await this.handleOrderLoading();

    if (event != null && event?.target) {
      let waitTime = 0;
      if (new Date().getTime() - 1000 < startTime) {
        waitTime = 1000;
      }
      setTimeout(() => {
        if (typeof event.target?.complete === 'function') {
          event.target.complete();
        }
      }, waitTime);
    }
  }

  private async handleOrderLoading() {
    this.loadingOrders = true;

    if (!this.currentOrdersSubscriptions?.closed) {
      this.currentOrdersSubscriptions?.unsubscribe();
    }

    this.currentOrdersSubscriptions = this.getNewOrders.currentOrders
      .pipe(untilDestroyed(this))
      .subscribe(
        (data: { AvailableOrders: AvailableOrder[]; Sudster: SudsterData }) => {
          const availableOrders = data.AvailableOrders;

          if (!availableOrders || !availableOrders.length) {
            this.loadingOrders = false;

            this.availableCount.emit(0);
            return;
          }

          this.sudster = data.Sudster;

          if (this.sudster != null) {
            const sudsterRadius = this.sudster.WorkRadius || 20;

            this.sudsterFirstOrder = !this.sudster.OrderCount;
            this.outsideAreaCounter = 0;
            this.fullOrderDataArray = [];

            availableOrders.forEach(async (doc: AvailableOrder) => {
              const order = doc.Order as AvailableOrderData;
              const sudsterLat = this.sudster.WorkAreaCenter.latitude;
              const sudsterLng = this.sudster.WorkAreaCenter.longitude;
              const lat = order.Address.Lat;
              const lng = order.Address.Long;

              if (
                getDistanceMi(sudsterLat, sudsterLng, lat, lng) <= sudsterRadius
              ) {
                this.fullOrderDataArray.push(doc);
              } else {
                this.outsideAreaCounter++;
              }
            });

            this.orderCardDataList = this.orderCardService.getCardDataList(
              this.fullOrderDataArray.map((order) => {
                return {
                  availableOrder: order,
                  sudsterData: this.sudster,
                  expressService: this.isExpressServiceEnabled,
                };
              })
            );

            this.orderCardDataList.forEach((orderCardData) => {
              const declineAction = orderCardData.getAction('decline');
              const acceptAction = orderCardData.getAction('accept');

              if (declineAction) {
                declineAction.actionHandler = (e: Event) => {
                  e.stopPropagation();
                  this.declineContract(orderCardData.id);
                };
              }

              if (acceptAction) {
                acceptAction.actionHandler = (e: Event) => {
                  e.stopPropagation();
                  this.openOrderContract(orderCardData.id);
                };
              }
            });
          }

          this.availableCount.emit(this.fullOrderDataArray.length);
          this.loadingOrders = false;
        }
      );

    await this.getNewOrders.LoadOrderList();
  }

  declineContract(id) {
    const order: AvailableOrder = this.getActiveOrderById(id);
    const orderItem: AvailableOrderData = order.Order;
    orderItem.OrderNumber = order.OrderNumber;

    if (!this.contractService.useNewContractComponentFlag()) {
      this.orderContractService.preferredDeclineAlert(
        orderItem,
        this.sudster,
        this.sudsterFirstOrder,
        this.goToActive,
        this.idVerification,
        this.checkrVerification
      );

      return;
    }

    this.contractComponent.preferredDeclineAlert(order, this.sudster);
  }

  openOrderContract(id: string) {
    const order: AvailableOrder = this.getActiveOrderById(id);
    const orderItem: AvailableOrderData = order.Order;
    orderItem.OrderNumber = order.OrderNumber;

    if (!this.contractService.useNewContractComponentFlag()) {
      this.orderContractService.openOrderContractFromAvailableOrderData(
        orderItem,
        this.sudster,
        this.sudsterFirstOrder,
        this.goToActive,
        this.idVerification,
        this.checkrVerification
      );

      return;
    }

    this.contractComponent.openContract(
      this.sudster,
      order,
      OrderType.Available
    );
  }

  async loadOrderList() {
    await this.handleOrderLoading();
  }

  noOrdersInfoAlert() {
    this.presentAlert(MoreInfoAlert.title, MoreInfoAlert.body).then();
  }

  async presentAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK'],
    });
    await alert.present();
  }

  private getActiveOrderById(id: string) {
    const order: AvailableOrder = this.fullOrderDataArray.find(
      (o) => o.OrderNumber === id
    );

    return order;
  }
}
