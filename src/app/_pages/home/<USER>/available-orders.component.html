<ion-refresher slot="fixed" (ionRefresh)="refreshOrderList($event)">
  <ion-refresher-content></ion-refresher-content>
</ion-refresher>

<ng-container *ngIf="orderCardDataList.length > 0 && !loadingOrders">
  <div
    id="new-order-component"
    *ngFor="let cardData of orderCardDataList; let i = index"
    class="order-container"
  >
    <app-order-card
      [data]="cardData"
      (actionHandler)="openOrderContract($event)"
    ></app-order-card>
  </div>
</ng-container>

<p id="noOrders" *ngIf="orderCardDataList.length === 0 && !loadingOrders">
  <b>No Available Orders.</b> Orders are assigned on a first-come first-serve
  basis. To increase your chances of getting orders, accept them immediately
  upon getting a New Order Notice.<br />
  <ion-button
    id="more-info-button"
    size="small"
    fill="clear"
    (click)="noOrdersInfoAlert()"
    >More Info</ion-button
  >
</p>

<div id="loadingLabel" *ngIf="loadingOrders && !orderCardDataList.length">
  Loading...
</div>

<div id="outsideArea" *ngIf="outsideAreaCounter > 0">
  There are <b>({{ OutsideAreaCounter }})</b> available orders outside your work
  area. Consider widening your work area in settings.
</div>

<app-contract [goToActive]="goToActive" #contract></app-contract>
