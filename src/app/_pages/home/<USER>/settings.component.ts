import { Component, OnInit, OnDestroy } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/auth';
import { AngularFirestore } from '@angular/fire/firestore';
import { PopoverController } from '@ionic/angular';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { SudsterData } from 'src/app/_interfaces/sudster-data.interface';
import { AuthidService } from 'src/app/_services/authid.service';
import { trackEvent } from 'src/app/_utils/track-event';

@UntilDestroy()
@Component({
  selector: 'app-settings',
  templateUrl: './settings.component.html',
  styleUrls: ['./settings.component.scss'],
})
export class SettingsComponent implements OnInit, OnDestroy {
  AlertsOn;
  WorkRadius;
  BlockedOrders;

  private originalAlertsOnValue;
  private originalWorkRadius;

  loaded = false;

  UserID = this.AuthID.getID();

  constructor(
    public firestore: AngularFirestore,
    public afAuth: AngularFireAuth,
    private AuthID: AuthidService,
    public popoverController: PopoverController
  ) {}

  ngOnInit() {
    this.firestore
      .doc<SudsterData>(`Sudsters/${this.UserID}`)
      .valueChanges()
      .pipe(untilDestroyed(this))
      .subscribe((doc) => {
        this.originalAlertsOnValue = doc.AlertsOnLp || false;
        this.originalWorkRadius = doc.WorkRadius;
        this.loaded = true;
        this.AlertsOn = doc.AlertsOnLp || false;
        this.WorkRadius = doc.WorkRadius;
        this.BlockedOrders = doc.BlockOrders;
      });
  }

  onWorkRadiusChanged(evt) {
    if (!evt.detail.value) {
      return;
    }
    this.WorkRadius = evt.detail.value;
  }

  async ngOnDestroy() {
    if (isNaN(this.WorkRadius) || !this.WorkRadius) {
      this.WorkRadius = 20;
    }
    if (this.WorkRadius >= 50) {
      this.WorkRadius = 50;
    }

    if (this.originalWorkRadius !== this.WorkRadius) {
      trackEvent({
        eventData: {
          event: 'LPWorkAreaAdjusted',
          userId: this.UserID,
          oldWorkArea: {
            isEnabled: false,
            radius: this.originalWorkRadius,
          },
          newWorkArea: {
            isEnabled: true,
            radius: this.WorkRadius,
          },
        },
      });
    }

    if (this.originalAlertsOnValue !== this.AlertsOn) {
      trackEvent({
        eventData: {
          event: 'LPOrderAccessChanged',
          userId: this.UserID,
          source: 'Laundry Pro',
          reasonText: 'Laundry pro toggled alerts input',
        },
      });
    }

    if (
      this.originalAlertsOnValue !== this.AlertsOn ||
      this.originalWorkRadius !== this.WorkRadius
    ) {
      await this.firestore.doc(`Sudsters/${this.UserID}`).update({
        AlertsOnLp: this.AlertsOn,
        WorkRadius: parseInt(this.WorkRadius),
      });
    }
  }

  closeMenu() {
    this.popoverController.dismiss();
  }
}
