import { Inject, Injectable } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON> } from '@ngneat/until-destroy';
import { IOrderCardAdapter, ORDER_CARD_ADAPTER } from './order-card.adapter';
import { OrderCardData } from './order-card.model';

@UntilDestroy()
@Injectable({
  providedIn: 'root',
})
export class OrderCardService<T = unknown> {
  constructor(
    @Inject(ORDER_CARD_ADAPTER) private adapter: IOrderCardAdapter<T>
  ) {}

  getCardDataList(items: T[]): OrderCardData[] {
    return items.map((item) => this.adapter.transform(item));
  }
}
