<ion-content [hidden]="!loaded">
  <ion-item>
    <ion-label style="white-space: nowrap; flex: 1">
      Receive New Order Alerts
    </ion-label>
    <ion-toggle
      (ionChange)="AlertsOn = $event.detail.checked"
      [checked]="AlertsOn"
      style="margin-left: auto"
    ></ion-toggle>
  </ion-item>
  <ion-item>
    <ion-label style="white-space: nowrap; flex: 1">
      Work Radius (50-mile max)
    </ion-label>
    <ion-input
      style="text-align: center; max-width: 80px"
      inputmode="numeric"
      placeholder="20"
      type="number"
      maxlength="2"
      minlength="2"
      (ionChange)="onWorkRadiusChanged($event)"
      [value]="WorkRadius"
    ></ion-input>
  </ion-item>
  <ion-item button (click)="closeMenu()" [routerLink]="['/account']">
    <ion-label> Update Home Address </ion-label>
    <ion-icon name="location-outline" style="margin-right: 13px"></ion-icon>
  </ion-item>
</ion-content>
