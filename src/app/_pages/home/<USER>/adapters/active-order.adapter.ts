import { Injectable } from '@angular/core';

import moment from 'moment';
import {
  TextData,
  TextStyle,
} from 'src/app/shared/components/icon-text/icon-text.model';
import {
  OrderData,
  Stages,
} from '../../../../_interfaces/order-data.interface';
import { LocationService } from '../../../../_services/location.service';
import {
  formatDeadline,
  getDeadline,
  getStage,
} from '../../../../_utils/order-utils';
import {
  CardItemData,
  CardItemPosition,
  CardItemType,
} from '../../order-card/card-item/card-item.model';
import { IOrderCardAdapter } from '../../order-card/order-card.adapter';
import { OrderAction, OrderCardData } from '../../order-card/order-card.model';

enum Icons {
  Dollar = 'custom_dollar',
  Location = 'location',
  Bolt = 'bolt',
  Alarm = 'alarm',
  AlarmFilled = 'alarm_filled',
  WavingHand = 'waving_hand_filled',
  Comment = 'comment_custom',
  Car = 'directions_car',
}

enum Colors {
  LimeGreen = '#A9E6C2',
  SalmonPink = '#FFCEBE',
  PaleBlue = '#B0DEFF',
  Yellow = '#FFE299',
  Pink = '#FF9CB5',
}

export enum Detergent {
  Hypoallergenic = 'Hypoallergenic',
  UnscentedHypoallergenic = 'Unscented Hypoallergenic',
  ClassicScented = 'Classic Scented',
  Provided = 'Detergent Provided',
}

@Injectable()
export class ActiveOrderAdapter
  implements IOrderCardAdapter<{ order: OrderData; expressService: boolean }>
{
  public late: boolean = false;
  public isExpressServiceEnabled: boolean;

  constructor(private locationService: LocationService) {}

  transform(data: {
    order: OrderData;
    expressService: boolean;
  }): OrderCardData {
    const order = data.order;
    const now = moment.tz(order.Timezone);
    const date = getDeadline(order);
    const isLate = date.isBefore(now);

    this.isExpressServiceEnabled = data.expressService;

    const cardItems: CardItemData[] = [
      ...this.getOrderItems(order),
      ...this.getOrderTags(order, isLate),
    ];

    const customerName = `${order.CustomerFirstName} ${
      order.CustomerLastName || ''
    }`;

    return new OrderCardData({
      id: order.OrderNumber,
      title: customerName,
      items: cardItems,
      actions: this.getOrderActions(order),
      borderAlert: isLate,
    });
  }

  private getOrderItems(order: OrderData): CardItemData[] {
    const deadline = formatDeadline(getDeadline(order));
    const locationName = this.locationService.getAreaName(order.Address);
    const estimatedDriveTime = order.EstimatedDriveTime || 'N/A';
    const currentStage = getStage(order);

    const bagCount =
      order.OrderStatusNumber < 2
        ? order.OrderSize
        : order.StatusHistoryInfo.Done?.BagCount ||
          order.StatusHistoryInfo.Pickup?.BagCount;

    const largeItems =
      order.OrderStatusNumber < 3
        ? order.LargeItems.Count
        : order.StatusHistoryInfo.Done.PuffyItems;

    let bagSize =
      order.BagSize === 'oversized'
        ? 'Large'
        : this.capitalizeFirstLetter(order.BagSize);

    let showBagSize = true;

    if (currentStage == Stages.Deliver) {
      bagSize = '';
    }

    if (currentStage == Stages.Launder || currentStage == Stages.Weigh) {
      showBagSize = false;
    }

    const orderItemMappings = [
      {
        condition: deadline,
        icon: Icons.Alarm,
        texts: [
          {
            content:
              (currentStage == Stages.PickUp ? 'Pickup by ' : 'Deliver by ') +
              deadline,
          },
          {
            content: showBagSize
              ? `${bagCount} ${bagSize} Bag${bagCount > 1 ? 's' : ''} ${
                  largeItems ? '+ ' + largeItems + ' Oversized' : ''
                }`
              : null,
          },
        ],
      },
      {
        condition: locationName,
        icon: Icons.Location,
        texts: [
          {
            content: `${estimatedDriveTime} min drive | ${locationName}`,
            style: TextStyle.Default,
          },
        ],
      },
    ];

    return orderItemMappings
      .filter((mapping) => mapping.condition)
      .map(({ icon, texts }) => ({
        type: CardItemType.Default,
        icon,
        texts: texts as TextData[],
      }));
  }

  private getOrderTags(order: OrderData, isLate: boolean): CardItemData[] {
    const stage = getStage(order);

    const tagMappings = [
      {
        condition: isLate,
        icon: Icons.AlarmFilled,
        tag: 'Late',
        color: Colors.Pink,
        position: CardItemPosition.Header,
      },
      {
        condition:
          order.SameDayService && this.isExpressServiceEnabled && !isLate,
        icon: Icons.Bolt,
        tag: 'Express',
        color: Colors.LimeGreen,
        position: CardItemPosition.Header,
      },
      {
        condition: order.FirstOrder,
        icon: Icons.WavingHand,
        tag: 'New to Poplin',
        color: Colors.Yellow,
        position: CardItemPosition.Footer,
      },
      {
        condition: stage !== Stages.DoNotDeliver,
        icon: null,
        tag: stage,
        color: Colors.PaleBlue,
        position: CardItemPosition.Footer,
      },
      {
        condition:
          (order.Preferences.Detergent === Detergent.Hypoallergenic ||
            order.Preferences.Detergent ===
              Detergent.UnscentedHypoallergenic) &&
          getStage(order) === Stages.Launder,
        icon: null,
        tag: 'Hypo',
        color: Colors.PaleBlue,
        position: CardItemPosition.Footer,
      },
      {
        condition: stage === Stages.DoNotDeliver,
        icon: Icons.Car,
        tag: stage,
        color: Colors.Pink,
        position: CardItemPosition.Footer,
      },
    ];

    return tagMappings
      .filter((mapping) => mapping.condition)
      .map(({ icon, tag, color, position }) => ({
        icon,
        tag,
        color,
        type: CardItemType.Tag,
        position,
        texts: [{ content: tag }],
      }));
  }

  private getOrderActions(order: OrderData): OrderAction[] {
    return [
      {
        id: 'contract',
        fill: 'outline',
        label: 'Contract',
      },
      {
        id: 'chat',
        icon: Icons.Comment,
        label: '',
        pulse: order.SudsterMessageBadge > 0,
      },
    ];
  }

  private capitalizeFirstLetter(val: string): string {
    return String(val).charAt(0).toUpperCase() + String(val).slice(1);
  }
}
