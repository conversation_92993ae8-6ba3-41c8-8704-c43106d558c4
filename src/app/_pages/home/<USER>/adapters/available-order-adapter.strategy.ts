import { Injectable } from '@angular/core';
import { Timestamp } from '@firebase/firestore-types';
import moment from 'moment';
import { SudsterData } from 'src/app/_interfaces/sudster-data.interface';
import { AuthidService } from 'src/app/_services/authid.service';
import { capitalizeFirstLetter } from 'src/app/_utils/utils';
import { OrderContractData } from 'src/app/shared/components/order-contract/order-contract.model';
import { AvailableOrder } from '../../../../_interfaces/available-orders-interfaces';
import { Detergent } from '../../current-orders/adapters/active-order.adapter';
import { ContractService } from '../contract.service';
import { OrderAdapterStrategy, RouteDelivery } from './order-adapter-strategy';

@Injectable()
export class AvailableOrderAdapterStrategy implements OrderAdapterStrategy {
  private userId: string;

  constructor(
    private contractService: ContractService,
    private authService: AuthidService
  ) {
    this.userId = this.authService.getID();
  }

  transformOrder(
    order: AvailableOrder,
    sudster: SudsterData,
    direction: Promise<RouteDelivery>,
    flexibleExtension: boolean
  ): OrderContractData {
    const lpFirstOrder = sudster.OrderCount === 0;
    const isHypoallergenic =
      order.Order.Detergent === Detergent.Hypoallergenic ||
      order.Order.Detergent === Detergent.UnscentedHypoallergenic;
    const addressObj = order.Order.Address;

    const pickup = this.getPickupDeadline(
      order,
      lpFirstOrder,
      flexibleExtension,
      order.Order.ExtraDeadlineTime
    );

    const delivery = this.getDeliveryDeadline(
      order,
      lpFirstOrder,
      flexibleExtension,
      order.Order.ExtraDeadlineTime
    );

    const orderData = {
      orderNumber: order.Order.OrderId,
      customerName: `${order.Order.CustomerFirstName} ${order.Order.CustomerLastName}.`,
      isBusinessCustomer: order.Order.IsCommercial,
      canProposeRate: this.contractService.canProposeRate(
        order.Order.Address.State
      ),
      hasPendingRateProposal: false,
      isPreferred: this.isPreferred(order),
      isExpress: this.isSameDay(order),
      hasBoost: order.Order.Bonus > 0,
      boostAmount: order.Order.Bonus,
      isNewCustomer: order.Order.IsCustomerFirstOrder,
      timing: {
        pickup: {
          timeMessage: pickup.message,
          time: pickup.time,
          place: '',
          instructions: '',
        },
        delivery: {
          timeMessage: delivery.message,
          time: delivery.time,
        },
      },
      price: {
        amount: order.Order.EstimatedEarnings || 0,
        weight: this.contractService.getSudshareRate(
          order.Order.PriceSchedule,
          this.isSameDay(order)
        ),
        bags: {
          bagSize: capitalizeFirstLetter(
            order.Order.BagSize === 'oversized' ? 'Large' : order.Order.BagSize
          ),
          quantity: order.Order.BagCount,
          oversized: order.LargeItems?.Count || 0,
        },
      },
      address: {
        city: addressObj.City || '',
        state: addressObj.State || '',
        zipCode: addressObj.ZipCode || '',
        distance: {
          miles: '0',
          estimatedMinutes: '0',
        },
      },
      isHypoallergenic: isHypoallergenic,
      isHangDry: !!order.Order.Preferences?.HangDry,
      isDelicate: !!order.Order.Preferences?.Delicates,
      hasSpecialNotes: !!order.Order.Preferences?.Instructions,
      specialNotes: order.Order.Preferences?.Instructions || '',
      acknowledgements: {
        expressOrder: this.isSameDay(order),
        hypoallergenicDetergent: isHypoallergenic,
        deadline: this.contractService.hasDeadLine(order.Order.Address.State),
      },
    };

    direction.then((dir) => {
      orderData.address.distance.miles = dir.distance;
      orderData.address.distance.estimatedMinutes = dir.expectedTravelTime;
    });

    return orderData;
  }

  async getCoordinates(order: AvailableOrder): Promise<[number, number]> {
    return [order.Order.Address.Lat, order.Order.Address.Long];
  }

  getPickupDeadline(
    order: AvailableOrder,
    lpFirstOrder: boolean,
    flexibleExtension: boolean,
    extraDeadlineTime = 0
  ): { message: string; time: string } {
    const pickupTime = moment.tz(
      order.Order.PickupTimestamp,
      order.Order.Timezone
    );

    return {
      message: `Pickup ${this.getDeadline(
        order.Order.PickupTimestamp,
        order.Order.CreatedTimestamp,
        order.Order.Timezone,
        order.Order.IsCustomerFirstOrder,
        this.isSameDay(order),
        lpFirstOrder,
        flexibleExtension,
        'pickup',
        extraDeadlineTime
      )}`,
      time: pickupTime.format('ha'),
    };
  }

  getDeliveryDeadline(
    order: AvailableOrder,
    lpFirstOrder: boolean,
    flexibleExtension: boolean,
    extraDeadlineTime = 0
  ): { message: string; time: string } {
    const deliveryTime = moment.tz(
      order.Order.DeliveryTimestamp,
      order.Order.Timezone
    );

    return {
      message: `Delivery ${this.getDeadline(
        order.Order.DeliveryTimestamp,
        order.Order.CreatedTimestamp,
        order.Order.Timezone,
        order.Order.IsCustomerFirstOrder,
        this.isSameDay(order),
        lpFirstOrder,
        flexibleExtension,
        'delivery',
        extraDeadlineTime
      )}`,
      time: deliveryTime.format('ha'),
    };
  }

  protected getDeadline(
    timestamp: Timestamp,
    createdDate: Timestamp,
    tz: string,
    customerFirstOrder: boolean,
    isSameDay: boolean,
    lpFirstOrder: boolean,
    flexibleExtension: boolean,
    type: string,
    extraDeadlineTime = 0
  ): string {
    const target = this.buildTargetMoment(timestamp, flexibleExtension, tz);

    if (lpFirstOrder && !isSameDay && customerFirstOrder) {
      const firstOrder = this.firstOrderPickup(timestamp, target, tz);

      return firstOrder;
    }

    if (isSameDay) {
      const sameDay = this.sameDayCutoff(
        createdDate,
        target,
        type,
        tz,
        extraDeadlineTime
      );

      return sameDay;
    }

    if (extraDeadlineTime) {
      target.add(extraDeadlineTime, 'hours');
    }

    return this.contractService.formatDeadline(target);
  }

  private buildTargetMoment(
    timestamp: Timestamp,
    flex: boolean,
    tz: string
  ): moment.Moment {
    const date = moment.tz(timestamp, tz);

    return flex ? date.add(1, 'day') : date;
  }

  private firstOrderPickup(
    timestamp: Timestamp,
    target: moment.Moment,
    tz: string
  ): string {
    const placed = moment.tz(timestamp.toMillis(), tz).startOf('day');

    if (placed.isSame(target, 'day')) {
      const cutoff = moment().tz(tz).startOf('hour').add(4, 'hours');

      if (cutoff.hour() > 19) {
        cutoff.hour(19);
      }

      return this.contractService.formatDeadline(cutoff);
    }

    target.hour(11);
    return this.contractService.formatDeadline(target);
  }

  private sameDayCutoff(
    timestamp: Timestamp,
    target: moment.Moment,
    type: string,
    tz: string,
    extraDeadlineTime = 0
  ): string | null {
    const now = moment().tz(tz);
    const placed = moment.tz(timestamp.toMillis(), tz);
    const placedHour = placed.hour();

    let cutoffHour = type == 'pickup' ? 14 : 20;

    if (extraDeadlineTime) {
      target.add(extraDeadlineTime, 'hours');
      cutoffHour += extraDeadlineTime;
    }

    if (!placed.isSame(now, 'day')) {
      return this.contractService.formatDeadline(target);
    }

    if (placedHour < 10) {
      return this.contractService.formatDeadline(now.clone().hour(cutoffHour));
    }

    if (placedHour >= 15) {
      return this.contractService.formatDeadline(
        now.clone().add(1, 'day').hour(cutoffHour)
      );
    }

    return this.contractService.formatDeadline(target);
  }

  isSameDay(order: AvailableOrder): boolean {
    return order.Order.Delivery === 'SameDay';
  }

  isPreferred(order: AvailableOrder): boolean {
    return order.PrioritySudsters.includes(this.userId);
  }
}
