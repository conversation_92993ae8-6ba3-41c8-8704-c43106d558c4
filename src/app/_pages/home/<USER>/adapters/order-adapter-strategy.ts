import { AvailableOrder } from 'src/app/_interfaces/available-orders-interfaces';
import { OrderData } from 'src/app/_interfaces/order-data.interface';
import { SudsterData } from 'src/app/_interfaces/sudster-data.interface';
import { OrderContractData } from 'src/app/shared/components/order-contract/order-contract.model';

export interface RouteDelivery {
  distance: string;
  expectedTravelTime: string;
}

export interface OrderAdapterStrategy {
  getCoordinates(order): Promise<[latitude: number, longitude: number]>;

  transformOrder(
    order: AvailableOrder | OrderData,
    sudster: SudsterData,
    direction: Promise<RouteDelivery>,
    flexibleExtension: boolean
  ): OrderContractData;
}
