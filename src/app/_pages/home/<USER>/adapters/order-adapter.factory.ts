import { Injectable } from '@angular/core';
import { OrderType } from '../contract.component';
import { ActiveOrderAdapterStrategy } from './active-order-adapter.strategy';
import { AvailableOrderAdapterStrategy } from './available-order-adapter.strategy';
import { OrderAdapterStrategy } from './order-adapter-strategy';

@Injectable()
export class OrderAdapterFactory {
  constructor(
    private active: ActiveOrderAdapterStrategy,
    private available: AvailableOrderAdapterStrategy
  ) {}

  getStrategy(type: OrderType): OrderAdapterStrategy {
    return type === OrderType.Active ? this.active : this.available;
  }
}
