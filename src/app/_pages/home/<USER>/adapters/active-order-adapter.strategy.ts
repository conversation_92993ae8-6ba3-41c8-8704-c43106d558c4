import { Injectable } from '@angular/core';
import moment from 'moment';
import { OrderData } from 'src/app/_interfaces/order-data.interface';
import { SudsterData } from 'src/app/_interfaces/sudster-data.interface';
import { OrderContractData } from 'src/app/shared/components/order-contract/order-contract.model';
import { Detergent } from '../../current-orders/adapters/active-order.adapter';
import { ContractService } from '../contract.service';
import { OrderAdapterStrategy, RouteDelivery } from './order-adapter-strategy';

@Injectable()
export class ActiveOrderAdapterStrategy implements OrderAdapterStrategy {
  private sudster: SudsterData;
  private flexibleExtension: boolean;

  constructor(private contractService: ContractService) {}

  async getCoordinates(order: OrderData): Promise<[number, number]> {
    return [order.GeoLocation.latitude, order.GeoLocation.longitude];
  }

  transformOrder(
    order: OrderData,
    sudster: SudsterData,
    direction: Promise<RouteDelivery>,
    flexibleExtension: boolean
  ): OrderContractData {
    this.sudster = sudster;
    this.flexibleExtension = flexibleExtension;

    const dynamicData = this.contractService.processDynamicData(order);
    const isHypoallergenic =
      order.Preferences.Detergent === Detergent.Hypoallergenic ||
      order.Preferences.Detergent === Detergent.UnscentedHypoallergenic;
    const addressObj =
      typeof order.Address === 'object' && order.Address !== null
        ? order.Address
        : undefined;

    const pickup = this.getPickupDeadline(order);
    const delivery = this.getDeliveryDeadline(order);

    const orderData: OrderContractData = {
      orderNumber: order.OrderId,
      customerName: `${order.CustomerFirstName} ${order.CustomerLastName}.`,
      isBusinessCustomer: order.IsBusiness,
      canProposeRate: order.AskForRate,
      hasPendingRateProposal: this.hasPendingRateProposal(order),
      isPreferred: !!order.Preferred,
      isExpress: order.SameDayService,
      hasBoost: order.Bonus > 0,
      boostAmount: order.Bonus,
      isNewCustomer: order.FirstOrder,
      timing: {
        pickup: {
          timeMessage: pickup.message,
          time: pickup.time,
          place: order.PickupSpot?.SimpleSpot || '',
          instructions: order.PickupSpot?.Instructions,
        },
        delivery: {
          timeMessage: delivery.message,
          time: delivery.time,
        },
      },
      price: dynamicData,
      address: {
        city: addressObj.City || '',
        state: addressObj.State || '',
        zipCode: addressObj.ZipCode || '',
        distance: {
          miles: '0',
          estimatedMinutes: '0',
        },
      },
      isHypoallergenic: isHypoallergenic,
      isHangDry: !!order.Preferences?.HangDry,
      isDelicate: !!order.Preferences?.Delicates,
      hasSpecialNotes: !!order.Preferences?.Instructions,
      specialNotes: order.Preferences?.Instructions || '',
      acknowledgements: {
        expressOrder: order.SameDayService,
        hypoallergenicDetergent: isHypoallergenic,
        deadline: this.contractService.hasDeadLine(addressObj.State),
      },
    };

    direction.then((dir) => {
      orderData.address.distance.miles = dir.distance;
      orderData.address.distance.estimatedMinutes = dir.expectedTravelTime;
    });

    return orderData;
  }

  getPickupDeadline(order: OrderData): { message: string; time: string } {
    if (order.Accepted) {
      if (order.OrderStatusNumber >= 2) {
        const pickupTime = moment.tz(
          order.StatusHistoryInfo.Pickup.UnixTime,
          order.Timezone
        );
        return {
          message: `Picked up at ${pickupTime.format('MMM D [at] h:mma')}`,
          time: pickupTime.format('h:mma'),
        };
      }

      const pickupDeadline = moment.tz(order.PickupDeadline, order.Timezone);
      return {
        message: `Pickup ${this.contractService.formatDeadline(
          pickupDeadline
        )}`,
        time: pickupDeadline.format('h:mma'),
      };
    }

    return { message: '', time: '' };
  }

  getDeliveryDeadline(order: OrderData): { message: string; time: string } {
    const deliveryDeadline = moment.tz(order.DeliveryDeadline, order.Timezone);
    const prefix = deliveryDeadline.isBefore(moment().tz(order.Timezone))
      ? 'on'
      : '';

    return {
      message: `Delivery ${prefix} ${this.contractService.formatDeadline(
        deliveryDeadline
      )}`,
      time: deliveryDeadline.format('h:mma'),
    };
  }

  hasPendingRateProposal(order: OrderData): boolean {
    return !order;
  }
}
