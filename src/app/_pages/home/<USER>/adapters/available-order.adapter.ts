import { Injectable } from '@angular/core';

import { TextData } from 'src/app/shared/components/icon-text/icon-text.model';
import { AvailableOrder } from '../../../../_interfaces/available-orders-interfaces';
import { SudsterData } from '../../../../_interfaces/sudster-data.interface';
import { AuthidService } from '../../../../_services/authid.service';
import { StatsigFactoryService } from '../../../../_services/statsig-factory.service';
import { getCathetusDistanceMi } from '../../../../_utils/geolocation.util';
import {
  CardItemData,
  CardItemPosition,
  CardItemType,
} from '../../order-card/card-item/card-item.model';
import { IOrderCardAdapter } from '../../order-card/order-card.adapter';
import { OrderAction, OrderCardData } from '../../order-card/order-card.model';

enum Icons {
  Dollar = 'custom_dollar',
  Location = 'location',
  Bolt = 'bolt',
}

enum Colors {
  LimeGreen = '#A9E6C2',
  SalmonPink = '#FFCEBE',
}

@Injectable()
export class AvailableOrderAdapter
  implements
    IOrderCardAdapter<{
      availableOrder: AvailableOrder;
      sudsterData: SudsterData;
      expressService: boolean;
    }>
{
  public userID = this.authID.getID();
  public isExpressServiceEnabled: boolean;
  constructor(
    private authID: AuthidService,
    private statsigFactoryService: StatsigFactoryService
  ) {}

  transform(data: {
    availableOrder: AvailableOrder;
    sudsterData?: SudsterData;
    expressService: boolean;
  }): OrderCardData {
    const availableOrder = data.availableOrder;
    const order = availableOrder.Order;
    const sudsterData = data.sudsterData;

    this.isExpressServiceEnabled = data.expressService;

    const customerName = `${order.CustomerFirstName} ${order.CustomerLastName}`;
    const cardItems: CardItemData[] = [
      ...this.getOrderItems(availableOrder, sudsterData),
      ...this.getOrderTags(availableOrder),
    ];

    return new OrderCardData({
      id: availableOrder.OrderNumber,
      title:
        customerName +
        (this.isPreferred(availableOrder) ? ' is requesting you!' : ''),
      items: cardItems,
      actions: this.getOrderActions(availableOrder),
    });
  }

  private isPreferred(order: AvailableOrder) {
    return order.PrioritySudsters.includes(this.userID);
  }

  private getOrderItems(
    availableOrder: AvailableOrder,
    sudsterData: SudsterData
  ): CardItemData[] {
    const order = availableOrder.Order;
    const sudsterRate = this.getSudshareRate(availableOrder);
    const locationName = `${order.Address.City}, ${order.Address.State} ${order.Address.ZipCode}`;
    const estimatedDistance = getCathetusDistanceMi(
      sudsterData.WorkAreaCenter.latitude,
      sudsterData.WorkAreaCenter.longitude,
      order.Address.Lat,
      order.Address.Long
    );

    const bagSize =
      order.BagSize === 'oversized'
        ? 'Large'
        : this.capitalizeFirstLetter(order.BagSize);

    const orderItemMappings = [
      {
        condition: order.EstimatedEarnings,
        icon: Icons.Dollar,
        texts: [
          {
            content: `$${order.EstimatedEarnings} Est. (${sudsterRate.toFixed(
              2
            )}/lb)`,
          },
          {
            content: `${order.BagCount} ${bagSize} Bag${
              order.BagCount > 1 ? 's' : ''
            } ${
              availableOrder.LargeItems?.CustomerSelected
                ? '+ ' + availableOrder.LargeItems.Count + ' Oversized'
                : ''
            }`,
          },
        ],
      },
      {
        condition: order.Address.City,
        icon: Icons.Location,
        texts: [
          { content: locationName },
          { content: `${estimatedDistance?.toFixed(1)} mile drive est.` },
        ],
      },
    ];

    return orderItemMappings
      .filter((mapping) => mapping.condition)
      .map(({ icon, texts }) => ({
        type: CardItemType.Default,
        icon,
        texts: texts as TextData[],
      }));
  }

  private getOrderTags(order: AvailableOrder): CardItemData[] {
    const tagMappings = [
      {
        condition:
          order.Order.Delivery === 'SameDay' && this.isExpressServiceEnabled,
        icon: Icons.Bolt,
        tag: 'Express',
        color: Colors.LimeGreen,
      },
      {
        condition: order.Order.Bonus > 0,
        icon: null,
        tag: `+$${order.Order.Bonus} Boost`,
        color: Colors.SalmonPink,
      },
    ];

    return tagMappings
      .filter((mapping) => mapping.condition)
      .map(({ icon, tag, color }) => ({
        icon,
        tag,
        color,
        type: CardItemType.Tag,
        texts: [{ content: tag }],
        position: CardItemPosition.Header,
      }));
  }

  private getOrderActions(order: AvailableOrder): OrderAction[] {
    if (this.isPreferred(order)) {
      return [
        {
          id: 'decline',
          fill: 'outline',
          label: 'Decline',
        },
        {
          id: 'accept',
          label: 'View Request',
          pulse: true,
        },
      ];
    }
  }

  private getSudshareRate(order: AvailableOrder): number {
    const pricePerPound =
      order.Order.Delivery === 'SameDay'
        ? order.Order.PriceSchedule?.ExpressRate || 2
        : order.Order.PriceSchedule?.StandardRate || 1;

    return pricePerPound * 0.75;
  }

  private capitalizeFirstLetter(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }
}
