#map {
  height: 100%;
  width: 100%;
}

#map-component {
  height: 230px;
  width: 90%;
  margin: 0 auto;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 30, 50, 0.2);
  border: solid 3px white;
  position: relative;
}

:host {
  position: relative;
  display: block;
}

#Fullscreen {
  position: absolute;
  right: 5px;
  top: 5px;
  z-index: 2;
  color: var(--BlueGray5);
  background: rgba(255, 255, 255, 0.8);
  padding: 2px;
  border-radius: 50%;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
}

:host.MapFullscreen #map-component {
  height: 100%;
  width: 100%;
  border: none;
  border-radius: 0px;
}

:host.MapFullscreen #Fullscreen {
  font-size: 35px;
  background: white;
  top: calc(env(safe-area-inset-top) + 15px);
}
