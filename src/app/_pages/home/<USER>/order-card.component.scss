.order-card {
  padding: 12px;
  border-radius: var(--border-radius-default);
  border: 1px solid var(--gray-200);
  background: var(--white);
  box-shadow: 0px 4px 24px 0px rgba(0, 0, 0, 0.04);

  &.border-alert {
    border-color: var(--pink-400);
    border: 2px solid var(--pink-400);
  }

  .wrap-content {
    max-width: calc(100% - 115px);
  }

  .actions-wrapper {
    margin-top: 12px;
    display: flex;
    gap: 12px;
    justify-content: space-between;
  }

  ion-col {
    padding: 0px;

    &.tags-header-wrapper {
      display: flex;
      flex-direction: column;
      gap: 12px;
      position: absolute;
      top: 14px;
      right: 14px;
    }

    &.tags-footer-wrapper {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
      margin-top: 10px;
      margin-bottom: 3px;
    }

    p {
      font-size: 14px;
      margin: 0px;
    }

    .title {
      margin-bottom: 4px;
      font-weight: 700;
    }
  }
}

::ng-deep ion-button {
  margin: 0px;

  span {
    font-family: 'PitchSans-Medium';
    font-size: 13px;
    font-weight: 700;
    letter-spacing: 1.3px;
  }
}

::ng-deep .order-card poplin-icon-text {
  .content-wrapper {
    .primary {
      font-size: 14px !important;
    }
  }
}
