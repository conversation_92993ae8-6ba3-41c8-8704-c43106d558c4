<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button (click)="closeModal()">
        <ion-icon slot="icon-only" name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>Decline Order</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-grid>
    <ion-row>
      <ion-col size="12" class="availability">
        <ion-label class="subsection">Include next availability</ion-label>
        <ion-toggle
          class="default"
          (ionChange)="availabilityToggle($event)"
          [checked]="includeAvailability"
        ></ion-toggle>
      </ion-col>
    </ion-row>
    <ion-row *ngIf="includeAvailability">
      <ion-col size="12">
        <app-short-day-selector
          (dateSelected)="onDateSelected($event)"
        ></app-short-day-selector>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col size="12" class="repeat-modal-options">
        <ion-label class="subsection">Select a reason for declining:</ion-label>
        <ion-list>
          <ion-radio-group
            (ionChange)="onDeclineReasonChange($event)"
            [value]="selectedDeclineReason"
          >
            <ion-item *ngFor="let declineReason of declineReasons" lines="none">
              <ion-label>{{ declineReason.label }}</ion-label>
              <ion-radio
                slot="start"
                value="{{ declineReason.value }}"
              ></ion-radio>
            </ion-item>
          </ion-radio-group>
        </ion-list>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col class="preview-msg">
        <div class="referenceMsg">{{ previewDeclineMessage }}</div>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col class="actions">
        <ion-button
          [disabled]="submitBtnDisabled"
          (click)="submitDecline()"
          class="h-large"
          color="primary"
        >
          <span class="title">send message</span>
          <ion-icon name="chatbox-outline"></ion-icon>
        </ion-button>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>
