import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { IonCol, IonRow } from '@ionic/angular/standalone';
import { PulseComponent } from 'src/app/shared/components/pulse/pulse.component';
import { ButtonComponent } from '../../../shared/components/button/button.component';
import { IconTextComponent } from '../../../shared/components/icon-text/icon-text.component';
import { TagComponent } from '../../../shared/components/tag/tag.component';
import {
  CardItemData,
  CardItemPosition,
  CardItemType,
} from './card-item/card-item.model';
import { OrderAction, OrderCardData } from './order-card.model';

@Component({
  selector: 'app-order-card',
  styleUrls: ['./order-card.component.scss'],
  templateUrl: './order-card.component.html',
  imports: [
    IonCol,
    IonRow,
    TagComponent,
    ButtonComponent,
    PulseComponent,
    CommonModule,
    IconTextComponent,
  ],
  standalone: true,
})
export class OrderCardComponent implements OnInit {
  @Input() data: OrderCardData;
  @Output() actionHandler = new EventEmitter();

  items: CardItemData[] = [];
  headerTags: CardItemData[] = [];
  footerTags: CardItemData[] = [];
  actions: OrderAction[] = [];

  ngOnInit(): void {
    this.items = this.data.items.filter(
      (item) => item.type === CardItemType.Default
    );

    this.headerTags = this.data.items.filter(
      (item) =>
        item.type === CardItemType.Tag &&
        item.position === CardItemPosition.Header
    );

    this.footerTags = this.data.items.filter(
      (item) =>
        item.type === CardItemType.Tag &&
        item.position === CardItemPosition.Footer
    );

    this.actions = this.data.actions;
  }

  clickHandler(): void {
    this.actionHandler.emit(this.data.id);
  }
}
