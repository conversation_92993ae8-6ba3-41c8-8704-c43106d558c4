import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  ViewChild,
} from '@angular/core';
import { AuthidService } from 'src/app/_services/authid.service';
import { OrderContractData } from 'src/app/shared/components/order-contract/order-contract.model';

import { CommonModule } from '@angular/common';
import { ModalController } from '@ionic/angular';
import { AlertController } from '@ionic/angular/standalone';
import {
  AvailableOrder,
  PreferredPickup,
} from 'src/app/_interfaces/available-orders-interfaces';
import { OrderData } from 'src/app/_interfaces/order-data.interface';
import { SudsterData } from 'src/app/_interfaces/sudster-data.interface';
import { AnalyticsLogService } from 'src/app/_services/analytics/analytics-log.service';
import { LaundryProService } from 'src/app/_services/api/laundry-pro.service';
import { OrderContractModalComponent } from '../../../shared/components/order-contract-modal/order-contract-modal.component';
import { DeclineOrderModalComponent } from '../decline-order-modal/decline-order-modal.component';
import { GreetingComponent } from '../greeting/greeting.component';
import { OrderAdapterStrategy } from './adapters/order-adapter-strategy';
import { OrderAdapterFactory } from './adapters/order-adapter.factory';
import { ContractService, FeatureFlagsConfig } from './contract.service';

export enum OrderType {
  Available = 'Available',
  Active = 'Active',
}

export const breakpoints = [0, 0.75, 0.8, 0.85, 0.9, 1];

@Component({
  selector: 'app-contract',
  templateUrl: './contract.component.html',
  styleUrls: ['./contract.component.scss'],
  standalone: true,
  imports: [CommonModule, OrderContractModalComponent],
})
export class ContractComponent implements OnInit {
  @ViewChild(OrderContractModalComponent)
  orderContractModalComponent!: OrderContractModalComponent;

  @Input() viewMode = false;
  @Input() goToActive!: EventEmitter<void>;

  public contractModel: OrderContractData;
  public userID = this.authID.getID();
  public showModal = false;
  public breakpoints = breakpoints;
  public initialBreakpoint = 0.75;
  public flexibleExtension = false;

  private sudster: SudsterData;
  private order: AvailableOrder | OrderData;
  private orderType: OrderType;

  private orderAdapter: OrderAdapterStrategy;
  private featureFlags: FeatureFlagsConfig;
  private sudsterFirstOrder: boolean;

  constructor(
    private authID: AuthidService,
    private contractService: ContractService,
    private adapterFactory: OrderAdapterFactory,
    private alertController: AlertController,
    private modalController: ModalController,
    private logService: AnalyticsLogService,
    private laundryProService: LaundryProService
  ) {}

  async ngOnInit() {
    this.featureFlags = await this.contractService.getFeatureFlags();
  }

  async openContract(
    sudster: SudsterData,
    order: AvailableOrder | OrderData,
    orderType: OrderType
  ): Promise<void> {
    this.sudster = sudster;
    this.order = order;
    this.orderType = orderType;

    this.orderAdapter = this.adapterFactory.getStrategy(orderType);
    this.sudsterFirstOrder = !sudster.OrderCount;

    const sudsterLocation = sudster.WorkAreaCenter;
    const [lat, long] = await this.orderAdapter.getCoordinates(order);

    const direction = this.contractService.getDirections(
      sudsterLocation.latitude,
      sudsterLocation.longitude,
      lat,
      long
    );

    this.contractModel = this.orderAdapter.transformOrder(
      order,
      sudster,
      direction,
      this.flexibleExtension
    );

    this.showModal = true;
  }

  private async acceptOrder(
    sudster: SudsterData,
    order: AvailableOrder
  ): Promise<void> {
    if (!this.contractService.canAcceptOrder(sudster, order)) {
      await this.presentAlert(
        'Cannot accept order',
        `You must be at Bronze level or above to accept ${
          this.featureFlags.isExpressServiceEnabled ? 'express' : 'same-day'
        } orders`
      );
      return;
    }

    if (this.sudsterFirstOrder && !this.contractService.isVerified(sudster)) {
      this.logService.logIDVStart(
        order.OrderNumber,
        this.featureFlags.checkrVerificationFlag,
        this.featureFlags.stripeIdv
      );

      await this.presentVerificationConfirmation();
      return;
    }

    this.laundryProService
      .acceptOrder(
        order.OrderNumber,
        {
          sudsterId: this.userID,
          orderNumber: order.OrderNumber,
          acceptedExtension: false,
          estimatedDriveTime:
            this.contractModel.address.distance.estimatedMinutes.toString(),
        },
        order.Order.IsNewOrderFlow
      )
      .subscribe({
        next: async (apiResponse) => {
          const response = apiResponse.rawResponse;
          const responseObj = (await apiResponse.data) as Record<
            string,
            unknown
          >;

          if (response.status === 200) {
            this.modalController.dismiss({
              CustomerFirstName: responseObj.firstName,
            });

            const greetingModal = await this.modalController.create({
              component: GreetingComponent,
              cssClass: 'PopupModal',
              componentProps: {
                CustomerFirstName: order.Order.CustomerFirstName,
                SudsterFirstName: sudster.FirstName,
                OrderNumber: order.OrderNumber,
                OrderTimezone: order.Order.Timezone,
              },
              backdropDismiss: false,
            });

            await greetingModal.present();
            await greetingModal.onDidDismiss();

            this.goToActive.emit();
          } else {
            await this.acceptErrorHandler(responseObj);
            this.showModal = false;
          }
        },
        error: async (error) => {
          await this.acceptErrorHandler(error);
          this.showModal = false;
        },
      });
  }

  async onDeclineContract(): Promise<void> {
    await this.preferredDeclineAlert(
      this.order as AvailableOrder,
      this.sudster
    );
  }

  async preferredDeclineAlert(
    orderItem: AvailableOrder,
    sudster: SudsterData
  ): Promise<void> {
    this.flexibleExtension = false;
    if (orderItem.Order.Pickup === PreferredPickup.ASAP) {
      const declined = await this.handleSudsterDecline(orderItem);

      if (declined) {
        this.showModal = false;
        this.goToActive.emit();
      }

      return;
    }

    const alert = await this.alertController.create({
      header: 'Need an extra day?',
      message:
        'The customer has indicated that they are flexible with an extra day if necessary. Would you like to accept this order with an extra day?',
      buttons: [
        {
          text: 'Accept Extension',
          handler: () => {
            this.flexibleExtension = true;
            this.openContract(sudster, orderItem, this.orderType);
          },
        },
        {
          text: 'Decline',
          handler: async () => {
            this.alertController.dismiss();
            const declined = await this.handleSudsterDecline(orderItem);

            if (declined) {
              this.showModal = false;
              this.goToActive.emit();
            }
          },
        },
      ],
    });
    await alert.present();
  }

  private async handleSudsterDecline(
    orderItem: AvailableOrder
  ): Promise<boolean> {
    const modal = await this.modalController.create({
      component: DeclineOrderModalComponent,
      breakpoints: this.breakpoints,
      initialBreakpoint: this.initialBreakpoint,
      cssClass: 'poplin-theme',
      handle: false,
      showBackdrop: true,
      componentProps: {
        internalOrderId: orderItem.OrderNumber,
        isNewOrderFlow: orderItem.Order.IsNewOrderFlow,
      },
    });
    await modal.present();

    const { data } = await modal.onDidDismiss();

    return !!data?.declinedReasonCode;
  }

  onCloseContract(): void {
    this.showModal = false;
  }

  onAcceptContract(): void {
    this.acceptOrder(this.sudster, this.order as AvailableOrder);
  }

  private async presentAlert(header: string, message: string): Promise<void> {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK'],
    });
    await alert.present();
  }

  private async presentVerificationConfirmation(): Promise<void> {
    const alert = await this.alertController.create({
      header: 'Verification Required',
      message:
        'You need to complete verification before accepting your first order.',
      buttons: ['OK'],
    });
    await alert.present();
  }

  private async acceptErrorHandler(
    response: Record<string, unknown>
  ): Promise<void> {
    let errorMessage = 'An error occurred while accepting the order.';

    if (response && response.message) {
      errorMessage = response.message as string;
    }

    await this.presentAlert('Error', errorMessage);
  }
}
