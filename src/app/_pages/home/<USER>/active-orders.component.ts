import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { AngularFirestore } from '@angular/fire/firestore';
import { Router } from '@angular/router';
import { AlertController } from '@ionic/angular';
import { OrderData } from 'src/app/_interfaces/order-data.interface';
import { SudsterData } from 'src/app/_interfaces/sudster-data.interface';
import { DeadlinesService } from 'src/app/_services/deadlines.service';
import { GetActiveOrdersService } from 'src/app/_services/get-active-orders.service';
import { StatsigFactoryService } from 'src/app/_services/statsig-factory.service';
import { StatsigService } from 'src/app/_services/statsig.service';
import { environment } from 'src/environments/environment';
import tz_lookup from 'tz-lookup';
import { NewOrdersComponent } from '../new-orders/new-orders.component';

@Component({
  selector: 'app-active-orders',
  templateUrl: './active-orders.component.html',
  styleUrls: ['./active-orders.component.scss'],
  providers: [NewOrdersComponent],
})
export class ActiveOrdersComponent implements OnInit {
  NoActiveOrders;
  isExpressServiceEnabled = false;

  ActiveOrderList: Array<{
    CustomerName: string;
    LocationName: string;
    BagCount: number;
    StageName: string;
    StageIcon: string;
    Deadline: string;
    MessagesMissed: number;
    OrderNumber: string;
    InternalOrderId: string;
    FirstOrder: boolean;
    FromDualReferral: boolean;
    SameDayService: boolean;
  }> = [];

  @Output() ActiveCount = new EventEmitter();
  SameDayService: boolean;
  private statsigService: StatsigService;

  constructor(
    public firestore: AngularFirestore,
    private GetActiveOrders: GetActiveOrdersService,
    private deadlinesService: DeadlinesService,
    private router: Router,
    private alertController: AlertController,
    private statsigFactoryService: StatsigFactoryService
  ) {
    this.statsigService = this.statsigFactoryService.getInstance();
  }

  ngOnInit() {
    this.statsigService
      .checkGate(environment.statsig.flags.ExpressService)
      .subscribe((expressservice) => {
        this.isExpressServiceEnabled = expressservice;
      });

    this.GetActiveOrders.LoadOrderList();

    this.GetActiveOrders.currentOrders.subscribe(
      (data: {
        Orders: (OrderData & { OrderNumber: string })[];
        Sudster: SudsterData;
      }) => {
        const Orders = data.Orders;

        this.ActiveOrderList = [];

        if (Orders != null && Orders.length != 0) {
          this.NoActiveOrders = false;
          Orders.forEach((doc) => {
            let stageName = 'PICKUP';
            let stageIcon = 'drive_eta';
            if (doc.OrderStatusNumber == 2) {
              stageName = 'LAUNDER';
              stageIcon = 'local_laundry_service';
            } else if (doc.OrderStatusNumber == 2.5) {
              stageName = 'WEIGH';
              stageIcon = 'iso';
            } else if (doc.OrderStatusNumber == 3) {
              stageName = 'DELIVER';
              stageIcon = 'beenhere';
            }

            const timezone = doc.Timezone
              ? doc.Timezone
              : tz_lookup(doc.Lat, doc.Lng);
            let Deadline = this.deadlinesService.ConvertUnixDeadlineToString(
              doc.PickupDeadline,
              timezone
            );
            if (doc.OrderStatusNumber >= 2) {
              Deadline = this.deadlinesService.ConvertUnixDeadlineToString(
                doc.DeliveryDeadline,
                timezone
              );
            }

            this.ActiveOrderList.push({
              CustomerName: `${doc.CustomerFirstName}  ${
                doc.CustomerLastName ? doc.CustomerLastName : ''
              }`,
              LocationName: this.GetAreaName(doc.Address),
              BagCount: doc.OrderSize,
              StageName: stageName,
              StageIcon: stageIcon,
              Deadline: Deadline,
              MessagesMissed: doc.SudsterMessageBadge,
              OrderNumber: doc.OrderNumber.startsWith('S')
                ? `#${doc.OrderNumber}`
                : `#${doc.OrderId}`,
              InternalOrderId: doc.OrderNumber,
              FirstOrder: doc.FirstOrder,
              FromDualReferral:
                doc.Referral?.ReferredBySudster == doc.SudsterID,
              SameDayService: doc.SameDayService,
            });
          });
        } else {
          this.NoActiveOrders = true;
        }
        this.ActiveCount.emit(this.ActiveOrderList.length);
      }
    );
  }

  async ClickActiveOrder(index) {
    this.router.navigate([
      '/active',
      { id: this.ActiveOrderList[index].InternalOrderId },
    ]);
  }

  async showNewCustomerInfo(evt: TouchEvent, order) {
    evt.stopImmediatePropagation();
    evt.preventDefault();

    const alert = await this.alertController.create({
      header: 'New customer',
      message: `This is ${order.CustomerName}’s first order with Poplin! Impress them with great communication and an amazing experience to create a loyal customer!`,
      buttons: ['OK'],
    });
    await alert.present();
  }

  GetAreaName(Address) {
    if (typeof Address === 'object') {
      return `${Address.City}, ${Address.State} ${Address.ZipCode}`;
    } else if (Address.split(', ').length < 2) {
      return Address.split(' ').splice(-3).toString().replaceAll(',', ', ');
    } else {
      return `${Address.split(', ')[1]}, ${this.abbrState(
        Address.split(', ')[2]
      )}`;
    }
  }

  abbrState(input) {
    if (input == null) {
      return '';
    }

    const Zip = (input.match(/\d+/) || [''])[0];
    input = input.replace(/[0-9]/g, '').trim();

    const states = [
      ['Arizona', 'AZ'],
      ['Alabama', 'AL'],
      ['Alaska', 'AK'],
      ['Arkansas', 'AR'],
      ['California', 'CA'],
      ['Colorado', 'CO'],
      ['Connecticut', 'CT'],
      ['Delaware', 'DE'],
      ['Florida', 'FL'],
      ['Georgia', 'GA'],
      ['Hawaii', 'HI'],
      ['Idaho', 'ID'],
      ['Illinois', 'IL'],
      ['Indiana', 'IN'],
      ['Iowa', 'IA'],
      ['Kansas', 'KS'],
      ['Kentucky', 'KY'],
      ['Louisiana', 'LA'],
      ['Maine', 'ME'],
      ['Maryland', 'MD'],
      ['Massachusetts', 'MA'],
      ['Michigan', 'MI'],
      ['Minnesota', 'MN'],
      ['Mississippi', 'MS'],
      ['Missouri', 'MO'],
      ['Montana', 'MT'],
      ['Nebraska', 'NE'],
      ['Nevada', 'NV'],
      ['New Hampshire', 'NH'],
      ['New Jersey', 'NJ'],
      ['New Mexico', 'NM'],
      ['New York', 'NY'],
      ['North Carolina', 'NC'],
      ['North Dakota', 'ND'],
      ['Ohio', 'OH'],
      ['Oklahoma', 'OK'],
      ['Oregon', 'OR'],
      ['Pennsylvania', 'PA'],
      ['Rhode Island', 'RI'],
      ['South Carolina', 'SC'],
      ['South Dakota', 'SD'],
      ['Tennessee', 'TN'],
      ['Texas', 'TX'],
      ['Utah', 'UT'],
      ['Vermont', 'VT'],
      ['Virginia', 'VA'],
      ['Washington', 'WA'],
      ['West Virginia', 'WV'],
      ['Wisconsin', 'WI'],
      ['Wyoming', 'WY'],
    ];

    let returnValue = input;
    for (let i = 0; i < states.length; i++) {
      if (states[i][0] == input) {
        returnValue = states[i][1];
      }
    }

    return returnValue + ' ' + Zip;
  }
}
