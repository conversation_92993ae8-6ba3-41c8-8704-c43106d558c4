<div
  class="order-card"
  [ngClass]="data.borderAlert ? 'border-alert' : ''"
  (click)="clickHandler()"
  aria-hidden="true"
>
  <ion-row>
    <!-- TITLE -->
    <ion-col>
      <p [ngClass]="headerTags?.length ? 'wrap-content title' : 'title'">
        {{ data.title }}
      </p>
    </ion-col>

    <!-- TAGS: TOP RIGHT -->
    <ion-col class="tags-header-wrapper" size="auto" *ngIf="headerTags.length">
      <poplin-tag
        *ngFor="let tag of headerTags"
        label="{{ tag.texts?.[0].content }}"
        icon="{{ tag.icon }}"
        color="{{ tag.color }}"
      ></poplin-tag>
    </ion-col>
  </ion-row>

  <ion-row>
    <ion-col [ngClass]="headerTags?.length > 1 ? 'wrap-content' : ''">
      <poplin-icon-text
        *ngFor="let item of items"
        [icon]="item.icon"
        [texts]="item.texts"
      >
      </poplin-icon-text>
    </ion-col>
  </ion-row>

  <!-- TAGS: LIST AFTER BODY CONTENT -->
  <ion-row>
    <ion-col class="tags-footer-wrapper" size="auto" *ngIf="footerTags.length">
      <poplin-tag
        *ngFor="let tag of footerTags"
        label="{{ tag.texts?.[0].content }}"
        icon="{{ tag.icon }}"
        color="{{ tag.color }}"
      ></poplin-tag>
    </ion-col>
  </ion-row>

  <!-- ACTIONS: BUTTONS -->
  <ion-row *ngIf="actions?.length" class="actions-wrapper">
    <ion-col
      *ngFor="let action of actions; let i = index"
      size="{{ i === 0 || action.icon ? 'auto' : null }}"
    >
      <poplin-button
        id="{{ action.id }}"
        size="small"
        fill="{{ action.fill || 'solid' }}"
        label="{{ action.label }}"
        (click)="action.actionHandler($event)"
        [showIcon]="!!action.icon"
        [iconSlot]="action.icon ? 'icon-only' : ''"
        [icon]="action.icon"
      ></poplin-button>

      <poplin-pulse *ngIf="action.pulse"></poplin-pulse>
    </ion-col>
  </ion-row>
</div>
