import { CardItemData } from './card-item/card-item.model';

export interface OrderAction {
  id: string;
  label: string;
  fill?: string;
  icon?: string;
  pulse?: boolean;
  actionHandler?: (event: Event) => void;
}

export class OrderCardData {
  id: string;
  title: string;
  items: CardItemData[];
  actions?: OrderAction[];
  borderAlert?: boolean;

  constructor(init?: Partial<OrderCardData>) {
    Object.assign(this, init);
  }

  getAction?(id: string): OrderAction {
    return this.actions?.find((action) => action.id === id);
  }
}
