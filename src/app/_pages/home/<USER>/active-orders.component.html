<div
  id="active-order-component"
  class="OrderDiv"
  *ngFor="let order of ActiveOrderList; let i = index"
  (click)="ClickActiveOrder(i)"
>
  <div class="same-day-service-pill" *ngIf="order.SameDayService">
    {{ isExpressServiceEnabled ? 'EXPRESS' : 'SAME-DAY' }}
    <span class="same-day-icon">$$</span>
  </div>
  <div id="active-order-content" class="InnerDiv">
    <label class="OrderNumber">{{ order.OrderNumber }}</label>

    <div *ngIf="order.MessagesMissed > 0" class="MissedMsgDiv">
      {{ order.MessagesMissed }}<i class="material-icons">chat</i>
    </div>

    <div class="LeftDiv">
      <h1 class="Name trunc">{{ order.CustomerName }}</h1>
      <label class="City trunc">{{ order.LocationName }}</label>
      <div class="new-cust">
        <ion-badge
          *ngIf="order.FirstOrder"
          color="warning"
          (click)="showNewCustomerInfo($event, order)"
        >
          New to Poplin <ion-icon name="information-circle"></ion-icon>
        </ion-badge>
        <ion-badge *ngIf="order.FromDualReferral" color="warning">
          Referred <ion-icon name="qr-code"></ion-icon>
        </ion-badge>
      </div>
    </div>

    <b class="InfoSeparaterLine"></b>

    <div class="RightDiv">
      <h1>
        <i class="material-icons">{{ order.StageIcon }}</i
        >{{ order.StageName }}
      </h1>
      <label
        >Deadline: <b>{{ order.Deadline }}</b></label
      >
    </div>
  </div>
</div>

<p id="NoOrders" *ngIf="NoActiveOrders">
  <b>No Active Orders.</b>Go to 'Available Orders' to accept an order.<br />
</p>
