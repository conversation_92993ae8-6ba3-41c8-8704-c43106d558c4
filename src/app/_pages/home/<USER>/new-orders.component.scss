@import 'src/theme/type-mixins.scss';

:host {
  display: block;
}

.preferred {
  height: 190px !important;
}

.OrderDiv {
  position: relative;
  height: 125px;
  overflow: hidden;
  padding: 10px;
  padding-top: 15px;
  padding-right: 15px;
  width: 90%;
  max-width: 350px;
  margin: 0 auto;
  min-width: 340px;

  .InnerDiv {
    background: white;
    box-shadow: 0 3px 6px rgba(0, 30, 50, 0.15);
    border-radius: 15px;
    width: 100%;
    height: 100%;
    border-bottom: solid 3px var(--pink-core);
    position: relative;
  }

  .OrderNumber {
    margin: 0 auto;
    margin-bottom: 5px;
    display: block;
    position: relative;
    width: fit-content;
    max-width: 135px;
    background: var(--BlueGray1);
    border-bottom-right-radius: 20px;
    border-bottom-left-radius: 20px;
    text-align: center;
    overflow: hidden;
    font-size: 10px;
    font-weight: 700;
    color: var(--BlueGray4);
    letter-spacing: 1px;
    padding: 2px 10px;
    box-shadow: 0px 1px 1px rgba(0, 30, 50, 0.15);
  }

  .BonusDiv {
    position: absolute;
    height: 50px;
    width: 50px;
    overflow: hidden;
    border-radius: 50%;
    background: var(--Pink);
    border: solid 2px white;
    box-shadow: -3px 3px 5px rgba(0, 30, 50, 0.15);
    right: -15px;
    top: -15px;
    color: white;
    font-size: 21px;
    font-weight: 900;
    letter-spacing: -2px;
    text-align: center;
    padding-top: 2px;

    small {
      font-size: 10px;
      display: block;
      font-weight: 900;
      letter-spacing: -0.5px;
      margin-top: -3px;
    }
  }

  .Name {
    @include xs-head;
    color: var(--BlueGray5);
    margin: 0;
    padding-left: 15px;
    padding-right: 10px;
    font-family: 'Fakt-Normal', sans-serif;
    font-style: normal;
    font-weight: 700;
    font-size: 20px;
    line-height: 140%;
  }

  .City {
    display: block;
    font-weight: 700;
    color: var(--BlueGray4);
    width: 100%;
    padding-left: 15px;
    font-family: 'Fakt-Normal', sans-serif;
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 140%;
  }

  .EstDiv {
    float: right;
    width: 40%;
    text-align: center;
    padding: 5px;
    display: flex;
    justify-content: center;
    box-sizing: border-box;

    &__bagsize {
      font-style: italic;
      text-transform: uppercase;
      color: var(--BlueGray3);
    }

    label {
      @include m-body;
      color: var(--gray-800);
      float: left;
      padding: 0px 5px;
      font-size: 20px;
      font-weight: 700;
      font-family: 'Fakt-Normal', sans-serif;
      font-style: normal;
      font-weight: 700;

      small {
        display: block;
        font-size: 11px;
      }
    }
  }

  .preferred-order-acccept {
    position: absolute;
    font-size: 10px;
    top: 65%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    text-align: center;

    p {
      margin: 0;
      margin-top: 30px;
      font-size: 11px;
      text-transform: uppercase;
      font-family: Roboto, sans-serif;
      font-weight: 700;
      color: var(--BlueGray4);
    }

    button {
      width: 150px;
      border-radius: 500px;
    }

    .button-open {
      background-color: transparent;
      margin-right: 5px;
      padding: 10px;
      color: var(--blue-700, #0076ca);
      border: 3px solid var(--blue-700, #0076ca);
      font-weight: 700;
    }

    .button-close {
      background-color: transparent;
      margin-left: 5px;
      padding: 10px;
      color: red;
      z-index: 7;
      border: 3px solid red;
      font-weight: 700;
    }
  }

  .InfoSeparaterLine {
    position: absolute;
    left: calc(60% - 5px);
    margin-top: 5px;
    width: 1px;
    height: 50px;
    background: var(--BlueGray2);
  }

  .LeftDiv {
    float: left;
    width: 59%;
  }
}

#ArrowButton {
  position: absolute;
  right: 0px;
  top: 70px;
  z-index: 2;
  background: var(--BlueGray5);
  border-top-left-radius: 25px;
  border-bottom-left-radius: 25px;
  color: white;
  padding: 10px 0px;
  max-width: 30px;
}

#ArrowCover {
  position: relative;
  z-index: 3;
  height: 100px;
  width: 100px;
  display: inline-block;
  background: #f8fcff;
  margin-left: 20px;
  overflow: hidden;
  white-space: normal;
  font-size: 10px;
  text-align: left;
  padding-right: 10px;
  color: var(--BlueGray4);
  padding-top: 5px;

  b {
    font-weight: 900;
  }
}

.Countdown {
  display: block;
  clear: both;
  text-align: center;
  font-size: 11px;
  color: var(--BlueGray3);
  padding: 2px;
  margin: 0;
  font-weight: 600;
}

#NoOrders {
  text-align: center;
  padding: 10px;
  width: 300px;
  color: var(--BlueGray4);
  font-size: 14px;
  line-height: 135%;
  margin: 0 auto;

  b {
    display: block;
    font-size: 17px;
    padding-bottom: 5px;
  }
}

#OutsideArea {
  width: 90%;
  display: block;
  text-align: center;
  font-size: 14px;
  margin: 10px auto;
}

#LoadingLabel {
  text-align: center;
  display: block;
  font-weight: 500;
  color: var(--BlueGray4);
  font-size: 18px;
}

.same-day-service-pill {
  background-color: #22ecab;
  border: 1px solid #fff;
  width: 114px;
  height: 32px;
  border-radius: 15px;
  text-align: center;
  padding-top: 6px;
  padding-left: 3px;
  font-weight: 600;
  font-size: 13px;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  box-shadow: 2px 2px 3px 0px #d9d9d9;
  -webkit-box-shadow: 2px 2px 3px 0px #d9d9d9;
  -moz-box-shadow: 2px 2px 3px 0px #d9d9d9;

  span {
    background-color: #fff;
    border-radius: 100%;
    padding: 2px;
    margin-left: 02px;
  }
}

.double-points-pill {
  background-color: #f9b011;
  border: 1px solid #fff;
  height: 30px;
  border-radius: 15px;
  text-align: center;
  padding: 6px 10px 6px 3px;
  font-weight: 600;
  font-size: 13px;
  position: absolute;
  top: 0;
  right: 0;
  z-index: 2;
  box-shadow: 2px 2px 3px 0px #d9d9d9;
  -webkit-box-shadow: 2px 2px 3px 0px #d9d9d9;
  -moz-box-shadow: 2px 2px 3px 0px #d9d9d9;

  span {
    background-color: #fff;
    border-radius: 100%;
    padding: 3px;
    margin-left: 2px;
    margin-right: 1px;
    font-size: 12px;
  }
}
.priceBoost {
  border-radius: 13px;
  border: 3px solid var(--pink-core);
}
.price-boost-div {
  text-align: center;
  position: absolute;
  right: -10px;
  top: -15px;
  width: 82px;
  height: 27px;
  flex-shrink: 0;
  border-radius: 15px;
  border: 1px solid var(--SudShare-White, #fff);
  background: var(--pink-core);
  box-shadow: 0px 3px 2px 0px rgba(0, 0, 0, 0.1);
  color: #fff;
  text-align: center;
  font-family: 'Fakt-Normal', sans-serif;
  font-size: 11px;
  font-style: normal;
  font-weight: 700;
  line-height: 20px; /* 181.818% */
  span {
    text-align: center;
    display: flex;
    height: 25px;
    flex-direction: column;
    justify-content: center;

    /* 181.818% */
  }
}
