import { Injectable } from '@angular/core';
import moment from 'moment';
import { firstValueFrom } from 'rxjs';
import { AvailableOrder } from 'src/app/_interfaces/available-orders-interfaces';
import { MapkitRoute } from 'src/app/_interfaces/mapkit/route.interface';
import {
  BagSize,
  OrderData,
  PriceSchedule,
  Stages,
} from 'src/app/_interfaces/order-data.interface';
import { SudsterData } from 'src/app/_interfaces/sudster-data.interface';
import { AuthidService } from 'src/app/_services/authid.service';
import { MapService } from 'src/app/_services/map.service';
import { StatsigFactoryService } from 'src/app/_services/statsig-factory.service';
import { StatsigService } from 'src/app/_services/statsig.service';
import { StatsigFlags } from 'src/app/_utils/enum';
import { getStage } from 'src/app/_utils/order-utils';
import { trackEvent } from 'src/app/_utils/track-event';
import { OrderContractPrice } from 'src/app/shared/components/order-contract/order-contract.model';
import { environment } from 'src/environments/environment';
import { Detergent } from '../current-orders/adapters/active-order.adapter';
import { RouteDelivery } from './adapters/order-adapter-strategy';

export type FeatureFlagsConfig = {
  useNewContractComponentFlag: boolean;
  stripeIdv: boolean;
  checkrVerificationFlag: boolean;
  hideFirstTimeCustomerOrder: boolean;
  isExpressServiceEnabled: boolean;
  LPFirstOrderPickUp: boolean;
  isShowAdditionalDetails: boolean;
  caContract: boolean;
};

const LOCALSTORAGE_KEY_UNIQUE_TRACKEVENTS = 'UniqueEvents';
@Injectable({
  providedIn: 'root',
})
export class ContractService {
  public preAcceptanceFlag: boolean;
  public flexibleExtension: boolean;
  public userId = this.authIdService.getID();
  public featureFlags: FeatureFlagsConfig;

  private statsigService: StatsigService;

  constructor(
    private authIdService: AuthidService,
    private statsigFactoryService: StatsigFactoryService,
    private mapService: MapService
  ) {
    this.statsigService = this.statsigFactoryService.getInstance();
  }

  async getFeatureFlags(): Promise<FeatureFlagsConfig> {
    if (!this.featureFlags) {
      await this.loadFeatureFlags();
    }

    return this.featureFlags;
  }

  async getDirections(
    sudsterLat: number,
    sudsterLong: number,
    lat: number,
    long: number
  ): Promise<RouteDelivery> {
    const direction = (await this.mapService.getDirections(
      sudsterLat,
      sudsterLong,
      lat,
      long
    )) as MapkitRoute;

    return {
      expectedTravelTime: (direction.expectedTravelTime / 60).toFixed(0),
      distance: (direction.distance / 1609).toFixed(1),
    } as RouteDelivery;
  }

  useNewContractComponentFlag(): boolean {
    return this.featureFlags.useNewContractComponentFlag;
  }

  formatDeadline(target: moment.Moment): string {
    return target.calendar(null, {
      sameDay: '[today by] ha',
      nextDay: '[tomorrow by] ha',
      lastDay: 'MMM D [at] h:mma',
      nextWeek: 'dddd [by] ha',
      lastWeek: 'MMM D [at] h:mma',
      sameElse: 'MMM D [at] h:mma',
    });
  }

  processDynamicData(orderData: OrderData): OrderContractPrice {
    const currentStage = getStage(orderData);

    switch (currentStage) {
      case Stages.Launder: {
        return this.buildContractPrice(
          orderData.StatusHistoryInfo.Pickup.BagCount,
          orderData.BagSize,
          orderData.SameDayService,
          orderData.PriceSchedule,
          null,
          orderData.LargeItems?.Count || 0
        );
      }
      case Stages.Weigh: {
        return this.buildContractPrice(
          orderData.StatusHistoryInfo.Pickup.BagCount,
          orderData.BagSize,
          orderData.SameDayService,
          orderData.PriceSchedule,
          orderData.PreWeight,
          orderData.LargeItems?.Count || 0
        );
      }
      case Stages.Deliver: {
        return this.buildContractPrice(
          orderData.StatusHistoryInfo.Done.BagCount,
          '',
          orderData.SameDayService,
          orderData.PriceSchedule,
          orderData.StatusHistoryInfo.Done.TotalWeight,
          orderData.StatusHistoryInfo.Done.PuffyItems
        );
      }
      default:
        return this.buildContractPrice(
          orderData.OrderSize,
          orderData.BagSize,
          orderData.SameDayService,
          orderData.PriceSchedule,
          orderData.PreWeight,
          orderData.LargeItems?.Count || 0
        );
    }
  }

  calculateEstimatedEarnings(
    bagCount: number,
    bagSize: string,
    sameDay: boolean,
    priceSchedule: PriceSchedule,
    weight?: number,
    largeItems: number = 0
  ): number {
    const pricePerPound = sameDay
      ? priceSchedule?.ExpressRate || 2
      : priceSchedule?.StandardRate || 1;

    const largePrice = 800;

    if (!weight) {
      weight = this.getDefaultWeight(bagSize, bagCount);
    }

    weight *= 100;

    return Math.ceil(
      ((weight * pricePerPound + largeItems * largePrice) / 100) * 0.75
    );
  }

  getDefaultWeight(bagSize: string, bagCount: number): number {
    const sizes = new Map<string, number>();
    sizes.set(BagSize.Small, 11);
    sizes.set(BagSize.Regular, 14);
    sizes.set(BagSize.OverSized, 19);

    return bagCount * sizes.get(bagSize);
  }

  getSudshareRate(priceSchedule: PriceSchedule, sameDay: boolean): number {
    const pricePerPound = sameDay
      ? priceSchedule?.ExpressRate || 2
      : priceSchedule?.StandardRate || 1;

    return pricePerPound * 0.75;
  }

  getDetergent(detergent: string): string {
    if (
      detergent === Detergent.Hypoallergenic ||
      detergent === Detergent.UnscentedHypoallergenic
    ) {
      return Detergent.UnscentedHypoallergenic;
    } else if (detergent == Detergent.ClassicScented) {
      return Detergent.ClassicScented;
    }

    return Detergent.Provided;
  }

  hasDeadLine(state: string): boolean {
    return this.isCA(state);
  }

  canProposeRate(state: string): boolean {
    return this.isCA(state) && this.featureFlags.caContract;
  }

  canAcceptOrder(sudster: SudsterData, order: AvailableOrder): boolean {
    const sudsterFirstOrder = !sudster.OrderCount;
    const sameDayDelivery = order.Order.Delivery === 'SameDay';

    return !sudsterFirstOrder || !sameDayDelivery || this.isVerified(sudster);
  }

  isVerified(sudster: SudsterData): boolean {
    return (
      (!this.featureFlags.stripeIdv || sudster.IdVerification === 'verified') &&
      (!this.featureFlags.checkrVerificationFlag ||
        sudster.CheckrVerification === 'verified')
    );
  }

  trackOrderView(orderNumber: string, sudsterId: string) {
    const LPOrderViewedKeyName = 'LPOrderViewed';
    const resultArrayStr = localStorage.getItem(
      LOCALSTORAGE_KEY_UNIQUE_TRACKEVENTS
    );

    let uniqueEventsArr = [];

    if (resultArrayStr) {
      uniqueEventsArr = JSON.parse(resultArrayStr);
    }

    const alreadyTracked = uniqueEventsArr.find(
      (entry) =>
        entry.id === orderNumber && entry.evtName === LPOrderViewedKeyName
    );

    if (alreadyTracked) {
      return;
    }

    trackEvent({
      eventData: {
        event: LPOrderViewedKeyName,
        userId: sudsterId,
        orderId: orderNumber,
      },
    });

    const trackEntry = {
      id: orderNumber,
      time: new Date().getTime(),
      evtName: LPOrderViewedKeyName,
    };
    uniqueEventsArr.push(trackEntry);

    // delete old entries more than 15 days old
    const now = new Date().getTime();
    uniqueEventsArr = uniqueEventsArr.filter(
      (entry) => now - entry.time < 15 * 24 * 60 * 60 * 1000
    );
    localStorage.setItem(
      LOCALSTORAGE_KEY_UNIQUE_TRACKEVENTS,
      JSON.stringify(uniqueEventsArr)
    );
  }

  private async loadFeatureFlags() {
    this.featureFlags = await firstValueFrom(
      this.statsigService.checkGates<FeatureFlagsConfig>({
        stripeIdv: environment.statsig.flags.stripeIdv,
        checkrVerificationFlag: environment.statsig.flags.checkrVerification,
        hideFirstTimeCustomerOrder: StatsigFlags.HIDE_FIRST_TIME_CUSTOMER_ORDER,
        isExpressServiceEnabled: environment.statsig.flags.ExpressService,
        LPFirstOrderPickUp:
          environment.statsig.flags.LPFirstOrderPickUpDeadline,
        isShowAdditionalDetails:
          environment.statsig.flags.showOrderDataPreacceptance,
        caContract: StatsigFlags.CA_CONTRACT,
        useNewContractComponentFlag: StatsigFlags.NEW_CONTRACT_COMPONENT,
      })
    );
  }

  private isCA(state: string): boolean {
    return state === 'CA';
  }

  private buildContractPrice(
    bagCount: number,
    bagSize: string,
    sameDayService: boolean,
    priceSchedule: PriceSchedule,
    weight: number | null,
    largeItems: number
  ): OrderContractPrice {
    const estimatedEarnings = this.calculateEstimatedEarnings(
      bagCount,
      bagSize,
      sameDayService,
      priceSchedule,
      weight ?? undefined,
      largeItems
    );
    return {
      amount: estimatedEarnings,
      weight: this.getSudshareRate(priceSchedule, sameDayService),
      bags: {
        quantity: bagCount,
        bagSize,
        oversized: largeItems,
      },
    };
  }
}
