@import 'src/theme/type-mixins.scss';

:host {
  display: block;
}

.OrderDiv {
  position: relative;
  height: 125px;
  overflow: hidden;
  padding: 10px;
  padding-top: 15px;
  padding-right: 15px;
  width: 90%;
  max-width: 350px;
  margin: 0 auto;

  .InnerDiv {
    background: white;
    box-shadow: 0 3px 6px rgba(0, 30, 50, 0.15);
    border-radius: 15px;
    width: 100%;
    height: 100%;
    border-bottom: solid 3px var(--Orange);
    position: relative;
  }

  .OrderNumber {
    margin: 0 auto;
    margin-bottom: 5px;
    display: block;
    position: relative;
    width: fit-content;
    max-width: 135px;
    background: var(--BlueGray1);
    border-bottom-right-radius: 20px;
    border-bottom-left-radius: 20px;
    text-align: center;
    overflow: hidden;
    font-size: 10px;
    font-weight: 700;
    color: var(--BlueGray4);
    letter-spacing: 1px;
    padding: 2px 10px;
    box-shadow: 0px 1px 1px rgba(0, 30, 50, 0.15);
  }

  .MissedMsgDiv {
    position: absolute;
    height: 35px;
    width: 50px;
    overflow: hidden;
    border-radius: 30px;
    background: var(--Orange);
    border: solid 2px white;
    box-shadow: -3px 3px 5px rgba(0, 30, 50, 0.15);
    right: -15px;
    top: -15px;
    color: white;
    font-size: 20px;
    font-weight: 900;
    letter-spacing: -2px;
    text-align: center;
    padding-top: 2px;

    i {
      vertical-align: -5px;
      padding-left: 3px;
      font-size: 22px;
    }
  }

  .Name {
    @include xs-head;
    color: var(--BlueGray5);

    margin: 0;
    padding-left: 15px;
    padding-right: 10px;
  }

  .City {
    display: block;
    @include s-body;
    color: var(--BlueGray4);
    width: 100%;
    padding-left: 15px;
  }

  .RightDiv {
    float: right;
    width: 40%;
    text-align: center;
    padding: 5px;
    box-sizing: border-box;

    h1 {
      margin: 0;
      color: var(--BlueGray5);
      text-transform: uppercase;
      letter-spacing: 1px;
      font-weight: 700;
      font-size: 17px;

      i {
        padding-right: 4px;
        vertical-align: -4px;
        font-size: 20px;
      }
    }

    label {
      @include m-body;
      color: var(--gray-800);

      b {
        display: block;
        font-size: 14px;
      }
    }
  }

  .InfoSeparaterLine {
    position: absolute;
    left: calc(60% - 5px);
    margin-top: 5px;
    width: 1px;
    height: 50px;
    background: var(--BlueGray2);
  }

  .LeftDiv {
    float: left;
    width: 60%;
  }
}

#ArrowButton {
  position: absolute;
  right: 0px;
  top: 70px;
  z-index: 2;
  background: var(--BlueGray5);
  border-top-left-radius: 25px;
  border-bottom-left-radius: 25px;
  color: white;
  padding: 10px 0px;
  max-width: 30px;
}

#ArrowCover {
  position: relative;
  z-index: 3;
  height: 100px;
  width: 100px;
  display: inline-block;
  background: #f8fcff;
  margin-left: 20px;
  overflow: hidden;
  white-space: normal;
  font-size: 10px;
  text-align: left;
  padding-right: 10px;
  color: var(--BlueGray4);
  padding-top: 5px;

  b {
    font-weight: 900;
  }
}

.Countdown {
  display: block;
  clear: both;
  text-align: center;
  font-size: 11px;
  color: var(--BlueGray3);
  padding: 2px;
  margin: 0;
  font-weight: 600;
}

#NoOrders {
  text-align: center;
  padding: 10px;
  width: 300px;
  color: var(--BlueGray4);
  font-size: 14px;
  line-height: 135%;
  margin: 0 auto;

  b {
    display: block;
    font-size: 17px;
    padding-bottom: 5px;
  }
}

#OutsideArea {
  width: 90%;
  display: block;
  text-align: center;
  font-size: 14px;
  margin: 10px auto;
}

.new-cust {
  position: absolute;
  bottom: 0px;
  left: 16px;

  ion-badge {
    height: 14px;
    font-size: 10px;
    text-transform: uppercase;
    padding: 1px 6px;
    font-weight: 500;
    margin: 0 5px 0 4px;
  }

  ion-icon {
    font-size: 12px;
    vertical-align: text-bottom;
    margin-left: 4px;
  }
}
.same-day-service-pill {
  background-color: #22ecab;
  border: 1px solid #fff;
  width: 110px;
  height: 30px;
  border-radius: 15px;
  text-align: center;
  padding-top: 6px;
  font-weight: 600;
  font-size: 13px;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  box-shadow: 2px 2px 3px 0px #d9d9d9;
  -webkit-box-shadow: 2px 2px 3px 0px #d9d9d9;
  -moz-box-shadow: 2px 2px 3px 0px #d9d9d9;
  span {
    background-color: #fff;
    border-radius: 100%;
    padding: 2px;
    margin-left: 2px;
  }
}
