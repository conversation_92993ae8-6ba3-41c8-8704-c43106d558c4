import { Component, OnInit } from '@angular/core';
import { ModalController } from '@ionic/angular';

@Component({
  selector: 'app-decline-order',
  templateUrl: './decline-order.component.html',
  styleUrls: ['./decline-order.component.scss'],
})
export class DeclineOrderComponent implements OnInit {
  rejectReason = '';

  constructor(private modalController: ModalController) {}

  ngOnInit() {}

  declineOrder() {
    this.modalController.dismiss({ rejectReason: parseInt(this.rejectReason) });
  }
}
