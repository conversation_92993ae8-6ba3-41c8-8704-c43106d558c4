$green: var(--Green);
$white: #fff;
$curve: cubic-bezier(0.65, 0, 0.45, 1);

.checkmark {
  margin: 15px auto;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: block;
  stroke-width: 5;
  stroke: $white;
  stroke-miterlimit: 10;
  box-shadow: inset 0px 0px 0px $green;
  animation: fill 0.4s ease-in-out 0.4s forwards,
    scale 0.3s ease-in-out 0.9s both;
}

.checkmark__circle {
  stroke-dasharray: 166;
  stroke-dashoffset: 166;
  stroke-width: 2;
  stroke-miterlimit: 10;
  stroke: $green;
  fill: none;
  animation: stroke 0.6s $curve forwards;
}

.checkmark__check {
  transform-origin: 50% 50%;
  stroke-dasharray: 48;
  stroke-dashoffset: 48;
  animation: stroke 0.3s $curve 0.8s forwards;
}

@keyframes stroke {
  100% {
    stroke-dashoffset: 0;
  }
}

@keyframes scale {
  0%,
  100% {
    transform: none;
  }

  50% {
    transform: scale3d(1.1, 1.1, 1);
  }
}

@keyframes fill {
  100% {
    box-shadow: inset 0px 0px 0px 50px $green;
  }
}

h1 {
  text-align: center;
  color: var(--BlueGray4);
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 700;
}

h2 {
  text-align: center;
  font-size: 20px;
  color: var(--BlueGray5);
}

ion-item {
  width: 90%;
  margin: 20px auto;
  border-radius: 5px;
  border: solid 1px var(--BlueGray3);
}

div {
  background: var(--BlueGray1);
  border: dashed 2px var(--BlueGray3);
  width: 90%;
  margin: 20px auto;
  padding: 10px;
  color: var(--BlueGray4);
  font-size: 14px;
}
