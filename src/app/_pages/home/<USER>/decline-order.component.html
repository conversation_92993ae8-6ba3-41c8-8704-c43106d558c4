<h1>Send a message to your customer</h1>

<ion-select
  interface="popover"
  (ionChange)="rejectReason = $event.srcElement.value"
  placeholder="Select a reason for declining:"
>
  <ion-select-option value="0">Not available</ion-select-option>
  <ion-select-option value="1">On vacation</ion-select-option>
  <ion-select-option value="2">Fully booked</ion-select-option>
  <ion-select-option value="3">Too far</ion-select-option>
</ion-select>

<div class="rejectMessage" [ngSwitch]="rejectReason">
  <q *ngSwitchCase="0"
    >Thanks for requesting me! Unfortunately, I'm unavailable today. Please
    request me again next time!</q
  >
  <q *ngSwitchCase="1"
    >Thanks for requesting me! I'm currently on vacation, so unfortunately, I'm
    unable to accept your order today. Please request me again next time!</q
  >
  <q *ngSwitchCase="2"
    >Thanks for requesting me! Unfortunately, I'm unable to accept any more
    orders today as I'm fully booked. Please request me again next time!</q
  >
  <q *ngSwitchCase="3"
    >Thanks for requesting me! Unfortunately, I'm unable to accept your order
    since your pickup location is outside my driving radius.</q
  >
  <q *ngSwitchDefault
    >Select the reason that better describes why you are declining...</q
  >
</div>

<section class="full-width">
  <ion-button
    id="send-button"
    expand="full"
    color="secondary"
    [disabled]="rejectReason === ''"
    (click)="declineOrder()"
    >Send</ion-button
  >
</section>
