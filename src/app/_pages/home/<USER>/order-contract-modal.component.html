<div
  *ngIf="order != null"
  [ngClass]="currentBreakpoint === 0.95 ? 'overflow-scroll' : ''"
  class="container"
>
  <div
    #EstAgreementTooltip
    (blur)="ShowEstAgreement = false"
    *ngIf="ShowEstAgreement"
    id="EstAgreement"
    tabindex="0"
  >
    <b id="Arrow"></b>
    <h1>Estimated Earnings</h1>
    <p>
      This is an estimate of what the system predicts you will earn for this
      order. This estimate is based on the customer's order history, tipping
      patterns, and other factors. This is not a guarantee and your actual
      earnings may differ (up or down). As always, you earn $.75 per pound of
      laundry plus tips.
    </p>
    <ion-button
      (click)="ShowEstAgreement = false"
      expand="block"
      fill="outline"
      id="understand-button"
      size="small"
      >I Understand
    </ion-button>
  </div>

  <div class="header">
    <div class="header__order-number">
      #{{
        order.OrderNumber.startsWith('S') ? order.OrderNumber : order.OrderId
      }}
    </div>
    <div class="header__title">ORDER CONTRACT</div>
  </div>

  <div class="content">
    <div class="content__top">
      <div
        [class.fullWidthName]="!order.Bonus && order['Bonus'] === 0"
        class="content__top__name"
      >
        {{ order.CustomerFirstName }}
        {{ order.CustomerLastName ? order.CustomerLastName : '' }}
      </div>
      <div
        (click)="BonusInfoPopup()"
        *ngIf="!!order.Bonus && order['Bonus'] > 0 && !SameDayService"
        class="content__top__bonus"
      >
        ${{ order.Bonus }} BOOST
      </div>
      <div
        (click)="BonusInfoPopup()"
        *ngIf="order.BonusPoints && order.BonusPoints > 0 && !SameDayService"
        class="content__top__bonus"
      >
        ${{ order.BonusPoints }} POINTS
      </div>
      <div class="pill-container">
        <div
          *ngIf="order.BonusPointsMultiplier == 2"
          class="double-points-pill"
        >
          <span class="same-day-icon">2X</span>
          Points
        </div>
        <div class="same__day__info">
          <ion-icon
            (click)="displaySameDayServiceInfo()"
            *ngIf="SameDayService"
            class="info-icon"
            name="information-circle-outline"
            size="small"
          ></ion-icon>

          <div *ngIf="SameDayService" class="same-day-service-pill">
            {{ isExpressServiceEnabled ? 'EXPRESS' : 'SAME-DAY' }}
            <span class="same-day-icon">$$</span>
          </div>
        </div>
      </div>
    </div>
    <div class="content__details">
      <div class="content__details__left">
        <div class="content__details__left__bags">
          <div class="content__details__left__bags__count">
            {{ bagCount }}<br />
          </div>
          <div class="content__details__left__bags__description">
            {{ bagCount === 1 ? 'BAG' : 'BAGS' }}
          </div>
          <span
            *ngIf="bagSize === 'oversized'"
            class="content__details__left__bags__bagsize"
            >(large)</span
          >
          <span
            *ngIf="bagSize !== 'oversized'"
            class="content__details__left__bags__bagsize"
            >({{ bagSize }})</span
          >
        </div>

        <div (click)="EstimatePopup()" class="content__details__left__estimate">
          <div class="content__details__left__estimate__amount">
            ${{ estimatedEarnings }}
          </div>
          <div class="content__details__left__estimate__description">
            <div class="content__details__left__estimate__description__text">
              ESTIMATE
            </div>
            <br />
          </div>
          <div
            *ngIf="sudsterRate > 0"
            class="content__details__same__day__service__estimate"
          >
            (${{ sudsterRate.toFixed(2) }} / LB)
          </div>
        </div>
      </div>

      <div class="content__details__divider"></div>

      <div class="content__details__location">
        <!-- [innerHTML]="fullAddress" -->
        <div class="content__details__location__place">
          {{ order.Address.City }} {{ order.Address.State }}
          {{ order.Address.ZipCode }}
        </div>
        <div
          (click)="DirectionsInfoPopup()"
          class="content__details__location__distance"
        >
          {{ orderExtra.distanceTime }} min /
          {{ orderExtra.distanceLength }} mile drive
        </div>
      </div>
    </div>

    <div class="content__deadline">
      <ion-label
        *ngIf="SudsterFirstOrder && LPFirstOrderPickUp"
        id="FirstOrderDeadlineNotice"
      >
        <ion-icon name="information-circle-outline"></ion-icon>
        Your first order must be picked up straight away.
      </ion-label>

      <div class="content__deadline__pickup">
        <div class="content__deadline__pickup__description">
          {{
            order.Accepted && this.order.OrderStatusNumber >= 2
              ? 'Picked up at:'
              : 'Pickup Deadline:'
          }}
        </div>
        <div
          [ngClass]="{ same__day__service__deadlines: SameDayService }"
          [style.textDecoration]="SudsterFirstOrder ? 'underline' : 'none'"
          class="content__deadline__pickup__deadline"
        >
          {{ pickupDeadline }}
        </div>
      </div>

      <div class="content__deadline__delivery">
        <div class="content__deadline__delivery__description">
          Delivery Deadline:
        </div>
        <div
          [ngClass]="{ same__day__service__deadlines: SameDayService }"
          class="content__deadline__delivery__deadline"
        >
          {{ deliveryDeadline }}
        </div>
      </div>
    </div>

    <ion-list
      *ngIf="
        (isShowAdditionalDetails && hasAdditionalInfo) || isOverweightOrder
      "
      class="specialInstructionsList"
      lines="none"
    >
      <ion-item *ngIf="isOverweightOrder" class="height-35">
        <i class="material-icons-outlined">scale</i>
        <ion-label> May exceed 100 pounds</ion-label>
      </ion-item>
      <ion-item *ngIf="isBusiness" class="height-35">
        <i class="material-icons-outlined">corporate_fare</i>
        <ion-label> Customer is a business</ion-label>
      </ion-item>
      <ion-item *ngIf="isCustomerFirstOrder" class="height-35">
        <i class="material-icons-outlined">waving_hand</i>
        <ion-label> First-time customer</ion-label>
      </ion-item>
      <ion-item *ngIf="isDelicates" class="height-35">
        <i class="material-symbols-outlined">laundry</i>
        <ion-label> Delicates included</ion-label>
      </ion-item>
      <ion-item *ngIf="isHangDry" class="height-35">
        <i class="material-icons-outlined">dry_cleaning</i>
        <ion-label> Hang-dry items included</ion-label>
      </ion-item>
      <ion-item *ngIf="isHangers" class="height-35">
        <i class="material-icons-outlined">checkroom</i>
        <ion-label> Hangers included</ion-label>
      </ion-item>
      <ion-item *ngIf="isDetergentProvided" class="height-35">
        <i class="material-icons-outlined">bubble_chart</i>
        <ion-label> Detergent provided</ion-label>
      </ion-item>
      <ion-item *ngIf="specialInstructions !== ''">
        <i class="material-icons-outlined">star_outline</i>
        <ion-label> Special Instructions</ion-label>
        <ion-note class="list-helper-text" slot="helper">
          "{{ order.Preferences.Instructions }}"
        </ion-note>
      </ion-item>
    </ion-list>

    <label
      *ngIf="SameDayService && !order.Accepted"
      [ngClass]="{ shake: shake }"
      class="same__day__checkbox"
    >
      <ion-checkbox
        (click)="setSameDayAgreed($event)"
        [(ngModel)]="SameDayAgreed"
        mode="md"
      ></ion-checkbox>
      <span
        >I understand this order is
        {{ isExpressServiceEnabled ? 'express' : 'same-day' }} and cannot be
        extended.</span
      >
    </label>
    <div
      *ngIf="
        SudsterFirstOrder &&
        (stripeIdvFlag || checkrVerificationFlag) &&
        !VerificationComplete
      "
      class="InformDiv"
    >
      <h1>
        <span class="material-icons-outlined" id="ccIcon"> credit_score </span>
        Have your ID ready!
      </h1>
      <p>
        As a first-time Laundry Pro, you'll need to verify your identity before
        you can pick up this order.
      </p>
    </div>
    <div
      *ngIf="
        [
          oredrDetergentTypes.Hypoallergenic,
          oredrDetergentTypes.UnscentedHypoallergenic
        ].includes(order.Detergent || order.Preferences.Detergent)
      "
      class="WarnDiv"
    >
      <ion-icon (click)="HypoInfoPopup()" name="information-circle"></ion-icon>
      <h1>Unscented Hypoallergenic Detergent Required!</h1>
      <label *ngIf="!order.Accepted">
        <ion-checkbox
          [(ngModel)]="HypoAgreed"
          [checked]="HypoAgreed"
        ></ion-checkbox
        >I confirm that I have Unscented Hypoallergenic detergent.
      </label>
    </div>

    <div class="content__accept">
      <div [class.hasFinished]="hasFinished" class="content__accept__slide">
        <div
          *ngIf="!isSuccess && !isError && !order.Accepted"
          [ngClass]="{
            content__accept__slide__background__isLoading__SameDay:
              SameDayService
          }"
          class="content__accept__slide__background__isLoading"
        >
          <div class="content__accept__slide__background__isLoading__inner">
            <div
              class="content__accept__slide__background__isLoading__inner__loading-icon"
            ></div>
          </div>
        </div>
        <div
          *ngIf="isSuccess"
          class="content__accept__slide__background isSuccess"
        >
          <div class="content__accept__slide__background__inner">
            <ion-icon
              class="content__accept__slide__background__inner__success-icon"
              name="checkmark-outline"
            >
            </ion-icon>
          </div>
        </div>
        <div *ngIf="isError" class="content__accept__slide__background isError">
          <div class="content__accept__slide__background__inner">
            <ion-icon
              class="content__accept__slide__background__inner__error-icon"
              name="alert-outline"
            >
            </ion-icon>
          </div>
        </div>
        <div
          (click)="checkSameDayAgreed()"
          *ngIf="!isError && !isSuccess && !order.Accepted"
          aria-hidden="true"
          class="content__accept__slide__pulse"
        ></div>
        <div
          *ngIf="!isError && !isSuccess && !order.Accepted"
          class="content__accept__slide__text"
        >
          SLIDE TO ACCEPT
        </div>
        <ion-range
          #slider
          (ionChange)="ionChange($event)"
          (touchend)="handleSliderMouseEvents('up')"
          (touchstart)="handleSliderMouseEvents('down')"
          *ngIf="!isError && !isSuccess && !order.Accepted"
          [(ngModel)]="sliderValue"
          [disabled]="hasFinished"
          [hidden]="isError && isSuccess"
          class="content__accept__slide__slider"
          debounce="500"
          max="500"
          min="0"
          mode="ios"
        >
        </ion-range>
      </div>

      <div
        *ngIf="!order.Accepted"
        [ngClass]="currentBreakpoint === 0.95 ? 'margin-bottom-overflow' : ''"
        class="content__accept__warning"
      >
        <ion-icon name="alert-circle-outline"></ion-icon>
        <span>Once you accept an order, you <b>CANNOT</b> cancel it.</span>
      </div>
    </div>
  </div>
</div>
