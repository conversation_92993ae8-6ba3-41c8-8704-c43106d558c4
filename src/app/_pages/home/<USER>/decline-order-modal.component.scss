@import 'type-mixins.scss';

:host {
  height: 100%;

  .preview-msg {
    text-align: center;
  }

  .referenceMsg {
    padding: 20px;
    margin: 5px 0 15px;
    border: 1px solid var(--blue-300);
    background: var(--blue-300);
    display: inline-block;
    width: 75%;
    min-width: 290px;
    max-width: 450px;
    border-radius: 25px;
    color: var(--color-content-alt);
    border-bottom-right-radius: 0;
    text-align: left;
  }

  .availability {
    display: flex;
    align-items: center;

    ion-label {
      flex: 1;
      text-align: left;
    }
  }

  .repeat-modal-options {
    ion-item {
      --padding-start: 0;
    }
  }

  .actions {
    text-align: center;

    ion-button {
      min-width: 100%;
      max-width: 360px;
    }
  }

  ion-label.subsection {
    @include m-alt-head;
    line-height: 39px;
    color: var(--BlueGray5);
  }
}
