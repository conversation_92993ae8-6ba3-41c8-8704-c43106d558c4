import {
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { AngularFirestore } from '@angular/fire/firestore';
import {
  AlertController,
  LoadingController,
  ModalController,
} from '@ionic/angular';
import { DailyDeliveryPhotoComponent } from 'src/app/_components/daily-delivery-photo/daily-delivery-photo.component';
import { OrderData } from 'src/app/_interfaces/order-data.interface';
import { AuthidService } from 'src/app/_services/authid.service';
import { MessagesComponent } from '../../active/messages/messages.component';

@Component({
  selector: 'app-greeting',
  templateUrl: './greeting.component.html',
  styleUrls: ['./greeting.component.scss'],
  providers: [MessagesComponent],
})
export class GreetingComponent implements OnInit, OnChanges {
  ETAText = '';
  @Input() CustomerFirstName: string;
  @Input() SudsterFirstName: string;
  @Input() OrderNumber: string;
  @Input() OrderTimezone: string;

  UserID = this.AuthID.getID();

  @ViewChild('MessageDiv') MessageText;

  constructor(
    private messageComp: MessagesComponent,
    private modalCntl: ModalController,
    private loadingController: LoadingController,
    private alertController: AlertController,
    public modalController: ModalController,
    private firestore: AngularFirestore,
    private AuthID: AuthidService,
    private cdr: ChangeDetectorRef // Inject ChangeDetectorRef
  ) {}

  ngOnInit() {
    if (
      !this.OrderNumber ||
      !this.SudsterFirstName ||
      !this.CustomerFirstName
    ) {
      if (!this.UserID) {
        this.UserID = this.AuthID.getID();
      }
      this.getInputVariables();
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (
      !this.OrderNumber ||
      !this.SudsterFirstName ||
      !this.CustomerFirstName
    ) {
      if (!this.UserID) {
        this.UserID = this.AuthID.getID();
      }
      this.getInputVariables();
    }
  }

  getInputVariables() {
    this.firestore
      .collection<OrderData>('Orders', (ref) =>
        ref
          .where('SudsterID', '==', this.UserID)
          .orderBy('TimeStamp', 'desc')
          .limit(1)
      )
      .get()
      .subscribe((orders) => {
        const order = orders.docs[0].data();
        this.OrderNumber = orders.docs[0].id;
        this.SudsterFirstName = order.SudsterName;
        this.CustomerFirstName = order.CustomerFirstName;
      });
  }

  GetGreetingText() {
    if (this.ETAText == '') {
      return '';
    } else {
      return `Hi ${this.CustomerFirstName}. I'm your Laundry Pro, ${this.SudsterFirstName}. I'll arrive to pick up your laundry around <b>${this.ETAText}</b>. Please make sure it’s ready at your pickup spot. Thank you!`;
    }
  }

  TextChange(event: CustomEvent | string) {
    this.ETAText =
      event && event instanceof CustomEvent ? event.detail.value : event || '';
    this.cdr.detectChanges();
  }

  async SendMessage() {
    const loading = await this.presentLoading();
    try {
      await this.messageComp.send(
        this.MessageText.nativeElement.innerText,
        this.OrderNumber,
        this.SudsterFirstName,
        this.OrderTimezone
      );
      await this.modalCntl?.dismiss();
      await loading.dismiss();
      this.OpenDailyDeliveryPhoto();
    } catch (e) {
      await loading.dismiss();
      await this.presentAlert('Message Issue', e);
    }
  }

  OpenDailyDeliveryPhoto() {
    this.firestore
      .doc<any>(`Code/Sudster`)
      .get()
      .subscribe((doc) => {
        const Photo = doc.data().DeliveryPhotoOfTheDay;
        if (
          Photo &&
          parseInt(
            localStorage.getItem('DeliveryPhotoOfTheDayLastShown') || '0'
          ) <
            new Date().getTime() - 3600000 * 24 &&
          parseInt(
            localStorage.getItem('DeliveryPhotoOfTheDayLastShown') || '0'
          ) <
            new Date().getTime() - 3600000 * 24 &&
          Photo.Date.toDate().getTime() > new Date().getTime() - 3600000 * 24
        ) {
          setTimeout(async () => {
            const modal = await this.modalController.create({
              component: DailyDeliveryPhotoComponent,
              componentProps: {
                Photo: Photo,
              },
            });
            await modal.present();
          }, 1000);
        }
      });
  }

  async presentLoading() {
    const loading = await this.loadingController.create({});
    await loading.present();
    return loading;
  }

  async presentAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK'],
    });
    await alert.present();
  }
}
