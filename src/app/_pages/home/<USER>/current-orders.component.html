<ng-container *ngIf="!noActiveOrders && activeOrderList.length > 0">
  <div
    class="order-container"
    *ngFor="let cardData of orderCardDataList; let i = index"
  >
    <app-order-card
      [data]="cardData"
      (actionHandler)="clickActiveOrder($event)"
    ></app-order-card>
  </div>
</ng-container>

<p id="noOrders" *ngIf="noActiveOrders">
  <b>No Active Orders.</b>Go to 'Available Orders' to accept an order.<br />
</p>

<app-contract #contract [viewMode]="true"></app-contract>
