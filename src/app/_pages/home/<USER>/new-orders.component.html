<ion-refresher slot="fixed" (ionRefresh)="RefreshOrderList($event)">
  <ion-refresher-content></ion-refresher-content>
</ion-refresher>

<div
  id="new-order-component"
  *ngFor="let order of NewOrders; let i = index"
  class="OrderDiv"
  [ngClass]="{ preferred: order.Preferred }"
  (click)="!order.Preferred && OpenOrderContract(i)"
>
  <div class="same-day-service-pill" *ngIf="order.SameDayService">
    {{ isExpressServiceEnabled ? 'EXPRESS' : 'SAME-DAY' }}
    <span class="same-day-icon">$$</span>
  </div>
  <div class="double-points-pill" *ngIf="order.BonusPointsMultiplier == 2">
    <span class="same-day-icon">2X</span>
    Points
  </div>
  <div
    id="new-order-content"
    class="InnerDiv"
    [ngClass]="{ priceBoost: order.PriceBoost }"
  >
    <label class="OrderNumber">{{ order.OrderNumber }}</label>

    <div *ngIf="order.Bonus != 0" class="BonusDiv">
      ${{ order.Bonus }}
      <small>BOOST</small>
    </div>
    <div *ngIf="order.PriceBoost && !order.Bonus" class="price-boost-div">
      <span>${{ order.SudsterRate.toFixed(2) }} / LB</span>
    </div>
    <div *ngIf="order.BonusPoints && order.BonusPoints != 0" class="BonusDiv">
      +{{ order.BonusPoints }}
      <small>POINTS</small>
    </div>

    <div class="LeftDiv">
      <h1 class="Name trunc">{{ order.Name }}</h1>
      <label class="City trunc">{{ order.LocationName }}</label>
    </div>

    <b class="InfoSeparaterLine"></b>
    <!-- Display Large to sudster-->
    <div class="EstDiv">
      <label
        >{{ order.BagCount
        }}<small
          ><span class="bag_count">{{
            order.BagCount === 1 ? 'BAG' : 'BAGS'
          }}</span>
          <br />
          <span *ngIf="order.BagSize === 'oversized'" class="EstDiv__bagsize"
            >(large)</span
          >
          <span
            *ngIf="order.BagSize !== 'oversized' && order.BagSize != null"
            class="EstDiv__bagsize"
            >({{ order.BagSize }})</span
          >
        </small></label
      >
      <label
        >${{ order.Earnings
        }}<small class="estimate-text" style="font-size: 0.5em"
          >ESTIMATED
          <small
            *ngIf="order.PriceBoost"
            style="font-size: 10px; color: #909090"
            >${{ order.SudsterRate.toFixed(2) }} / LB</small
          >
        </small></label
      >
    </div>

    <!-- end EstDiv -->
    <div *ngIf="order.Preferred" class="preferred-order-acccept">
      <p>
        {{
          order.FromDualReferral
            ? 'Referred by Customer'
            : "You've Been Requested"
        }}
      </p>
      <br />
      <button class="button-open" (click)="OpenOrderContract(i)">OPEN</button>
      <button class="button-close" (click)="PreferredDeclineAlert(order, i)">
        DECLINE
      </button>
    </div>
  </div>
</div>

<p id="NoOrders" *ngIf="NewOrders.length == 0 && LoadingOrders == false">
  <b>No Available Orders.</b> Orders are assigned on a first-come first-serve
  basis. To increase your chances of getting orders, accept them immediately
  upon getting a New Order Notice.<br />
  <ion-button
    id="more-info-button"
    size="small"
    fill="clear"
    (click)="noOrdersInfoAlert()"
    >More Info</ion-button
  >
</p>

<label id="LoadingLabel" *ngIf="LoadingOrders && NewOrders.length == 0"
  >Loading...</label
>

<span id="OutsideArea" *ngIf="OutsideAreaCounter > 0"
  >There are <b>({{ OutsideAreaCounter }})</b> available orders outside your
  work area. Consider widening your work area in settings.</span
>
