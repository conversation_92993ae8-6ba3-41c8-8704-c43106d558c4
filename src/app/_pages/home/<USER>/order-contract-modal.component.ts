import {
  After<PERSON>iew<PERSON>nit,
  Component,
  ElementRef,
  Input,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { AngularFireAuth } from '@angular/fire/auth';
import { AngularFirestore } from '@angular/fire/firestore';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>er, ModalController } from '@ionic/angular';
import { LoadingController } from '@ionic/angular/standalone';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import moment from 'moment';
import { TermsAndPoliciesComponent } from 'src/app/_components/terms-and-policies/terms-and-policies.component';
import { SudsterData } from 'src/app/_interfaces/sudster-data.interface';
import { AnalyticsLogService } from 'src/app/_services/analytics/analytics-log.service';
import { LaundryProService } from 'src/app/_services/api/laundry-pro.service';
import { AuthidService } from 'src/app/_services/authid.service';
import { LegacyApiService } from 'src/app/_services/legacy-api.service';
import { MapService } from 'src/app/_services/map.service';
import { OrderExtensionService } from 'src/app/_services/order-extension.service';
import { StatsigFactoryService } from 'src/app/_services/statsig-factory.service';
import { StatsigService } from 'src/app/_services/statsig.service';
import { StatsigFlags } from 'src/app/_utils/enum';
import { getStage } from 'src/app/_utils/order-utils';
import { environment } from 'src/environments/environment';
import tz_lookup from 'tz-lookup';
import {
  BagSize,
  OrderData,
  PriceSchedule,
  Stages,
} from '../../../_interfaces/order-data.interface';
import { DATETIME_FORMAT } from '../../../_utils/enum';
import { VerificationConfirmComponent } from '../../verification/verification-confirm/verification-confirm.component';
import { Detergent } from '../current-orders/adapters/active-order.adapter';

interface responseJson {
  title?: string;
  firstName: string;
  errors: string[];
  type: string;
  message: string;
}

@UntilDestroy()
@Component({
  selector: 'app-order-contract-modal',
  templateUrl: './order-contract-modal.component.html',
  styleUrls: ['./order-contract-modal.component.scss'],
})
export class OrderContractModalComponent
  implements OnInit, OnDestroy, AfterViewInit
{
  readonly acceptanceThreshold = 473;
  readonly sliderStartValue = 0;
  @ViewChild('EstAgreementTooltip') estTooltipRef!: ElementRef;
  @Input() order: any = null;
  @Input() SudsterFirstOrder: boolean;
  @Input() flexibleExtension: boolean;
  @Input() IdVerification: string;
  @Input() CheckrVerification: string;
  @Input() sudsterRate: number;
  @Input() isBusiness: boolean;
  OrderCount: number;
  VerificationComplete: boolean;
  bagSize: string;
  bagCount: number;
  estimatedEarnings: number;
  largeItems: number;
  oredrDetergentTypes = Detergent;

  currentSliderPosition = this.sliderStartValue;
  ShowEstAgreement = false;
  orderExtra = { distanceTime: '~', distanceLength: '~' };
  UserID = this.AuthID.getID();
  sameDayDeliveryToggle = false;
  isExpressServiceEnabled = false;
  autoMoveToStartInterval;
  isSelecting = false;
  hasFinished = false;
  isSuccess = false;
  isError = false;
  HypoAgreed = false;
  SameDayAgreed: boolean;
  shake: boolean;
  SameDayService: boolean;
  orderTimezone: string = null;
  isUp: boolean;
  accepted: boolean;
  backdrop: HTMLElement;
  stripeIdvFlag = false;
  checkrVerificationFlag = false;
  isDetergentProvided: boolean;
  isCustomerFirstOrder: boolean;
  LPFirstOrderPickUp: boolean;
  isDelicates: boolean;
  isHangDry: boolean;
  specialInstructions: string = null;
  currentBreakpoint: number;
  isShowAdditionalDetails: boolean;
  displayVerificationModal: boolean = true;
  isHangers: boolean = false;
  hasAdditionalInfo: boolean = false;
  isOverweightOrder: boolean = false;
  currentHandler: (ev: CustomEvent) => void;
  private isTouchEnabled: boolean;
  private hideFirstTimeCustomerOrder: boolean;
  private statsigService: StatsigService;

  constructor(
    private alertController: AlertController,
    private mapService: MapService,
    public afAuth: AngularFireAuth,
    public firestore: AngularFirestore,
    public modalController: ModalController,
    private AuthID: AuthidService,
    private apiService: LegacyApiService,
    private statsigFactoryService: StatsigFactoryService,
    private logService: AnalyticsLogService,
    private orderExtensionService: OrderExtensionService,
    private loadingController: LoadingController,
    private laundryProService: LaundryProService
  ) {
    this.statsigService = this.statsigFactoryService.getInstance();
    this.firestore
      .doc<SudsterData>(`Sudsters/${this.UserID}`)
      .valueChanges()
      .pipe(untilDestroyed(this))
      .subscribe((doc) => {
        this.OrderCount = doc.OrderCount;
        this.VerificationComplete = doc.ConfirmedVerification
          ? doc.ConfirmedVerification
          : false;

        const lat = this.order.Address.Lat || this.order.GeoLocation.latitude;
        const long =
          this.order.Address.Long || this.order.GeoLocation.longitude;

        this.mapService
          .getDirections(
            doc.WorkAreaCenter.latitude,
            doc.WorkAreaCenter.longitude,
            lat,
            long
          )
          .then((dir: any) => {
            this.orderExtra = {
              distanceTime: (dir.expectedTravelTime / 60).toFixed(0),
              distanceLength: (dir.distance / 1609).toFixed(1),
            };
          });
      });

    this.statsigService
      .checkGate(environment.statsig.flags.stripeIdv)
      .subscribe((value) => {
        this.stripeIdvFlag = value;
      });
    this.statsigService
      .checkGate(environment.statsig.flags.checkrVerification)
      .subscribe((value) => {
        this.checkrVerificationFlag = value;
      });
    this.statsigService
      .checkGate(StatsigFlags.HIDE_FIRST_TIME_CUSTOMER_ORDER)
      .subscribe((value) => {
        this.hideFirstTimeCustomerOrder = value;
      });
  }

  get pickupDeadline() {
    if (this.order.Accepted) {
      if (this.order.OrderStatusNumber >= 2) {
        return moment
          .tz(this.order.StatusHistoryInfo.Pickup.UnixTime, this.orderTimezone)
          .format(DATETIME_FORMAT);
      }

      return moment
        .tz(this.order.PickupDeadline, this.orderTimezone)
        .format(DATETIME_FORMAT);
    }

    const pickupMoment = moment.tz(
      this.order.PickupTimestamp || this.order.PickupDeadline,
      this.orderTimezone
    );

    const now = moment().tz(this.orderTimezone);

    if (this.flexibleExtension) {
      pickupMoment.add(1, 'days');
    }

    // new sudster will have a 3 full hours window to pickup today or before 11am next day.
    if (
      this.SudsterFirstOrder &&
      !this.SameDayService &&
      this.LPFirstOrderPickUp
    ) {
      const placedMoment = moment.tz(
        (this.order.CreatedTimestamp || this.order.TimeStamp).toMillis(),
        this.orderTimezone
      );
      // pickupDeadline is same day as placed:
      //    pick up must be withing the next 3 hours of accepted of before 7pm
      //    pickupDeadline is next day as placed:
      //    if checking the app the same day as placed, shows 'tomorrow before 11am'
      //    if checking the app next day as placed, shows 'today before 11am'

      if (placedMoment.isSame(pickupMoment, 'day')) {
        // show deadline withing the next '3' hours.
        // remove any elapse mins, seconds and guarantee 3 full hours (may end up with 4 hours).
        now.minutes(0).seconds(0).add(4, 'hours');
        if (now.hours() > 19) {
          // max pickup time is 7pm
          now.hours(19);
        }

        return 'Today, ' + this.formatAMPM(now, this.orderTimezone);
      } else {
        return `${
          pickupMoment.isSame(now, 'day') ? 'Today' : 'Tomorrow'
        } before 11am`;
      }
    }

    if (this.SameDayService) {
      const placedMoment = moment.tz(
        (this.order.CreatedTimestamp || this.order.TimeStamp).toMillis(),
        this.orderTimezone
      );

      if (
        placedMoment.hours() < 10 ||
        (placedMoment.hours() >= 15 && now.days() > placedMoment.days())
      ) {
        return `Today, 2PM`;
      } else if (placedMoment.hours() >= 15) {
        return `Tomorrow, 2PM`;
      }
    }
    return `${
      pickupMoment.isSame(now, 'day') ? 'Today' : 'Tomorrow'
    }, ${this.formatAMPM(pickupMoment, this.orderTimezone)}`;
  }

  get deliveryDeadline() {
    if (this.order.Accepted) {
      const deliveryDeadline = moment.tz(
        this.order.DeliveryDeadline,
        this.orderTimezone
      );

      return deliveryDeadline.format(DATETIME_FORMAT);
    }

    const deliveryMoment = moment.tz(
      this.order.Accepted
        ? this.order.DeliveryDeadline
        : this.order.DeliveryTimestamp,
      this.orderTimezone
    );
    const now = moment().tz(this.orderTimezone);
    if (!this.SameDayService) {
      if (this.flexibleExtension) {
        deliveryMoment.add(1, 'days');
      }

      if (deliveryMoment.isSame(now, 'day')) {
        return `Today ${this.formatAMPM(deliveryMoment, this.orderTimezone)}`;
      }

      // next calculation set the minutes, seconds to match now and future date,
      // or it will depend on the time the user checks the contract.
      if (
        deliveryMoment
          .clone()
          .startOf('day')
          .diff(now.clone().startOf('day'), 'days') == 1
      ) {
        return `Tomorrow ${this.formatAMPM(
          deliveryMoment,
          this.orderTimezone
        )}`;
      }
    }

    if (this.SameDayService) {
      const placedMoment = moment.tz(
        (this.order.CreatedTimestamp || this.order.TimeStamp).toMillis(),
        this.orderTimezone
      );
      if (
        placedMoment.hours() < 10 ||
        (placedMoment.hours() >= 15 && now.days() > placedMoment.days())
      ) {
        return `Today, 8PM`;
      } else if (placedMoment.hours() >= 15) {
        return `Tomorrow, 8PM`;
      }
    }
    return `${deliveryMoment.format('dddd')}, ${this.formatAMPM(
      deliveryMoment,
      this.orderTimezone
    )}`;
  }

  ngOnInit() {
    this.statsigService
      .checkGate(environment.statsig.flags.ExpressService)
      .subscribe((expressservice) => {
        this.isExpressServiceEnabled = expressservice;
      });

    this.statsigService
      .checkGate(environment.statsig.flags.LPFirstOrderPickUpDeadline)
      .subscribe((lpfirstorderpickupdeadline) => {
        this.LPFirstOrderPickUp = lpfirstorderpickupdeadline;
      });

    const modal = document.querySelector('ion-modal');
    if (modal) {
      const modalShadowRoot = modal.shadowRoot;
      if (modalShadowRoot) {
        this.backdrop = modalShadowRoot.querySelector('ion-backdrop');
        if (this.backdrop) {
          this.currentHandler = this.getBackdropHandler();
          this.backdrop.addEventListener('ionBackdropTap', this.currentHandler);
        }
      }

      this.statsigService
        .checkGate(environment.statsig.flags.showOrderDataPreacceptance)
        .subscribe((v) => {
          this.isShowAdditionalDetails = v;
          if (this.isShowAdditionalDetails) {
            this.setDetailsFlags();
            this.setBreakpoint(modal);
          }
        });
      this.isOversizedOrder();
    }
    this.SameDayService =
      this.order.Delivery === 'SameDay' || this.order.SameDayService; // triggers the class for same day stuff
    this.isTouchEnabled = 'ontouchstart' in window;
    this.orderTimezone = this.order.Timezone
      ? this.order.Timezone
      : tz_lookup(this.order.Lat, this.order.Lng);

    this.processDynamicData();
  }

  ngAfterViewInit() {
    this.showEstAgreement();
  }

  showEstAgreement(): void {
    this.ShowEstAgreement = this.SudsterFirstOrder;
    if (this.ShowEstAgreement)
      setTimeout(() => {
        this.estTooltipRef?.nativeElement.focus();
      });
  }

  processDynamicData(): void {
    this.bagSize = this.order.BagSize;
    this.bagCount = this.order.BagCount;
    this.estimatedEarnings = this.order.EstimatedEarnings;
    this.largeItems = this.order.LargeItems?.Count || 0;

    // Calculate the contract is shown on active orders.
    if (this.order.Accepted) {
      const orderData = this.order as OrderData;
      const currentStage = getStage(orderData);

      this.bagCount = orderData.OrderSize;

      switch (currentStage) {
        case Stages.Launder:
          this.bagCount = orderData.StatusHistoryInfo.Pickup.BagCount;

          this.estimatedEarnings = this.calculateEstimatedEarnings(
            this.bagCount,
            this.bagSize,
            this.SameDayService,
            orderData.PriceSchedule,
            null,
            this.largeItems
          );
          break;
        case Stages.Weigh:
          this.bagCount = orderData.StatusHistoryInfo.Pickup.BagCount;

          this.estimatedEarnings = this.calculateEstimatedEarnings(
            this.bagCount,
            this.bagSize,
            this.SameDayService,
            orderData.PriceSchedule,
            orderData.PreWeight,
            this.largeItems
          );
          break;
        case Stages.Deliver:
          this.bagCount = orderData.StatusHistoryInfo.Done.BagCount;
          this.largeItems = orderData.StatusHistoryInfo.Done.PuffyItems;
          this.bagSize = 'to deliver';

          this.estimatedEarnings = this.calculateEstimatedEarnings(
            this.bagCount,
            this.bagSize,
            this.SameDayService,
            orderData.PriceSchedule,
            orderData.StatusHistoryInfo.Done.TotalWeight,
            this.largeItems
          );
          break;
      }
    }
  }

  calculateEstimatedEarnings(
    bagCount: number,
    bagSize: string,
    sameDay: boolean,
    priceSchedule: PriceSchedule,
    weight?: number,
    largeItems: number = 0
  ): number {
    const pricePerPound = sameDay
      ? priceSchedule?.ExpressRate || 2
      : priceSchedule?.StandardRate || 1;

    const largePrice = 800;

    if (!weight) {
      const sizes = new Map<string, number>();
      sizes.set(BagSize.Small, 11);
      sizes.set(BagSize.Regular, 14);
      sizes.set(BagSize.OverSized, 19);

      weight = bagCount * sizes.get(bagSize);
    }

    weight *= 100;

    return Math.ceil(
      ((weight * pricePerPound + largeItems * largePrice) / 100) * 0.75
    );
  }

  ngOnDestroy(): void {
    if (this.backdrop) {
      this.backdrop.removeEventListener('ionBackdropTap', this.currentHandler);
    }
  }

  getBackdropHandler() {
    return (e) => {
      e.stopPropagation();
      if (!this.accepted || this.isError) {
        this.modalController.dismiss();
      }
    };
  }

  async presentAlert(
    header: string,
    message: string
  ): Promise<HTMLIonAlertElement> {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK'],
    });
    await alert.present();
    return alert;
  }

  EstimatePopup() {
    this.presentAlert(
      'Estimated Earnings',
      "This is an estimate of what the system predicts you will earn for this order. This estimate is based on the customer's order history, tipping patterns, and other factors. This is not a guarantee and your actual earnings may differ (up or down). As always, you earn $.75 per pound of laundry plus tips."
    );
  }

  SameDayInfoPopup() {
    this.presentAlert(
      `${this.isExpressServiceEnabled ? 'Express' : 'Same-Day'} Delivery`,
      `This order requires ${
        this.isExpressServiceEnabled ? 'express' : 'same-day'
      } delivery. ${
        this.isExpressServiceEnabled ? 'Express' : 'Same-Day'
      } delivery orders pay you more. There are other differences too. See below for details. <ul><li>You earn an extra 100% ($1.50/LB)</li><li>Minimum earning level increased to $30</li><li>Tip recommendations increased</li><li>Order limited to 60 LBS</li><li>Pickup deadline 1pm today</li><li>Delivery deadline 8pm today</li></ul> Failure to pickup or deliver on time will result in the customer being refunded.`
    );
  }

  HypoInfoPopup() {
    this.presentAlert(
      'Unscented Hypoallergenic Detergent',
      'Unscented Hypoallergenic detergent is free from dyes, perfumes, scents, and optical brighteners. Some customers need unscented hypoallergenic detergent because they have sensitive skin, allergies, or other health issues. So this is not something to mess around with. <br><br>About 20% of customers require unscented hypoallergenic detergent, so it’s worthwhile to have some in supply. Our recommended unscented hypoallergenic detergent is Persil Power Liquid. It’s the highest rated by Consumer Reports and is very economical. If you want the option of accepting unscented hypoallergenic orders too, pick up some now.'
    );
  }

  BonusInfoPopup() {
    this.presentAlert(
      'Extra Boost!',
      "This order has an extra boost. After you deliver the order, you'll be paid the boost amount in addition to your main order payment plus tips. Boosts don't last long, so accept them quick before someone else does!"
    );
  }

  DirectionsInfoPopup() {
    this.presentAlert(
      'Drive Info',
      'The distance and travel time estimate is calculated by the fastest route from your home address to the customer if you leave now. Travel time includes current traffic delays.'
    );
  }

  autoMoveToStart() {
    this.autoMoveToStartInterval = setInterval(() => {
      if (
        this.currentSliderPosition > this.sliderStartValue &&
        !this.hasFinished &&
        !this.isSelecting
      ) {
        this.currentSliderPosition -= 4;
      } else {
        this.currentSliderPosition = this.sliderStartValue;
        clearInterval(this.autoMoveToStartInterval);
      }
    }, 1);
  }

  handleSliderMouseEvents(event: 'up' | 'down') {
    if (event === 'up') {
      this.isUp = true;
      this.isSelecting = false;
      if (
        this.currentSliderPosition >= this.acceptanceThreshold &&
        !this.hasFinished &&
        this.isSelecting === false
      ) {
        clearInterval(this.autoMoveToStartInterval);

        if (!this.accepted) {
          this.AcceptOrder();
        }
      }
      this.autoMoveToStart();
    } else if (event === 'down') {
      this.isUp = false;
      this.isSelecting = true;
      clearInterval(this.autoMoveToStartInterval);
    }
  }

  toggleSameDayDelivery() {
    this.sameDayDeliveryToggle = !this.sameDayDeliveryToggle;
  }

  async AcceptOrder() {
    if (this.SameDayService && !this.SameDayAgreed) {
      this.shake = true;
      setTimeout(() => {
        this.shake = false;
      }, 1000);
      return;
    }

    if (
      (this.order.SameDay && !this.SameDayAgreed) ||
      ((this.order.Detergent === Detergent.Hypoallergenic ||
        this.order.Detergent === Detergent.UnscentedHypoallergenic) &&
        !this.HypoAgreed)
    ) {
      this.isSuccess = false;
      this.hasFinished = false;
      this.isSelecting = false;
      clearInterval(this.autoMoveToStartInterval);
      this.presentAlert(
        'Check to Agree',
        'Before accepting, you must agree to the order contract by tapping the checkbox.'
      );
      return;
    }
    // If New Sudster has not been through verification yet, send to IDV flow and move order to 'pending' state

    if (
      this.SudsterFirstOrder &&
      this.SameDayService &&
      (this.CheckrVerification !== 'verified' ||
        this.IdVerification !== 'verified')
    ) {
      this.presentAlert(
        'Cannot accept order',
        `You must be at Bronze level or above to accept ${
          this.isExpressServiceEnabled ? 'express' : 'same-day'
        } orders`
      );
      return;
    }
    if (this.SudsterFirstOrder && this.notVerified()) {
      // if (mainflag === true && )

      this.logService.logIDVStart(
        this.order.OrderNumber,
        this.checkrVerificationFlag,
        this.stripeIdvFlag
      );
      if (this.displayVerificationModal) {
        this.displayVerificationModal = false;
        this.presentVerificationConfirmation();
      }
      return;
    } else {
      this.accepted = true;
      this.hasFinished = true; // Finished Slide - start loading

      try {
        const apiResponse = await this.acceptOrderRequest();

        const response = apiResponse.rawResponse;
        const responseInJson = (await apiResponse.data) as responseJson;
        if (response.status == 200) {
          this.isSuccess = true;
          this.modalController.dismiss({
            CustomerFirstName: responseInJson.firstName,
          });
        } else {
          this.acceptErrorHandler(response, responseInJson);
        }
      } catch (error) {
        this.isError = true;
        this.presentAlert('Error', error);
      }
    }
  }

  parseErrorData(
    statusCode: number,
    responseInJson: responseJson
  ): {
    type: string;
    title: string;
    message: string;
  } {
    const mapError = new Map([
      [409, { title: 'Order Unavailable', message: responseInJson.message }],
      [400, { title: 'Error', message: responseInJson.errors?.toString() }],
    ]);

    const errorData = mapError.get(statusCode);

    return {
      title: errorData?.title ?? responseInJson?.title ?? 'Error',
      type: responseInJson.type,
      message: errorData?.message ?? responseInJson.message,
    };
  }

  async openTermsAndPolicies() {
    const loadingTermsToAccpet = await this.loadingController.create({
      message: 'Loading terms',
      backdropDismiss: false,
    });
    loadingTermsToAccpet.present();
    const termsToAccept =
      await this.laundryProService.getTermsAndPoliciesToAccept(
        this.AuthID.UserID
      );

    await loadingTermsToAccpet.dismiss();

    if (termsToAccept.policies.needAccept || termsToAccept.terms.needAccept) {
      const termsAndPoliciesModal = await this.modalController.create({
        component: TermsAndPoliciesComponent,
        cssClass: 'modal-fullscreen',
        backdropDismiss: false,
        componentProps: {
          termsAndPolicies: {
            terms: termsToAccept.terms.term,
            policies: termsToAccept.policies.privacyPolicy,
          },
        },
      });
      await termsAndPoliciesModal.present();
      const confirmTermsAndPolicies =
        await termsAndPoliciesModal.onDidDismiss();

      if (
        confirmTermsAndPolicies.role === 'confirm' &&
        confirmTermsAndPolicies.data.accepted
      ) {
        const loading = await this.loadingController.create({
          message: 'Saving terms and policies accepted',
        });
        await loading.present();
        await this.laundryProService.saveNewTermsAndPoliciesAccepted(
          this.AuthID.UserID,
          {
            policyVersion: termsToAccept.policies.needAccept
              ? confirmTermsAndPolicies.data.policyVersion
              : undefined,
            termsVersion: termsToAccept.terms.needAccept
              ? confirmTermsAndPolicies.data.termsVersion
              : undefined,
          }
        );
        loading.dismiss();
      }
    }
  }

  async acceptErrorHandler(response: Response, responseInJson: responseJson) {
    this.isError = true;
    const { type, title, message } = this.parseErrorData(
      response.status,
      responseInJson
    );
    console.error(type, title, message);
    const alertError = await this.presentAlert(title, message);
    await alertError.onDidDismiss();
    if (type === 'terms_and_service_required') {
      await this.openTermsAndPolicies();
    }
  }

  formatAMPM(date: Date | moment.Moment, timezone: string) {
    const dateMoment = moment.tz(date, timezone);

    return dateMoment.format('h A');
  }

  ionChange($event: CustomEvent) {
    this.currentSliderPosition = $event.detail.value;
    if (!this.isTouchEnabled) {
      if (this.currentSliderPosition >= this.acceptanceThreshold) {
        //simulate condition for touch finished
        this.handleSliderMouseEvents('down');
        this.handleSliderMouseEvents('up');
      } else {
        this.autoMoveToStart();
      }
    }

    if (this.isUp) {
      this.isSelecting = false;

      if (
        this.currentSliderPosition >= this.acceptanceThreshold &&
        !this.hasFinished
      ) {
        clearInterval(this.autoMoveToStartInterval);

        if (!this.accepted) {
          this.AcceptOrder();
        }
      }
      this.autoMoveToStart();
    }
  }

  async displaySameDayServiceInfo(): Promise<HTMLIonAlertElement> {
    const message = `<strong>Deadlines</strong>: ${
      this.isExpressServiceEnabled
        ? 'For express orders : \n Placed before 10 am: <ul><li>Pickup by 2 pm</li><li>Delivery by 8 pm</li></ul> Placed 10 am-2:59 pm: <ul><li>Pickup by 8 pm</li><li>Delivery by 10 am next-day</li></ul> ​Placed after 3 pm: <ul><li>Pickup next day by 2 pm</li><li>Delivery next day by 8 pm</li></ul>'
        : 'For same-day orders placed before 10am, the pickup-deadline is 2pm, and the delivery deadline is 8pm that day. For same-day orders placed after 3pm, the deadlines are shifted to the next-day.\n\n'
    }
      <strong>Pay:</strong> You earn 2x on ${
        this.isExpressServiceEnabled ? 'express' : 'same-day'
      } orders ($1.50/LB instead of $0.75/LB). Minimum pay is also 2x, $30 instead of $15. \n\n<strong>Weight limit:</strong> Customers are told that if their order is more than 60 pounds the Laundry Pro may need more time. \n\n<strong>Hang-dry:</strong>
      Customers are not given a hang-dry option on ${
        this.isExpressServiceEnabled ? 'express' : 'same-day'
      } orders.\n\n <strong>Extensions:</strong> Extension requests are disabled for ${
      this.isExpressServiceEnabled ? 'express' : 'same-day'
    } orders, and late deliveries result in reduced pay.\n\n<strong>Eligibility:</strong> Bronze-badged Laundry Pros and higher are eligible to accept ${
      this.isExpressServiceEnabled ? 'express' : 'same-day'
    } orders. `.replaceAll('\n', '<br>');

    return await this.presentAlert(
      `${this.isExpressServiceEnabled ? 'Express' : 'Same Day'} Service`,
      message
    );
  }

  setSameDayAgreed(ev: any): boolean {
    return (this.SameDayAgreed = ev.target.checked);
  }

  checkSameDayAgreed(): HTMLIonAlertElement {
    if (this.SameDayService && !this.SameDayAgreed) {
      this.presentAlert(
        `${this.isExpressServiceEnabled ? 'Express' : 'Same Day'} Order`,
        `Please check that you understand that this is a ${
          this.isExpressServiceEnabled ? 'express' : 'same day'
        } order and the deadline cannot be extended.`
      );
    } else {
      return;
    }
  }

  async presentVerificationConfirmation(): Promise<HTMLIonModalElement> {
    const modal = await this.modalController.create({
      component: VerificationConfirmComponent,
      cssClass: 'verification-confirm-component',
      componentProps: {
        OrderNumber: this.order.OrderNumber,
        checkrVerificationFlag: this.checkrVerificationFlag,
        stripeIdvFlag: this.stripeIdvFlag,
        UserID: this.UserID,
      },
      backdropDismiss: false,
      backdropBreakpoint: 0.8,
    });
    await this.modalController.dismiss();
    await modal.present();
    return modal;
  }

  notVerified(): boolean {
    return (
      (!this.stripeIdvFlag || this.IdVerification === 'verified') &&
      (!this.checkrVerificationFlag || this.CheckrVerification === 'verified')
    );
  }

  setDetailsFlags() {
    this.isCustomerFirstOrder =
      (this.order.IsCustomerFirstOrder && !this.hideFirstTimeCustomerOrder) ||
      this.order.FirstOrder;
    this.isDelicates = this.order.Preferences.Delicates;
    this.isDetergentProvided = (
      this.order.Detergent || this.order.Preferences.Detergent
    ).includes('I Will Provide');
    this.isHangDry = this.order.Preferences.HangDry;
    this.isHangers = this.order.Preferences.Hangers;
    this.specialInstructions = this.order.Preferences.Instructions;

    this.hasAdditionalInfo =
      this.isBusiness ||
      this.isCustomerFirstOrder ||
      this.isDetergentProvided ||
      this.isDelicates ||
      this.isHangDry ||
      this.isHangers ||
      this.specialInstructions !== '';
  }

  setBreakpoint(modal: HTMLIonModalElement) {
    modal
      .getCurrentBreakpoint()
      .then((x) => {
        this.currentBreakpoint = x;
      })
      .catch((e) => {
        console.error('Error retrieving breakpoint: ', e);
      });
  }

  isOversizedOrder() {
    this.orderExtensionService
      .canExtendAvailableOrder(
        this.order.BagSize,
        this.OrderCount,
        this.SameDayService
      )
      .then((val) => {
        this.isOverweightOrder = val;
      });
  }

  async acceptOrderRequest() {
    const body = {
      sudsterId: this.UserID,
      orderNumber: this.order.OrderNumber,
      acceptedExtension: this.flexibleExtension,
      estimatedDriveTime: this.orderExtra.distanceTime,
    };

    const isNewOrderFlow = this.order.IsNewOrderFlow;

    if (isNewOrderFlow) {
      return this.apiService
        .put(`/${this.order.OrderNumber}/status/accept`, body, {
          baseUrl: `${environment.apiPathV2}/orders`,
        })
        .toPromise();
    }

    return this.apiService.post('UpdateOrder/v1/accept', body).toPromise();
  }
}
