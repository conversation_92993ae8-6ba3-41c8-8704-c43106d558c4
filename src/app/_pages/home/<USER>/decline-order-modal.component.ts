import { Component, Input, OnInit } from '@angular/core';
import { LoadingController, ModalController } from '@ionic/angular';
import moment from 'moment-timezone';
import { LegacyApiService } from 'src/app/_services/legacy-api.service';
import { getOrdinalDay, tokenReplace } from 'src/app/_utils/utils';
import { environment } from 'src/environments/environment';

enum DeclineReason {
  UNAVAILABLE = 'unavailable',
  VACATION = 'vacation',
  FULLY_BOOKED = 'fullyBooked',
  DISTANCE = 'distance',
}

@Component({
  selector: 'app-decline-order-modal',
  templateUrl: './decline-order-modal.component.html',
  styleUrls: ['./decline-order-modal.component.scss'],
})
export class DeclineOrderModalComponent implements OnInit {
  includeAvailability = false;
  sendingDecline = false;
  previewDeclineMessage: string = '';
  submitBtnDisabled: boolean = false;
  isNewOrderFlow: boolean = false;

  declineReasons = [
    { label: 'Not available', value: DeclineReason.UNAVAILABLE },
    { label: 'On vacation', value: DeclineReason.VACATION },
    { label: 'Fully booked', value: DeclineReason.FULLY_BOOKED },
    { label: 'Too far', value: DeclineReason.DISTANCE },
  ];

  declineMessageMap: Record<string, string> = {
    unavailable:
      'Thanks for requesting me! Unfortunately, I’m unavailable today. Hopefully next time!',
    vacation:
      'Thanks for requesting me! I’m currently on vacation, so unfortunately, I’m unable to accept your order today. Hopefully next time!',
    fullyBooked:
      'Thanks for requesting me! Unfortunately, I’m unable to accept any more orders today as I’m fully booked. Hopefully next time!',
    distance:
      'Thanks for requesting me! Unfortunately, I’m unable to accept your order since your pickup location is outside my driving radius.',
  };

  declineMessageMapWithAvailability: Record<string, string> = {
    unavailable:
      'Thanks for requesting me! Unfortunately, I’m currently unavailable to take your order. My next availability is on {{dayName}}, {{dayMonth}} {{dayNumber}}, if you’d like to place an order with me at that time.',
    vacation:
      'Thanks for requesting me! I’m currently on vacation, so unfortunately, I’m unable to accept your order today. My next availability is on {{dayName}}, {{dayMonth}} {{dayNumber}}, if you’d like to place an order with me at that time.',
    fullyBooked:
      'Thanks for requesting me! Unfortunately, I’m unable to accept any more orders today as I’m fully booked. My next availability is on {{dayName}}, {{dayMonth}} {{dayNumber}}, if you’d like to place an order with me at that time.',
    distance:
      'Thanks for requesting me! Unfortunately, I’m unable to accept your order since your pickup location is outside my driving radius today. My next availability is on {{dayName}}, {{dayMonth}} {{dayNumber}}, if you’d like to place an order with me at that time.',
  };

  selectedNextAvailableDate: Date;
  selectedDeclineReason: string = DeclineReason.UNAVAILABLE;

  @Input() internalOrderId: string = null;

  constructor(
    private modalController: ModalController,
    private apiService: LegacyApiService,
    private loadingController: LoadingController
  ) {}

  ngOnInit(): void {
    this.generatePreviewMsg();
  }

  availabilityToggle($event) {
    this.includeAvailability = $event.detail.checked;
    if (this.includeAvailability) {
      this.modalController.getTop().then((modal) => {
        modal.setCurrentBreakpoint(0.9);
      });
    } else {
      this.selectedNextAvailableDate = null;
    }
    this.generatePreviewMsg();
    this.evaluateSubmitDisabled();
  }

  onDeclineReasonChange($event) {
    this.selectedDeclineReason = $event.detail.value;
    this.generatePreviewMsg();
    this.evaluateSubmitDisabled();
  }

  evaluateSubmitDisabled() {
    this.submitBtnDisabled =
      !this.selectedDeclineReason ||
      this.sendingDecline ||
      (this.includeAvailability && !this.selectedNextAvailableDate);
  }

  async submitDecline() {
    const modalData = {
      includeAvailability: this.includeAvailability,
      nextAvailability:
        this.includeAvailability && this.selectedNextAvailableDate
          ? moment(this.selectedNextAvailableDate).format('YYYY-MM-DD')
          : null,
      declinedReasonCode: this.selectedDeclineReason,
      orderNumber: this.internalOrderId,
    };
    this.sendingDecline = true;

    await this.presentLoading();

    this.declineOrderRequest().subscribe({
      error: () => {
        // just catch the error as this could be due to the order getting accepted
      },
      complete: async () => {
        await this.loadingController.dismiss();
        this.modalController.dismiss(modalData);
        this.sendingDecline = false;
      },
    });
  }

  async presentLoading() {
    const loading = await this.loadingController.create({});
    await loading.present();
    return loading;
  }

  onDateSelected(selectedDate: Date): void {
    this.selectedNextAvailableDate = selectedDate;
    this.generatePreviewMsg();
    this.evaluateSubmitDisabled();
  }

  closeModal() {
    this.modalController.dismiss(null);
  }

  private generatePreviewMsg() {
    if (this.selectedNextAvailableDate) {
      const dayNumber = getOrdinalDay(this.selectedNextAvailableDate);
      const dayName = this.selectedNextAvailableDate.toLocaleString('en-US', {
        weekday: 'long',
      });
      const monthName = this.selectedNextAvailableDate.toLocaleString('en-US', {
        month: 'short',
      });

      this.previewDeclineMessage = tokenReplace(
        this.declineMessageMapWithAvailability[
          this.selectedDeclineReason || DeclineReason.UNAVAILABLE
        ],
        {
          dayName,
          dayNumber,
          dayMonth: monthName,
        }
      );
    } else {
      this.previewDeclineMessage =
        this.declineMessageMap[
          this.selectedDeclineReason || DeclineReason.UNAVAILABLE
        ];
    }
  }

  declineOrderRequest() {
    const modalData = {
      includeAvailability: this.includeAvailability,
      nextAvailability:
        this.includeAvailability && this.selectedNextAvailableDate
          ? moment(this.selectedNextAvailableDate).format('YYYY-MM-DD')
          : null,
      declinedReasonCode: this.selectedDeclineReason,
      orderNumber: this.internalOrderId,
    };

    if (this.isNewOrderFlow) {
      return this.apiService.put(
        `/${this.internalOrderId}/status/decline`,
        modalData,
        {
          baseUrl: `${environment.apiPathV2}/orders`,
        }
      );
    }

    return this.apiService.post('UpdateOrder/v2/decline', modalData);
  }
}
