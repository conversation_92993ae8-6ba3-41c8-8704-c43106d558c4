import { EventEmitter, Injectable } from '@angular/core';
import { <PERSON>ert<PERSON>ontroller, ModalController, Platform } from '@ionic/angular';
import { untilDestroyed } from '@ngneat/until-destroy';
import { OrderData } from 'src/app/_interfaces/order-data.interface';
import { SudsterData } from 'src/app/_interfaces/sudster-data.interface';
import { trackEvent } from 'src/app/_utils/track-event';
import { environment } from 'src/environments/environment';
import {
  AvailableOrderData,
  PreferredPickup,
} from '../../../_interfaces/available-orders-interfaces';
import { AnalyticsLogService } from '../../../_services/analytics/analytics-log.service';
import { AuthidService } from '../../../_services/authid.service';
import { GetSudsterDataService } from '../../../_services/get-sudster-data.service';
import { OrderExtensionService } from '../../../_services/order-extension.service';
import { StatsigFactoryService } from '../../../_services/statsig-factory.service';
import { StatsigService } from '../../../_services/statsig.service';
import { Detergent } from '../current-orders/adapters/active-order.adapter';
import { DeclineOrderModalComponent } from '../decline-order-modal/decline-order-modal.component';
import { GreetingComponent } from '../greeting/greeting.component';
import { OrderContractModalComponent } from './order-contract-modal.component';

export const breakpoints = [0, 0.75, 0.8, 0.85, 0.9, 1];
const LOCALSTORAGE_KEY_UNIQUE_TRACKEVENTS = 'UniqueEvents';

@Injectable({
  providedIn: 'root',
})
export class OrderContractService {
  public preAcceptanceFlag: boolean;
  public flexibleExtension: boolean;
  public UserID = this.AuthID.getID();
  public breakpoints = breakpoints;
  public initialBreakpoint = 0.75;
  private statsigService: StatsigService;
  constructor(
    private statsigFactoryService: StatsigFactoryService,
    private orderExtensionService: OrderExtensionService,
    public platform: Platform,
    private modalController: ModalController,
    private alertController: AlertController,
    private logService: AnalyticsLogService,
    private AuthID: AuthidService,
    private sudsterDocService: GetSudsterDataService
  ) {
    this.statsigService = this.statsigFactoryService.getInstance();
  }

  async openOrderContractFromOrderData(
    orderItem?: OrderData,
    sudster?: SudsterData,
    sudsterFirstOrder?: boolean,
    goToActive?: EventEmitter<void>,
    idVerification?: string,
    checkrVerification?: string
  ) {
    const orderContractData =
      this.convertOrderDataToOrderContractData(orderItem);

    await this.openOrderContract(
      orderContractData,
      sudster,
      sudsterFirstOrder,
      goToActive,
      idVerification,
      checkrVerification
    );
  }

  async openOrderContractFromAvailableOrderData(
    orderItem?: AvailableOrderData,
    sudster?: SudsterData,
    sudsterFirstOrder?: boolean,
    goToActive?: EventEmitter<void>,
    idVerification?: string,
    checkrVerification?: string
  ) {
    const orderContractData =
      this.convertAvailableOrderToOrderContractData(orderItem);

    await this.openOrderContract(
      orderContractData,
      sudster,
      sudsterFirstOrder,
      goToActive,
      idVerification,
      checkrVerification
    );
  }

  private async openOrderContract(
    orderContract: OrderContractData,
    sudster?: SudsterData,
    sudsterFirstOrder?: boolean,
    goToActive?: EventEmitter<void>,
    idVerification?: string,
    checkrVerification?: string
  ) {
    orderContract.order.OrderNumber = orderContract.OrderNumber;

    const pricePerPound = orderContract.SameDayService
      ? orderContract.PriceSchedule?.ExpressRate || 2
      : orderContract.PriceSchedule?.StandardRate || 1;
    const sudsterRate = pricePerPound * 0.75;

    let isOverweightOrder = false;
    await this.statsigService
      .checkGate(environment.statsig.flags.showOrderDataPreacceptance)
      .pipe(untilDestroyed(this))
      .subscribe((val) => {
        this.preAcceptanceFlag = val;

        this.orderExtensionService
          .canExtendAvailableOrder(
            orderContract.BagSize,
            orderContract.BagCount,
            orderContract.SameDayService
          )
          .then((val) => {
            isOverweightOrder = val;
          });
      });

    let minHeight = 420;
    if (
      orderContract.SameDayService &&
      (orderContract.Detergent === Detergent.Hypoallergenic ||
        orderContract.Detergent === Detergent.UnscentedHypoallergenic)
    ) {
      minHeight = 570;
    } else if (
      sudsterFirstOrder &&
      (orderContract.Detergent === Detergent.Hypoallergenic ||
        orderContract.Detergent === Detergent.UnscentedHypoallergenic)
    ) {
      minHeight = 650;
    } else if (sudsterFirstOrder) {
      minHeight = 570;
    } else if (
      orderContract.SameDayService ||
      orderContract.Detergent === Detergent.Hypoallergenic ||
      orderContract.Detergent === Detergent.UnscentedHypoallergenic
    ) {
      minHeight = 490;
    }

    if (orderContract.order.Preferences?.Detergent) {
      minHeight -= 100;
    }

    const min = minHeight / this.platform.height();
    let minHeightBP = min;
    let breakpoints = [0, minHeightBP, 0.65];
    if (this.preAcceptanceFlag) {
      minHeightBP = this.updateModalBreakpoints(
        orderContract,
        min,
        isOverweightOrder
      );
      breakpoints = [0, minHeightBP, 1];
    }
    const modal = await this.modalController.create({
      component: OrderContractModalComponent,
      breakpoints: breakpoints,
      initialBreakpoint: minHeightBP,
      handle: false,
      showBackdrop: true,
      componentProps: {
        order: orderContract.order,
        SudsterFirstOrder: sudsterFirstOrder,
        flexibleExtension: this.flexibleExtension,
        IdVerification: idVerification,
        CheckrVerification: checkrVerification,
        sudsterRate: sudsterRate,
        isBusiness: orderContract.IsCommercial,
      },
    });
    await modal.present();

    this.trackOnceOrderView(orderContract.OrderNumber, this.UserID);

    this.logService.logOrderViewedEvent(
      this.UserID,
      sudster.ContactEmail,
      sudster.Phone,
      orderContract.OrderNumber,
      this.preAcceptanceFlag
    );
    const { data } = await modal.onWillDismiss();

    if (data != null) {
      const SudsterDoc = await this.sudsterDocService.getOnce();
      const modal2 = await this.modalController.create({
        component: GreetingComponent,
        cssClass: 'PopupModal',
        componentProps: {
          CustomerFirstName: data.CustomerFirstName,
          SudsterFirstName: SudsterDoc.FirstName,
          OrderNumber: orderContract.OrderNumber,
          OrderTimezone: orderContract.Timezone,
        },
        backdropDismiss: false,
      });
      await modal2.present();
      await modal2.onDidDismiss();
      goToActive.emit();
    }
  }

  async preferredDeclineAlert(
    orderItem: AvailableOrderData,
    sudster: SudsterData,
    sudsterFirstOrder: boolean,
    goToActive: EventEmitter<void>,
    idVerification: string,
    checkrVerification: string
  ) {
    this.flexibleExtension = false;
    if (orderItem.Pickup === PreferredPickup.ASAP) {
      this.handleSudsterDecline(orderItem);
      return;
    }

    const alert = await this.alertController.create({
      header: 'Need an extra day?',
      message:
        'The customer has indicated that they are flexible with an extra day if necessary. Would you like to accept this order with an extra day?',
      buttons: [
        {
          text: 'Accept Extension',
          handler: () => {
            this.flexibleExtension = true;
            this.openOrderContractFromAvailableOrderData(
              orderItem,
              sudster,
              sudsterFirstOrder,
              goToActive,
              idVerification,
              checkrVerification
            );
          },
        },
        {
          text: 'Decline',
          handler: () => {
            this.alertController.dismiss();
            this.handleSudsterDecline(orderItem);
          },
        },
      ],
    });
    await alert.present();
  }

  private async handleSudsterDecline(orderItem: AvailableOrderData) {
    const modal = await this.modalController.create({
      component: DeclineOrderModalComponent,
      breakpoints: this.breakpoints,
      initialBreakpoint: this.initialBreakpoint,
      cssClass: 'poplin-theme',
      handle: false,
      showBackdrop: true,
      componentProps: {
        internalOrderId: orderItem.OrderNumber,
        isNewOrderFlow: orderItem.IsNewOrderFlow,
      },
    });
    await modal.present();
  }

  private trackOnceOrderView(orderNumber: string, sudsterId: string) {
    const LPOrderViewedKeyName = 'LPOrderViewed';
    const resultArrayStr = localStorage.getItem(
      LOCALSTORAGE_KEY_UNIQUE_TRACKEVENTS
    );
    let uniqueEventsArr = [];
    if (resultArrayStr) {
      uniqueEventsArr = JSON.parse(resultArrayStr);
    }
    const alreadyTracked = uniqueEventsArr.find(
      (entry) =>
        entry.id === orderNumber && entry.evtName === LPOrderViewedKeyName
    );
    if (alreadyTracked) {
      return;
    }

    trackEvent({
      eventData: {
        event: LPOrderViewedKeyName,
        userId: sudsterId,
        orderId: orderNumber,
      },
    });

    const trackEntry = {
      id: orderNumber,
      time: new Date().getTime(),
      evtName: LPOrderViewedKeyName,
    };
    uniqueEventsArr.push(trackEntry);

    // delete old entries more than 15 days old
    const now = new Date().getTime();
    uniqueEventsArr = uniqueEventsArr.filter(
      (entry) => now - entry.time < 15 * 24 * 60 * 60 * 1000
    );
    localStorage.setItem(
      LOCALSTORAGE_KEY_UNIQUE_TRACKEVENTS,
      JSON.stringify(uniqueEventsArr)
    );
  }

  private updateModalBreakpoints(
    orderContract: OrderContractData,
    min: number,
    isOverweightOrder: boolean
  ): number {
    const arr = [
      orderContract.IsCommercial,
      orderContract.IsCustomerFirstOrder,
      orderContract.Preferences?.Delicates,
      orderContract.Detergent.includes('I Will Provide'),
      orderContract.Preferences?.HangDry,
      orderContract.Preferences?.Instructions !== '',
      isOverweightOrder,
    ];

    let count = arr.filter(Boolean).length;

    if (count === 0) {
      return min;
    }
    count += orderContract.Preferences?.Instructions.length / 150;
    const size = (count * 35) / this.platform.height() + 0.08;
    let minHeight = min + size;
    if (minHeight > 0.9) {
      minHeight = 0.95;
    }
    return minHeight;
  }

  private convertAvailableOrderToOrderContractData(
    order: AvailableOrderData
  ): OrderContractData {
    return {
      order: order,
      OrderNumber: order.OrderNumber,
      PriceSchedule: order.PriceSchedule,
      Detergent: order.Detergent,
      Preferences: order.Preferences,
      IsCommercial: order.IsCommercial,
      IsCustomerFirstOrder: order.IsCustomerFirstOrder,
      IsNewOrderFlow: order.IsNewOrderFlow,
      BagSize: order.BagSize,
      BagCount: order.BagCount,
      SameDayService: order.Delivery == 'SameDay',
      Timezone: order.Timezone,
      PreferredPickup: order.Pickup,
    };
  }

  private convertOrderDataToOrderContractData(
    order: OrderData
  ): OrderContractData {
    return {
      order: order,
      OrderNumber: order.OrderNumber,
      PriceSchedule: order.PriceSchedule,
      Detergent: order.Preferences.Detergent,
      Preferences: {
        Delicates: !!order.Preferences?.Delicates,
        HangDry: order.Preferences?.HangDry,
        Instructions: order.Preferences?.Instructions,
      },
      IsCommercial: order.IsBusiness,
      IsCustomerFirstOrder: order.FirstOrder,
      IsNewOrderFlow: order.IsNewOrderFlow,
      BagSize: order.BagSize,
      BagCount: order.OrderSize,
      SameDayService: order.SameDayService,
      Timezone: order.Timezone,
      PreferredPickup: order.PreferredPickup,
    };
  }
}

export interface OrderContractData {
  order: any;
  OrderNumber: string;
  PriceSchedule?: {
    ExpressRate?: number;
    StandardRate?: number;
  };
  Detergent: string;
  Preferences?: {
    Delicates?: boolean;
    HangDry?: boolean;
    Instructions?: string;
  };
  IsCommercial?: boolean;
  IsCustomerFirstOrder?: boolean;
  IsNewOrderFlow?: boolean;
  BagSize: string;
  BagCount: number;
  SameDayService: boolean;
  Timezone: string;
  PreferredPickup: string;
}
