@import 'src/theme/type-mixins.scss';

:host {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 40px;
}

.order-container {
  position: relative;
  min-height: 114px;
  width: calc(100% - 72px);
  max-width: 350px;
  margin: 0 auto;

  ::ng-deep .solid {
    --color-button-primary-main: var(--purple-800);
    --color-button-primary-alt: var(--white);
    --color-button-primary-disabled: var(--purple-core);
    --color-button-primary-hover: var(--purple-800);
    --color-button-primary-active: var(--purple-core);
    --color-button-primary-wash: var(--purple-core);
  }
}

#noOrders {
  text-align: center;
  padding: 10px;
  width: 300px;
  color: var(--BlueGray4);
  font-size: 14px;
  line-height: 135%;
  margin: 0 auto;

  b {
    display: block;
    font-size: 17px;
    padding-bottom: 5px;
  }
}

#outsideArea {
  width: 90%;
  display: block;
  text-align: center;
  font-size: 14px;
  margin: 10px auto;
}

#loadingLabel {
  text-align: center;
  display: block;
  font-weight: 500;
  color: var(--BlueGray4);
  font-size: 18px;
}
