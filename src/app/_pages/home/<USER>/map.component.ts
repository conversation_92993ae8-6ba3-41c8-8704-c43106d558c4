import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/auth';
import { AngularFirestore } from '@angular/fire/firestore';
import { AngularFireFunctions } from '@angular/fire/functions';
import { Router } from '@angular/router';
import { Geolocation } from '@capacitor/geolocation';
import { ModalController, Platform } from '@ionic/angular';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import {
  AvailableOrder,
  AvailableOrderData,
} from 'src/app/_interfaces/available-orders-interfaces';
import { OrderData } from 'src/app/_interfaces/order-data.interface';
import { AuthidService } from 'src/app/_services/authid.service';
import { GetActiveOrdersService } from 'src/app/_services/get-active-orders.service';
import { GetSudsterDataService } from 'src/app/_services/get-sudster-data.service';
import { OrderExtensionService } from 'src/app/_services/order-extension.service';
import { StatsigFactoryService } from 'src/app/_services/statsig-factory.service';
import { StatsigService } from 'src/app/_services/statsig.service';
import { GetNewOrdersService } from 'src/app/service/new-order-listener';
import { environment } from 'src/environments/environment';
import { Detergent } from '../current-orders/adapters/active-order.adapter';
import { GreetingComponent } from '../greeting/greeting.component';
import { OrderContractModalComponent } from '../order-contract-modal/order-contract-modal.component';
import { SudsterData } from './../../../_interfaces/sudster-data.interface';

declare let mapkit: any;

@UntilDestroy()
@Component({
  selector: 'app-map',
  templateUrl: './map.component.html',
  styleUrls: ['./map.component.scss'],
})
export class MapComponent implements OnInit {
  @Output() Fullscreen = new EventEmitter<string>();
  @Input() IdVerification: string;
  @Input() CheckrVerification: string;
  MapFullscreen = false;

  FullOrderDataArray: Array<AvailableOrder> = [];
  CurrentLocationAnnotation;

  SudsterFirstOrder = false;

  UserID = this.AuthID.getID();
  preAcceptanceFlag: boolean;
  private statsigService: StatsigService;
  constructor(
    private GetNewOrders: GetNewOrdersService,
    private AuthID: AuthidService,
    private GetActiveOrders: GetActiveOrdersService,
    private cloudFunctions: AngularFireFunctions,
    private modalController: ModalController,
    private SudsterDocService: GetSudsterDataService,
    private router: Router,
    public platform: Platform,
    public firestore: AngularFirestore,
    public afAuth: AngularFireAuth,
    private statsigFactoryService: StatsigFactoryService,
    private orderExtensionService: OrderExtensionService
  ) {
    this.statsigService = this.statsigFactoryService.getInstance();
  }

  ngOnInit() {
    let CenterLat = 0;
    let CenterLng = 0;

    let TotalLat = 0;
    let TotalLng = 0;
    let AvgLatCount = 0;
    let AvgLngCount = 0;

    let CurrentLocationAnnotation;

    new Promise((resolve, reject) => {
      mapkit.init({
        authorizationCallback: (done) => {
          return this.cloudFunctions
            .httpsCallable('SudsterV3_GenerateMapKitKey')({})
            .toPromise()
            .then((res) => {
              done(res);
              resolve(null);
            });
        },
        language: 'en',
      });
    }).then(() => {
      let LongestLon = 0.04;
      let LongestLat = 0.04;

      let center = new mapkit.Coordinate(CenterLat, CenterLng);
      const map = new mapkit.Map('map', {
        center: center,
        showsUserLocation: false,
        tracksUserLocation: false,
        showsUserLocationControl: false,
        region: new mapkit.CoordinateRegion(
          center,
          new mapkit.CoordinateSpan(0.1, 0.1)
        ),
        mapType: mapkit.Map.MapTypes.standard,
        showsCompass: mapkit.FeatureVisibility.Hidden,
        showsMapTypeControl: false,
        isZoomEnabled: true,
        showsPointsOfInterest: true,
        showsZoomControls: true,
      });
      let overlay;
      let home;

      this.firestore
        .doc<SudsterData>(`Sudsters/${this.UserID}`)
        .valueChanges()
        .pipe(untilDestroyed(this))
        .subscribe((doc) => {
          const data = doc;
          center = new mapkit.Coordinate(
            data.WorkAreaCenter.latitude,
            data.WorkAreaCenter.longitude
          );

          SetAvgCenter(
            data.WorkAreaCenter.latitude,
            data.WorkAreaCenter.longitude
          );
          CenterMap();

          if (overlay != null) {
            map.removeOverlay(overlay);
          }
          AddWorkAreaOverlay(
            data.WorkAreaCenter.latitude,
            data.WorkAreaCenter.longitude,
            data.WorkRadius || 20
          );

          if (home != null) {
            map.removeAnnotation(home);
          }
          home = new mapkit.ImageAnnotation(center, {
            title: 'Home',
            subtitle:
              data.StreetAddress +
              ', ' +
              data.City +
              ', ' +
              data.State +
              ' ' +
              data.Zipcode,
            url: {
              1: 'assets/img/Map/<EMAIL>',
              2: 'assets/img/Map/<EMAIL>',
            },
            displayPriority: 1000,
          });
          map.addAnnotation(home);
        });

      map.addEventListener('region-change-end', (event) => {
        if (map.cameraDistance < 5000) {
          const center = new mapkit.Coordinate(
              event.target.region.center.latitude,
              event.target.region.center.longitude
            ),
            span = new mapkit.CoordinateSpan(0.02, 0.02),
            region = new mapkit.CoordinateRegion(center, span);
          map.setRegionAnimated(region, true);
        }
      });
      this.GetNewOrders.currentOrders.subscribe(
        (data: { AvailableOrders: AvailableOrder[]; Sudster: SudsterData }) => {
          const Sudster = data.Sudster;
          const Orders = data.AvailableOrders;
          this.FullOrderDataArray = [];

          this.FullOrderDataArray = [];
          this.SudsterFirstOrder = (Sudster?.OrderCount || 0) <= 0;

          map.annotations.forEach((annotation) => {
            if (
              annotation.clusteringIdentifier == 'Orders' ||
              annotation.clusteringIdentifier == 'Outside' ||
              annotation.clusteringIdentifier == 'CurrentLocation'
            ) {
              map.removeAnnotation(annotation);
            }
          });

          if (Orders != null && Orders.length != 0) {
            Orders.forEach((availableOrder: AvailableOrder) => {
              const order = availableOrder.Order;
              this.FullOrderDataArray.push(availableOrder);
              const DistDiff = getCordDistance(
                order.Address.Lat,
                order.Address.Long,
                Sudster.WorkAreaCenter.latitude,
                Sudster.WorkAreaCenter.longitude
              );

              if (DistDiff.lon > LongestLon) {
                LongestLon = DistDiff.lon;
              }
              if (DistDiff.lat > LongestLat) {
                LongestLat = DistDiff.lat;
              }

              SetAvgCenter(order.Address.Lat, order.Address.Long);

              const OrderCord = new mapkit.Coordinate(
                order.Address.Lat,
                order.Address.Long
              );
              if (
                getDistanceinMi(
                  Sudster.WorkAreaCenter.latitude,
                  Sudster.WorkAreaCenter.longitude,
                  order.Address.Lat,
                  order.Address.Long
                ) <= (Sudster.WorkRadius || 20)
              ) {
                if (order.Bonus > 0 || order.BonusPoints > 0) {
                  const isBonusPoinst = order.BonusPoints > 0;
                  const subtitle = !isBonusPoinst
                    ? '$' + order.Bonus + ' BOOST!'
                    : `+${order.BonusPoints} POINTS!`;

                  const iconBonus = isBonusPoinst
                    ? 'assets/img/Map/BonusPoints.png'
                    : 'assets/img/Map/Bonus.png';
                  const colorBonus = isBonusPoinst ? '#00d0ff' : '#ff00d0';
                  const annotation = new mapkit.MarkerAnnotation(OrderCord, {
                    title: order.CustomerFirstName,
                    subtitle: subtitle,
                    data: { OrderNumber: availableOrder.OrderNumber },
                    subtitleVisibility: 'visible',
                    glyphImage: { 1: iconBonus },
                    color: colorBonus,
                    displayPriority: 1001,
                    clusteringIdentifier: 'Orders',
                  });
                  map.addAnnotation(annotation);
                  annotation.addEventListener('select', (event) => {
                    this.OpenOrderContract(event.target.data.OrderNumber);
                  });
                } else {
                  const annotation = new mapkit.MarkerAnnotation(OrderCord, {
                    title: order.CustomerFirstName,
                    data: { OrderNumber: availableOrder.OrderNumber },
                    glyphText: 'NEW',
                    color: '#00d0ff',
                    displayPriority: 1000,
                    clusteringIdentifier: 'Orders',
                  });
                  map.addAnnotation(annotation);
                  annotation.addEventListener('select', (event) => {
                    this.OpenOrderContract(event.target.data.OrderNumber);
                  });
                }
              } else {
                const annotation = new mapkit.MarkerAnnotation(OrderCord, {
                  title: '',
                  subtitle: '',
                  data: { OrderNumber: availableOrder.OrderNumber },
                  color: '#aab4be',
                  displayPriority: 999,
                  clusteringIdentifier: 'Outside',
                });
                map.addAnnotation(annotation);
              }
            });

            CenterMap();
          }
        }
      );

      map.annotationForCluster = function (clusterAnnotation) {
        clusterAnnotation.addEventListener('select', function (event) {
          let LongestClusterLat = 0.01;
          let LongestClusterLon = 0.01;
          const cluster = event.target;
          cluster.memberAnnotations.forEach((element) => {
            const DistDiff = getCordDistance(
              center.latitude,
              center.longitude,
              element.coordinate.latitude,
              element.coordinate.longitude
            );

            if (DistDiff.lon > LongestClusterLon) {
              LongestClusterLon = DistDiff.lon;
            }
            if (DistDiff.lat > LongestClusterLat) {
              LongestClusterLat = DistDiff.lat;
            }
          });
          LongestClusterLat = LongestClusterLat;
          LongestClusterLon = LongestClusterLon;
          CenterMap();
        });

        if (clusterAnnotation.clusteringIdentifier === 'Orders') {
          clusterAnnotation.glyphText =
            '+' + clusterAnnotation.memberAnnotations.length;
          let OrderNames = '';
          let TotalBonus = 0;
          clusterAnnotation.memberAnnotations.forEach((Order) => {
            OrderNames += Order.title + ', ';
            if (Order.subtitle.includes('$')) {
              const Bonus = Order.subtitle.replace(/^\D+/g, '');
              TotalBonus += parseInt(Bonus);
            }
          });
          clusterAnnotation.subtitle = OrderNames.slice(0, -2);
          if (TotalBonus > 0) {
            clusterAnnotation.title = `$${TotalBonus} BOOST`;
            clusterAnnotation.color = '#ff00d0';
          } else {
            clusterAnnotation.title = '';
            clusterAnnotation.color = '#00d0ff';
          }
        } else if (clusterAnnotation.clusteringIdentifier === 'Active') {
          clusterAnnotation.glyphText =
            '+' + clusterAnnotation.memberAnnotations.length;
          let OrderNames = '';
          clusterAnnotation.memberAnnotations.forEach((Order) => {
            OrderNames += Order.title + ', ';
          });
          clusterAnnotation.subtitle = OrderNames.slice(0, -2);
          clusterAnnotation.title = '';
          clusterAnnotation.color = '#ffb700';
        }
      };

      this.GetActiveOrders.currentOrders.subscribe(
        (data: {
          Orders: (OrderData & { OrderNumber: string })[];
          Sudster: SudsterData;
        }) => {
          const Sudster = data.Sudster;
          const Orders = data.Orders;

          if (Orders != null && Orders.length != 0) {
            map.annotations.forEach((annotation) => {
              if (annotation.clusteringIdentifier == 'Active') {
                map.removeAnnotation(annotation);
              }
            });

            Orders.forEach((order: OrderData & { OrderNumber: string }) => {
              const orderLong = order.Long ?? order.Lng;
              const DistDiff = getCordDistance(
                order.Lat,
                orderLong,
                Sudster.WorkAreaCenter.latitude,
                Sudster.WorkAreaCenter.longitude
              );

              if (DistDiff.lon > LongestLon) {
                LongestLon = DistDiff.lon;
              }
              if (DistDiff.lat > LongestLat) {
                LongestLat = DistDiff.lat;
              }

              const OrderCord = new mapkit.Coordinate(order.Lat, orderLong);

              let ActiveImage = 1;
              if (
                order.OrderStatusNumber == 2 ||
                order.OrderStatusNumber == 2.5
              ) {
                ActiveImage = 2;
              } else if (order.OrderStatusNumber >= 3) {
                ActiveImage = 3;
              }

              const annotation = new mapkit.MarkerAnnotation(OrderCord, {
                title: order.CustomerFirstName,
                data: { OrderNumber: order.OrderNumber },
                color: '#ffb700',
                displayPriority: 1000,
                glyphImage: { 1: `assets/img/Map/active-${ActiveImage}.png` },
                clusteringIdentifier: 'Active',
              });
              annotation.addEventListener('select', (event) => {
                this.router.navigate([
                  '/active',
                  { id: event.target.data.OrderNumber },
                ]);
              });
              map.addAnnotation(annotation);

              SetAvgCenter(order.Lat, orderLong);
            });

            CenterMap();
          }
        }
      );

      function getCordDistance(lat1, lon1, lat2, lon2) {
        return { lat: Math.abs(lat1 - lat2), lon: Math.abs(lon1 - lon2) };
      }

      function SetAvgCenter(lat, lng) {
        // While initializing lng is NaN randomly
        if (!lat || !lng) {
          return;
        }

        TotalLat += lat;
        TotalLng += lng;
        AvgLatCount++;
        AvgLngCount++;
        CenterLat = TotalLat / AvgLatCount;
        CenterLng = TotalLng / AvgLngCount;
      }
      function CenterMap() {
        if (!CenterLat || !CenterLng) {
          return;
        }
        const MapCenter = new mapkit.Coordinate(CenterLat, CenterLng);
        map.center = MapCenter;
        map.region = new mapkit.CoordinateRegion(
          MapCenter,
          new mapkit.CoordinateSpan(
            Math.max(LongestLat * 2, 0.04),
            Math.max(LongestLon * 2, 0.04)
          )
        );
      }

      function AddWorkAreaOverlay(lat, lng, WorkMiles) {
        overlay = new mapkit.CircleOverlay(
          new mapkit.Coordinate(lat, lng),
          WorkMiles * 1609.34
        );
        overlay.style = new mapkit.Style({
          lineWidth: 1,
          strokeColor: '#78828c',
          fillColor: '',
        });
        map.addOverlay(overlay);
      }

      function getDistanceinMi(lat1, lon1, lat2, lon2) {
        function deg2rad(deg) {
          return deg * (Math.PI / 180);
        }
        const R = 6371; // Radius of the earth in km
        const dLat = deg2rad(lat2 - lat1); // deg2rad below
        const dLon = deg2rad(lon2 - lon1);
        const a =
          Math.sin(dLat / 2) * Math.sin(dLat / 2) +
          Math.cos(deg2rad(lat1)) *
            Math.cos(deg2rad(lat2)) *
            Math.sin(dLon / 2) *
            Math.sin(dLon / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        const d = R * c; // Distance in km
        const dm = d * 0.621371; // Distance in miles
        return dm;
      }

      UpdateUserLocation();
      setInterval(() => {
        UpdateUserLocation();
      }, 100000);
      this.platform.resume.subscribe(async () => {
        UpdateUserLocation();
      });

      async function UpdateUserLocation() {
        if (CurrentLocationAnnotation != null) {
          map.removeAnnotation(CurrentLocationAnnotation);
        }

        map.annotations.forEach((annotation) => {
          if (annotation.title == 'Current Location') {
            map.removeAnnotation(annotation);
          }
        });

        const lastLocationUpdateTime = new Date().toLocaleTimeString();
        await Geolocation.getCurrentPosition()
          .then((res) => {
            CurrentLocationAnnotation = new mapkit.ImageAnnotation(
              new mapkit.Coordinate(res.coords.latitude, res.coords.longitude),
              {
                title: 'Current Location',
                subtitle: 'Last updated: ' + lastLocationUpdateTime,
                url: {
                  1: 'assets/img/Map/location-pin.svg',
                },
                displayPriority: 1000,
              }
            );
          })
          .catch((e) => {
            if (e.code === 1) {
              CurrentLocationAnnotation = new mapkit.ImageAnnotation(
                new mapkit.Coordinate(0, 0),
                {
                  title: 'Current Location',
                  subtitle: 'Last updated: ' + lastLocationUpdateTime,
                  url: {
                    1: 'assets/img/Map/location-pin.svg',
                  },
                  displayPriority: 1000,
                }
              );
            }
          });
        map.addAnnotation(CurrentLocationAnnotation);
      }
    });
  }

  ionViewDidEnter() {
    Geolocation.getCurrentPosition().then(() => {
      Promise.resolve();
    });
  }

  ClickFullscreen() {
    this.MapFullscreen = !this.MapFullscreen;
    this.Fullscreen.emit();
  }

  async OpenOrderContract(OrderNumber) {
    const CurrentOrderData = this.FullOrderDataArray.filter((obj) => {
      return obj.OrderNumber == OrderNumber;
    })[0].Order;

    let isOverweightOrder = false;
    await this.statsigService
      .checkGate(environment.statsig.flags.showOrderDataPreacceptance)
      .subscribe((val) => {
        this.preAcceptanceFlag = val;

        this.orderExtensionService
          .canExtendAvailableOrder(
            CurrentOrderData.BagSize,
            CurrentOrderData.BagCount,
            CurrentOrderData.Delivery === 'SameDay'
          )
          .then((val) => {
            isOverweightOrder = val;
          });
      });

    CurrentOrderData.SameDayService = CurrentOrderData.Delivery === 'SameDay';
    CurrentOrderData.OrderNumber = OrderNumber;

    let minHeight = 420;
    if (
      CurrentOrderData.SameDayService &&
      (CurrentOrderData.Detergent === Detergent.Hypoallergenic ||
        CurrentOrderData.Detergent === Detergent.UnscentedHypoallergenic)
    ) {
      minHeight = 570;
    } else if (
      this.SudsterFirstOrder &&
      (CurrentOrderData.Detergent === Detergent.Hypoallergenic ||
        CurrentOrderData.Detergent === Detergent.UnscentedHypoallergenic)
    ) {
      minHeight = 650;
    } else if (this.SudsterFirstOrder) {
      minHeight = 570;
    } else if (
      CurrentOrderData.SameDayService ||
      CurrentOrderData.Detergent === Detergent.Hypoallergenic ||
      CurrentOrderData.Detergent === Detergent.UnscentedHypoallergenic
    ) {
      minHeight = 490;
    }
    const min = minHeight / this.platform.height();
    let minHeightBP = min;
    let breakpoints = [0, minHeightBP, 0.65];
    if (this.preAcceptanceFlag) {
      minHeightBP = this.updateModalBreakpoints(
        CurrentOrderData,
        min,
        isOverweightOrder
      );
      breakpoints = [0, minHeightBP, 1];
    }
    const modal = await this.modalController.create({
      component: OrderContractModalComponent,
      breakpoints: breakpoints,
      initialBreakpoint: minHeightBP,
      handle: false,
      showBackdrop: true,
      componentProps: {
        order: CurrentOrderData,
        SudsterFirstOrder: this.SudsterFirstOrder,
        flexibleExtension: false,
        idVerification: this.IdVerification,
        CheckrVerification: this.CheckrVerification,
      },
    });
    await modal.present();

    const { data } = await modal.onWillDismiss();

    if (data != null) {
      const SudsterDoc = await this.SudsterDocService.getOnce();
      const modal2 = await this.modalController.create({
        component: GreetingComponent,
        cssClass: 'PopupModal',
        componentProps: {
          // CustomerFirstName: CurrentOrderData.CustomerFirstName,
          SudsterFirstName: SudsterDoc.FirstName,
          OrderNumber: OrderNumber,
          OrderTimezone: CurrentOrderData.Timezone,
        },
        backdropDismiss: false,
      });
      await modal2.present();
    }
  }

  updateModalBreakpoints(
    order: AvailableOrderData,
    min: number,
    isOverweightOrder: boolean
  ): number {
    const arr = [
      order.IsCommercial,
      order.IsCustomerFirstOrder,
      order.Preferences.Delicates,
      order.Detergent.includes('I Will Provide'),
      order.Preferences.HangDry,
      order.Preferences.Instructions !== '',
      isOverweightOrder,
    ];
    let count = arr.filter(Boolean).length;

    if (count === 0) {
      return min;
    }
    count += order.Preferences.Instructions.length / 150;
    const size = (count * 35) / this.platform.height() + 0.08;
    let minHeight = min + size;
    if (minHeight > 0.9) {
      minHeight = 0.95;
    }
    return minHeight;
  }
}
