import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule } from '@ionic/angular';

import { OrderContractModalComponent } from './order-contract-modal.component';

describe('OrderContractModalComponent', () => {
  let component: OrderContractModalComponent;
  let fixture: ComponentFixture<OrderContractModalComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [OrderContractModalComponent],
      imports: [IonicModule.forRoot()],
    }).compileComponents();

    fixture = TestBed.createComponent(OrderContractModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  }));

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
