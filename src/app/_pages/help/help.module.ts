import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { HelpPage } from './help.page';
import { SupportBaseComponent } from './support-base/support-base.component';
import { SupportContentPageModule } from './support-content/support-content.module';
import { ContactComponent } from './contact/contact.component';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {
    path: '',
    component: HelpPage,
  },
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    SupportContentPageModule,
    RouterModule.forChild(routes),
  ],
  declarations: [HelpPage, SupportBaseComponent, ContactComponent],
  exports: [SupportBaseComponent],
})
export class HelpPageModule {}
