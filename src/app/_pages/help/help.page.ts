import { Component, OnInit } from '@angular/core';
import {
  IonRouterOutlet,
  ModalController,
  NavController,
} from '@ionic/angular';
import { SupportBaseComponent } from './support-base/support-base.component';
import { SupportContentPage } from './support-content/support-content.page';

@Component({
  selector: 'app-help',
  templateUrl: './help.page.html',
  styleUrls: ['./help.page.scss'],
})
export class HelpPage implements OnInit {
  constructor(
    private modalController: ModalController,
    private routerOutlet: IonRouterOutlet,
    private NavController: NavController
  ) {}

  ngOnInit() {
    this.OpenSupportTree();
  }

  async OpenSupportTree() {
    const modal = await this.modalController.create({
      presentingElement: this.routerOutlet.nativeEl,
      component: SupportBaseComponent,
      componentProps: {
        rootPage: SupportContentPage,
      },
    });
    await modal.present();

    const { data } = await modal.onWillDismiss();

    this.NavController.navigateBack(['/home']);
  }
}
