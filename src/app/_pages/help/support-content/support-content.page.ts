import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/auth';
import { AngularFirestore } from '@angular/fire/firestore';
import { AngularFireFunctions } from '@angular/fire/functions';
import { Router } from '@angular/router';
import { CallNumber } from '@ionic-native/call-number/ngx';
import {
  AlertController,
  IonNav,
  LoadingController,
  ModalController,
  ToastController,
} from '@ionic/angular';
import { first, takeWhile } from 'rxjs/operators';
import { SudsterData } from 'src/app/_interfaces/sudster-data.interface';
import { AuthidService } from 'src/app/_services/authid.service';
import { ZendeskService } from 'src/app/_services/zendesk.service';
import { ContactComponent } from '../contact/contact.component';

@Component({
  selector: 'app-support-content',
  templateUrl: './support-content.page.html',
  styleUrls: ['./support-content.page.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class SupportContentPage implements OnInit {
  level = 0;
  TreeData: {
    Title: string;
    AdminNotes?: string;
    Body?: string;
    Questions?: Array<{ Title: string; Body: string }>;
    Private?: boolean;
    ContactMethods?: {
      CTA: string;
      Email: boolean;
      Phone: boolean;
      Text: boolean;
    };
    CustomOrder?: string;
    Children?: Array<any>;
    AutomationFunction?: string;
    AutomationConfig?: {
      title: string;
      confirm?: boolean;
    };
  } = { Title: 'Help', Children: [] };
  BreadcrumbArray = [];
  PathArray = [];
  QuestionAnswers: Array<boolean> = [];
  nextPage = SupportContentPage;

  UserID = this.AuthID.getID();
  Sudster: SudsterData;

  Senior = false;
  NewCustomer = false;
  error: string;
  completed: string;

  unreadChats = 0;
  isZendeskLoggedIn = false;

  constructor(
    private modalController: ModalController,
    private nav: IonNav,
    public firestore: AngularFirestore,
    private loadingController: LoadingController,
    public toastController: ToastController,
    private callNumber: CallNumber,
    private afAuth: AngularFireAuth,
    private AuthID: AuthidService,
    private alertController: AlertController,
    private cloudFunctions: AngularFireFunctions,
    private router: Router,
    private zendeskService: ZendeskService
  ) {}

  ngOnInit() {
    const loading = this.presentLoading();
    this.zendeskService.init();
    this.firestore
      .collection<any>(
        `Code/HelpTree/Sudster/${this.PathArray.join('/')}`,
        (ref) => ref.orderBy('CustomOrder', 'asc').where('Private', '==', false)
      )
      .valueChanges({ idField: 'ArticleID' })
      .pipe(first())
      .subscribe((collection) => {
        this.TreeData.Children = [];

        collection.forEach((doc) => {
          if (doc.Private != true) {
            this.TreeData.Children.push(doc);
          }
        });

        loading.then(function (ld) {
          ld.dismiss();
        });
      });
    this.firestore
      .doc('Sudsters/' + this.UserID)
      .get()
      .subscribe((sudsterDoc) => {
        if (sudsterDoc.exists) {
          this.Sudster = sudsterDoc.data() as SudsterData;
          this.getUnreadCount();
        }
      });
  }

  goForward(index) {
    this.BreadcrumbArray.push(this.TreeData.Children[index].Title);
    this.PathArray.push(this.TreeData.Children[index].ArticleID, 'Children');
    this.nav.push(this.nextPage, {
      level: this.level + 1,
      TreeData: this.TreeData.Children[index],
      BreadcrumbArray: this.BreadcrumbArray,
      PathArray: this.PathArray,
    });
  }

  goRoot() {
    for (let i = 0; i < this.level; i++) {
      this.BreadcrumbArray.pop();
      this.PathArray.pop();
    }
    this.nav.popToRoot();
  }

  openHelpCenter() {
    window.open('https://poplin-laundry-pro.zendesk.com/hc/en-us', '_blank');
  }

  callAction(treeData) {
    if (treeData.AutomationConfig?.confirm) {
      this.presentAlert(
        'Confirm action',
        'Please confirm you want to execute this action.'
      ).then((res) => {
        if (res.role == 'ok') {
          this.cloudFunctions
            .httpsCallable('SudsterV3_Automation')({
              process: treeData.AutomationFunction,
            })
            .toPromise()
            .then((res) => {
              if (treeData.AutomationFunction == 'remove-account') {
                this.completed = 'Delete completed';
                this.afAuth.signOut();
                setTimeout(() => {
                  // show the user for an instant before they are signed out
                  this.modalController.dismiss();
                  this.router.navigate(['intro']);
                }, 2000);
              } else {
                this.completed = res;
              }
            })
            .catch((err) => {
              this.error = err;
            });
        }
      });
    }
  }

  clickBack() {
    this.BreadcrumbArray.pop();
    this.PathArray.pop();
    this.PathArray.pop();
    if (this.level == 0) {
      this.modalController.dismiss({ Back: true });
    }
  }

  Close() {
    this.modalController.dismiss();
  }

  goBack(index) {
    for (let i = 0; i < this.level - index; i++) {
      this.BreadcrumbArray.pop();
      this.PathArray.pop();
      this.PathArray.pop();
    }
    this.nav.popTo(index);
  }

  CallSeniorSupport() {
    this.callNumber
      .callNumber('18337837427', true)
      .catch((err) => this.presentToast('Call us at ************'));
  }

  QuestionAnswered(ev, index) {
    const Answer = ev.detail.value == 'Yes' ? true : false;
    this.QuestionAnswers[index] = Answer;
  }

  ShowContactMethod(ContactMethods) {
    const checker = (arr) => arr.every((v) => v === true);

    if (
      (ContactMethods.Email == true ||
        ContactMethods.Phone == true ||
        ContactMethods.Text == true) &&
      this.QuestionAnswers.length >= this.TreeData.Questions.length &&
      checker(this.QuestionAnswers)
    ) {
      return true;
    } else {
      return false;
    }
  }

  async confirmChat() {
    if (this.isZendeskLoggedIn) {
      this.unreadChats = 0;
      this.zendeskService.show();
    } else {
      const loading = this.presentLoading();
      this.zendeskService.getIsLoggedIn
        .pipe(takeWhile(() => !this.isZendeskLoggedIn))
        .subscribe((zendeskLoggedIn) => {
          if (zendeskLoggedIn) {
            loading.then(function (ld) {
              ld.dismiss();
            });
            this.unreadChats = 0;
            this.zendeskService.show();
            this.isZendeskLoggedIn = true;
          }
        });
    }
  }

  async OpenContact() {
    const modal = await this.modalController.create({
      component: ContactComponent,
      componentProps: {
        ContactMethods: this.TreeData.ContactMethods || {
          Email: true,
          Text: true,
          Phone: false,
          CTA: '',
        },
      },
    });
    return await modal.present();
  }

  async presentToast(text) {
    const toast = await this.toastController.create({
      message: text,
      buttons: [
        {
          text: 'Close',
          role: 'cancel',
        },
      ],
      cssClass: 'dark-toast',
    });
    toast.present();
  }

  async presentLoading() {
    const loading = await this.loadingController.create({});
    await loading.present();
    return loading;
  }

  async presentAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel',
          cssClass: 'secondary',
        },
        {
          text: 'Ok',
          role: 'ok',
        },
      ],
    });

    alert.present();
    return alert.onDidDismiss();
  }

  unreadTimeout() {
    setTimeout(
      () => {
        this.getUnreadCount();
      },
      10000,
      this
    );
  }

  getUnreadCount() {
    this.zendeskService
      .getUnreadCount()
      .then((res) => {
        this.unreadChats = res;
        this.unreadTimeout();
      })
      .catch((err) => {
        if (err === 'zE is not defined') {
          this.unreadTimeout();
        } else {
          console.error(`Error getting unread count: ${err}`);
        }
      });
  }
}
