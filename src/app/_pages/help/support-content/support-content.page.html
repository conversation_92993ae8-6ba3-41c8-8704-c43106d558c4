<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button
        id="back-button"
        defaultHref="help"
        (click)="clickBack()"
      ></ion-back-button>
    </ion-buttons>
    <ion-title>Help</ion-title>
    <ion-buttons slot="end">
      <ion-button
        id="close-button"
        (click)="Close()"
        *ngIf="level != 0"
        style="--color: var(--BlueGray5)"
      >
        <ion-icon name="close"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div id="Breadcrumbs" *ngIf="BreadcrumbArray.length > 0">
    <i class="material-icons" (click)="goRoot()">home</i>
    <ion-label
      (click)="goBack(i)"
      *ngFor="let item of BreadcrumbArray;let i = index"
      >{{item}}
      <ion-icon
        *ngIf="i < (BreadcrumbArray.length - 1)"
        name="chevron-forward-sharp"
      ></ion-icon>
    </ion-label>
  </div>

  <div id="Body" *ngIf="TreeData?.Body?.length">
    <h1>{{TreeData.Title}}</h1>
    <div [innerHTML]="TreeData.Body"></div>
    <div *ngIf="TreeData?.AutomationFunction" class="action">
      <ion-button
        id="title-button"
        [disabled]="error || completed"
        (click)="callAction(TreeData)"
        expand="block"
        fill="outline"
      >
        {{ TreeData.AutomationConfig.title }}
      </ion-button>

      <p
        *ngIf="error || completed"
        [class.error]="error"
        [class.completed]="completed"
      >
        {{ completed }} {{ error }}
      </p>
    </div>

    <div id="ContactDiv" [class.Shadow]="TreeData?.Questions.length > 0">
      <div
        class="QuestionItem"
        *ngFor="let item of TreeData?.Questions; let i = index;"
        [hidden]="i > QuestionAnswers.length || QuestionAnswers[i - 1] == false"
      >
        <h2>{{item.Title}}</h2>
        <ion-segment (ionChange)="QuestionAnswered($event,i)">
          <ion-segment-button value="Yes">
            <ion-label>Yes</ion-label>
          </ion-segment-button>
          <ion-segment-button value="No">
            <ion-label>No</ion-label>
          </ion-segment-button>
        </ion-segment>
        <p *ngIf="QuestionAnswers[i] == false">{{item.Body}}</p>
      </div>

      <ion-button
        id="contact-support-button"
        (click)="OpenContact()"
        *ngIf="ShowContactMethod(TreeData?.ContactMethods) && TreeData?.ContactMethods.CTA === 'Contact'"
        fill="outline"
        >Contact Support
        <ion-icon
          style="padding-left: 5px"
          name="chatbubbles-outline"
        ></ion-icon>
      </ion-button>
      <ion-button
        id="more-help-button"
        (click)="OpenContact()"
        *ngIf="ShowContactMethod(TreeData?.ContactMethods) && TreeData?.ContactMethods.CTA !== 'Contact'"
        fill="clear"
        size="small"
        style="color: var(--BlueGray4)"
        >Need more help
        <ion-icon
          style="padding-left: 5px"
          name="help-circle-outline"
        ></ion-icon>
      </ion-button>
    </div>
  </div>

  <ion-list *ngIf="TreeData?.Children?.length > 0">
    <ion-item (click)="openHelpCenter()">
      <ion-label text-wrap>Help Center</ion-label>
      <ion-icon name="chevron-forward-outline"></ion-icon>
    </ion-item>
    <ion-item
      (click)="goForward(i)"
      *ngFor="let item of TreeData.Children;let i = index"
    >
      <ion-label text-wrap>{{item.Title}}</ion-label>
      <ion-icon name="chevron-forward-outline"></ion-icon>
    </ion-item>
  </ion-list>
  <button
    id="urgent-chat-button"
    *ngIf="Sudster"
    class="urgent_chat"
    type="button"
    (click)="confirmChat()"
  >
    <i class="material-icons">question_answer</i>
    <div class="inner">
      <div class="urgent-type">
        Help Chat
        <span class="chat-status-badge" *ngIf="unreadChats"
          >{{unreadChats}}</span
        >
      </div>
    </div>
  </button>
</ion-content>
