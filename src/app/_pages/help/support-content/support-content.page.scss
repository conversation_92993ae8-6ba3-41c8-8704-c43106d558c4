#Breadcrumbs {
  color: var(--BlueGray3);
  font-size: 14px;
  padding-top: 10px;
  padding-left: 10px;
  padding-right: 10px;
  padding-bottom: 10px;

  ion-icon {
    vertical-align: -3px;
    margin-left: -3px;
    padding-right: 2px;
  }

  .material-icons {
    font-size: 18px;
    vertical-align: -3px;
    padding-right: 5px;
  }
}

#Body {
  padding: 0px 20px;
  text-align: center;
  border-bottom: solid 1px var(--BlueGray2);
  padding-bottom: 15px;

  h1 {
    font-size: 30px;
    color: var(--BlueGray5);
    font-weight: 800;
  }

  div {
    text-align: justify;
    line-height: 155%;
    font-size: 18px;
    text-align: justify;

    p {
      padding: 0px;
      margin: 0px;
    }
  }

  .error,
  .completed {
    padding: 20px;
    font-size: 15px;
    line-height: 20px;
  }

  .error {
    color: var(--Red);
  }

  #ContactDiv {
    padding: 10px 10px;
    border-radius: 5px;
    text-align: center;
  }

  #ContactDiv.Shadow {
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.35);
  }

  .QuestionItem {
    margin-bottom: 15px;

    h2 {
      text-align: left;
      font-size: 17px;
      margin: 0;
      color: var(--BlueGray5);
      line-height: 140%;
    }

    p {
      font-size: 14px;
      line-height: 140%;
      font-weight: 500;
    }

    ion-segment {
      margin-top: 10px;
    }
  }
}

.CallLineDiv {
  width: 320px;
  margin: 0 auto;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  background: var(--BlueGray1);
  border-radius: 15px;
  padding: 10px;
  overflow: hidden;
  position: absolute;
  margin-top: 10px;
  left: calc(50% - 160px);
  bottom: 20px;

  h1 {
    margin: 0;
    float: left;
    font-size: 25px;
    color: var(--BlueGray4);
    font-weight: 700;
  }

  p {
    display: block;
    float: left;
    clear: both;
    margin: 0;
    padding: 3px 0px;
    font-size: 15px;
    color: var(--BlueGray5);
    font-weight: 500;
  }

  ion-button {
    position: absolute;
    right: 10px;
  }
}

ion-list {
  ion-item {
    ion-label {
      font-size: 17px !important;
      font-weight: 500;
    }

    ion-icon {
      color: var(--BlueGray3);
    }
  }
}

.urgent_chat {
  display: flex;
  position: fixed;
  bottom: calc(env(safe-area-inset-bottom) + 1vh);
  right: 2vh;
  align-items: center;
  color: white;
  background-color: grey;
  gap: 10px;
  box-sizing: border-box;
  border-radius: 30px;
  padding: 10px;
  padding-left: 14px;
  padding-right: 30px;
  filter: drop-shadow(0 0 4px rgba(32, 27, 27, 0.288));

  .urgent-type {
    font-size: 18px;
    font-weight: 500;
    font-style: italic;
    span {
      position: absolute;
      right: -10px;
      top: 0px;
      background-color: var(--Orange);
      color: white;
      border-radius: 50%;
      height: 20px;
      width: 20px;
      font-size: 14px;
      font-weight: 900;
      line-height: 1.3;
      text-align: center;
    }
  }
  .inner {
    text-align: left;

    .tally {
      width: auto;
      position: relative;
      font-weight: 500;
      text-transform: uppercase;
      font-size: 70%;
      letter-spacing: 1px;
      margin-left: 2px;
      margin-top: 4px;
      span {
        margin-left: 2px;
      }
    }
  }
}

.chat-inner-container {
  text-align: left;
  position: relative;

  .chat-status-badge {
    position: absolute;
    top: -10px;
    right: -15px;
    background-color: orange;
    color: #fff;
    border-radius: 50%;
    font-size: 14px;
    font-weight: 900;
    padding: 2px 4px;
  }
}
