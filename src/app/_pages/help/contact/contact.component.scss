#Title {
  text-align: center;
}

ion-textarea {
  width: 90%;
  margin: 15px auto;
  margin-top: 25px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
  min-height: 150px;
  border-radius: 5px;
  padding: 10px;
}

#ContactMethods {
  width: 90%;
  margin: 0 auto;
  margin-top: 20px;
  text-align: center;

  label {
    font-size: 17px;
    font-weight: 500;
    color: var(--BlueGray4);
    margin-bottom: 7px;
    display: block;
  }
}

#SendButton {
  margin: 20px auto;
  width: 90%;

  ion-icon {
    padding-left: 10px;
  }
}

.ContactInfoFields {
  width: 90%;
  max-width: 320px;
  margin: 15px auto;
  display: block;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  border-radius: 5px;
  --padding-start: 10px;
  color: gray;
  font-size: 18px;
}

#AttachmentsPreview {
  width: 90%;
  max-width: 400px;
  margin: 0 auto;

  .AttatchmentItem {
    display: inline-block;
    width: 70px;
    height: 70px;
    border-radius: 10px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    margin: 10px;

    img {
      height: 100%;
      width: 100%;
      object-fit: cover;
    }
  }
}
