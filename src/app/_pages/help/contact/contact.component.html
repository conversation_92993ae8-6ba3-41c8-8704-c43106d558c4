<ion-content>
  <ion-header>
    <ion-toolbar>
      <ion-buttons slot="start">
        <ion-icon
          name="close"
          (click)="DismissModal()"
          style="font-size: 25px"
        ></ion-icon>
      </ion-buttons>
      <ion-title>Contact Support</ion-title>
    </ion-toolbar>
  </ion-header>

  <h1 id="Title">How can we help?</h1>
  <ion-textarea
    [(ngModel)]="Message"
    placeholder="Type your message here."
    auto-grow="true"
  ></ion-textarea>

  <ion-button
    id="attach-file-button"
    (click)="SelectPhoto()"
    size="small"
    expand="block"
    fill="none"
  >
    Attach File <ion-icon name="attach"></ion-icon>
  </ion-button>

  <div id="AttachmentsPreview">
    <div
      *ngFor="let file of AttachedFiles; let i = index"
      (click)="DetachPhoto(i)"
      class="AttatchmentItem"
    >
      <img [src]="file" />
    </div>
  </div>

  <ion-input
    [(ngModel)]="ReplyEmail"
    class="ContactInfoFields"
    type="email"
    placeholder="Enter Your Email"
  ></ion-input>

  <ion-button
    [disabled]="validateEmail(ReplyEmail) == false || Message.length < 5"
    id="SendButton"
    expand="block"
    (click)="SendMessage()"
    >Send <ion-icon name="send"></ion-icon>
  </ion-button>
</ion-content>
