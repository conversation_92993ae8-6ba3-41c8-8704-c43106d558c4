import { Component, Input, OnInit } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/auth';
import { AngularFirestore } from '@angular/fire/firestore';
import { AngularFireFunctions } from '@angular/fire/functions';
import { AngularFireStorage } from '@angular/fire/storage';
import { DomSanitizer } from '@angular/platform-browser';
import { Camera, CameraResultType, CameraSource } from '@capacitor/camera';
import {
  AlertController,
  LoadingController,
  ModalController,
} from '@ionic/angular';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { AuthidService } from 'src/app/_services/authid.service';

@UntilDestroy()
@Component({
  selector: 'app-contact',
  templateUrl: './contact.component.html',
  styleUrls: ['./contact.component.scss'],
})
export class ContactComponent implements OnInit {
  ReplyMethod = '';
  Message = '';
  AttachedFiles: Array<string> = [];
  ReplyEmail = '';
  @Input() ContactMethods: {
    CTA: string;
    Email: boolean;
    Phone: boolean;
    Text: boolean;
  } = { Email: true, Text: true, Phone: false, CTA: '' };

  constructor(
    private modalCtrl: ModalController,
    private sanitizer: DomSanitizer,
    private alertController: AlertController,
    private cloudFunctions: AngularFireFunctions,
    private afAuth: AngularFireAuth,
    public firestore: AngularFirestore,
    private storage: AngularFireStorage,
    private loadingController: LoadingController,
    private AuthID: AuthidService
  ) {}

  UserID = this.AuthID.getID();

  ngOnInit() {
    this.firestore
      .doc<any>(`Sudsters/${this.UserID}`)
      .valueChanges()
      .pipe(untilDestroyed(this))
      .subscribe((doc) => {
        this.ReplyEmail = doc.ContactEmail;
      });
  }

  async DismissModal() {
    await this.modalCtrl.dismiss();
  }

  async SendMessage() {
    const loading = this.presentLoading();

    const FilesLinkArray: Array<{ content: string; filename: string }> = [];

    const UploadImage = (img_url) => {
      return new Promise((resolve, reject) => {
        const FileLocation =
          this.UserID != null ? this.UserID : this.ReplyEmail;

        const FileName =
          Math.floor(Math.random() * 100000000) +
          '.' +
          img_url.split(';')[0].split('/')[1];
        const fileRef = this.storage.ref(
          'email/files/' + FileLocation + '/inbound/' + FileName
        );

        fileRef
          .putString(img_url, 'data_url')
          .then(() => {
            fileRef.getDownloadURL().subscribe((result) => {
              FilesLinkArray.push({ content: result, filename: FileName });
              resolve(null);
            });
          })
          .catch((err) => {
            reject(err);
          });
      });
    };

    Promise.all(this.AttachedFiles.map((item) => UploadImage(item)))
      .then(() => {
        this.cloudFunctions
          .httpsCallable('SubmitTicket')({
            Message: this.Message,
            ReplyEmail: this.ReplyEmail.toLowerCase(),
            Attachments: FilesLinkArray,
            IsSudster: true,
          })
          .toPromise()
          .then((res) => {
            loading.then(function (ld) {
              ld.dismiss();
            });
            this.modalCtrl.dismiss();
            this.presentAlert(
              'Email Sent!',
              'A support agent will get back to you shortly via email.'
            );
          })
          .catch((err) => {
            loading.then(function (ld) {
              ld.dismiss();
            });
            this.presentAlert(
              'Error',
              'There was an error sending this email. Please try again or reach <NAME_EMAIL> for assistance.'
            );
          });
      })
      .catch(() => {
        this.presentAlert(
          'Error',
          'There was an error sending this email. Please try again or reach <NAME_EMAIL> for assistance.'
        );
      });
  }

  SelectPhoto = async () => {
    const image = await Camera.getPhoto({
      quality: 70,
      resultType: CameraResultType.DataUrl,
      source: CameraSource.Photos,
    });

    const imageUrl = image.dataUrl;

    this.AttachedFiles.push(imageUrl);
  };

  async DetachPhoto(index) {
    const alert = await this.alertController.create({
      header: 'Remove this attachment?',
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel',
          cssClass: 'secondary',
        },
        {
          text: 'Remove',
          cssClass: 'warning',
          handler: () => {
            this.AttachedFiles.splice(index, 1);
          },
        },
      ],
    });

    await alert.present();
  }

  validateEmail(email) {
    const re =
      /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
  }

  async presentLoading() {
    const loading = await this.loadingController.create({});
    await loading.present();
    return loading;
  }

  async presentAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK'],
    });
    await alert.present();
  }
}
