ion-header {
  height: 60px;
  margin-top: env(safe-area-inset-top);
}
.id-verification-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  width: 100%;
  background-color: var(--BlueGray1);
}

.size-container {
  margin-top: 40px;
  max-width: 85%;
  min-width: 300px;
  width: 400px;
  height: 297px;
  background-color: white;
  border-radius: 5px;
  -webkit-box-shadow: 0px 0px 6px 2px rgba(240, 235, 240, 1);
  -moz-box-shadow: 0px 0px 6px 2px rgba(240, 235, 240, 1);
  box-shadow: 0px 0px 6px 2px rgba(240, 235, 240, 1);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
}

.title {
  display: flex;
  flex-direction: column;
  align-self: stretch;
  color: var(--content-primary, #000);
  text-align: center;

  /* Heading/Small */
  font-family: 'PitchSans-Medium', 'Helvetica Neue', sans-serif;
  font-size: 24px;
  font-style: normal;
  font-weight: 500;
  line-height: 120%;
  letter-spacing: -0.72px;
}

.icons {
  display: flex;
  align-items: center;
}

.icon-container {
  padding: 5px;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.cc-container {
  border-radius: 1000px;
  background: var(--poplin-pink, #ff6289);
  -webkit-box-shadow: 0px 4px 2px 0px rgba(240, 235, 240, 0.54);
  -moz-box-shadow: 0px 4px 2px 0px rgba(240, 235, 240, 0.54);
  box-shadow: 0px 4px 2px 0px rgba(240, 235, 240, 0.54);
}

.cc-icon {
  color: white;
  font-size: 18px;
}

.person-container {
  background-color: var(--BlueGray2);
  width: 30px;
  height: 30px;
}

.person-icon {
  color: #fff;
  font-size: 16px;
}

.pending-container {
  background-color: var(--Orange);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.pending-icon {
  color: white;
  font-size: 40px;
  transform: rotate(45deg);
}

.failed-icon {
  color: red;
  font-size: 80px;
}

.horizontal-line {
  width: 30px;
  height: 2px;
  background-color: var(--BlueGray3);
}

.unverified,
.pending {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 95%;
  font-size: 14px;
  color: var(--BlueGray5);
  p {
    display: flex;
    flex-direction: column;
    align-self: stretch;
    color: var(--content-primary, #000);

    /* Body Text/Small */
    font-family: 'Fakt-Normal', sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 140%;
  }
  span {
    text-align: center;
    position: relative;
    top: 15px;
    left: 25%;
    color: var(--content-alt, #4b4b4b);
    text-align: center;

    /* Body Text/Small */
    font-family: 'Fakt-Normal', sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 140%;
  }
  ion-button {
    &::part(native) {
      color: var(--button-color-primary-alt, #fff);
      text-align: center;
      /* Aux Text/M */
      font-family: 'PitchSans-Medium', sans-serif;
      font-size: 16px;
      font-style: normal;
      font-weight: 700;
      line-height: 20px;
      letter-spacing: 1.6px;
      text-transform: uppercase;
      border-radius: var(--button-radius-radius-square, 8px);
      background: var(--poplin-pink, #ff6289);

      /* Light/Basic Drop Shadow */
      box-shadow: 0px 4px 24px 0px rgba(0, 0, 0, 0.04);
    }
  }
}

.verify-container,
.contact-support {
  width: 95%;
}

.failed-attempts {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 95%;
  text-align: center;
  font-size: 14px;
  color: var(--BlueGray5);
  h2 {
    margin: 0;
  }
}

.timer-container {
  -webkit-box-shadow: 0px 4px 2px 0px rgba(240, 235, 240, 0.54);
  -moz-box-shadow: 0px 4px 2px 0px rgba(240, 235, 240, 0.54);
  box-shadow: 0px 4px 2px 0px rgba(240, 235, 240, 0.54);
  background-color: white;
  border-radius: 50px;
  width: 230px;
  min-width: 230px;
  margin-top: 20px;
  padding: 3px;
}

.timer-text {
  display: flex;
  justify-content: center;
  gap: 5px;
  font-size: 12px;
}
.help-icon {
  position: fixed;
  right: 25px;
}
