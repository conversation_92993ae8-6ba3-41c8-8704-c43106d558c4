import { Component, OnInit } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/auth';
import { AngularFirestore } from '@angular/fire/firestore';
import { AngularFireFunctions } from '@angular/fire/functions';
import { Router } from '@angular/router';
import { Alert<PERSON>ontroller, LoadingController } from '@ionic/angular';
import { untilDestroyed } from '@ngneat/until-destroy';
import { SudsterData } from 'src/app/_interfaces/sudster-data.interface';
import { AuthidService } from 'src/app/_services/authid.service';
import { StatsigFactoryService } from 'src/app/_services/statsig-factory.service';
import { StatsigService } from 'src/app/_services/statsig.service';
import { selectDistinctBy } from 'src/app/_utils/operators';
import { environment } from './../../../environments/environment';

type VerificationState = Pick<
  SudsterData,
  | 'IdVerification'
  | 'IdVerificationError'
  | 'IdVerificatonAttemptsLeft'
  | 'extraAttemptAdded'
  | 'CheckrVerification'
  | 'ReservedOrderId'
  | 'IdVerificationDeadline'
>;

declare const Stripe: any;
const stripe = Stripe(environment.Stripe);

@Component({
  selector: 'app-id-verification',
  templateUrl: './id-verification.page.html',
  styleUrls: ['./id-verification.page.scss'],
})
export class IdVerificationPage implements OnInit {
  IdVerification;
  IdVerificationError: string;
  // timeout;
  timer = false;

  StripeAccountID = '';
  faceRecognition = '';
  standardVerification = '';
  userFaceVerified = false;
  verifying = false;
  UserID = this.AuthID.getID();
  loading = false;
  IdVerificationDeadline;
  attemptsLeft: number;
  noAttemptsLeft = false;
  extraAttemptAdded = false;
  unverified = false;
  pending = false;
  timerInterval;

  reservedOrderId;

  backgroundCheck = false;
  CheckrVerification: string;
  private statsigService: StatsigService;
  constructor(
    private cloudFunctions: AngularFireFunctions,
    public loadingController: LoadingController,
    public alertController: AlertController,
    public afAuth: AngularFireAuth,
    public firestore: AngularFirestore,
    private AuthID: AuthidService,
    private router: Router,
    private statsigFactoryService: StatsigFactoryService
  ) {
    this.statsigService = this.statsigFactoryService.getInstance();
  }

  ngOnInit() {
    this.statsigService
      .checkGate(environment.statsig.flags.checkrVerification)
      .subscribe((value) => {
        this.backgroundCheck = value;
      });

    this.firestore
      .doc<SudsterData>(`Sudsters/${this.UserID}`)
      .valueChanges()
      .pipe(
        untilDestroyed(this),
        selectDistinctBy(
          (doc): VerificationState => ({
            IdVerification: doc?.IdVerification,
            IdVerificationError: doc?.IdVerificationError,
            IdVerificatonAttemptsLeft: doc?.IdVerificatonAttemptsLeft,
            extraAttemptAdded: doc?.extraAttemptAdded,
            CheckrVerification: doc?.CheckrVerification,
            ReservedOrderId: doc?.ReservedOrderId,
            IdVerificationDeadline: doc?.IdVerificationDeadline,
          })
        )
      )
      .subscribe((doc) => {
        this.IdVerification = doc.IdVerification;
        this.IdVerificationError = doc.IdVerificationError;
        this.attemptsLeft = doc.IdVerificatonAttemptsLeft;
        this.extraAttemptAdded = doc.extraAttemptAdded;
        this.CheckrVerification = doc.CheckrVerification;
        this.reservedOrderId = doc.ReservedOrderId;

        if (this.IdVerification === 'verified') {
          this.handleRedirect();
        }

        if (this.attemptsLeft <= 0) {
          this.noAttemptsLeft = true;
        } else {
          this.noAttemptsLeft = false;
        }
        this.unverified = this.IdVerification === 'unverified';
        this.pending = this.IdVerification === 'pending';
        if (this.timerInterval) {
          clearInterval(this.timerInterval);
          this.timerInterval = null;
        }
        this.countdownTimer(doc.IdVerificationDeadline);
      });
  }

  verifyIdentity() {
    if (this.noAttemptsLeft) {
      return;
    }
    this.verifying = true;

    this.cloudFunctions
      .httpsCallable('SudsterV3_VerifyID')({
        reservedOrderId: this.reservedOrderId,
        idvStepEnabled: !!this.IdVerification,
        backgroundCheckStepEnabled: !!this.CheckrVerification,
      })
      .toPromise()
      .then((res) => {
        stripe.verifyIdentity(res).then(async (res) => {
          const error = res.error;

          if (error && error.code) {
            if (error.code === 'session_cancelled') {
              console.log('session cancelled');
            } else {
              this.presentAlert('Error', error.code);
            }
            this.verifying = false;
            return;
          }

          this.verifying = false;
          this.IdVerification = 'pending';
          this.pending = true;
          this.unverified = false;
        });
      });
  }

  async presentAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK'],
    });
    await alert.present();
  }

  async presentLoading() {
    this.loading = true;
    const loading = await this.loadingController.create({});
    await loading.present();
    return loading;
  }

  countdownTimer(deadline) {
    this.timer = true;

    if (this.noAttemptsLeft && !this.pending) {
      return;
    }

    this.timerInterval = setInterval(function () {
      const now = new Date().getTime();
      const t = deadline - now;
      const minutes = Math.floor((t % (1000 * 60 * 60)) / (1000 * 60));
      let seconds: number | string = Math.floor((t % (1000 * 60)) / 1000);
      if (seconds < 10) {
        seconds = '0' + seconds;
      }

      //TODO: Figure out why timer does not exist in the DOM
      const timer = document.getElementById('timer');
      if (timer) {
        timer.innerHTML = minutes + ':' + seconds;
      }
      if (t < 0) {
        clearInterval(this.timerInterval);
        this.timerInterval = null;
        document.getElementById('timer').innerHTML = '0';
      }
    }, 1000);
  }

  handleRedirect() {
    // checks flags to see where to send user.
    // in this case we will assume that there is no need to send the user to background check
    // we need to call a function to add the order to the sudster.
    // we will accept the order for them and then display the message modal?
    if (
      this.CheckrVerification === 'unverified' ||
      this.CheckrVerification === 'pending'
    ) {
      this.router.navigate(['/background'], { replaceUrl: true });
    } else {
      this.router.navigate(['/verification-success'], { replaceUrl: true });
    }
  }

  contactSupport() {
    return window.open(
      'https://poplin-laundry-pro.zendesk.com/hc/en-us/articles/17130545822619-Security-Check'
    );
  }
}
