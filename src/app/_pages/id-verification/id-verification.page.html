<ion-header>
  <ion-row>
    <ion-col size="11">
      <ion-title>Security Check </ion-title>
    </ion-col>
    <ion-col size="1">
      <i class="material-icons-outlined help-icon" (click)="contactSupport()"
        >help_outline</i
      >
    </ion-col>
  </ion-row>
</ion-header>

<div class="id-verification-container">
  <div class="size-container" *ngIf="unverified && !noAttemptsLeft">
    <div class="icons">
      <div class="icon-container cc-container">
        <span class="material-icons-outlined cc-icon">credit_score</span>
      </div>
      <div *ngIf="CheckrVerification" class="horizontal-line"></div>
      <div *ngIf="CheckrVerification" class="icon-container person-container">
        <span class="material-icons-outlined person-icon">how_to_reg</span>
      </div>
    </div>
    <h2 class="title">ID Verification</h2>
    <div class="unverified">
      <p *ngIf="attemptsLeft < 2 && !extraAttemptAdded">
        We had trouble processing your ID. Please try again.
      </p>

      <p>
        Have your driver's license ready and click the button below. A new
        window will open to begin the identity verification process through
        Stripe.
      </p>

      <div class="verify-container">
        <ion-button
          id="begin-button"
          (click)="verifyIdentity()"
          [disabled]="verifying || noAttemptsLeft"
          expand="block"
        >
          Begin
        </ion-button>
        <span class="container-footer"> Takes about 5 minutes </span>
      </div>
    </div>
  </div>
  <app-verification-failure
    *ngIf="unverified && noAttemptsLeft"
    [IdVerification]="true"
  ></app-verification-failure>

  <app-verification-processing
    *ngIf="pending && !loading"
    [IdVerification]="true"
  ></app-verification-processing>

  <div class="timer-container" *ngIf="(!noAttemptsLeft || pending) && timer">
    <div class="timer-text">
      Order reserved, <span id="timer"></span> minutes remaining
    </div>
  </div>
</div>
