import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { NgxMaskModule } from 'ngx-mask';

import { IonicModule } from '@ionic/angular';

import { SharedModule } from 'src/app/shared.module';
import { IdVerificationPage } from './id-verification.page';

const routes: Routes = [
  {
    path: '',
    component: IdVerificationPage,
  },
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes),
    ReactiveFormsModule,
    NgxMaskModule.forRoot(),
    SharedModule,
  ],
  declarations: [IdVerificationPage],
})
export class IdVerificationPageModule {}
