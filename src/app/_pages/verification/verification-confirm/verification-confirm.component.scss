ion-content {
  font-family: 'Roboto', arial, helvetica, sans-serif;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
  width: 352px;
  height: 584px;
  padding: 30px 36px;
  gap: 18px;
  left: 20px;
  top: env(safe-area-inset-top);
  background: var(--themes-light-background-alt, #f9f9f9);
  ion-row {
    max-width: 342px;
    ion-col {
      h4 {
        color: #323c46;
        font-size: 14px;
        font-weight: 700;
      }
      p {
        color: #575757;
        font-size: 14px;
        font-weight: 400;
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 18px;
      }
    }
  }

  .idv-header {
    color: var(--content-alt, #4b4b4b);
    font-family: 'PitchSans-Medium', sans-serif;
    font-size: 24px;
    font-style: normal;
    font-weight: 500;
    line-height: 120%;
    letter-spacing: -0.72px;
  }
  .idv-description {
    height: 72px;
    margin-top: 10px;
    flex: none;
    order: 1;
    align-self: stretch;
    flex-grow: 0;
    font-family: 'Fakt-Normal', sans-serif;
  }
  .material-icons-outlined {
    position: absolute;
    height: 40px;
    width: 40px;
    left: 0%;
    right: 0%;
    top: 0%;
    bottom: 0%;
    background-color: var(--poplin-pink, #ff6289);
    color: #fff;
    border-radius: 40px;
    padding: 8px;
    margin-top: 20px;
    margin-left: 8px;
  }
  .idv-steps {
    h4 {
      color: var(--content-primary, #000);

      /* Subhead/Small */
      font-family: 'PitchSans-Medium', sans-serif;
      font-size: 14px;
      font-style: normal;
      font-weight: 700;
      line-height: 120%;
      letter-spacing: 2.1px;
      text-transform: uppercase;
    }
    margin: 12px 16px;
    position: relative;
    right: 15px;
    width: 342px;
    p {
      color: var(--content-alt, #4b4b4b);
      display: flex;
      flex-direction: column;
      align-self: stretch;
      /* Body Text/Small */
      font-family: 'Fakt-Normal', sans-serif;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 140%;
      margin-top: 0px;
    }
    border-radius: 8px;
    border: 1px solid var(--border-alt, rgba(144, 144, 144, 0.24));
    background: var(--poplin-white, #fff);

    /* Light/Basic Drop Shadow */
    box-shadow: 0px 4px 24px 0px rgba(0, 0, 0, 0.04);
    .background-check-col {
      top: 10px;
    }
  }
  .idv-checkbox-parent {
    margin-left: 10px;
    .idv-checkbox-text {
      span {
        vertical-align: middle;
        width: 327px;
        padding: 6px 0px;
        align-items: flex-start;
        gap: 8px;
        /**Type */
        color: var(--checkbox-text-label-unchecked, #4b4b4b);

        /* Body Text/Medium */
        font-family: 'Fakt-Normal' sans-serif;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 140%;
        ion-checkbox {
          margin-top: 5px;
        }
      }
      span.checkbox-confirm {
        margin-left: 6px;
      }
    }
  }
  .background-check-section {
    margin-top: -18px;
    .material-icons-outlined {
      margin-top: 34px;
    }
  }
  .continue-section {
    margin-top: -14px;
  }
  ion-row.button-row {
    ion-col {
      .continue-button {
        display: flex;
        width: 342px;
        min-height: 48px;
        padding: var(--button-padding-vertical-default, 6px)
          var(--button-padding-horizontal-default, 8px);
        justify-content: center;
        align-items: center;
        border-radius: var(--button-radius-radius-square, 8px);
        background: var(--poplin-pink, #ff6289);
        margin-top: 24px;

        /* Light/Basic Drop Shadow */
        box-shadow: 0px 4px 24px 0px rgba(0, 0, 0, 0.04);
        /** Type*/
        color: var(--button-color-primary-alt, #fff);
        text-align: center;

        /* Aux Text/M */
        font-family: 'PitchSans-Medium', sans-serif;
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        line-height: 20px;
        letter-spacing: 1.6px;
        text-transform: uppercase;
      }
      .cancel-button {
        display: flex;
        width: 342px;
        min-height: 36px;
        padding: var(--button-padding-vertical-small, 6px)
          var(--button-padding-horizontal-small, 4px);
        justify-content: center;
        align-items: center;
        border-radius: var(--button-radius-radius-square, 8px);
        background: var(--themes-light-background-alt, #f9f9f9);
        /* Light/Basic Drop Shadow */
        box-shadow: 0px 4px 24px 0px rgba(0, 0, 0, 0.04);
        color: var(--button-color-black-main, #000);
        text-align: center;

        /* Aux Text/S */
        font-family: 'PitchSans-Medium', sans-serif;
        font-size: 13px;
        font-style: normal;
        font-weight: 700;
        line-height: 16px;
        letter-spacing: 1.3px;
        text-transform: uppercase;
      }
    }
  }
}
