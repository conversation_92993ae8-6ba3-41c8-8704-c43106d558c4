<ion-content>
  <ion-row class="idv-header">
    <h3>Complete a Security Check Before Accepting</h3>
  </ion-row>
  <ion-row class="idv-descrtiption">
    <p>
      For our customers' safety, we require all Laundry Pros to verify their
      identity and pass a background check before accepting their first order.
    </p></ion-row
  >
  <ion-row class="idv-steps">
    <ion-row *ngIf="stripeIdvFlag" class="idv-section">
      <ion-col size="2"
        ><span class="material-icons-outlined"> credit_score </span></ion-col
      >
      <ion-col size="9">
        <ion-row>
          <h4>ID Verification</h4>
        </ion-row>
        <ion-row>
          <p>
            Upload a photo of your driver’s license and a selfie — takes about 5
            min.
          </p>
        </ion-row>
      </ion-col>
    </ion-row>
  </ion-row>
  <ion-row class="idv-steps">
    <ion-row *ngIf="checkrVerificationFlag" class="background-check-section">
      <ion-col size="2"
        ><span class="material-icons-outlined"> how_to_reg </span></ion-col
      >
      <ion-col size="9" class="background-check-col">
        <ion-row>
          <h4>Background Check</h4>
        </ion-row>
        <ion-row>
          <p>
            Use your date of birth and social security number to complete the
            background check through Checkr. Processing time can take 2 minutes
            to 5 business days.
          </p>
        </ion-row>
      </ion-col>
    </ion-row>
  </ion-row>
  <ion-row class="continue-section"
    ><ion-col>
      <p>
        Once you click continue,
        <strong>we’ll reserve this order for 10 minutes</strong> while you
        complete this process. You’ll only need to complete this once.
      </p>
    </ion-col></ion-row
  >
  <ion-row class="idv-checkbox-parent">
    <div class="idv-checkbox-text" [ngClass]="{ shake: shake }">
      <span>
        <ion-checkbox
          (click)="setConfirmVerification($event)"
          [(ngModel)]="verificationConfirmed"
        ></ion-checkbox
      ></span>

      <span class="checkbox-confirm"
        >Yes, I have my ID
        {{ checkrVerificationFlag ? 'and SSN' : '' }} ready</span
      >
    </div>
  </ion-row>
  <ion-row class="button-row">
    <ion-col size="12">
      <button
        id="continue-button"
        (click)="onContinue()"
        class="continue-button"
        mode="md"
      >
        Continue
      </button></ion-col
    >
  </ion-row>
  <ion-row class="button-row">
    <ion-col size="12">
      <button
        id="cancel-button"
        fill="clear"
        (click)="onCancel()"
        class="cancel-button"
        mode="md"
      >
        Cancel
      </button>
    </ion-col>
  </ion-row>
</ion-content>
