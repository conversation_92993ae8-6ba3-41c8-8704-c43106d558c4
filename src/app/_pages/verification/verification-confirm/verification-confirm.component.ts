import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import {
  Alert<PERSON>ontroller,
  LoadingController,
  ModalController,
} from '@ionic/angular';
import { AnalyticsLogService } from 'src/app/_services/analytics/analytics-log.service';
import { LegacyApiService } from 'src/app/_services/legacy-api.service';

interface responseJson {
  errors: string[];
}

@Component({
  selector: 'app-verification-confirm',
  templateUrl: './verification-confirm.component.html',
  styleUrls: ['./verification-confirm.component.scss'],
})
export class VerificationConfirmComponent implements OnInit {
  constructor(
    private modal: ModalController,
    private router: Router,
    private apiService: LegacyApiService,
    private loadingController: LoadingController,
    private logService: AnalyticsLogService,
    private alertController: AlertController
  ) {}
  @Input() OrderNumber: string;
  @Input() checkrVerificationFlag: boolean;
  @Input() stripeIdvFlag: boolean;
  @Input() UserID: string;
  shake: boolean;
  verificationConfirmed: boolean = false;

  ngOnInit() {}

  onCancel() {
    this.logService.logUICancelIDV(
      this.OrderNumber,
      this.checkrVerificationFlag,
      this.stripeIdvFlag
    );

    this.modal.dismiss();
  }
  async onContinue() {
    if (!this.verificationConfirmed) {
      this.shake = true;
      setTimeout(() => {
        this.shake = false;
      }, 1000);
      return;
    } else {
      this.modal.dismiss();

      try {
        await this.presentLoading();

        const apiResponse = await this.apiService
          .post(`Sudster_Order/v1/reserve/${this.OrderNumber}`, {})
          .toPromise();

        this.loadingController.dismiss();

        const response = apiResponse.rawResponse;
        const responseInJson = (await apiResponse.data) as responseJson;

        if (response.status === 200) {
          if (this.stripeIdvFlag) {
            this.router.navigate(['id-verification'], { replaceUrl: true });
          }
          if (!this.stripeIdvFlag && this.checkrVerificationFlag) {
            this.router.navigate(['background'], { replaceUrl: true });
          }
        } else if (response.status === 409) {
          this.presentAlert(
            `Order Unavailable`,
            responseInJson['message'] ||
              'This order is currently being accepted by another Laundry Pro. It will disappear from your available queue shortly.'
          );
        } else {
          this.presentAlert(
            `Error ${response.status}`,
            responseInJson.errors.toString()
          );
        }
      } catch (error) {
        this.loadingController.dismiss();
        this.presentAlert(
          'We’re unable to offer you this order at this time.',
          'Unfortunately, this order will remain in your "Active" list but cannot currently be reserved.</br><a href="https://poplin-laundry-pro.zendesk.com/hc/en-us/articles/17130545822619#h_01HBBSZV608GV2WBSA3TR13T5Y">Learn more.</a>'
        );
      }
    }
  }
  setConfirmVerification(ev: any): boolean {
    return (this.verificationConfirmed = ev.target.checked);
  }

  async presentLoading() {
    const loading = await this.loadingController.create({});
    await loading.present();
    return loading;
  }

  async presentAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK'],
    });
    await alert.present();
  }
}
