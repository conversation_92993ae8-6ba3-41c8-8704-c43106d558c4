import { Component, OnInit } from '@angular/core';
import { AngularFirestore } from '@angular/fire/firestore';
import { Router } from '@angular/router';
import { Browser } from '@capacitor/browser';
import { LoadingController } from '@ionic/angular';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Subscription } from 'rxjs';
import {
  CheckrInformation,
  SudsterData,
} from 'src/app/_interfaces/sudster-data.interface';
import { LegacyApiService } from 'src/app/_services/legacy-api.service';
import { AuthidService } from 'src/app/_services/authid.service';

@UntilDestroy()
@Component({
  selector: 'app-background-check',
  templateUrl: './background-check.component.html',
  styleUrls: ['./background-check.component.scss'],
})
export class BackgroundCheckComponent implements OnInit {
  UserID = this.AuthID.getID();
  countDown: Subscription;
  formatCounter: string;
  getTime: NodeJS.Timeout;
  Sudster: SudsterData;
  isChatting: boolean;
  unreadChats: number;
  chatListeners = false;
  token: string;
  deadline: number;
  checkr: boolean;
  stripeIdv: boolean;
  pending: boolean;
  unverified: boolean;
  consider: boolean;
  failed: boolean;
  verified: boolean;
  canceled: boolean;
  CheckrVerification: string;
  CheckrInformation: CheckrInformation;
  clicked = false;
  constructor(
    public firestore: AngularFirestore,
    private AuthID: AuthidService,
    private loadingController: LoadingController,
    private router: Router,
    private apiService: LegacyApiService
  ) {}

  ngOnInit() {
    this.firestore
      .doc(`Sudsters/${this.UserID}`)
      .valueChanges()
      .pipe(untilDestroyed(this))
      .subscribe((doc: any) => {
        if (doc) {
          this.getDeadline(doc.IdVerificationDeadline);
          this.Sudster = doc;
          if (this.Sudster.CheckrVerification) {
            this.CheckrVerification = this.Sudster.CheckrVerification;
            this.unverified = this.CheckrVerification === 'unverified';
            this.pending = this.CheckrVerification === 'pending';
            this.failed = this.CheckrVerification === 'failed';
            this.consider = this.CheckrVerification === 'consider';
            this.verified = this.CheckrVerification === 'verified';
            this.canceled = this.CheckrVerification === 'canceled';
          }
          if (this.verified) {
            return this.router.navigate(['verification-success']);
          }
        }
      });
  }

  formatTime(value: number): string {
    const minutes: number = Math.floor(value / 60000);
    const seconds = Math.floor((value % (1000 * 60)) / 1000);
    return (
      ('00' + minutes).slice(-2) + ':' + ('00' + Math.floor(seconds)).slice(-2)
    );
  }

  getDeadline(deadline: number): string {
    this.getTime = setInterval(() => {
      const now = new Date().getTime();
      const timeLeft = Math.floor(deadline - now);
      this.formatCounter = this.formatTime(timeLeft);
      if (timeLeft <= 0) {
        clearInterval(this.getTime);
        this.formatCounter = '00:00';
      }
    }, 1000);
    return this.formatCounter;
  }

  async runCheckr() {
    const loading = this.presentLoading();
    const call = await this.apiService
      .post(`CheckrAPI/v1/checkr`, {})
      .toPromise()
      .then(async (res: any) => {
        const data = await res.data;
        const url = data.data.invitation_url;
        if (url) {
          Browser.open({
            url: `${url}`,
          });
        }
        loading.then((ld) => ld.dismiss());
        return res;
      })
      .catch((err) => {
        return err;
      });
    return call;
  }
  contactSupport() {
    this.clicked = true;
    window.open(
      'https://poplin-laundry-pro.zendesk.com/hc/en-us/articles/17130545822619-Security-Check'
    );
    this.clicked = false;
  }
  async presentLoading(): Promise<HTMLIonLoadingElement> {
    const loading = await this.loadingController.create({});
    await loading.present();
    return loading;
  }
}
