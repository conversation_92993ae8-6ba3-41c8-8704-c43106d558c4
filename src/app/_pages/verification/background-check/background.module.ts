import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { NgxMaskModule } from 'ngx-mask';

import { IonicModule } from '@ionic/angular';

import { SharedModule } from 'src/app/shared.module';
import { BackgroundCheckComponent } from './background-check.component';

const routes: Routes = [
  {
    path: '',
    component: BackgroundCheckComponent,
  },
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes),
    ReactiveFormsModule,
    NgxMaskModule.forRoot(),
    SharedModule,
  ],
  declarations: [BackgroundCheckComponent],
})
export class BackgroundCheckComponentModule {}
