ion-header {
  height: 60px;
  margin-top: env(safe-area-inset-top);
}
ion-content {
  .main {
    height: 100%;
    background-color: #6666;
    position: relative;
    .background-check-main {
      position: relative;
      z-index: 10;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 18px;
      gap: 18px;

      width: 342px;
      height: 322px;

      /* SudShare/White */

      background: #ffffff;
      box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.12);
      border-radius: 6px;

      /* Inside auto layout */
      top: 20px;
      order: 0;
      background-color: #fff;
      margin: auto;
      .background-check-icons {
        .spacer {
          color: #898989;
          font-size: 18px;
          font-weight: 700;
        }
        span {
          color: #fff;
          border-radius: 100%;
        }
        .credit-score {
          position: relative;
          background: var(--poplin-pink, #ff6289);
          height: 30px;
          width: 30px;
          padding: 8px;
          left: 70px;
          top: 5px;
          font-size: 14px;
          margin-left: 2px;
        }
        .how-to-reg {
          background: var(--poplin-pink, #ff6289);
          height: 36px;
          width: 36px;
          padding: 10px;
          font-size: 16px;
          position: relative;
          right: 13px;
          top: 2px;
        }
      }
      .background-check-title {
        /* ID Verification */

        width: 306px;
        height: 32px;

        font-family: 'Roboto';
        font-style: normal;
        font-weight: 700;
        font-size: 27px;
        line-height: 32px;
        text-align: center;
        display: flex;
        justify-content: center;
        margin-top: -10px;
        h3 {
          margin-right: 58px;
        }
        color: #323c46;
        order: 1;
      }
      .background-check-details {
        width: 280px;
        height: 54px;

        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 18px;
        /* or 129% */

        /* SudShare/AAA white contrast */

        color: #575757;

        /* Inside auto layout */

        order: 0;
        p {
          position: relative;
          right: 20px;
          top: 10px;
        }
      }
      .background-check-button {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        padding: 9px 18px;
        grid-gap: 12px;
        gap: 12px;
        width: 325px;
        height: 55px;
        border-radius: 6px;
        flex: none;
        order: 1;
        flex-grow: 1;
        position: relative;
        top: 55px;
        right: 40px;
        color: var(--button-color-primary-alt, #fff);
        text-align: center;

        /* Aux Text/M */
        font-family: 'PitchSans-Medium', sans-serif;
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        line-height: 20px;
        letter-spacing: 1.6px;
        text-transform: uppercase;
      }
    }
    .background-check-timer-main {
      position: relative;
      z-index: 10;
      top: 60px;
      margin: auto;
      /* Auto layout */

      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      padding: 3px 9px;
      gap: 12px;

      width: 284px;
      height: 24px;

      background: #ffffff;
      box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.12);
      border-radius: 24px;
      text-align: center;
      letter-spacing: 0.04em;
      font-family: 'Roboto';
      font-style: normal;
      font-weight: 400;
      font-size: 13px;
      line-height: 18px;
      /* Inside auto layout */

      flex: none;
      order: 1;
      flex-grow: 0;

      p {
        margin-top: -5px;
      }
    }
  }
}
.help-icon {
  position: fixed;
  right: 25px;
}
.container-footer {
  color: var(--content-alt, #4b4b4b);
  text-align: center;
  position: relative;
  top: 50px;

  /* Body Text/Small */
  font-family: 'Fakt-Normal' sans-serif;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 140%;
}
