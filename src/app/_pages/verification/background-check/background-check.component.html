<ion-header>
  <ion-row>
    <ion-col size="11">
      <ion-title>Security Check </ion-title>
    </ion-col>
    <ion-col size="1">
      <i class="material-icons-outlined help-icon" (click)="contactSupport()"
        >help_outline</i
      >
    </ion-col>
  </ion-row> </ion-header
><ion-content>
  <div *ngIf="unverified" class="main">
    <ion-row class="background-check-main">
      <ion-col size="10">
        <ion-row class="background-check-icons">
          <ion-col size="5" class="credit-score-col"
            ><span class="material-icons-outlined credit-score">
              credit_score
            </span>
          </ion-col>
          <ion-col size="2" class="spacer">___</ion-col>
          <ion-col size="5">
            <span class="material-icons-outlined how-to-reg"> how_to_reg </span>
          </ion-col>
        </ion-row>
        <ion-row class="background-check-title">
          <h3>Background Check</h3>
        </ion-row>
        <ion-row class="background-check-details">
          <p>
            Congrats, your identity is verified! Now, let's move on to the
            background check. With your date of birth and SSN ready, click the
            button below. A new window will open to start the background check
            process through Checkr.
          </p>
        </ion-row>
        <ion-row class="background-check-button-row">
          <ion-button
            id="begin-button"
            (click)="runCheckr()"
            class="background-check-button"
            >Begin</ion-button
          >
        </ion-row>
        <ion-row class="container-footer">
          <span
            >Processing time can take between 2 minutes to 5 business
            days.</span
          >
        </ion-row>
      </ion-col>
    </ion-row>
    <ion-row class="background-check-timer-main">
      <ion-col class="background-check-timer-col">
        <ion-row class="background-check-timer">
          <p>Order reserved, {{ formatCounter }} minutes remaining</p>
        </ion-row>
      </ion-col>
    </ion-row>
  </div>
  <app-verification-processing
    *ngIf="pending"
    [backgroundCheck]="true"
  ></app-verification-processing>
  <app-verification-failure
    *ngIf="failed || consider || canceled"
    [backgroundCheck]="true"
  ></app-verification-failure>
</ion-content>
