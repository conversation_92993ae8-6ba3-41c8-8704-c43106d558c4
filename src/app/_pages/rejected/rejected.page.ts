import { Component, OnInit } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/auth';
import { AngularFirestore } from '@angular/fire/firestore';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { SudsterData } from 'src/app/_interfaces/sudster-data.interface';
import { AuthidService } from 'src/app/_services/authid.service';

@UntilDestroy()
@Component({
  selector: 'app-rejected',
  templateUrl: './rejected.page.html',
  styleUrls: ['./rejected.page.scss'],
})
export class RejectedPage implements OnInit {
  UserID = this.AuthID.getID();

  AccountRejected = false;
  RejectedReason = '';
  AccountRejectedForterReason: string;

  constructor(
    public afAuth: AngularFireAuth,
    public firestore: AngularFirestore,
    private AuthID: AuthidService
  ) {}

  ngOnInit() {
    this.firestore
      .doc<SudsterData>(`Sudsters/${this.UserID}`)
      .valueChanges()
      .pipe(untilDestroyed(this))
      .subscribe((doc) => {
        this.AccountRejected = doc.AccountRejected;
        this.RejectedReason = doc.AccountRejectedDetails;
        this.AccountRejectedForterReason = doc.AccountRejectedForterReason
          ? doc.AccountRejectedForterReason
          : '';
      });
  }
}
