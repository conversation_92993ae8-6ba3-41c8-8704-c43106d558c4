<ion-content class="rejection-wrapper">
  <i class="material-icons">security</i>

  <h1 *ngIf="!AccountRejectedForterReason">
    {{AccountRejected ? 'Access Denied' : 'Access Granted'}}
  </h1>
  <h1
    *ngIf="AccountRejected && AccountRejectedForterReason && AccountRejectedForterReason !==''"
  >
    Validation in Progress
  </h1>

  <p
    class="rejection-reason"
    *ngIf="AccountRejected && AccountRejectedForterReason  === ''"
  >
    {{RejectedReason}}
  </p>
  <p
    *ngIf="AccountRejected && AccountRejectedForterReason && AccountRejectedForterReason !==''"
  >
    Account currently in validation. Check back later.
  </p>

  <ion-button
    [routerLink]="['/home']"
    size="large"
    expand="block"
    *ngIf="!AccountRejected"
    >Continue To App</ion-button
  >
  <ion-button
    href="https://poplin.co/contact"
    size="small"
    expand="block"
    fill="clear"
    >Contact Us</ion-button
  >
</ion-content>
