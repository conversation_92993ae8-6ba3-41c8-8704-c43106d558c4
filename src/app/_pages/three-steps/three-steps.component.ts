import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { AngularFirestore } from '@angular/fire/firestore';
import { AuthidService } from 'src/app/_services/authid.service';

@Component({
  selector: 'app-three-steps',
  templateUrl: './three-steps.component.html',
  styleUrls: ['./three-steps.component.scss'],
})
export class ThreeStepsComponent implements OnInit {
  constructor(
    public firestore: AngularFirestore,
    private AuthID: AuthidService
  ) {}
  @Output() NextStep = new EventEmitter();
  UserID = this.AuthID.getID();
  ngOnInit() {}
  Next() {
    this.NextStep.emit();
    this.firestore.doc('Sudsters/' + this.UserID).update({
      SignupStepNumber: 3,
    });
  }
}
