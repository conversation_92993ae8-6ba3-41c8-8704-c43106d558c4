.steps {
  height: 30px;
  width: 30px;
  background-color: #285652;
  color: #fff;
  display: flex;
  border-radius: 100%;
  font-family: 'PitchSans-Medium';
  position: relative;
  right: 20px;
  top: 16px;
  padding-top: 4px;
  padding-left: 10px;
}
.main {
  max-width: 367px;
  margin: auto;
}
ion-col {
  p {
    color: #000;
    font-family: 'Fakt-Normal' sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 140%;
    margin-bottom: 0;
  }
  .subtext {
    font-size: 13px;
    color: #909090;
  }
}
ion-button {
  &::part(native) {
    display: flex;
    width: 285px;
    min-height: 48px;
    padding: var(--button-padding-vertical-default, 6px)
      var(--button-padding-horizontal-default, 8px);
    justify-content: center;
    align-items: center;
    border-radius: var(--button-radius-radius-square, 8px);
    position: relative;
    right: 25px;
    /* Light/Basic Drop Shadow */
    box-shadow: 0px 4px 24px 0px rgba(0, 0, 0, 0.04);
    color: var(--button-color-primary-alt, #fff);
    text-align: center;
    margin-top: 10px;

    /* Aux Text/M */
    font-family: 'PitchSans-Medium', 'Helvetica Neue', Helvetica, Arial,
      sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 20px;
    letter-spacing: 1.6px;
    text-transform: uppercase;
  }
}
ion-row {
  #next-button {
    --background: var(--viridian-core);
  }
}
