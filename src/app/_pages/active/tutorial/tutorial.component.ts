import {
  Component,
  ElementRef,
  EventEmitter,
  Input,
  isDevMode,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { AlertController, ToastController } from '@ionic/angular';

@Component({
  selector: 'app-tutorial',
  templateUrl: './tutorial.component.html',
  styleUrls: ['./tutorial.component.scss'],
})
export class TutorialComponent implements OnInit {
  @Input() videoLink;
  @Input() quizQuestion = '';
  @Input() quizOptions = [];
  @Input() quizAnswer = 0;
  @Input() required = false;
  @Input() videoLength = 0;
  @Input() recommendedTraining = true;
  @Output() tutorialComplete = new EventEmitter();
  @ViewChild('video') videoBlock: ElementRef;

  OpenVideo = false;
  VideoCountdown = 0;
  HidePlayButton = false;
  videoPaused = false;
  PassedQuiz = false;

  constructor(
    private alertController: Al<PERSON><PERSON>ontroller,
    public toastController: ToastController
  ) {}

  ngOnInit() {
    this.VideoCountdown = this.videoLength - 5;
    if (isDevMode()) {
      this.VideoCountdown = 1;
    }
  }

  PlayVideo() {
    this.videoBlock.nativeElement.play();
    this.HidePlayButton = true;
    const videoInterval = setInterval(() => {
      if (this.VideoCountdown == 1) {
        clearInterval(videoInterval);
      }
      if (!this.videoPaused) {
        this.VideoCountdown--;
      }
    }, 1000);
  }

  videoStatusChanged() {
    if (this.videoBlock.nativeElement.paused) {
      this.videoPaused = true;
    } else {
      this.videoPaused = false;
    }
  }

  VideoCountdownMinutes() {
    return (
      Math.floor(this.VideoCountdown / 60) +
      ':' +
      (this.VideoCountdown % 60 < 10
        ? '0' + (this.VideoCountdown % 60)
        : this.VideoCountdown % 60)
    );
  }

  async TakeQuiz() {
    this.videoBlock.nativeElement.pause();
    this.videoBlock.nativeElement.currentTime = 0;
    const RadioArray = [];
    for (let i = 0; i < this.quizOptions.length; i++) {
      RadioArray.push({
        name: this.quizOptions[i],
        type: 'radio',
        label: this.quizOptions[i],
        value: i + 1,
      });
    }

    const alert = await this.alertController.create({
      header: 'Quiz',
      subHeader: this.quizQuestion,
      inputs: RadioArray,
      buttons: [
        {
          text: 'Close',
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {},
        },
        {
          text: 'Submit',
          handler: (selection) => {
            if (selection == this.quizAnswer) {
              this.required = false;
              this.tutorialComplete.emit();
              this.toastController
                .create({
                  message: 'Correct! Please proceed with the steps.',
                  duration: 3000,
                })
                .then((toast) => {
                  toast.present();
                });
            } else {
              this.WrongAnswer();
            }
          },
        },
      ],
    });

    await alert.present();
  }

  async WrongAnswer() {
    const alert = await this.alertController.create({
      header: 'Incorrect Answer',
      message:
        'Please re-watch the training video and make sure to pay careful attention.',
      backdropDismiss: false,
      buttons: [
        {
          text: 'OK',
          handler: (selection) => {
            this.VideoCountdown = this.videoLength;
            this.PlayVideo();
          },
        },
      ],
    });
    await alert.present();
  }
}
