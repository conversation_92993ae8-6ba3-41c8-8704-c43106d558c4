#InnerDiv {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.25);
  border-radius: 15px;
  margin-top: 20px;
  overflow: hidden;
  position: relative;
  border: solid 4px white;
  margin-bottom: 20px;
}

video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  margin-bottom: -5px;
  &::-webkit-media-controls-timeline,
  &::-webkit-media-controls-current-time {
    display: none;
  }
}

i {
  position: absolute;
  font-size: 100px;
  left: calc(50% - 50px);
  width: 100px;
  text-align: center;
  top: calc(50% - 50px);
  height: 100px;
  background: white;
  color: var(--Orange);
  box-shadow: 0 5px 30px rgba(0, 0, 0, 0.3);
  z-index: 10;
  border-radius: 50%;
}

label,
p {
  color: var(--BlueGray3);
  font-size: 13px;
  padding-bottom: 10px;
  display: block;
  line-height: 150%;
  font-weight: 800;

  b {
    font-size: 17px;
  }
}

ion-button {
  margin: 0px;
  margin-top: 5px;
  max-width: none;
}
