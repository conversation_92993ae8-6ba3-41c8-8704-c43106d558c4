<div *ngIf="required || OpenVideo">
  <div id="InnerDiv">
    <i (click)="PlayVideo()" *ngIf="!HidePlayButton" class="material-icons"
      >play_circle_filled</i
    >
    <video
      playsinline
      #video
      [controls]="HidePlayButton"
      preload="metadata"
      (pause)="videoStatusChanged()"
      poster="assets/img/trainingvideoposter2.png"
      (playing)="videoStatusChanged()"
    >
      <source src="{{ videoLink }}" type="video/mp4" />
    </video>
    <ion-button
      (click)="TakeQuiz()"
      *ngIf="HidePlayButton && !OpenVideo"
      [disabled]="VideoCountdown !== 0"
      expand="full"
      >Take Quiz&nbsp;<span *ngIf="VideoCountdown !== 0">
        ({{ VideoCountdownMinutes() }})</span
      ></ion-button
    >
  </div>
  <p *ngIf="required && recommendedTraining; else training">
    Watch this quick video for pro tips during your first order<br />
    and earn 15 points when you pass the quiz!
  </p>

  <ng-template #training>
    <p *ngIf="required && !recommendedTraining">
      Please watch the entire training video<br />and take the quiz before
      continuing.
    </p>
  </ng-template>
</div>
<ion-button
  *ngIf="!OpenVideo && !required"
  (click)="OpenVideo = true"
  size="small"
  expand="block"
  fill="clear"
>
  <ion-icon
    name="play-circle-outline"
    style="margin: 0px; padding-right: 5px"
  ></ion-icon
  >{{
    recommendedTraining ? 'Review Best Practices' : 'REWATCH training Video'
  }}
</ion-button>
