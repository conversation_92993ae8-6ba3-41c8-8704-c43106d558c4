ion-content {
  --background: var(--BlueGray1);
}
ion-title {
  span.title-order-number {
    font-weight: 700;
    font-size: 16px;
    line-height: 16px;
    letter-spacing: 0.15em;
  }
  span.title-customer-name {
    font-size: 24px;
    font-weight: 500;
    line-height: 120%;
  }
}

#MessageIcon {
  position: absolute;
  right: 15px;
  top: 13px;
  z-index: 3;

  i {
    font-size: 33px;
    color: var(--BlueGray5);
  }

  span {
    position: absolute;
    color: white;
    left: 10px;
    font-size: 12px;
    font-weight: 800;
    top: 3px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }
}

#MessageIcon.highlight {
  animation-name: zoombounce;
  animation-duration: 1s;
  animation-iteration-count: infinite;
  animation-direction: alternate;
  animation-timing-function: ease-out;

  i {
    color: var(--Green);
  }
}

@keyframes zoombounce {
  from {
    transform: scale(0.9);
  }

  to {
    transform: scale(1.1);
  }
}

#InnerDiv {
  text-align: center;
  margin: 0 auto;
  max-width: 400px;
  width: 90%;
  position: relative;
}

#DeadlineLabel {
  border-radius: 20px;
  padding: 3px 15px;
  font-weight: 400;
  font-size: 15px;
  color: var(--BlueGray5);

  i {
    font-size: 15px;
    color: var(--BlueGray4);
    padding: 2px;
    padding-left: 5px;
    vertical-align: -2px;
  }
}

#DeadlineSuggestion {
  font-size: 12px;
  color: var(--Green);
  background: white;
  border-radius: 50px;
  display: inline-block;
  padding: 3px 7px;
  margin-top: 7px;
  font-weight: 500;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

  i {
    font-size: 17px;
    vertical-align: -4px;
    padding-right: 5px;
  }
}

#DeadlineLabel.PastDue {
  background: red;
  color: white;
}

#HeaderSection {
  margin-top: 20px;

  h1 {
    margin: 0;
    font-size: 40px;
    font-weight: 300;
    color: var(--BlueGray5);
  }

  #BreadcrumbDiv {
    display: block;
    width: 250px;
    position: relative;
    text-align: center;
    margin: 30px auto;
    margin-top: 20px;
    height: 45px;

    span {
      width: 200px;
      background: var(--BlueGray2);
      height: 2px;
      display: block;
      position: absolute;
      left: 25px;
      top: 25px;
      z-index: 1;
    }

    i {
      display: inline-block;
      width: 30px;
      height: 30px;
      margin: 0px 14px;
      border-radius: 50%;
      background: var(--BlueGray2);
      position: relative;
      z-index: 2;
      color: var(--BlueGray4);
      line-height: 30px;
      font-size: 18px !important;
    }

    .edit-weight {
      position: relative;
      display: inline-block;

      .edit-label {
        position: absolute;
        top: 33px;
        left: 0;
        right: 0;
        margin: 0 auto;
        width: 33px;
        height: auto;
        background: white;
        color: #ffc107;
        font-size: 10px;
        line-height: 10px;
        text-transform: uppercase;
        font-weight: 500;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        padding: 1px 0px;
        border-radius: 3px;
        z-index: 2;

        &.step-active {
          top: 45px;
          background: #ffc107;
          color: white;
          box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
        }
      }
    }

    .BreadcrumbDone {
      border: none;
      background: white;
      color: #ffc107;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .BreadcrumbProgress {
      border: solid 4px #ffe69b;
      background: var(--Orange);
      color: white;
      height: 45px;
      width: 45px;
      padding-top: 4px;
      font-size: 25px !important;
      box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .double-points-pill {
      display: inline-block;
      position: absolute;
      top: 10px;
      width: 74px;
      right: -72px;
      background-color: #f9b011;
      border: 1px solid #fff;
      height: 30px;
      border-radius: 15px;
      text-align: center;
      padding: 4px 5px 2px 2px;
      margin-right: 5px;
      font-weight: 600;
      font-size: 13px;
      z-index: 2;
      box-shadow: 2px 2px 3px 0px #d9d9d9;
      -webkit-box-shadow: 2px 2px 3px 0px #d9d9d9;
      -moz-box-shadow: 2px 2px 3px 0px #d9d9d9;
      color: #000;

      span {
        position: unset;
        width: auto;
        margin: 0 1px 0 2px;
        padding: 3px;
        display: inline-block;
        height: auto;
        background-color: #fff;
        border-radius: 100%;
        font-size: 12px;
      }
    }
  }
}

#SectionVertLine {
  display: block;
  position: absolute;
  z-index: 1;
  background: var(--BlueGray2);
  height: calc(100% - 280px);
  width: 2px;
  left: 31px;
  top: 165px;
}

#SameDayServiceDiv {
  background: rgba(0, 82, 101, 0.17);
  border-radius: 50px;
  height: 55px;
  width: 100%;
  padding: 4px;

  p {
    font-size: 0.85rem;
    margin: 0px 17px;
    text-align: left;
    padding: 4px 0px 0px 4px;
  }
}

.same-day-service-pill {
  background-color: #22ecab;
  border: 1px solid #fff;
  min-width: 85px;
  max-width: 99px;
  height: 30px;
  border-radius: 15px;
  text-align: center;
  font-weight: 600;
  font-size: 0.6rem;
  position: absolute;
  top: 0.55rem;
  right: 1.85vw;
  font-weight: 800;
  padding: 8px;
  box-shadow: 0px 3px 2px rgba(0, 0, 0, 0.25);
  -webkit-box-shadow: 0px 3px 2px rgba(0, 0, 0, 0.25);
  -moz-box-shadow: 0px 3px 2px rgba(0, 0, 0, 0.25);

  span.same-day-icon {
    background-color: #fff;
    border-radius: 100%;
    padding: 2px;
    margin-left: 2px;
  }
}

.display-block {
  display: block;
}

.order-extension {
  position: absolute;
}

.order-extension-button {
  margin-left: -5px;
  top: -4px;
  font-size: 16px;
  padding: 4px;
  border-radius: 50%;
  border-style: solid;
  border-width: 1px;
}

.tooltip {
  position: absolute;
  background-color: var(--viridian-800);
  border-radius: 8px;
  color: #fff;
  padding: 12px 16px;
  font-size: 14px;
  right: -10px;
  top: 30px;
  width: 235px;
  z-index: 200;

  &::before {
    content: '';
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-bottom: 5px solid var(--viridian-800);
    position: absolute;
    left: calc(88% + 1px);
    top: -5px;
  }
  div {
    text-transform: uppercase;
    font-family: 'PitchSans-Medium', Helvetica, sans-serif;
    font-weight: 700;
    line-height: 120%;
    letter-spacing: 2.1px;
    text-align: left;
  }
  p {
    margin-top: 5px;
    text-align: left;
  }
  a {
    float: right;
    text-decoration: underline;
    color: #fff;
    background-color: var(--viridian-800);
  }
}
