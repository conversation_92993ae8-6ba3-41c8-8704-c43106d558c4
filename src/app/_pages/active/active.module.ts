import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { ActivePageRoutingModule } from './active-routing.module';

import { NgxMaskModule } from 'ngx-mask';
import { AdjustBagSizeComponent } from 'src/app/_components/adjust-bag-size/adjust-bag-size.component';
import { CountSelectorComponent } from 'src/app/_components/count-selector/count-selector.component';
import { FoldingVideosComponent } from 'src/app/_components/folding-videos/folding-videos.component';
import { PhotoUploadComponent } from 'src/app/_components/photo-upload/photo-upload.component';
import { VideoPopoverComponent } from 'src/app/_components/video-popover/video-popover.component';
import { SwiperModule } from 'swiper/angular';
import { ActivePage } from './active.page';
import { DeliverComponent } from './deliver/deliver.component';
import { HelpLocateComponent } from './help-locate/help-locate.component';
import { LaunderComponent } from './launder/launder.component';
import { MessagesComponent } from './messages/messages.component';
import { OrderExtensionComponent } from './order-extension/order-extension.component';
import { PickupComponent } from './pickup/pickup.component';
import { StepComponent } from './step/step.component';
import { TutorialComponent } from './tutorial/tutorial.component';
import { WeighComponent } from './weigh/weigh.component';
import { NotificationComponent } from '../../shared/components/notification/notification.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ActivePageRoutingModule,
    NgxMaskModule.forRoot(),
    SwiperModule,
    NotificationComponent,
  ],
  declarations: [
    ActivePage,
    StepComponent,
    PhotoUploadComponent,
    TutorialComponent,
    CountSelectorComponent,
    PickupComponent,
    LaunderComponent,
    WeighComponent,
    DeliverComponent,
    HelpLocateComponent,
    MessagesComponent,
    FoldingVideosComponent,
    VideoPopoverComponent,
    OrderExtensionComponent,
    AdjustBagSizeComponent,
  ],
  providers: [{ provide: 'windowObject', useValue: window }],
})
export class ActivePageModule {}
