#InnerDiv {
  margin: 0 auto;
  display: block;
  overflow: scroll;
  width: 100%;
  height: calc(100% - 50px - env(safe-area-inset-bottom));
  --padding-bottom: 30px;
  --padding-top: 30px;
}

.MessageItem {
  background: var(--BlueGray1);
  border-radius: 15px;
  border-bottom-left-radius: 0px;
  max-width: 80%;
  float: left;
  color: var(--BlueGray5);
  margin-top: 22px;
  margin-left: 18px;
  margin-right: 18px;
  position: relative;
  min-width: 120px;
  min-height: 50px;
  display: block;
  clear: both;
}

.MessageItem.LP {
  background: var(--Green) !important;
  color: white !important;
  float: right !important;
  border-bottom-right-radius: 0px !important;
  border-bottom-left-radius: 15px !important;
}

.MessageItem p {
  font-size: 18px;
  font-weight: 400;
  margin: 12px;
}

.MessageItem label {
  position: absolute;
  top: -15px;
  font-size: 10px;
  padding-left: 7px;
  color: var(--<PERSON>Gray4);
  font-weight: 700;
  letter-spacing: 0.5px;
}

.chatInput {
  background: white;
  color: #757575;
  box-shadow: 0 1px 2px rgba(0, 0, 50, 0.2);
  border: #fafafa 1px solid !important;
  border-radius: 25px;
  transition: 0.1s ease-out;
  padding-left: 15px;
  padding-right: 10px;
  margin-right: 5px;
  height: 40px;
  width: calc(100% - 50px);
}

.chatInput:focus {
  color: #212121;
  transition: 0.2s ease-in;
}

ion-button {
  margin: 0;

  ion-icon {
    margin: 0;
  }
}

.chatButton {
  margin: 0;
  --background: var(--Green) !important;
  --width: 37px;
  --height: 37px;
  width: 37px;
  height: 37px;
  --padding-start: 0px;
  --padding-end: 0px;
  margin-top: -10px;
  --border-radius: 50%;

  ion-icon {
    margin: 0;
  }
}

.chatFormWrapper {
  background: var(--BlueGray1);
  padding: 5px;
  padding-bottom: calc(env(safe-area-inset-bottom) + 5px);
}

.chatFormWrapper p {
  padding: 0 0px;
  font-size: 14px;
  text-align: center;
  color: var(--BlueGray5);
  margin: 3px;
}
.chat-inner {
  display: flex;
  position: relative;
  min-height: 40px;
  margin: 0 -3px;

  .input-wrapper {
    position: absolute;
    bottom: 0;
    width: 100%;
    background: var(--BlueGrayLight);
    min-height: 40px;
    display: flex;
    padding: 0 3px;
  }

  ion-textarea {
    width: calc(100% - 50px);
    background: white;
    color: #757575;
    box-shadow: 0 1px 2px rgba(0, 0, 50, 0.2);
    border: #fafafa 1px solid !important;
    border-radius: 15px;
    transition: 0.1s ease-out;
    --padding-start: 15px;
    --padding-end: 10px;

    margin: 5px 5px 0 0;
    flex-grow: 1;

    &.sc-ion-textarea-md-h {
      --highlight-color-focused: unset;
    }

    &:focus {
      color: #212121;
      transition: 0.2s ease-in;
    }
  }

  ion-button {
    width: 37px;
    flex-grow: 0;
    margin: 0 10px 3px 0;
    align-self: flex-end;
  }
}

#ResponseTimeWarning {
  display: block;
  clear: both;
  font-weight: 500;
  font-size: 13px;
  text-align: center;
  color: var(--BlueGray3);
  padding-top: 15px;
  i {
    font-size: 17px;
    vertical-align: -3px;
    padding-right: 5px;
  }
}

.new-customer-info {
  background-color: #fffaec;
  border: 1px solid #fff3d2;
  border-radius: 15px;
  max-width: 80%;
  float: left;
  color: var(--ion-color-dark-shade);
  margin: -10px 10% 7px;
  position: relative;
  min-width: 120px;
  min-height: 50px;
  display: block;
  clear: both;
  font-size: 12px;
  padding: 8px 10px;
  font-size: 13px;
  text-align: justify;
}

ion-badge.new-cust {
  position: absolute;
  bottom: 5px;
  left: 57px;
  font-size: 9px;
  padding: 2px 3px;
  text-transform: uppercase;
  height: 13px;
  background: white;
  color: var(--ion-color-warning-shade);
  border: 1px solid var(--ion-color-warning-shade);
  font-weight: 400;
}
