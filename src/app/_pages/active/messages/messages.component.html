<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button id="dismiss-button" (click)="dismissModal()">
        <ion-icon name="arrow-back-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>Message {{ CustomerName }}</ion-title>
    <ion-badge *ngIf="isFirstOrder" class="new-cust" color="warning"
      >New</ion-badge
    >
  </ion-toolbar>
</ion-header>

<ion-content
  #chatContent
  style="overflow: hidden; --padding-bottom: 0 !important"
>
  <ion-content id="InnerDiv" #MessageContainer>
    <div class="new-customer-info" *ngIf="isFirstOrder">
      <b>This is {{ CustomerName }}’s first order with Poplin!</b>
      Impress them with great communication and an amazing experience to create
      a loyal customer!
    </div>
    <div
      *ngFor="let message of MessagesArray; let i = index"
      class="MessageItem"
      [class.LP]="!message.Customer"
    >
      <label>{{ convertMessageTime(message.Time) }}</label>
      <p>{{ message.Text }}</p>
    </div>

    <label
      [style.color]="LastMessageResponseTimeColor()"
      *ngIf="LastMessageIsCustomer()"
      id="ResponseTimeWarning"
      (click)="ResponseTimePopup()"
      ><i class="material-icons">{{
        LastMessageResponseTimeColor() === 'red'
          ? 'warning_amber'
          : 'info_outline'
      }}</i
      >Respond to <b>every</b> message within 1 hour.</label
    >
  </ion-content>

  <div class="chatFormWrapper">
    <p *ngIf="orderStatusNumber === 4">
      You can no longer message your Customer after delivery. Please contact
      support instead.
    </p>
    <p *ngIf="orderStatusNumber === 0">
      Order was cancelled. You can no longer message the Customer. Please
      contact support instead.
    </p>
    <div
      class="chat-inner"
      *ngIf="orderStatusNumber >= 1.5 && orderStatusNumber < 4"
    >
      <div class="input-wrapper">
        <ion-textarea
          #chatInput
          [ngStyle]="getInputChatMaxHeightStyle()"
          id="chat-input"
          rows="1"
          autocapitalize
          placeholder="Type a message"
          [autoGrow]="true"
          [value]="message"
          (ionInput)="setMessagesValue($event)"
          (keydown)="CharTyped($event)"
          (focus)="InputFocus()"
          [disabled]="DisableForm"
        ></ion-textarea>
        <ion-button
          id="send-button"
          size="small"
          (click)="SendButton()"
          class="chatButton"
          [disabled]="
            message === undefined ||
            message === '' ||
            message === ' ' ||
            DisableForm
          "
        >
          <ion-icon name="send"></ion-icon>
        </ion-button>
      </div>
    </div>
  </div>
</ion-content>
