import {
  After<PERSON><PERSON>w<PERSON>nit,
  Component,
  HostListener,
  Input,
  On<PERSON><PERSON>roy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { AngularFireAuth } from '@angular/fire/auth';
import { AngularFirestore } from '@angular/fire/firestore';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>er,
  LoadingController,
  ModalController,
} from '@ionic/angular';
import { IonContent, IonTextarea } from '@ionic/angular/standalone';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import 'firebase/firestore';
import { first, map } from 'rxjs/operators';
import { OrderData } from 'src/app/_interfaces/order-data.interface';
import { SudsterData } from 'src/app/_interfaces/sudster-data.interface';
import { AuthidService } from 'src/app/_services/authid.service';
import { MaskCallService } from 'src/app/_services/mask-call.service';
import { StatsigFactoryService } from 'src/app/_services/statsig-factory.service';
import { StatsigService } from 'src/app/_services/statsig.service';
import { environment } from 'src/environments/environment';
import { ApiService } from '../../../_services/api.service';

export interface MessageData {
  CustomerID?: string;
  SudsterID?: string;
  Text: string;
  Time: string;
  Name: string;
  Customer: string;
  ExtensionConfirmed?: boolean;
}

export interface MessageDataID extends MessageData {
  id: string;
}

interface ApiResponseData {
  statusText: string;
  success: boolean;
}

@UntilDestroy()
@Component({
  selector: 'app-messages',
  templateUrl: './messages.component.html',
  styleUrls: ['./messages.component.scss'],
})
export class MessagesComponent implements OnInit, OnDestroy, AfterViewInit {
  message: string;

  @ViewChild('MessageContainer', { static: true }) private MessageContainer;
  @ViewChild('chatContent', { static: false }) private chatContent: IonContent;
  @ViewChild('chatInput', { static: false }) private chatInput: IonTextarea;

  private resizeObserver!: ResizeObserver;

  FirstName = '';
  MessagesArray = [];
  DisableForm = false;
  SameDayService?: boolean;

  UserID = this.AuthID.getID();

  LoadedOnce = false;
  ExtensionsEnabled: boolean = false;
  OrderTimezone: string;
  private statsigService: StatsigService;
  private maxChatInputHeight = 200;
  private chatInputHeight = 50;

  @Input() SudsterName;
  @Input() OrderNumber;
  @Input() CustomerName;
  @Input() isFirstOrder: boolean;
  @Input() orderStatusNumber: number;

  constructor(
    public firestore: AngularFirestore,
    public afAuth: AngularFireAuth,
    private modalCtrl: ModalController,
    private alertController: AlertController,
    private MaskCall: MaskCallService,
    private loadingController: LoadingController,
    private AuthID: AuthidService,
    private statsigFactoryService: StatsigFactoryService,
    private apiService: ApiService
  ) {
    this.statsigService = this.statsigFactoryService.getInstance();
  }

  @HostListener('window:resize')
  onResize() {
    this.updateMaxHeight();
  }

  ionViewDidEnter(): void {
    this.updateMaxHeight();
  }

  ngAfterViewInit() {
    this.chatInput?.getInputElement().then((textareaElement) => {
      this.resizeObserver = new ResizeObserver((entries) => {
        this.chatInputHeight = entries[0].contentRect.height;
      });

      this.resizeObserver.observe(textareaElement);
    });
  }

  updateMaxHeight() {
    this.chatContent.getScrollElement().then((scrollElement) => {
      const height = scrollElement.clientHeight;
      this.maxChatInputHeight = height - 20;
    });
  }

  ngOnInit() {
    this.firestore
      .doc<SudsterData>(`Sudsters/${this.UserID}`)
      .valueChanges()
      .pipe(first())
      .subscribe((doc) => {
        this.FirstName = doc.FirstName;
      });

    this.firestore
      .doc(`Orders/${this.OrderNumber}`)
      .valueChanges()
      .pipe(untilDestroyed(this))
      .subscribe((doc: OrderData) => {
        this.orderStatusNumber = doc.OrderStatusNumber;
        if (this.orderStatusNumber === 0 || this.orderStatusNumber === 4) {
          this.DisableForm = true;
        }
      });

    this.firestore
      .collection<MessageData>(`Orders/${this.OrderNumber}/Messages`)
      .snapshotChanges()
      .pipe(
        untilDestroyed(this),
        map((actions) =>
          actions.map((a) => {
            const data = a.payload.doc.data() as MessageData;
            const id = a.payload.doc.id;
            return { id, ...data };
          })
        )
      )
      .subscribe((collection) => {
        this.MessagesArray = [];
        collection.forEach((doc) => {
          let MessageText = doc.Text;

          //TODO - hide phone and email from PN's in cloud functions
          MessageText = MessageText.replace(
            /(?:(?:\+?([1-9]|[0-9][0-9]|[0-9][0-9][0-9])\s*(?:[.-]\s*)?)?(?:\(\s*([2-9]1[02-9]|[2-9][02-8]1|[2-9][02-8][02-9])\s*\)|([0-9][1-9]|[0-9]1[02-9]|[2-9][02-8]1|[2-9][02-8][02-9]))\s*(?:[.-]\s*)?)?([2-9]1[02-9]|[2-9][02-9]1|[2-9][02-9]{2})\s*(?:[.-]\s*)?([0-9]{4})(?:\s*(?:#|x\.?|ext\.?|extension)\s*(\d+))?/g,
            '[PHONE HIDDEN]'
          );
          MessageText = MessageText.replace(
            /(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])/g,
            '[EMAIL HIDDEN]'
          );
          this.MessagesArray.push({
            SudsterID: doc.SudsterID || null,
            Text: MessageText,
            Time: parseInt(doc.id),
            Customer: doc.SudsterID
              ? this.UserID != doc.SudsterID
              : doc.Customer,
            Name: doc.Name,
            ExtensionConfirmed: doc.ExtensionConfirmed,
          });
        });

        if (this.LoadedOnce) {
          this.MessageContainer.scrollToBottom(400);
        } else {
          this.MessageContainer.scrollToBottom(0);
        }

        this.LoadedOnce = true;
      });
    this.firestore
      .doc<OrderData>(`Orders/${this.OrderNumber}`)
      .valueChanges()
      .pipe(first())
      .subscribe((doc) => {
        this.SameDayService = doc.SameDayService;
        this.OrderTimezone = doc.Timezone;
      });

    this.statsigService
      .checkGate(environment.statsig.flags.orderExtension)
      .subscribe((value) => {
        this.ExtensionsEnabled = value;
      });
  }

  SendButton() {
    this.send(
      this.message,
      this.OrderNumber,
      this.FirstName,
      this.OrderTimezone
    )
      .then((res) => {
        if (res) {
          this.message = '';
        }
        this.DisableForm = false;
      })
      .catch((err) => {
        this.presentAlert(
          'Message Issue',
          err?.message || 'An error occurred while processing your request.'
        );
      });
  }

  async send(messageText, OrderNumber, FirstName, Timezone) {
    return new Promise(async (resolve, reject) => {
      if (!messageText || !messageText.trim()) {
        resolve(true);
        return;
      }

      if (
        messageText.search(
          /(?:(?:\+?([1-9]|[0-9][0-9]|[0-9][0-9][0-9])\s*(?:[.-]\s*)?)?(?:\(\s*([2-9]1[02-9]|[2-9][02-8]1|[2-9][02-8][02-9])\s*\)|([0-9][1-9]|[0-9]1[02-9]|[2-9][02-8]1|[2-9][02-8][02-9]))\s*(?:[.-]\s*)?)?([2-9]1[02-9]|[2-9][02-9]1|[2-9][02-9]{2})\s*(?:[.-]\s*)?([0-9]{4})(?:\s*(?:#|x\.?|ext\.?|extension)\s*(\d+))?/g
        ) != -1
      ) {
        this.setDirectContactAlert(OrderNumber);
        //Trying to send Phone Number
        reject(
          "It looks like you're trying to send a phone number. Personal contact information is never allowed to be shared, and is a violation of our terms of service. Please remove the phone number from your message and keep all communication in the built-in app messaging. If you need any help, please reach out to our management office."
        );
      } else if (
        messageText.search(
          /(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*|"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])/g
        ) != -1
      ) {
        this.setDirectContactAlert(OrderNumber);
        //Trying to send Email
        reject(
          "It looks like you're trying to send an email address. Personal contact information is never allowed to be shared, and is a violation of our terms of service. Please remove the email address from your message and keep all communication in the built-in app messaging. If you need any help, please reach out to our management office."
        );
      } else {
        this.DisableForm = true;
        const DaysOfWeek = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];

        const body = {
          SudsterID: this.UserID,
          Name: FirstName,
          Customer: false,
          Text: messageText,
          OrderNumber: OrderNumber,
          Timezone: Timezone,
        };
        try {
          this.apiService.post('SendMessage', body).subscribe({
            next: (res) => {
              console.log(JSON.stringify(res));
              if (res?.rawResponse?.status === 403) {
                const data = res.data as ApiResponseData;
                const message = data.statusText;
                this.presentMessageErrorString(message).then(() => {
                  resolve(false);
                });
                return;
              }
              if (!res || parseInt(res.status) !== 200) {
                this.presentMessageError().then(() => {
                  resolve(false);
                });
                return;
              }
              resolve(true);
            },
            error: (err) => {
              console.log('error', err);
              this.presentMessageError().then(() => {
                resolve(false);
              });
            },
          });
        } catch (err) {
          console.log('err', err);
          console.log('stack', err.stack);

          await this.presentMessageError();
          resolve(false);
          return;
        }
      }
    });
  }

  async presentMessageError() {
    const alert = await this.alertController.create({
      header: 'Error',
      message: 'There was an error sending your message. Please try again.',
      buttons: ['OK'],
    });
    await alert.present();
  }

  async presentMessageErrorString(errorMessage: string) {
    const alert = await this.alertController.create({
      header: 'Error',
      message: errorMessage,
      buttons: ['OK'],
    });
    await alert.present();
  }

  CharTyped(event) {
    if (event.keyCode === 13) {
      this.SendButton();
    }
  }

  convertMessageTime(Timestamp) {
    let Time = '';

    const now = new Date();
    const MsgDate = new Date(Timestamp);

    if (now.getTime() - Timestamp < 60000) {
      Time = 'Just Now';
    } else if (now.getTime() - Timestamp < 3600000) {
      Time = ((now.getTime() - Timestamp) / 60000).toFixed(0) + ' MIN AGO';
    } else if (MsgDate.getDay() == now.getDay()) {
      Time = 'Today, ' + formatAMPM(MsgDate);
    } else {
      const DaysOfWeek = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];
      Time = DaysOfWeek[MsgDate.getDay()] + ', ' + formatAMPM(MsgDate);
    }

    function formatAMPM(date) {
      let hours = date.getHours();
      let minutes = date.getMinutes();
      const ampm = hours >= 12 ? 'PM' : 'AM';
      hours = hours % 12;
      hours = hours ? hours : 12; // the hour '0' should be '12'
      minutes = minutes < 10 ? '0' + minutes : minutes;
      const strTime = hours + ':' + minutes + ' ' + ampm;
      return strTime;
    }

    return Time;
  }

  dismissModal() {
    const body = {
      orderNumber: this.OrderNumber,
      orderPartial: { SudsterMessageBadge: 0 } as Partial<OrderData>,
      type: 'SudsterMessageBadge',
    };
    this.apiService.post('UpdateOrder/v1/minor-update', body).toPromise();
    this.modalCtrl.dismiss();
  }

  async presentAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK'],
    });
    await alert.present();
  }

  CallCustomer() {
    this.MaskCall.CallCustomer(this.OrderNumber);
  }

  InputFocus() {
    setTimeout(() => {
      this.MessageContainer.scrollToBottom(0);
    }, 1000);
  }

  ResponseTimePopup() {
    this.presentAlert(
      'Respond to every message',
      `If a customer messages you, you must respond within 1 hour. Even if there’s nothing important to say, even if all the practical information has been communicated, you should always send the last message. Laundry is a very personal service and being a responsive Laundry Pro makes customers feel at ease. Your last message might be... "Thank you.", "Got it.", "Have a nice day.", "OK". Again, always respond to a customer message, always respond within 1 hour, and, of course, always be polite and courteous. Remember, your messaging and response time will also affect your points.`
    );
  }

  LastMessageIsCustomer() {
    return this.MessagesArray[this.MessagesArray.length - 1]?.Customer == true;
  }

  LastMessageResponseTimeColor() {
    const diff =
      new Date().getTime() -
      this.MessagesArray[this.MessagesArray.length - 1].Time;
    const Hours = diff / 1000 / 3600;
    // || new Date().getHours() < 8 || new Date().getHours() > 20
    if (Hours < 0.5) {
      return 'var(--BlueGray3)';
    } else if (Hours < 1) {
      return 'var(--BlueGray5)';
    } else {
      return 'red';
    }
  }

  formatAMPM(date) {
    let hours = date.getHours();
    let minutes = date.getMinutes();
    const ampm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12;
    hours = hours ? hours : 12; // the hour '0' should be '12'
    minutes = minutes < 10 ? '0' + minutes : minutes;
    const strTime = hours + ':' + minutes + ' ' + ampm;
    return strTime;
  }

  async presentLoading() {
    const loading = await this.loadingController.create({});
    await loading.present();
    return loading;
  }

  async setDirectContactAlert(OrderNumber: string) {
    const body = {
      orderNumber: OrderNumber,
      orderPartial: { ContactAlert: true } as Partial<OrderData>,
      type: 'ContactAlert',
    };
    await this.apiService.post('UpdateOrder/v1/minor-update', body).toPromise();
  }

  setMessagesValue(event: any): void {
    this.message = event.target.value;
  }

  getInputChatMaxHeightStyle(): any {
    return {
      'max-height': this.maxChatInputHeight + 'px',
      'overflow-y':
        this.chatInputHeight > this.maxChatInputHeight ? 'auto' : 'hidden',
    };
  }

  ngOnDestroy(): void {
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
  }
}
