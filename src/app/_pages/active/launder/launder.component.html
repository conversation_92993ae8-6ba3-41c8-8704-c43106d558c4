<div
  id="PrefSumDiv"
  *ngIf="
    OrderData.Preferences.Instructions !== '' ||
    OrderData.Preferences.Detergent === 'I Will Provide' ||
    OrderData.Preferences.Detergent === oredrDetergentTypes.Hypoallergenic ||
    OrderData.Preferences.Detergent ===
      oredrDetergentTypes.UnscentedHypoallergenic ||
    OrderData.Preferences.Delicates ||
    OrderData.Preferences.HangDry ||
    OrderData.Preferences.Hangers ||
    OrderData.LargeItems?.CustomerSelected
  "
>
  <h1>Special Instructions</h1>
  <p
    *ngIf="
      OrderData.Preferences.Instructions !== '' &&
      OrderData.Preferences.Instructions !== null
    "
  >
    "{{ OrderData.Preferences.Instructions }}"
  </p>

  <label *ngIf="OrderData.Preferences.Detergent === 'I Will Provide'">
    <ion-icon name="checkmark-circle"></ion-icon>Detergent Provided
  </label>
  <label
    *ngIf="
      OrderData.Preferences.Detergent === oredrDetergentTypes.Hypoallergenic ||
      OrderData.Preferences.Detergent ===
        oredrDetergentTypes.UnscentedHypoallergenic
    "
  >
    <ion-icon name="checkmark-circle"></ion-icon>Unscented Hypoallergenic
  </label>
  <label *ngIf="OrderData.Preferences.Delicates">
    <ion-icon name="checkmark-circle"></ion-icon>Delicate Items
  </label>
  <label *ngIf="OrderData.Preferences.HangDry">
    <ion-icon name="checkmark-circle"></ion-icon>Hang-Dry Items
  </label>
  <label *ngIf="OrderData.Preferences.Hangers">
    <ion-icon name="checkmark-circle"></ion-icon>Hangers Included
  </label>

  <div id="OversizedItemsNotice" *ngIf="OrderData.LargeItems?.CustomerSelected">
    <b>Oversized Items Included</b>
    <span class="how-it-works" (click)="HowOversizedItemsWorks()"
      >How this works</span
    >
  </div>
</div>
<app-step
  index="1"
  title="Pre-Weigh"
  body="{{ GetPreWeighStepBody() }}"
  [recommended]="!OrderData.SameDayService"
  (click)="ClickStepSection(1)"
  [class.Complete]="SectionLevel > 1"
  [class.Open]="OpenSection === 1"
  (NextStep)="StepComplete($event)"
>
  <div *ngIf="OrderData.SameDayService">
    <p>
      Pre-weigh and extensions are unavailable for
      {{ isExpressServiceEnabled ? 'Express' : 'Same-Day' }} orders.
    </p>
  </div>

  <div
    *ngIf="
      OrderData.OrderExtensionReasons &&
      OrderData.OrderExtensionReasons.length > 0
    "
  >
    <p>You're all set! This order has already been extended.</p>
  </div>

  <div *ngIf="DisplayAlreadyPreWeighed()">
    <p>
      You're all set! The <b>{{ OrderData.PreWeight }} lbs</b> has been
      successfully recorded.
    </p>
  </div>

  <div *ngIf="DisplaySubmitPreWeigh()">
    <p *ngIf="PreWeighStep === 1">
      When in doubt, a quick pre-weigh can save the day, especially with heavier
      loads!
    </p>
    <p *ngIf="PreWeighStep === 2"><strong>Enter your exact weight</strong></p>

    <section class="pre-weight-button-container" *ngIf="PreWeighStep === 1">
      <button
        class="skip-step-button pre-weight-button"
        id="skip-step-button"
        (click)="StepComplete(SectionLevel)"
      >
        Skip Step
      </button>
      <button
        class="pre-weigh-button pre-weight-button"
        id="pre-weight-button"
        (click)="BeginPreWeightStep()"
      >
        Pre-Weigh Laundry
      </button>
    </section>

    <input
      *ngIf="PreWeighStep === 2"
      id="MyWeightInput"
      [ngModel]="MyWeightInput"
      type="tel"
      mask="000.0"
      placeholder="0 LBS"
      (blur)="MyWeightInputEvent($event)"
    />

    <label
      *ngIf="
        PreWeighStep === 2 &&
        !this.MyWeightInput.includes('.') &&
        this.MyWeightInput !== '' &&
        MyWeightInput.length >= 3
      "
      id="DecimalPointWarning"
    >
      <ion-icon name="alert-circle-outline"></ion-icon> Decimal point required
      (ex: 155<u>.0</u>)
    </label>
    <section *ngIf="PreWeighStep === 2" class="next-button-section">
      <ion-button
        class="next-button"
        id="next-button"
        [ngClass]="{ disabled: MyWeightInput <= 0 }"
        (click)="EnterWeight()"
      >
        Next
      </ion-button>
    </section>
    <section *ngIf="PreWeighStep === 3" class="enter-pre-weights-section">
      <p>
        Enter your weight holding each bag and write its calculated weight on
        the label.
      </p>

      <div id="WeighListDiv">
        <div class="ListItem" *ngFor="let item of WeighList; let i = index">
          <label>YOU + BAG</label>
          <input
            [id]="'ListItemInput-' + i"
            [ngModel]="item"
            type="tel"
            mask="000.0"
            placeholder="000.0"
            (blur)="WeightItemsInputBlur(i, $event)"
            (focus)="WeightItemsInputFocus(i)"
            [disabled]="
              PreWeightNumber > 0 || (PreWeightSubmitted && OpenSection !== 1)
            "
          />
          <i
            *ngIf="i !== 0 && PreWeightNumber === 0 && !PreWeightSubmitted"
            class="material-icons"
            (click)="WeightItemRemove(i)"
            >cancel</i
          >
          <span>{{ CalcBagWeight(i) }} lbs</span>
          <div class="ErrorDiv" *ngIf="GetBagWeightError(i) !== ''">
            <ion-icon name="warning-outline"></ion-icon>
            {{ GetBagWeightError(i) }}
          </div>
        </div>

        <ion-button
          id="add-bag-button"
          color="warning"
          expand="full"
          (click)="AddWeightLine()"
          [disabled]="
            WeighList[WeighList.length - 2] === '' ||
            PreWeightNumber > 0 ||
            (PreWeightSubmitted && OpenSection !== 1)
          "
          >Add Bag <i class="material-icons">add</i>
        </ion-button>
      </div>
      <ion-button
        class="step-complete-button"
        (click)="CheckPreWeights(SectionLevel)"
        [disabled]="
          PreWeightNumber > 0 || (PreWeightSubmitted && OpenSection !== 1)
        "
        expand="full"
      >
        {{
          PreWeightNumber === 0 && !PreWeightSubmitted
            ? 'Step Complete'
            : 'Pre-Weight Added'
        }}<ion-icon
          *ngIf="PreWeightNumber === 0 && !PreWeightSubmitted"
          name="arrow-down-outline"
        ></ion-icon>
      </ion-button>
    </section>
  </div>
</app-step>

<app-step
  index="2"
  title="Separate Loads"
  [class.Complete]="SectionLevel > 2"
  [class.Open]="OpenSection === 2"
  (click)="ClickStepSection(2)"
  (NextStep)="StepComplete($event)"
>
  <p>
    Separate colors from whites, but do not mix bags (as each bag may belong to
    a different person).
  </p>
</app-step>

<app-step
  [disableNext]="TakePicDisabled || checkIsTrainingRequired(3, true)"
  title="Add Detergent"
  index="3"
  [class.Complete]="SectionLevel > 3"
  [class.Open]="OpenSection === 3"
  (click)="ClickStepSection(3)"
  (NextStep)="StepComplete($event)"
>
  <app-tutorial
    [required]="TrainerLockLevel <= 4 && SudsterFirstOrder === true"
    (tutorialComplete)="tutorialComplete(firstOrderQuizzes.ADD_DETERGENT)"
    quizQuestion="Which of the following will cause the laundry to NOT come out fresh and clean?"
    [quizOptions]="[
      'Overstuffed machine',
      'Use minimal detergent',
      'Use any detergents',
      'All the above'
    ]"
    [quizAnswer]="4"
    [videoLength]="videoRefs.inOrder.detergent.length"
    [videoLink]="videoRefs.inOrder.detergent.ref"
    [recommendedTraining]="recommendedTraining"
  >
  </app-tutorial>

  <p>Add the right amount so clothes<br />come out fresh & clean.</p>

  <div
    *ngIf="
      OrderData.Preferences.Detergent === oredrDetergentTypes.Hypoallergenic ||
      OrderData.Preferences.Detergent ===
        oredrDetergentTypes.UnscentedHypoallergenic
    "
    class="WarnDiv"
  >
    <h1>Unscented Hypoallergenic</h1>
    <h2>This customer requires Unscented Hypoallergenic!</h2>
    <p>
      Unscented Hypoallergenic detergent is free from dyes, perfumes, scents,
      and optical brighteners. Some customers need unscented hypoallergenic
      detergent because they have sensitive skin, allergies, or other health
      issues. So this is not something to mess around with.
    </p>
  </div>

  <div
    *ngIf="OrderData.Preferences.Detergent === 'I Will Provide'"
    class="WarnDiv"
  >
    <h1>Detergent Provided</h1>
    <p>
      The customer has included their own detergent for this order. Please only
      use the provided detergent in the right amount and return the rest.
    </p>
  </div>

  <p>Take photo of detergent:</p>

  <app-photo-upload
    (uploadStarted)="UploadStarted($event)"
    (pictureStored)="PictureStored($event)"
    [savePhotoPath]="'orderPhotos/' + OrderData.OrderNumber"
    [photoSafeUrl]="storedPhotoSafeUrl"
  >
  </app-photo-upload>
</app-step>

<app-step
  title="Wash"
  index="4"
  [class.Complete]="SectionLevel > 4"
  [class.Open]="OpenSection === 4"
  (click)="ClickStepSection(4)"
  (NextStep)="StepComplete($event)"
  [disableNext]="checkIsTrainingRequired(4)"
>
  <p>Wash in cold water only, unless otherwise instructed by customer.</p>

  <div *ngIf="OrderData.Preferences.Delicates" class="WarnDiv">
    <h1>Wash Delicates Separate</h1>
    <h2>Some items require delicate cycle.</h2>
    <p>
      This customer has included some items that must be washed on a delicate
      cycle. The customer described the delicate items in their special
      instructions above. Please message them if you need clarification.
    </p>
  </div>

  <app-tutorial
    [required]="TrainerLockLevel <= 5 && SudsterFirstOrder === true"
    (tutorialComplete)="tutorialComplete(firstOrderQuizzes.WASH)"
    quizQuestion="What's the most important key to making sure laundry is not wrinkled?"
    [quizOptions]="[
      'Smooth the laundry',
      'Fold immediately',
      'Hang the laundry',
      'All the above'
    ]"
    [quizAnswer]="2"
    [videoLength]="videoRefs.inOrder.noWrinkles.length"
    [videoLink]="videoRefs.inOrder.noWrinkles.ref"
    [recommendedTraining]="recommendedTraining"
  >
  </app-tutorial>
</app-step>

<app-step
  title="Dry"
  index="5"
  [class.Complete]="SectionLevel > 5"
  [class.Open]="OpenSection === 5"
  (click)="ClickStepSection(5)"
  (NextStep)="StepComplete($event)"
  [disableNext]="checkIsTrainingRequired(5)"
>
  <app-tutorial
    [required]="TrainerLockLevel <= 5 && SudsterFirstOrder === true"
    (tutorialComplete)="tutorialComplete(firstOrderQuizzes.DRY)"
    quizQuestion="It's OK in some circumstances to deliver clothes slightly damp."
    [quizOptions]="['True', 'False', 'Not enough info']"
    [quizAnswer]="2"
    [videoLength]="videoRefs.inOrder.dry.length"
    [videoLink]="videoRefs.inOrder.dry.ref"
    [recommendedTraining]="recommendedTraining"
  >
  </app-tutorial>

  <p>Make sure all items are completely dry.</p>

  <div *ngIf="OrderData.Preferences.HangDry === true" class="WarnDiv">
    <h1>Hang-Dry Required</h1>
    <h2>Some items require Hang-Dry.</h2>
    <p>
      This customer has included some items that must be hang-dried (and may
      never be put in the dryer). The customer has described the hang-dry items
      in their special instructions above. Please message them if you need
      clarification.
    </p>
  </div>
</app-step>

<app-step
  title="Fold"
  index="6"
  [class.Complete]="SectionLevel > 6"
  [class.Open]="OpenSection === 6"
  (click)="ClickStepSection(6)"
  (NextStep)="StepComplete($event)"
  [disableNext]="checkIsTrainingRequired(6)"
>
  <app-tutorial
    [required]="TrainerLockLevel <= 6 && SudsterFirstOrder === true"
    (tutorialComplete)="tutorialComplete(firstOrderQuizzes.FOLD)"
    quizQuestion="Hang dry items should be folded neatly and packed along with the machine dry laundry."
    [quizOptions]="['True', 'False', 'Not enough info']"
    [quizAnswer]="2"
    [videoLength]="videoRefs.inOrder.folding.length"
    [videoLink]="videoRefs.inOrder.folding.ref"
    [recommendedTraining]="recommendedTraining"
  >
  </app-tutorial>

  <p>Fold all the laundry neatly and make sure to follow these rules:</p>
  <ul style="text-align: left">
    <li>Turn clothes right side out</li>
    <li>Make a pile for each family member</li>
    <li>Stack like items together</li>
    <li>Button and zip everything</li>
    <li>Match socks</li>
    <li>Fold <i>Poplin Neat</i></li>
  </ul>

  <ion-button
    id="how-to-fold-button"
    (click)="OpenFoldingVideos($event)"
    size="small"
    expand="block"
    fill="clear"
    style="margin-bottom: 15px"
  >
    <ion-icon
      name="play-circle-outline"
      style="margin: 0px; padding-right: 5px"
    ></ion-icon
    >How to Fold&nbsp;<b>Poplin Neat</b>
  </ion-button>

  <div *ngIf="OrderData.Preferences.HangDry === true" class="WarnDiv">
    <h1>Hang Some Items</h1>
    <h2>Customer included hangers.</h2>
    <p>
      This customer requires that some items be return hung on hangers. Customer
      provided hangers for your convenience and described the items in "Special
      Instructions" (above). If you have any questions please message the
      customer.
    </p>
  </div>
</app-step>

<app-step
  title="Pack"
  index="7"
  [class.Complete]="SectionLevel > 7"
  [class.Open]="OpenSection === 7"
  (click)="ClickStepSection(7)"
  (NextStep)="StepComplete($event)"
  [disableNext]="checkIsTrainingRequired(7)"
>
  <app-tutorial
    [required]="TrainerLockLevel <= 7 && SudsterFirstOrder === true"
    (tutorialComplete)="tutorialComplete(firstOrderQuizzes.PACK)"
    quizQuestion="Drawstring bags are OK as long as they're clear."
    [quizOptions]="['True', 'False', 'Not enough info']"
    [quizAnswer]="2"
    [videoLength]="videoRefs.inOrder.pack.length"
    [videoLink]="videoRefs.inOrder.pack.ref"
    [recommendedTraining]="recommendedTraining"
  >
  </app-tutorial>

  <p>
    Pack the laundry neatly in clear-plastic colorless bags. Do not use
    drawstring bags.
  </p>

  <label
    ><i
      >Do NOT tie or seal the bags yet.<br />You may need to move items
      later.</i
    ></label
  >
</app-step>

<app-step
  title="Label"
  index="8"
  [class.Complete]="SectionLevel > 8"
  [class.Open]="OpenSection === 8"
  (click)="ClickStepSection(8)"
  (NextStep)="StepComplete($event)"
  [disableNext]="checkIsTrainingRequired(8)"
>
  <app-tutorial
    [required]="TrainerLockLevel <= 8 && SudsterFirstOrder === true"
    (tutorialComplete)="tutorialComplete(firstOrderQuizzes.LABEL)"
    quizQuestion="The laundry must be delivered neat and presented well."
    [quizOptions]="['True', 'False', 'Sometimes']"
    [quizAnswer]="1"
    [videoLength]="videoRefs.inOrder.label.length"
    [videoLink]="videoRefs.inOrder.label.ref"
    [recommendedTraining]="recommendedTraining"
  >
  </app-tutorial>

  <p>Label each bag with the customer's name and order number.</p>

  <div class="LabelExample">
    {{ OrderData.CustomerFirstName }} - #{{ orderId }}
  </div>
</app-step>

<ion-button
  [disabled]="SectionLevel <= 8 || OpenSection <= 8"
  id="laundry-complete-button"
  color="warning"
  size="large"
  expand="block"
  (click)="LaundryComplete()"
  >Laundry Complete<ion-icon name="checkmark-done"></ion-icon>
</ion-button>
