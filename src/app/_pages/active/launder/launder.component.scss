#PrefSumDiv {
  width: 100%;
  background: white;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  z-index: 2;
  position: relative;
  margin-bottom: 35px;
  border: solid 4px var(--BlueGray5);
  padding: 15px;
  overflow: hidden;

  h1 {
    margin: 0 0 15px 3px;
    text-align: left;
    font-weight: 700;
    font-size: 16px;
    line-height: 22.4px;
    font-family: 'Fakt Normal', sans-serif;
    color: var(--BlueGray5);
    margin-bottom: 15px;
  }

  p {
    color: var(--BlueGray4);
    text-align: left;
  }

  label {
    width: 50%;
    display: block;
    float: left;
    text-align: left;
    font-size: 16px;
    font-weight: 400;
    font-style: normal;
    line-height: 140%;
    color: var(--BlueGray5);
    margin-bottom: 7px;

    ion-icon {
      padding-right: 5px;
      vertical-align: -2px;
    }
  }
}

.LabelExample {
  width: auto;
  padding: 5px 20px;
  border: dashed 3px var(--Orange);
  display: inline-block;
  font-weight: 700;
  color: var(--<PERSON><PERSON>ray4);
  margin: 20px auto;
}

#laundry-complete-button {
  z-index: 2;
  position: relative;
  margin-top: 30px;
  margin-bottom: 75px;
  max-width: 400px;
  font-weight: 600;
}

.WarnDiv {
  width: 100%;
  border-top: solid 4px red;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  border-radius: 10px;

  h1 {
    font-size: 24px;
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  h2 {
    font-size: 15px;
    margin: 0;
    color: var(--BlueGray5);
  }

  p {
    font-size: 12px;
    color: var(--BlueGray4);
    padding: 10px;
    padding-top: 0px;
    text-align: justify;
    line-height: 135%;
    font-weight: 500;
  }
}

#OversizedItemsNotice {
  background: var(--BlueGray1);
  border-radius: 5px;
  padding: 10px;
  overflow: hidden;

  b {
    font-weight: 600;
    font-size: 15px;
    color: var(--BlueGray5);
    display: block;
  }

  span {
    display: block;
    text-transform: uppercase;
    font-size: 12px;
    font-weight: 600;
    color: var(--Blue);
    line-height: 150%;
    margin-top: 5px;
  }
}

#OverweightAlert {
  margin-top: 1rem;
  margin-bottom: 1rem;
  margin-left: 0;
  width: 100%;
  font-size: 1em;
  border-radius: 8px;
  z-index: 2;
  p {
    display: flex;
    align-items: center;
    text-align: left;
    margin-bottom: 0.5rem;
    span {
      margin-left: 11px;
      margin-right: 5px;
    }
    i {
      border-radius: 100%;
      background-color: #fff;
      color: rgb(255, 199, 58);
      margin-left: 12px;
    }
  }
  .Confirm {
    font-size: 15px;
    text-decoration: underline;
    text-align: right;
    display: flex;
    flex-direction: row-reverse;
    cursor: pointer;
  }
}

ion-card-content {
  padding-top: 0px;
}

#MyWeightInput {
  text-align: center;
  font-size: 30px;
  width: 120px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  border: none;
  border-top: solid 3px var(--Orange);
  border-radius: 3px;
  margin-bottom: 15px;
  color: var(--BlueGray5);
  padding: 5px 10px;
}
#AgreeDiv {
  border-bottom: solid 1px var(--BlueGray3);
  padding-bottom: 20px;
  margin-bottom: 10px;

  h1 {
    font-size: 18px;
    font-weight: 400;

    ion-icon {
      font-size: 20px;
      vertical-align: -4px;
    }
  }

  p {
    text-align: justify;
    font-size: 14px;
    line-height: 140%;
    color: var(--BlueGray5);
  }

  #Checkbox {
    font-weight: 800;
    letter-spacing: 1px;
    font-size: 14px;
    color: red;

    ion-checkbox {
      vertical-align: -3px;
      margin-right: 5px;
      --border-color: red;
      --border-color-checked: red;
      --background-checked: red;
    }
  }
}

#MyWeightInput {
  text-align: center;
  font-size: 30px;
  width: 120px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  border: none;
  border-top: solid 3px var(--Orange);
  border-radius: 3px;
  margin-bottom: 15px;
  color: var(--BlueGray5);
  padding: 5px 10px;
}

#DecimalPointWarning {
  display: block;
  margin: 15px 0px;
  border-radius: 5px;
  padding: 5px;
  color: var(--BlueGray5);
  font-weight: 600;
  border: solid 2px red;

  ion-icon {
    vertical-align: -4px;
    font-size: 20px;
    color: red;
    margin-right: 5px;
  }
}

#WeighListDiv {
  box-shadow: 0 2px 10px -1px rgba(0, 0, 0, 0.2);
  border-radius: 15px;
  margin-bottom: 20px;
  margin-top: 20px;
  overflow: hidden;

  .ListItem {
    overflow: hidden;
    position: relative;
    text-align: center;
    min-height: 40px;

    .OverlayDiv {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.8);
      text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
      color: white;
      opacity: 0;
      top: 100%;
      text-align: center;
      line-height: 40px;
      font-size: 15px;
      border-top-left-radius: 15px;
      border-top-right-radius: 15px;
      pointer-events: none;
    }

    .OverlayAnimation.OverlayDiv {
      animation: MAOWeighListItemOverlayDivAnimation;
      animation-duration: 5s;
    }

    @keyframes MAOWeighListItemOverlayDivAnimation {
      0% {
        top: 100%;
        opacity: 0;
      }

      10% {
        top: 0%;
        opacity: 1;
      }

      90% {
        top: 0%;
        opacity: 1;
      }

      100% {
        top: 100%;
        opacity: 0;
      }
    }

    label,
    span {
      font-size: 12px;
      color: rgb(180, 180, 180);
      text-transform: uppercase;
      position: absolute;
      left: 15px;
      top: 13px;
    }

    span {
      right: 35px;
      left: auto;
    }

    input {
      border: none;
      font-size: 20px;
      color: black;
      height: 100%;
      width: 100px;
      text-align: center;
      margin: 0 auto;
      display: block;
      margin-top: 5px;
    }

    i {
      height: 100%;
      font-size: 18px;
      color: rgb(220, 220, 220);
      overflow: hidden;
      position: absolute;
      right: 8px;
      top: 11px;
    }

    .ErrorDiv {
      font-size: 12px;
      color: red;
      padding: 10px;
      font-weight: 500;
      overflow: hidden;

      ion-icon {
        font-size: 20px;
        padding-right: 5px;
        vertical-align: -6px;
      }
    }
  }

  ion-button {
    margin-bottom: 0px;
    margin-top: 0px;
  }
}

.btn-action {
  z-index: 2;
  position: relative;
  margin-top: 30px;
  max-width: 400px;
}

#weight-complete-button {
  z-index: 2;
  position: relative;
  margin-top: 30px;
  margin-bottom: 75px;
  max-width: 400px;
  font-weight: 600;
}

#LargeItemsDiv {
  background: var(--BlueGray1);
  border-radius: 5px;

  #InnerDiv {
    padding: 10px;
  }

  #CounterDiv {
    border-radius: 5px;
    width: 150px;
    margin: 0 auto;
    text-align: center;
    height: 40px;
    line-height: 32px;
    margin-top: 10px;
    clear: both;
  }

  #CounterDiv ion-icon {
    font-size: 30px;
    height: 35px;
    width: 35px;
    box-shadow: 0 1px 2px rgba(0, 0, 50, 0.35);
    border-radius: 50%;
    color: var(--Orange);
    background: white;
  }

  .Disabled {
    pointer-events: none;
    color: var(--BlueGray3) !important;
    box-shadow: 0 1px 2px rgba(0, 0, 50, 0.2) !important;
  }

  label {
    font-size: 18px;
  }

  ion-note {
    font-size: 14px;
    display: block;
    text-align: center;
    font-weight: 500;
    padding-top: 10px;
  }

  ion-grid {
    padding: 0;
    padding-top: 10px;

    ion-row:first-child {
      color: var(--BlueGray4);

      b {
        font-weight: 600;
      }
    }

    ion-col {
      padding: 0;
      text-align: left;

      ion-icon {
        font-size: 18px;
        vertical-align: -3px;
      }

      .YesIcon {
        color: var(--Green);
      }

      .NoIcon {
        color: Red;
      }
    }
  }
}
.pre-weight-button-container {
  display: flex;
  flex-direction: column;
  position: relative;
  top: 12px;
  button {
    width: 100%;
    margin: 2px 0px 2px 0px;
  }
  @media (min-width: 400px) {
    flex-direction: row;
    button {
      margin: 0px 2px 0px 2px;
    }
  }
}

.pre-weigh-button {
  display: inline-flex;
  min-height: 36px;
  padding: var(--button-padding-vertical-small, 6px)
    var(--button-padding-horizontal-small, 4px);
  justify-content: center;
  align-items: center;
  border-radius: var(--button-radius-radius-square, 8px);
  background: var(--button-color-primary-main, #285652);

  /* Light/Basic Drop Shadow */
  box-shadow: 0px 4px 24px 0px rgba(0, 0, 0, 0.04);
  color: var(--button-color-primary-alt, #fff);
  text-align: center;

  /* Aux Text/Small */
  font-family: 'PitchSans-Medium', 'Helvetica Neue', Helvetica, Arial;
  font-size: 13px;
  font-style: normal;
  font-weight: 700;
  line-height: 16px; /* 123.077% */
  letter-spacing: 1.3px;
  text-transform: uppercase;
  width: 195px;
}
.skip-step-button {
  display: inline-flex;
  min-height: 36px;
  padding: var(--button-padding-vertical-small, 6px)
    var(--button-padding-horizontal-small, 4px);
  justify-content: center;
  align-items: center;
  border-radius: var(--button-radius-radius-square, 8px);
  border: 1px solid var(--button-color-primary-main, #285652);

  /* Light/Basic Drop Shadow */
  box-shadow: 0px 4px 24px 0px rgba(0, 0, 0, 0.04);
  color: var(--button-color-primary-main, #285652);
  text-align: center;

  /* Aux Text/Small */
  font-family: 'PitchSans-Medium', 'Helvetica Neue', Helvetica, Arial;
  font-size: 13px;
  font-style: normal;
  font-weight: 700;
  line-height: 16px; /* 123.077% */
  letter-spacing: 1.3px;
  text-transform: uppercase;
  background-color: #fff;
  width: 105px;
  margin-right: 25px;
}

.next-button,
.step-complete-button {
  width: 100%;
  max-width: 100%;
  position: absolute;
  left: 0px;
  right: 0px;
  --background: var(--Orange);
}
.disabled {
  pointer-events: none;
  opacity: 0.5;
}
