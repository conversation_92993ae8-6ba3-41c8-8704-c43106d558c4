import {
  ApplicationRef,
  Component,
  EventEmitter,
  inject,
  Inject,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { AngularFireFunctions } from '@angular/fire/functions';
import { SafeUrl } from '@angular/platform-browser';
import { Network } from '@capacitor/network';
import {
  AlertController,
  IonContent,
  IonicSafeString,
  LoadingController,
  PopoverController,
} from '@ionic/angular';
import { untilDestroyed } from '@ngneat/until-destroy';
import Rollbar from 'rollbar';
import { finalize } from 'rxjs';
import { FoldingVideosComponent } from 'src/app/_components/folding-videos/folding-videos.component';
import {
  PictureTakenEvent,
  UploadStartedEvent,
} from 'src/app/_components/photo-upload/photo-upload.component';
import { OrderData } from 'src/app/_interfaces/order-data.interface';
import { VIDEOREFS } from 'src/app/_interfaces/videoRefs';
import { LegacyApiService } from 'src/app/_services/legacy-api.service';
import { PhotoService } from 'src/app/_services/photo.service';
import { PointsService } from 'src/app/_services/points/points.service';
import { StatsigFactoryService } from 'src/app/_services/statsig-factory.service';
import { StatsigService } from 'src/app/_services/statsig.service';
import { ToastService } from 'src/app/_services/toast.service';
import { environment } from 'src/environments/environment';
import { RollbarService } from '../../../_services/rollbar/rollbar.service';
import { getAddress } from '../../../_utils/order-utils';
import { FirstOrderQuizzes } from '../../academy/academy.page';
import { Detergent } from '../../home/<USER>/adapters/active-order.adapter';

@Component({
  selector: 'app-launder',
  templateUrl: './launder.component.html',
  styleUrls: ['./launder.component.scss'],
})
export class LaunderComponent implements OnInit {
  @Input() OrderData: OrderData;
  @Input() editMode: boolean = false;
  @Output() PreWeight: EventEmitter<number> = new EventEmitter();

  OpenSection = 1;
  SectionLevel = 1;
  TrainerLockLevel = 1;
  StepButtonClicked = false;

  TakePicDisabled = true;
  PhotoGPS;

  SudsterFirstOrder = false;
  videoRefs = VIDEOREFS;
  connected: boolean = true;
  MyWeightInput: string = '';
  PreWeighStep: number = 1;
  WeightEntered: boolean = false;
  PreWeightNumber: number = 0;
  PreWeightSubmitted: boolean = false;
  isExpressServiceEnabled = false;
  WeighList = [''];
  orderId = '';

  uploadingPhotoId;
  storedPhotoPath: string = '';
  storedPhotoSafeUrl: SafeUrl = '';
  firstOrderQuizzes = FirstOrderQuizzes;
  recommendedTraining = true;
  oredrDetergentTypes = Detergent;

  private statsigService: StatsigService;
  private readonly _pointsService = inject(PointsService);

  constructor(
    private loadingController: LoadingController,
    private alertController: AlertController,
    private cloudFunctions: AngularFireFunctions,
    private appRef: ApplicationRef,
    public popoverController: PopoverController,
    private toastService: ToastService,
    private photoService: PhotoService,
    private content: IonContent,
    private apiService: LegacyApiService,
    private statsigFactoryService: StatsigFactoryService,
    @Inject(RollbarService) private rollbar: Rollbar
  ) {
    this.statsigService = this.statsigFactoryService.getInstance();
  }

  async ngOnInit() {
    this.statsigService
      .checkGate(environment.statsig.flags.ExpressService)
      .subscribe((expressservice) => {
        this.isExpressServiceEnabled = expressservice;
      });

    this.statsigService
      .checkGate(environment.statsig.flags.lpFirstOrderRecommendedTraining)
      .subscribe((showTraining) => {
        this.recommendedTraining = showTraining;
      });

    // If same day order, skip to second step:
    if (this.OrderData.SameDayService) {
      this.OpenSection = 2;
    }

    if (this.OrderData.SudsterFirstOrder == true) {
      this.SudsterFirstOrder = true;
    }
    if (this.OrderData.PreWeight && this.OrderData.PreWeight > 0) {
      this.PreWeightSubmitted = true;
      this.PreWeight.emit(this.OrderData.PreWeight);
    }

    const storedPhotoSafeUrl = localStorage.getItem(
      this.OrderData.OrderNumber + '-DetergentPictureUrl'
    );
    const storedPhotoPath = localStorage.getItem(
      this.OrderData.OrderNumber + '-DetergentPhotoLocalPath'
    );
    const storedPhotoId = localStorage.getItem(
      this.OrderData.OrderNumber + '-DetergentPhotoId'
    );

    if (
      storedPhotoSafeUrl != null &&
      storedPhotoPath != null &&
      storedPhotoId !== null
    ) {
      this.storedPhotoSafeUrl = JSON.parse(storedPhotoSafeUrl);
      this.storedPhotoPath = JSON.parse(storedPhotoPath);
      this.uploadingPhotoId = JSON.parse(storedPhotoId);

      const storedPhoto = await this.photoService.loadSavedPhoto(
        this.storedPhotoPath
      );

      if (storedPhoto && storedPhoto?.size > 0) {
        this.TakePicDisabled = false;

        const storedPhotoGPS = localStorage.getItem(
          this.OrderData.OrderNumber + '-PhotoGPS'
        );
        if (storedPhotoGPS != '[object Object]') {
          this.PhotoGPS = JSON.parse(storedPhotoGPS);
        }

        const StepNumberStorage = parseInt(
          localStorage.getItem(this.OrderData.OrderNumber + '-StepNumber')
        );
        if (StepNumberStorage > 0) {
          this.OpenSection = StepNumberStorage;
          this.SectionLevel = StepNumberStorage;
          this.TrainerLockLevel = StepNumberStorage;
        }
      } else {
        this.storedPhotoSafeUrl = '';
        this.storedPhotoPath = '';
        this.uploadingPhotoId = null;
        this.SectionLevel = 3;
        this.OpenSection = 3;
        this.TakePicDisabled = true;
      }
    }

    const BagPreWeightsStorage = localStorage.getItem(
      this.OrderData.OrderNumber + '-BagPreWeights'
    );
    if (BagPreWeightsStorage != null) {
      this.WeighList = JSON.parse(BagPreWeightsStorage);
    }

    this.orderId = this.OrderData.OrderId
      ? this.OrderData.OrderId
      : this.OrderData.OrderNumber;
  }

  ClickStepSection(index) {
    if (!this.StepButtonClicked) {
      if (index <= this.SectionLevel) {
        this.OpenSection = index;
      }
    }
    this.StepButtonClicked = false;
  }

  StepComplete(index) {
    if (index == this.SectionLevel) {
      this.SectionLevel++;
      this.OpenSection++;
      this.TrainerLockLevel = this.SectionLevel;
    } else {
      this.OpenSection = this.SectionLevel;
    }
    this.StepButtonClicked = true;
    localStorage.setItem(
      this.OrderData.OrderNumber + '-StepNumber',
      this.SectionLevel.toString()
    );
  }

  UploadStarted(event: UploadStartedEvent) {
    if (!this.uploadingPhotoId) {
      this.uploadingPhotoId = event.photoFileId;

      localStorage.setItem(
        this.OrderData.OrderNumber + '-DetergentPhotoId',
        JSON.stringify(this.uploadingPhotoId)
      );
    }
  }

  PictureStored(event: PictureTakenEvent) {
    this.TakePicDisabled = false;
    this.PhotoGPS = event.photoLocation;
    this.storedPhotoPath = event.photoLocalPath;
    this.storedPhotoSafeUrl = event.photoLocalUrl;
    this.uploadingPhotoId = null;

    localStorage.setItem(
      this.OrderData.OrderNumber + '-DetergentPhotoLocalPath',
      JSON.stringify(this.storedPhotoPath)
    );
    localStorage.setItem(
      this.OrderData.OrderNumber + '-DetergentPictureUrl',
      JSON.stringify(this.storedPhotoSafeUrl)
    );
    localStorage.setItem(
      this.OrderData.OrderNumber + '-PhotoGPS',
      JSON.stringify(this.PhotoGPS)
    );
    this.appRef.tick();
  }

  async LaundryComplete() {
    const loading = this.presentLoading();

    const submitLaunderStep = () => {
      localStorage.removeItem(this.OrderData.OrderNumber + '-StepNumber');

      this.launderCompleteRequest()
        .toPromise()
        .then(async (res) => {
          localStorage.removeItem(
            this.OrderData.OrderNumber + '-DetergentPictureUrl'
          );
          localStorage.removeItem(this.OrderData.OrderNumber + '-PhotoGPS');
          localStorage.removeItem(
            this.OrderData.OrderNumber + '-DetergentPhotoId'
          );
          loading.then(function (ld) {
            ld.dismiss();
          });
          await this.photoService.removeSavedPhoto(
            this.storedPhotoPath,
            this.uploadingPhotoId
          );
        })
        .catch((err) => {
          localStorage.setItem(
            this.OrderData.OrderNumber + '-StepNumber',
            this.OpenSection.toString()
          );
          loading.then(function (ld) {
            ld.dismiss();
          });
          if (this.connected) {
            this.presentAlert(
              'System Error',
              err?.message || 'An error occurred while processing your request.'
            );
          }
        });
    };

    await Network.getStatus()
      .then(async (status) => {
        if (!status.connected) {
          loading.then((ld) => ld.dismiss());
          const message = `A connection glitch interrupted your request. Keep the app open, find a stronger signal, and try again.<br/> Points are not impacted if the first attempt was before the deadline.`;
          this.connected = false;
          this.toastService.errorToast(
            'Lost Connection',
            message.replace('<br/>', '<br/><br/>')
          );
          return;
        } else {
          setTimeout(() => {
            loading.then((ld) => {
              ld.message =
                'Taking too long? After 10 seconds, try disabling Wi-Fi.';
            });
          }, 3000);

          // If the photo was not uploaded, start the upload
          if (!this.uploadingPhotoId) {
            await this.ResumePhotoUpload();
          } else if (
            this.photoService.photoHasAlreadyBeenUploaded(this.uploadingPhotoId)
          ) {
            submitLaunderStep();
            return;
          }

          if (this.uploadingPhotoId) {
            const photoUploadState =
              this.photoService.photoUploadState$.subscribe(
                async (photoState) => {
                  if (!photoState) {
                    try {
                      throw new Error('photoState is null');
                    } catch (e) {
                      this.rollbar.error(e);
                    }

                    loading.then((ld) => ld.dismiss());
                    this.presentAlert(
                      'Upload Error',
                      'An error occurred while uploading the photo. Please try again.'
                    );

                    this.RetakePhoto();

                    return;
                  }

                  const { photoFileId, uploadState } = photoState;

                  if (photoFileId !== this.uploadingPhotoId) return;

                  if (uploadState === 'complete') {
                    submitLaunderStep();
                    photoUploadState.unsubscribe();
                  }

                  if (uploadState === 'error') {
                    loading.then((ld) => ld.dismiss());
                    this.presentAlert(
                      'Upload Error',
                      'An error occurred while uploading the photo. Please try again.'
                    );
                    photoUploadState.unsubscribe();

                    this.RetakePhoto();
                  }
                }
              );
          }
        }
      })
      .catch((err) => {
        loading.then((ld) => ld.dismiss());
        return err;
      });
  }

  async presentLoading() {
    const loading = await this.loadingController.create({});
    await loading.present();
    return loading;
  }

  async presentAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK'],
    });
    await this.setAlertMessageInnerHtml(alert, message);
    await alert.present();
  }

  private async setAlertMessageInnerHtml(
    alert: HTMLIonAlertElement,
    message: string | IonicSafeString
  ) {
    const messageElement = alert.querySelector('.alert-message');
    if (messageElement) {
      if (typeof message !== 'string') {
        message = message.toString();
      }
      messageElement.innerHTML = message;
    }
  }

  GetStreetAddress() {
    return getAddress(this.OrderData.Address);
  }

  tutorialComplete(quizNumber): void {
    if (this.recommendedTraining && this.SudsterFirstOrder) {
      this._pointsService
        .addFirstOrderPoints(this.OrderData.OrderNumber, quizNumber)
        .pipe(
          finalize(() => {
            this.incrementTrainingLevel();
          }),
          untilDestroyed(this)
        )
        .subscribe(() => {});
    } else {
      this.incrementTrainingLevel();
    }
  }

  private incrementTrainingLevel(): void {
    this.TrainerLockLevel++;
    this.appRef.tick();
  }

  async OpenFoldingVideos(ev: any) {
    const popover = await this.popoverController.create({
      component: FoldingVideosComponent,
      event: ev,
      translucent: true,
    });
    return await popover.present();
  }

  HowOversizedItemsWorks() {
    this.presentAlert(
      'Oversized Items',
      "Some orders may include oversized items, and since they require extra resources to wash/dry you'll get paid extra per item (you'll be prompted later to report these items).<br><br>For any items that don't fit in a standard residential machine, we recommend you either go to a laundromat or return them unwashed and unweighed. You may want to let the customer know in that case."
    );
  }
  MyWeightInputEvent(event) {
    const MyWeightNumber = parseFloat(event.srcElement.value);
    if (MyWeightNumber < 50) {
      this.presentAlert(
        'Enter YOUR Weight',
        "It looks like you're trying to enter the bag's weight. Please first just enter YOUR body weight and the bag's weight will be calculated later."
      );
    } else {
      this.MyWeightInput = event.srcElement.value.toString();
      localStorage.setItem(
        this.OrderData.OrderNumber + '-MyPreWeight',
        this.MyWeightInput
      );

      if (this.editMode) {
        // uses the info from the OrderData...Done Obj
        this.WeighList = this.computePreviousBagWeights(this.MyWeightInput);
      }
    }
  }
  private computePreviousBagWeights(myWeight: string) {
    return this.OrderData.StatusHistoryInfo.Done.WeightArray.split(',').map(
      (w) => (parseFloat(w) + parseFloat(myWeight)).toFixed(1)
    );
  }
  BeginPreWeightStep(): number {
    this.PreWeighStep = 2;
    return this.PreWeighStep;
  }
  EnterWeight(): number {
    if (parseInt(this.MyWeightInput) > 0) {
      this.PreWeighStep = 3;
    }
    return this.PreWeighStep;
  }
  AddWeightLine() {
    this.WeighList.push('');
    setTimeout(() => {
      document
        .getElementById('ListItemInput-' + (this.WeighList.length - 1))!
        .focus();
    }, 300);
  }
  WeightItemRemove(index) {
    this.WeighList.splice(index, 1);
  }
  CalcBagWeight(index) {
    if (this.WeighList[index] == '') {
      return '00.0';
    } else {
      return (
        parseFloat(this.WeighList[index]) - parseFloat(this.MyWeightInput)
      ).toFixed(1);
    }
  }
  GetBagWeightError(index) {
    const BagWeight = parseFloat(this.CalcBagWeight(index));
    if (BagWeight < 1) {
      return '1 lb minimum per bag. Please try again.';
    } else if (parseInt(this.WeighList[index]) < 35) {
      return "Please weigh yourself while holding the bag. It looks like you just entered the bag's weight.";
    } else if (BagWeight < 1 && BagWeight > 0) {
      return 'Bags must be a minimum of 1 LBS. Please add some items from another bag and reweigh.';
    } else if (BagWeight < 0) {
      return 'You + bag weight is less than just your weight. Please update your body weight and reweigh.';
    } else if (
      !this.WeighList[index].includes('.') &&
      this.WeighList[index] != '' &&
      this.WeighList[index].length >= 3
    ) {
      return 'Decimal point required. Please weigh precisely and add a decimal point (ex: 150.0).';
    } else {
      return '';
    }
  }
  WeightItemsInputFocus(index) {
    this.content.scrollToPoint(0, 130 + index * 50, 200);
  }
  WeightItemsInputBlur(index, event) {
    this.WeighList[index] = event.srcElement.value;
    localStorage.setItem(
      this.OrderData.OrderNumber + '-BagPreWeights',
      JSON.stringify(this.WeighList)
    );
  }
  async CheckPreWeights(index: number) {
    // Once this button is clicked, we assume the LP is submitting
    this.PreWeightSubmitted = true;
    const loading = await this.presentLoading();
    for (let i = 0; i < this.WeighList.length; i++) {
      this.PreWeightNumber += parseFloat(this.CalcBagWeight(i));
    }
    return await this.apiService
      .post('UpdateOrder/v1/minor-update', {
        type: 'PreweightAdded',
        orderNumber: this.OrderData.OrderNumber,
        orderPartial: { PreWeight: this.PreWeightNumber },
      })
      .toPromise()
      .then(() => {
        loading.dismiss();
        this.StepComplete(index);
        return this.PreWeight.emit(this.PreWeightNumber);
      })
      .catch((err) => {
        loading.dismiss();
        return err;
      });
  }

  DisplaySubmitPreWeigh() {
    if (this.OrderData.SameDayService) {
      return false;
    } else {
      if (
        this.PreWeightNumber > 100 ||
        this.PreWeightSubmitted ||
        (this.OrderData.OrderExtensionReasons &&
          this.OrderData.OrderExtensionReasons.length > 0)
      ) {
        return false;
      } else {
        return true;
      }
    }
  }

  DisplayAlreadyPreWeighed() {
    if (
      this.OrderData.OrderExtensionReasons &&
      this.OrderData.OrderExtensionReasons.length > 0
    ) {
      return false;
    } else if (this.OrderData.SameDayService) {
      return false;
    } else {
      if (this.PreWeightNumber > 100 || this.PreWeightSubmitted) {
        return true;
      } else {
        return false;
      }
    }
  }

  GetPreWeighStepBody() {
    return (
      this.PreWeightSubmitted ||
      this.PreWeightNumber > 100 ||
      this.OrderData.SameDayService ||
      (this.OrderData.OrderExtensionReasons &&
        this.OrderData.OrderExtensionReasons.length > 0)
    );
  }

  async ResumePhotoUpload() {
    if (!this.storedPhotoPath) {
      this.presentAlert('Error', 'Missing Photo');
      return;
    }

    const { fileId } = await this.photoService.uploadPhoto(
      this.storedPhotoPath
    );

    this.uploadingPhotoId = fileId;
  }

  async RetakePhoto() {
    if (this.storedPhotoPath) {
      this.photoService.removeSavedPhoto(
        this.storedPhotoPath,
        this.uploadingPhotoId
      );
    }

    // Take the user to take the photo again
    this.storedPhotoSafeUrl = '';
    this.storedPhotoPath = '';
    this.uploadingPhotoId = null;
    this.SectionLevel = 3;
    this.OpenSection = 3;
    this.TakePicDisabled = true;

    localStorage.removeItem(
      this.OrderData.OrderNumber + '-DetergentPhotoLocalPath'
    );
    localStorage.removeItem(
      this.OrderData.OrderNumber + '-DetergentPictureUrl'
    );
    localStorage.removeItem(this.OrderData.OrderNumber + '-PhotoGPS');
    localStorage.removeItem(this.OrderData.OrderNumber + '-DetergentPhotoId');
  }

  checkIsTrainingRequired(currentStep: number, requiresPhoto = false): boolean {
    if (this.recommendedTraining) {
      return requiresPhoto ? this.TakePicDisabled : false;
    }

    return this.TrainerLockLevel <= currentStep && this.SudsterFirstOrder;
  }

  launderCompleteRequest() {
    const isNewOrderFlow = this.OrderData.IsNewOrderFlow;

    if (isNewOrderFlow) {
      return this.apiService.put(
        `/${this.OrderData.OrderNumber}/status/launder`,
        {
          detergentPhoto: this.uploadingPhotoId,
          detergentGPS: this.PhotoGPS,
        },
        {
          baseUrl: `${environment.apiPathV2}/orders`,
        }
      );
    }

    return this.cloudFunctions.httpsCallable('SudsterV3_LaundryComplete')({
      OrderNumber: this.OrderData.OrderNumber,
      DetergentPhoto: this.uploadingPhotoId,
      DetergentGPS: this.PhotoGPS,
    });
  }
}
