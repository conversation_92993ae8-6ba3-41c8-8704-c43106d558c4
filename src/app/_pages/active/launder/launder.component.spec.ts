import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { IonicModule } from '@ionic/angular';

import { LaunderComponent } from './launder.component';

describe('LaunderComponent', () => {
  let component: LaunderComponent;
  let fixture: ComponentFixture<LaunderComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [LaunderComponent],
      imports: [IonicModule.forRoot()],
    }).compileComponents();

    fixture = TestBed.createComponent(LaunderComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  }));

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
