<i id="CloseExtensionModal" class="material-icons" (click)="close(false)">
  cancel
</i>

<ion-content class="ion-padding">
  <div class="main">
    <span class="header" *ngIf="!isExtended">
      {{ title | uppercase }}
    </span>

    <h2>{{ subtitle }}</h2>
    <p>{{ body }}</p>
    <p *ngIf="isError">{{ errorMessage }}</p>

    <div class="buttons">
      <ion-button
        *ngIf="!isExtended && !isError"
        id="RequestExtensionButton"
        class="button-main"
        color="secondary"
        expand="block"
        [disabled]="disableExtensions()"
        (click)="confirm(!isExtended && !isError)"
      >
        REQUEST EXTENSION
      </ion-button>
      <div
        class="preweight-notice"
        *ngIf="!isExtended && (!PreWeightNumber || PreWeightNumber < 100)"
      >
        <ion-row *ngIf="disableExtensionPreWeight()">
          <ion-col size="1">
            <span><i class="material-icons error-icon">error</i></span>
          </ion-col>
          <ion-col size="11">
            <span class="pre-weigh-info"
              >First complete the <b>Pre-Weigh Step</b></span
            >
            <span
              class="pre-weigh-info"
              *ngIf="PreWeightNumber > 0 && PreWeightNumber < 100"
              >Order is less than 100 lbs and cannot be extended</span
            >
          </ion-col>
        </ion-row>
      </div>
      <div class="deadline-notice" *ngIf="now > requestDeadline && !isExtended">
        <p>Order no longer eligible for extension</p>
      </div>
      <div class="deadline-notice" *ngIf="now < requestDeadline && !isExtended">
        <p>Extension Deadline: <strong>Today by 9:00pm</strong></p>
      </div>
    </div>
  </div>
</ion-content>
