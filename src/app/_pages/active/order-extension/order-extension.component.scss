#footer {
  color: black;
  margin-left: 10%;
  margin-bottom: 1vh;
}

ion-content {
  .main {
    width: 85%;
    margin-left: 8%;

    h2 {
      white-space: pre-line;
    }
  }
}

.overweight {
  ion-checkbox {
    --size: 24px;
  }
  ion-checkbox::part(container) {
    border-radius: 0;
    border: 2px solid var(--ion-color-secondary);
  }
  .label {
    padding-left: 9px;
    font-size: 14px;
    font-weight: 700;
    line-height: 140%;
    padding-top: 3px;
    position: absolute;
  }
}

.header {
  font-family: 'PitchSans-Medium', Helvetica, sans-serif;
  font-weight: 700;
  line-height: 16px;
  letter-spacing: 1.3px;
  font-size: 13px;
}

i {
  position: fixed;
  top: 7px;
  right: 7px;
  font-size: 36px;
  z-index: 5;
}

.button-main {
  margin-top: 30px;
  margin-bottom: 12px;
}

.instructions {
  color: var(--themes-light-content-low-contrast, #909090);
  text-align: center;
  font-size: 14px;
  line-height: 140%;
  margin-top: 25px;
  span {
    font-weight: 700;
  }
  a {
    text-decoration: underline;
  }
}

ion-button {
  font-family: 'PitchSans-Medium', Helvetica, sans-serif;
  font-size: 13px;
  font-weight: 700;
}
.pre-weight-notice {
  text-align: center;
  font-size: 13px;
  font-weight: 400;
  line-height: 22px;

  span {
    vertical-align: middle;
  }
}
.error-icon {
  position: relative;
  color: red;
  font-size: 22px;
}
.deadline-notice {
  width: 100%;
  border-radius: var(--chip-padding-vertical-small, 0px)
    var(--chip-padding-vertical-small, 0px) 6px 6px;
  background: var(--form-fields-background-disabled, #e6e6e6);
  position: absolute;
  top: 392px;
  right: 1px;
  width: 100%;
  p {
    color: var(--form-fields-content-message-help, #4b4b4b);
    text-align: center;

    /* Body Text/Small */
    font-family: 'Fakt-Normal';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 142.857% */
  }
}
.pre-weigh-info {
  position: relative;
  top: 6px;
}
