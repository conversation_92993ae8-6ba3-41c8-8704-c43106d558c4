import { Component, Input, OnInit } from '@angular/core';
import { AngularFireFunctions } from '@angular/fire/functions';
import { Loading<PERSON>ontroller, ModalController } from '@ionic/angular';
import moment from 'moment';
import { ExtensionReasons } from 'src/app/_interfaces/order-data.interface';
import { AnalyticsLogService } from 'src/app/_services/analytics/analytics-log.service';

@Component({
  selector: 'app-order-extension',
  templateUrl: './order-extension.component.html',
  styleUrls: ['./order-extension.component.scss'],
})
export class OrderExtensionComponent implements OnInit {
  @Input() sudsterId: string;
  @Input() orderNumber: string;
  @Input() reason: ExtensionReasons;
  @Input() isExtended: boolean;
  @Input() customerName: string;
  @Input() deadline: number;
  @Input() PreWeightNumber: number = 0;
  @Input() requestDeadline: number;
  AgreeWeight = false;
  isError = false;

  errorMessage: string;
  title: string;
  subtitle: string;
  body: string;
  deadlineString: string;
  now: number = new Date().valueOf();

  constructor(
    private cloudFunctions: AngularFireFunctions,
    private loadingController: LoadingController,
    private modalController: ModalController,
    private analyticsLogService: AnalyticsLogService
  ) {}

  ngOnInit(): void {
    if (this.deadline && this.isExtended) {
      this.setDeadline(this.deadline);
    }
    this.setCopyValues();
    if (this.isExtended || this.isError) {
      this.analyticsLogService.logOrderExtension(
        this.sudsterId,
        this.orderNumber,
        this.reason,
        'Extension Request Success Modal Opened'
      );
    } else {
      this.analyticsLogService.logOrderExtension(
        this.sudsterId,
        this.orderNumber,
        this.reason,
        'Extension Request Modal Opened'
      );
    }
  }

  confirm() {
    const loading = this.presentLoading();
    this.cloudFunctions
      .httpsCallable('ManualOrderExtension')({
        OrderId: this.orderNumber,
        SudsterId: this.sudsterId,
        Reason: this.reason,
        PreWeight: this.PreWeightNumber,
      })
      .toPromise()
      .then((res) => {
        this.setDeadline(res);
        this.isExtended = true;
        this.setCopyValues();
        this.analyticsLogService.logOrderExtension(
          this.sudsterId,
          this.orderNumber,
          this.reason,
          'Extension requested'
        );
        loading.then(function (ld) {
          ld.dismiss();
        });
      })
      .catch((err) => {
        this.isError = true;
        this.errorMessage = err;
        this.setCopyValues();
        loading.then(function (ld) {
          ld.dismiss();
        });
      });
  }

  async presentLoading() {
    const loading = await this.loadingController.create({});
    await loading.present();
    return loading;
  }

  close(wasDeclined: boolean) {
    this.analyticsLogService.logOrderExtension(
      this.sudsterId,
      this.orderNumber,
      this.reason,
      wasDeclined
        ? 'Extension Request Declined'
        : 'Extension Request Modal Closed'
    );
    this.modalController.dismiss(true);
  }

  setCopyValues() {
    if (this.isError) {
      this.title = 'error';
      this.subtitle = 'Unable to Extend the Delivery Deadline';
      this.body =
        'Apologies, something went wrong on our end. Please contact the Concierge Team.';
    } else if (this.isExtended) {
      this.subtitle = 'Success!';
      this.body = `We notified ${this.customerName} about the new delivery date: ${this.deadlineString}`;
    } else if (this.reason === ExtensionReasons.Overweight) {
      this.title = 'optional';
      this.subtitle = 'Need an extension for a large order?';
      this.body = `We understand that perfection can’t be rushed. For pre-weighed orders over 100 pounds, we invite you to request a 24-hour extension.`;
    } else {
      this.title = 'optional';
      this.subtitle = 'Do you need an extension?';
      this.body = `We noticed a few extra bags of laundry at pickup — quite a surprise! We can grant you a 24-hour extension if it will help you succeed with this order.`;
    }
  }

  setDeadline(time: number) {
    const days = [
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ];

    const tz = moment(time);
    const Day = days[tz.day()];
    const Time = tz.format('LT');
    this.deadlineString = `${Time} ${Day}`;
  }

  disableExtensions() {
    if (
      this.PreWeightNumber < 100 &&
      this.reason === ExtensionReasons.Overweight
    ) {
      return true;
    } else if (
      this.now < this.requestDeadline &&
      this.isExtended === undefined
    ) {
      return false;
    }

    return true;
  }

  disableExtensionPreWeight() {
    if (
      this.PreWeightNumber < 100 &&
      this.reason === ExtensionReasons.Overweight
    ) {
      return true;
    } else if (this.PreWeightNumber < 100 && this.isExtended === undefined) {
      return false;
    }

    return true;
  }
}
