<ion-header *ngIf="DoneLoadingPage">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button
        id="back-button"
        [routerLink]="['/home']"
        text=""
        icon="arrow-back"
        color="black"
        class="display-block"
      ></ion-back-button>
    </ion-buttons>
    <ion-title
      ><span class="title-customer-name"
        >{{OrderData.CustomerFirstName}} #{{orderId}}</span
      ></ion-title
    >
    <div
      [class.highlight]="OrderData.SudsterMessageBadge > 0"
      id="MessageIcon"
      (click)="this.OpenMessages()"
    >
      <span *ngIf="OrderData.SudsterMessageBadge > 0"
        >{{OrderData.SudsterMessageBadge}}</span
      >
      <i class="material-icons"> question_answer </i>
    </div>
  </ion-toolbar>
</ion-header>

<ion-content #ScrollDiv *ngIf="DoneLoadingPage">
  <div id="InnerDiv">
    <div id="HeaderSection">
      <label
        *ngIf="!SameDayService"
        id="DeadlineLabel"
        [class.PastDue]="GetDeadline().includes('Past Due')"
        >{{StageNumber === 1 ? 'Pickup' : 'Delivery'}} Deadline:
        <b>{{GetDeadline()}}</b></label
      >

      <span
        *ngIf="(isOverweightOrder || isBagDiscrepancy || PreWeightNumber >= 100) && StageNumber === 2"
        class="order-extension"
      >
        <i
          id="OrderExtensionButton"
          (click)="openExtensionModal()"
          class="material-icons order-extension-button"
        >
          edit
        </i>
        <div
          class="tooltip"
          *ngIf="!isExtended && showedExtensionTooltip && !tooltipDismissed"
        >
          <div>Need more time?</div>
          <p>
            Request an extension before <br />
            9:00pm today.
          </p>
          <a id="TooltipDismiss" (click)="dismissTooltip()"> Got it, thanks</a>
        </div>
      </span>

      <div *ngIf="SameDayService" id="SameDayServiceDiv">
        <p>Pickup Deadline: <strong>{{SameDayPickup}}</strong></p>
        <p>Drop-off Deadline: <strong>{{SameDayDelivery}}</strong></p>
        <div class="same-day-service-pill" *ngIf="SameDayService">
          {{ isExpressServiceEnabled ? 'EXPRESS' : 'SAME-DAY' }}
          <span class="same-day-icon">$$</span>
        </div>
      </div>

      <label
        id="DeadlineSuggestion"
        *ngIf="GetDeadlineSuggestion() !== '' && StageNumber === 1"
        ><i class="material-icons">trending_up</i
        >{{GetDeadlineSuggestion()}}</label
      >

      <div id="BreadcrumbDiv">
        <span></span>
        <i
          class="material-icons"
          [class.BreadcrumbProgress]="StageNumber === 1"
          [class.BreadcrumbDone]="StageNumber > 1"
          >explore</i
        >
        <i
          class="material-icons"
          [class.BreadcrumbProgress]="StageNumber === 2"
          [class.BreadcrumbDone]="StageNumber > 2"
          >local_laundry_service</i
        >
        <i
          class="material-icons"
          *ngIf="StageNumber !==  4"
          [class.BreadcrumbProgress]="StageNumber === 3"
          [class.BreadcrumbDone]="StageNumber > 3"
          >exposure</i
        >
        <div
          *ngIf="StageNumber === 4"
          class="edit-weight"
          (click)="disableEdit || editWeight()"
        >
          <i
            class="material-icons BreadcrumbDone"
            [class.BreadcrumbProgress]="editWeightMode"
            >exposure</i
          >
          <span
            *ngIf="!OrderData?.SudsterWeightChanged"
            class="edit-label"
            [class.step-active]="editWeightMode"
            >Edit</span
          >
        </div>
        <i
          class="material-icons"
          [class.BreadcrumbProgress]="(StageNumber === 4 && !editWeightMode) "
          [class.BreadcrumbDone]="StageNumber > 4 && editWeightMode"
          (click)="GoBackToDelivery()"
          >drive_eta</i
        >

        <div class="double-points-pill" *ngIf="OrderData.BonusPointsMultiplier">
          <span class="same-day-icon">2X</span>
          Points
        </div>
      </div>
    </div>

    <span id="SectionVertLine" *ngIf="OrderData && !disableEdit"></span>

    <app-pickup
      [OrderData]="OrderData"
      [orderNumber]="OrderNumber"
      [isOverweightOrder]="isOverweightOrder"
      [isBagDiscrepancy]="isBagDiscrepancy"
      *ngIf="StageNumber === 1"
    ></app-pickup>
    <app-launder
      [OrderData]="OrderData"
      *ngIf="StageNumber === 2"
      [editMode]="editWeightMode"
      (PreWeight)="CheckPreWeights($event)"
    ></app-launder>
    <app-weigh
      [OrderData]="OrderData"
      *ngIf="StageNumber === 3 || editWeightMode"
      [editMode]="editWeightMode || isWeighModeLockedIn"
      (editFinished)="onEditFinished($event)"
    >
    </app-weigh>
    <app-deliver
      [editFinished]="editFinished"
      [OrderData]="OrderData"
      (disableEdit)="disableEdit = $event"
      *ngIf="StageNumber === 4 && !editWeightMode"
    ></app-deliver>
  </div>
</ion-content>
