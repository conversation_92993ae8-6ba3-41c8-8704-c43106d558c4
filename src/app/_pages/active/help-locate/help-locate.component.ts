import { Component, Input, OnInit, ViewChild } from '@angular/core';
import {
  AlertController,
  IonicSafeString,
  LoadingController,
  ModalController,
} from '@ionic/angular';
import { MaskCallService } from 'src/app/_services/mask-call.service';
import { SwiperComponent } from 'swiper/angular';
import { MessagesComponent } from '../messages/messages.component';
import { HelpLocateService } from './help-locate.service';
import { AngularFireFunctions } from '@angular/fire/functions';

@Component({
  selector: 'app-help-locate',
  templateUrl: './help-locate.component.html',
  styleUrls: ['./help-locate.component.scss'],
})
export class HelpLocateComponent implements OnInit {
  disabledCounter = 10;

  @Input() OrderNumber: string;
  @Input() SudsterName: string;
  @Input() CustomerFirstName: string;
  @Input() StreetAddress: string;
  @Input() AddressLine2: string;
  @Input() isNewOrderFlow = false;
  @Input() PickupSpot = { SimpleSpot: '', Instructions: '' };

  slides = [];

  @ViewChild('slider', { static: false }) swiperComponent:
    | SwiperComponent
    | undefined;

  slideOpts = {
    initialSlide: parseInt(localStorage.getItem('HelpPickup') || '0'),
    speed: 400,
    autoHeight: true,
  };

  constructor(
    private modalCtrl: ModalController,
    private alertController: AlertController,
    private loadingController: LoadingController,
    private MaskCall: MaskCallService,
    private service: HelpLocateService,
    private cloudFunctions: AngularFireFunctions
  ) {}

  ngOnInit() {
    setInterval(() => {
      if (this.disabledCounter > 0) {
        this.disabledCounter--;
      }
    }, 1000);

    this.slides = [
      {
        title: 'Check Address',
        body:
          "Double check to make sure you're at <b>" +
          this.StreetAddress +
          '</b>.',
      },
      {
        title: 'Check Pickup Spot',
        body:
          'Make sure you are at the right pickup spot and have followed all customer instructions. <br><br>Unit Number: <b>' +
          this.AddressLine2 +
          '</b><br>Pickup Spot: <b>' +
          this.PickupSpot.SimpleSpot +
          '</b><br>Instructions: <b>' +
          this.PickupSpot.Instructions +
          '</b>',
      },
      {
        title: 'Message Customer',
        body: 'We recommend messaging the customer (twice if needed) to ask about their laundry whereabouts and <strong>waiting at least 5 minutes for a response before continuing.</strong>',
      },
      {
        title: 'Knock',
        body: "You might consider knocking on the customer's door.",
      },
      {
        title: 'Call Customer',
        body: "You can call the customer's cell phone using our call masking service (your caller ID will be hidden).",
      },
      {
        title: 'Come back later',
        body: "If you still don't have the laundry, we recommend messaging your customer to ask when you should come back for the pickup.",
      },
    ];
  }

  dismissModal() {
    this.modalCtrl.dismiss({
      dismissed: true,
    });
  }

  NextSlide() {
    if (this.swiperComponent && this.swiperComponent.swiperRef) {
      const swiperInstance = this.swiperComponent.swiperRef;

      if (swiperInstance) {
        const activeIndex = swiperInstance.activeIndex;
        localStorage.setItem('HelpPickup', (activeIndex + 1).toString());

        // Reset the counter when the user moves to the next slide
        this.disabledCounter = 10; // Reset timer to 10 seconds or any desired value
        swiperInstance.slideNext(this.slideOpts.speed);
      }
    } else {
      console.error('SwiperComponent instance not found.');
    }
  }

  async MessageCustomer() {
    const modal = await this.modalCtrl.create({
      component: MessagesComponent,
      componentProps: {
        SudsterName: this.SudsterName,
        OrderNumber: this.OrderNumber,
        CustomerName: this.CustomerFirstName,
      },
    });
    return await modal.present();
  }

  CallCustomer() {
    this.MaskCall.CallCustomer(this.OrderNumber);
  }

  async presentAlert(Title: string, Message: string | IonicSafeString) {
    const alert = await this.alertController.create({
      header: Title,
      message: Message,
      buttons: ['OK'],
    });
    await this.setAlertMessageInnerHtml(alert, Message);
    await alert.present();
  }

  private async setAlertMessageInnerHtml(
    alert: HTMLIonAlertElement,
    message: string | IonicSafeString
  ) {
    const messageElement = alert.querySelector('.alert-message');
    if (messageElement) {
      if (typeof message !== 'string') {
        message = message.toString();
      }
      messageElement.innerHTML = message;
    }
  }

  async presentLoading() {
    const loading = await this.loadingController.create({});
    await loading.present();
    return loading;
  }

  async GetCompensation() {
    const alert = await this.alertController.create({
      header: 'Missed Pickup',
      message:
        'If you want to receive a missed pickup fee of $7.50 for your time/gas, you can choose to “fine” your customer. <br><br><strong>Please confirm:</strong>',
      inputs: [
        {
          name: 'agree1',
          type: 'checkbox',
          label: 'I checked pickup spot',
          value: 'agree1',
        },
        {
          name: 'agree2',
          type: 'checkbox',
          label: 'I messaged customer',
          value: 'agree2',
        },
        {
          name: 'agree3',
          type: 'checkbox',
          label: 'I knocked on door',
          value: 'agree3',
        },
        {
          name: 'agree4',
          type: 'checkbox',
          label: 'I called customer',
          value: 'agree4',
        },
        {
          name: 'agree5',
          type: 'checkbox',
          label: 'I waited 5 minutes',
          value: 'agree5',
        },
      ],
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel',
          cssClass: 'secondary',
        },
        {
          text: 'Charge Fine',
          cssClass: 'danger',
          handler: async (data: string[]) => {
            if (data.length === 5) {
              //Fine customer
              const loading = await this.presentLoading();
              const chargeFine$ = this.isNewOrderFlow
                ? this.service.chargeFine(this.OrderNumber)
                : this.cloudFunctions.httpsCallable(
                    'SudsterV3_PickupCompensation'
                  )({
                    OrderNumber: this.OrderNumber,
                  });

              chargeFine$.subscribe({
                next: async () => {
                  await loading.dismiss();
                  await this.presentAlert(
                    'Compensation Sent',
                    'We sent you a missed-pickup compensation to your balance. We recommend you message your customer to reschedule the pickup. If you are unable to reschedule, please contact Concierge for help cancelling the order.'
                  );
                  this.dismissModal();
                },
                error: async (err) => {
                  await loading.dismiss();
                  await this.presentAlert('Error', err.message);
                },
              });
            } else {
              await this.presentAlert(
                'Confirm and try again',
                'Please confirm all checkboxes before proceeding.'
              );
            }
          },
        },
      ],
    });

    await this.setAlertMessageInnerHtml(alert, alert.message!);
    await alert.present();
  }
}
