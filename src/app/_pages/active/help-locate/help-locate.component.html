<ion-header translucent>
  <ion-toolbar>
    <ion-title>Help Locate Bags</ion-title>
    <ion-buttons slot="end">
      <ion-button id="close-button" (click)="dismissModal()">Close</ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-content>
    <swiper
      [initialSlide]="slideOpts.initialSlide"
      [speed]="slideOpts.speed"
      [autoHeight]="true"
      [allowTouchMove]="false"
      pagination
      #slider
    >
      <ng-template swiperSlide *ngFor="let slide of slides; let i = index">
        <div class="SlideDiv">
          <h1>{{ slide.title }}</h1>
          <div class="Body" [innerHTML]="slide.body"></div>

          <ion-button
            id="message-customer-button"
            *ngIf="i == 2"
            (click)="MessageCustomer()"
            color="warning"
          >
            Message Customer
          </ion-button>

          <ion-button
            id="call-customer-button"
            *ngIf="i == 4"
            (click)="CallCustomer()"
            color="warning"
          >
            Call Customer
          </ion-button>

          <ion-button
            id="missed-pickup-button"
            fill="outline"
            *ngIf="i == 5"
            (click)="GetCompensation()"
            color="warning"
          >
            Missed Pickup Compensation
          </ion-button>
          <br />

          <ion-button
            id="more-help-button"
            *ngIf="i < slides.length - 1"
            [disabled]="disabledCounter > 0"
            fill="clear"
            (click)="NextSlide()"
          >
            More Help
            {{ disabledCounter > 0 ? '(' + disabledCounter + 's)' : '' }}
            <ion-icon name="arrow-forward-outline"></ion-icon>
          </ion-button>
        </div>
      </ng-template>
    </swiper>
  </ion-content>
</ion-content>
