import { <PERSON><PERSON><PERSON><PERSON> } from '@ngneat/until-destroy';
import { inject, Injectable } from '@angular/core';
import { environment } from '../../../../environments/environment';
import { LegacyApiService } from '../../../_services/legacy-api.service';

@UntilDestroy()
@Injectable({
  providedIn: 'root',
})
export class HelpLocateService {
  apiService = inject(LegacyApiService);

  chargeFine(orderId: string) {
    return this.apiService.post(
      `/${orderId}/pickup-compensation`,
      {},
      {
        baseUrl: `${environment.apiPathV2}/orders`,
      }
    );
  }
}
