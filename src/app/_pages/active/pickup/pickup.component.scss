p {
  font-size: 14px;
  line-height: 150%;
  text-align: left;
}

label {
  display: block;
  margin: 10px 0px;
  text-align: center;
  font-weight: 600;
  color: var(--BlueGray5);
  font-size: 15px;
}

h1 {
  font-size: 20px;
  font-weight: 400;
  line-height: 150%;
}

small {
  display: block;
  color: var(--BlueGray3);
  font-weight: 600;
  padding: 5px;
}

.LabelExample {
  width: auto;
  padding: 5px 20px;
  border: dashed 3px var(--Orange);
  display: inline-block;
  font-weight: 700;
  color: var(--BlueGray4);
  margin: 20px auto;
}

#pickup-complete-button {
  z-index: 2;
  position: relative;
  margin-top: 30px;
  margin-bottom: 75px;
  max-width: 400px;
  font-weight: 600;
}
#SameDayServiceDiv {
  box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  background-color: #f5f5f5;
  border: 2px solid #fff;
  p {
    font-weight: 400;
    font-size: 12px;
    line-height: 12px;
    text-align: center;
    margin-bottom: 12px;
  }
  h5 {
    font-weight: 700;
    font-size: 17px;
  }
  .same__day__confirm {
    &::part(native) {
      color: black;
      background-color: #22ecab;
      left: 10px;
    }
  }
  ion-button {
    &::part(native) {
      height: 25px;
      width: 125px;
      font-weight: 700;
      border-radius: 20px;
      right: 10px;
    }
  }
  .deliverability-confirmed {
    border: 2px solid #22ecab;
    background-color: #fff;
    margin: auto;
    width: 251px;
    height: 27px;
    border-radius: 15px;
    color: #22ecab;
    font-weight: 600;
    margin-bottom: 5px;
    span {
      vertical-align: 7px;
      margin-left: 2px;
    }
  }
}
