<app-step
  [disableNext]="checkIsTrainingRequired(1)"
  title="Prepare For Pickup"
  index="1"
  [class.Complete]="SectionLevel > 1"
  [class.Open]="OpenSection === 1"
  (click)="ClickStepSection(1)"
  (NextStep)="StepComplete($event)"
>
  <app-tutorial
    [required]="TrainerLockLevel <= 1 && SudsterFirstOrder === true"
    (tutorialComplete)="tutorialComplete(firstOrderQuizzes.PREPARE_FOR_PICKUP)"
    quizQuestion="It's crucial to follow the steps in the app."
    [quizOptions]="['True', 'False', 'Need more info']"
    [quizAnswer]="1"
    [videoLength]="videoRefs.inOrder.welcome.length"
    [videoLink]="videoRefs.inOrder.welcome.ref"
    [recommendedTraining]="recommendedTraining"
  >
  </app-tutorial>

  <h1>Put in your car:<br /><b>Bags | Labels | A Pen</b></h1>
</app-step>

<app-step
  title="Go To Address"
  index="2"
  [class.Complete]="SectionLevel > 2"
  [class.Open]="OpenSection === 2"
  (click)="ClickStepSection(2)"
  (NextStep)="StepComplete($event)"
>
  <label (click)="CopyAddress()">{{ address }}</label>

  <ion-button
    id="directions-button"
    fill="outline"
    color="warning"
    [href]="'https://www.google.com/maps?q=' + address"
    target="_blank"
    >Directions
  </ion-button>

  <small>Proceed to next step for exact location details.</small>
</app-step>

<app-step
  title="Locate Bags"
  [disableNext]="SameDayService && !DeliverabilityConfirmed"
  index="3"
  [class.Complete]="SectionLevel > 3"
  [class.Open]="OpenSection === 3"
  (click)="ClickStepSection(3)"
  (NextStep)="StepComplete($event)"
>
  <p style="font-size: 17px">
    Unit Number:
    <b>{{ addressLine2 }}</b
    ><br />
    Pickup Spot: <b>{{ OrderData.PickupSpot.SimpleSpot }}</b>
    <span *ngIf="OrderData.PickupSpot.Instructions !== ''"
      ><br />Instructions: <b>{{ OrderData.PickupSpot.Instructions }}</b
      ><br
    /></span>
  </p>

  <ion-button
    id="call-customer-button"
    (click)="CallCustomer()"
    *ngIf="OrderData.PickupSpot.SimpleSpot === 'Call Me'"
    expand="block"
    fill="clear"
    color="warning"
    >Call Customer</ion-button
  >

  <ion-button
    id="locate-bags-button"
    size="small"
    fill="outline"
    color="warning"
    (click)="HelpLocateBags()"
    >Help locate bags <ion-icon name="help-circle-outline"> </ion-icon>
  </ion-button>

  <label><i>Don’t handle the bags yet.</i></label>
  <div *ngIf="SameDayService" id="SameDayServiceDiv">
    <h5 class="same__day__header">
      Confirm
      {{ isExpressServiceEnabled ? 'Express' : 'Same-Day' }} Deliverability
    </h5>
    <p *ngIf="!DeliverabilityConfirmed" class="same__day__body">
      Based on the laundry here, please confirm you can deliver on-time.
    </p>
    <ion-row *ngIf="!DeliverabilityConfirmed">
      <ion-button
        id="confirm-button"
        (click)="ConfirmSameDay()"
        expand="block"
        fill="solid"
        shape="round"
        color="success"
        class="same__day__confirm"
        >Confirm</ion-button
      >
      <ion-button
        id="not-sure-button"
        (click)="DeclineSameDay()"
        expand="block"
        fill="outline"
        shape="round"
        color="danger"
        class="same__day__decline"
        >Not sure</ion-button
      >
    </ion-row>
    <ion-row *ngIf="DeliverabilityConfirmed">
      <div class="deliverability-confirmed">
        <i class="material-icons"> done_all </i><span>Confirmed</span>
      </div>
    </ion-row>
  </div>
</app-step>

<app-step
  [disableNext]="TakePicDisabled || checkIsTrainingRequired(4, true)"
  title="Take A Photo"
  index="4"
  [class.Complete]="SectionLevel > 4"
  [class.Open]="OpenSection === 4"
  (click)="ClickStepSection(4)"
  (NextStep)="StepComplete($event)"
>
  <app-tutorial
    [required]="TrainerLockLevel <= 4 && SudsterFirstOrder === true"
    (tutorialComplete)="tutorialComplete(firstOrderQuizzes.TAKE_PHOTO)"
    quizQuestion="A good delivery photo includes:"
    [quizOptions]="[
      'All the bags',
      'Bags are countable',
      'The entire bag',
      'Identifiable location',
      'All of the above'
    ]"
    [quizAnswer]="5"
    [videoLength]="videoRefs.inOrder.deliveryPhoto.length"
    [videoLink]="videoRefs.inOrder.deliveryPhoto.ref"
    [recommendedTraining]="recommendedTraining"
  >
  </app-tutorial>

  <label>Step back and take a clear photo of all the bags together.</label>

  <app-photo-upload
    (uploadStarted)="UploadStarted($event)"
    (pictureStored)="PictureStored($event)"
    [savePhotoPath]="'orderPhotos/' + OrderData.OrderNumber"
    [photoSafeUrl]="storedPhotoSafeUrl"
  >
  </app-photo-upload>
</app-step>

<app-step
  title="Label Bags"
  index="5"
  [class.Complete]="SectionLevel > 5"
  [class.Open]="OpenSection === 5"
  (click)="ClickStepSection(5)"
  (NextStep)="StepComplete($event)"
>
  <label
    >Label each bag with the customer's name, order number and
    <u>bag number</u>. For example:
  </label>

  <div class="LabelExample">
    {{ OrderData.CustomerFirstName }} - #{{ orderId }}
    <u>(1/{{ OrderData.OrderSize }})</u>
  </div>
</app-step>

<app-step
  [disableNext]="BagCount < 1"
  title="Count Bags"
  index="6"
  [class.Complete]="SectionLevel > 6"
  [class.Open]="OpenSection === 6"
  (click)="ClickStepSection(6)"
  (NextStep)="StepComplete($event)"
>
  <label>How many bags?</label>

  <app-count-selector
    (CountChanged)="BagCount = $event"
    [userId]="UserID"
    [orderData]="OrderData"
    [orderNumber]="orderNumber"
    [isOverweightOrder]="isOverweightOrder"
    [isBagDiscrepancy]="isBagDiscrepancy"
  ></app-count-selector>
</app-step>

<app-step
  title="Pickup"
  index="7"
  [class.Complete]="SectionLevel > 7"
  [class.Open]="OpenSection === 7"
  (click)="ClickStepSection(7)"
  (NextStep)="StepComplete($event)"
>
  <label>Transfer the bags to your vehicle.</label>
</app-step>

<ion-button
  [disabled]="SectionLevel <= 7 || OpenSection <= 7"
  id="pickup-complete-button"
  color="warning"
  size="large"
  expand="block"
  (click)="PickupComplete()"
  >Pickup Complete<ion-icon name="checkmark-done"></ion-icon>
</ion-button>
