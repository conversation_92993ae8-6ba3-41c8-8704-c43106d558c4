import {
  ApplicationRef,
  Component,
  inject,
  Inject,
  Input,
  OnInit,
} from '@angular/core';
import { AngularFirestore } from '@angular/fire/firestore';
import {
  <PERSON><PERSON><PERSON>ontroller,
  LoadingController,
  ModalController,
  ToastController,
} from '@ionic/angular';
import { HelpLocateComponent } from '../help-locate/help-locate.component';

import { AngularFireFunctions } from '@angular/fire/functions';
import { SafeUrl } from '@angular/platform-browser';
import { Clipboard } from '@capacitor/clipboard';
import { Network } from '@capacitor/network';
import { untilDestroyed } from '@ngneat/until-destroy';
import Rollbar from 'rollbar';
import { finalize, takeWhile } from 'rxjs/operators';
import {
  PictureTakenEvent,
  UploadStartedEvent,
} from 'src/app/_components/photo-upload/photo-upload.component';
import { OrderData } from 'src/app/_interfaces/order-data.interface';
import { SudsterData } from 'src/app/_interfaces/sudster-data.interface';
import { VIDEOREFS } from 'src/app/_interfaces/videoRefs';
import { AnalyticsLogService } from 'src/app/_services/analytics/analytics-log.service';
import { AuthidService } from 'src/app/_services/authid.service';
import { LegacyApiService } from 'src/app/_services/legacy-api.service';
import { MaskCallService } from 'src/app/_services/mask-call.service';
import { PhotoService } from 'src/app/_services/photo.service';
import { PointsService } from 'src/app/_services/points/points.service';
import { RollbarService } from 'src/app/_services/rollbar/rollbar.service';
import { StatsigFactoryService } from 'src/app/_services/statsig-factory.service';
import { StatsigService } from 'src/app/_services/statsig.service';
import { ToastService } from 'src/app/_services/toast.service';
import { ZendeskService } from 'src/app/_services/zendesk.service';
import { environment } from 'src/environments/environment';
import { getAddress } from '../../../_utils/order-utils';
import { FirstOrderQuizzes } from '../../academy/academy.page';
import { Detergent } from '../../home/<USER>/adapters/active-order.adapter';
import { MessagesComponent } from '../messages/messages.component';

@Component({
  selector: 'app-pickup',
  templateUrl: './pickup.component.html',
  styleUrls: ['./pickup.component.scss'],
})
export class PickupComponent implements OnInit {
  OpenSection = 1;
  SectionLevel = 1;
  TrainerLockLevel = 1;
  StepButtonClicked = false;
  SudsterFirstOrder = false;

  TakePicDisabled = true;
  BagCount = 0;
  PhotoGPS;
  SameDayService: boolean;
  DeliverabilityConfirmed: boolean;
  Sudster: SudsterData;
  UserID = this.AuthID.getID();
  unreadChats = 0;
  videoRefs = VIDEOREFS;
  bagSize: string;
  connected: boolean = true;
  @Input() OrderData: OrderData;
  @Input() orderNumber: string;
  @Input() isOverweightOrder: boolean;
  @Input() isBagDiscrepancy: boolean;
  isZendeskLoggedIn = false;
  isExpressServiceEnabled = false;
  address = '';
  addressLine2 = '';
  orderId = '';
  uploadingPhotoId;
  storedPhotoSafeUrl: SafeUrl = '';
  storedPhotoPath: string;
  firstOrderQuizzes = FirstOrderQuizzes;
  recommendedTraining = true;
  private statsigService: StatsigService;
  private readonly _pointsService = inject(PointsService);

  constructor(
    public modalController: ModalController,
    public firestore: AngularFirestore,
    private loadingController: LoadingController,
    private toastController: ToastController,
    private cloudFunctions: AngularFireFunctions,
    private alertController: AlertController,
    private maskCall: MaskCallService,
    private appRef: ApplicationRef,
    private modalCtrl: ModalController,
    private AuthID: AuthidService,
    private zendeskService: ZendeskService,
    private analyticsService: AnalyticsLogService,
    private toastService: ToastService,
    private photoService: PhotoService,
    private statsigFactoryService: StatsigFactoryService,
    private apiService: LegacyApiService,
    @Inject(RollbarService) private rollbar: Rollbar
  ) {
    this.statsigService = this.statsigFactoryService.getInstance();
  }

  ngOnInit() {
    this.statsigService
      .checkGate(environment.statsig.flags.ExpressService)
      .subscribe((expressservice) => {
        this.isExpressServiceEnabled = expressservice;
      });

    this.statsigService
      .checkGate(environment.statsig.flags.lpFirstOrderRecommendedTraining)
      .subscribe((showTraining) => {
        this.recommendedTraining = showTraining;
      });

    this.zendeskService.init();
    if (this.OrderData.SudsterFirstOrder === true) {
      this.SudsterFirstOrder = true;
    }

    this.SameDayService = this.OrderData.SameDayService;
    this.DeliverabilityConfirmed = false;

    this.StepButtonClicked = false;
    const StepNumberStorage = parseInt(
      localStorage.getItem(this.OrderData.OrderNumber + '-StepNumber')
    );
    if (StepNumberStorage > 0) {
      this.OpenSection = StepNumberStorage;
      this.SectionLevel = StepNumberStorage;
      this.TrainerLockLevel = StepNumberStorage;
    }

    if (StepNumberStorage > 2) {
      this.OpenSection = 2;
      this.SectionLevel = 2;
      this.TrainerLockLevel = 2;
    }
    this.firestore
      .doc('Sudsters/' + this.UserID)
      .get()
      .subscribe((sudsterDoc) => {
        if (sudsterDoc.exists) {
          this.Sudster = sudsterDoc.data() as SudsterData;
        }
      });
    this.bagSize = this.OrderData.BagSize;
    this.address =
      typeof this.OrderData.Address === 'string'
        ? this.OrderData.Address
        : this.OrderData.Address['Full'];
    this.addressLine2 = this.OrderData.AddressLine2
      ? this.OrderData.AddressLine2
      : this.OrderData.Address['Line2'];
    this.orderId = this.OrderData.OrderId
      ? this.OrderData.OrderId
      : this.OrderData.OrderNumber;
  }

  ClickStepSection(index) {
    if (!this.StepButtonClicked) {
      if (index <= this.SectionLevel) {
        this.OpenSection = index;
      }
    }
    this.StepButtonClicked = false;
  }

  StepComplete(index) {
    localStorage.removeItem('HelpPickup');
    if (index == this.SectionLevel) {
      this.SectionLevel++;
      this.OpenSection++;
      this.TrainerLockLevel = this.SectionLevel;
    } else {
      this.OpenSection = this.SectionLevel;
    }
    this.StepButtonClicked = true;
    localStorage.setItem(
      this.OrderData.OrderNumber + '-StepNumber',
      this.SectionLevel.toString()
    );
  }

  UploadStarted(event: UploadStartedEvent) {
    if (!this.uploadingPhotoId) {
      this.uploadingPhotoId = event.photoFileId;
    }
  }

  PictureStored(event: PictureTakenEvent) {
    this.TakePicDisabled = false;
    this.PhotoGPS = event.photoLocation;
    this.storedPhotoSafeUrl = event.photoLocalUrl;
    this.storedPhotoPath = event.photoLocalPath;
    this.uploadingPhotoId = null;
    this.appRef.tick();
  }

  async PickupComplete() {
    const loading = this.presentLoading();

    const FinishPickup = async () => {
      const loadingF = this.presentLoading();

      const submitPickupStep = () => {
        localStorage.removeItem(this.OrderData.OrderNumber + '-StepNumber');

        this.pickupCompleteRequest()
          .toPromise()
          .then(async () => {
            this.analyticsService.logOrderPickup(
              this.UserID,
              this.OrderData.OrderNumber,
              this.OrderData.OrderSize,
              this.BagCount,
              this.OrderData.OrderSize !== this.BagCount,
              this.OrderData.PickupDeadline
            );
            localStorage.removeItem(this.OrderData.OrderNumber + '-StepNumber');
            loadingF.then(function (ld) {
              ld.dismiss();
            });
            await this.photoService.removeSavedPhoto(
              this.storedPhotoPath,
              this.uploadingPhotoId
            );
          })
          .catch((err) => {
            localStorage.setItem(
              this.OrderData.OrderNumber + '-StepNumber',
              this.OpenSection.toString()
            );
            loadingF.then(function (ld) {
              ld.dismiss();
            });
            if (this.connected) {
              this.presentAlert(
                'System Error',
                err?.message ||
                  'An error occurred while processing your request.'
              );
            }
          });
      };

      setTimeout(() => {
        loadingF.then((ld) => {
          ld.message =
            'Taking too long? After 10 seconds, try disabling Wi-Fi.';
        });
      }, 3000);

      // If the photo was not uploaded, start the upload
      if (!this.uploadingPhotoId) {
        await this.ResumePhotoUpload();
      } else if (
        this.photoService.photoHasAlreadyBeenUploaded(this.uploadingPhotoId)
      ) {
        submitPickupStep();
        return;
      }

      if (this.uploadingPhotoId) {
        const photoUploadState = this.photoService.photoUploadState$.subscribe(
          async (photoState) => {
            if (!photoState) {
              try {
                throw new Error('photoState is null');
              } catch (e) {
                this.rollbar.error(e);
              }

              loadingF.then(function (ld) {
                ld.dismiss();
              });
              this.presentAlert(
                'Upload Error',
                'An error occurred while uploading the photo. Please try again.'
              );

              this.RetakePhoto();

              return;
            }

            const { photoFileId, uploadState } = photoState;

            if (photoFileId !== this.uploadingPhotoId) return;

            if (uploadState === 'complete') {
              submitPickupStep();
              photoUploadState.unsubscribe();
            }

            if (uploadState === 'error') {
              loadingF.then((ld) => ld.dismiss());
              this.presentAlert(
                'Upload Error',
                'An error occurred while uploading the photo. Please try again.'
              );
              photoUploadState.unsubscribe();

              this.RetakePhoto();
            }
          }
        );
      }
    };

    await Network.getStatus()
      .then(async (status) => {
        if (!status.connected) {
          loading.then((ld) => ld.dismiss());
          this.connected = false;
          const message = `A connection glitch interrupted your request. Keep the app open, find a stronger signal, and try again.<br/> Points are not impacted if the first attempt was before the deadline.`;

          this.toastService.errorToast(
            'Lost Connection',
            message.replace('<br/>', '<br/><br/>')
          );
          return;
        } else {
          let AdditionalInstructions = '';
          if (
            this.OrderData.Preferences.Detergent === Detergent.Hypoallergenic ||
            this.OrderData.Preferences.Detergent ===
              Detergent.UnscentedHypoallergenic
          ) {
            AdditionalInstructions +=
              '[Unscented Hypoallergenic Detergent]<br>';
          }
          if (this.OrderData.Preferences.Delicates) {
            AdditionalInstructions += '[Delicates Includes]<br>';
          }
          if (this.OrderData.Preferences.HangDry) {
            AdditionalInstructions += '[Hang-Dry Items Includes]<br>';
          }
          if (this.OrderData.Preferences.Hangers) {
            AdditionalInstructions += '[Hangers Provided]<br>';
          }

          const alert = await this.alertController.create({
            header: 'Special Instructions',
            subHeader:
              'Your customer has included special instructions. Please read and confirm before continuing.',
            message: 'Loading instructions...', // Temporary placeholder
            buttons: [
              {
                text: 'I HAVE READ & CONFIRM',
                handler: async () => {
                  loading.then((ld) => ld.dismiss());
                  FinishPickup();
                },
              },
            ],
          });

          if (
            (this.OrderData.Preferences.Instructions != '' &&
              !!this.OrderData.Preferences.Instructions) ||
            AdditionalInstructions != ''
          ) {
            loading.then((ld) => ld.dismiss());
            await alert.present();
            const alertMessageEl = document.querySelector(
              'ion-alert .alert-message'
            );
            if (alertMessageEl) {
              alertMessageEl.innerHTML = `
    ${AdditionalInstructions}
    ${this.OrderData.Preferences.Instructions}
  `;
            }
          } else {
            loading.then((ld) => ld.dismiss());
            FinishPickup();
          }
        }
      })
      .catch((err) => {
        loading.then((ld) => ld.dismiss());
        return err;
      });
  }

  async presentLoading(message?: string) {
    const loading = await this.loadingController.create({
      message,
    });
    await loading.present();
    return loading;
  }

  async HelpLocateBags() {
    const modal = await this.modalController.create({
      component: HelpLocateComponent,
      componentProps: {
        StreetAddress: getAddress(this.OrderData.Address),
        isNewOrderFlow: this.OrderData.IsNewOrderFlow,
        PickupSpot: this.OrderData.PickupSpot,
        AddressLine2: this.addressLine2,
        OrderNumber: this.OrderData.OrderNumber,
        SudsterName: this.OrderData.SudsterName,
        CustomerFirstName: this.OrderData.CustomerFirstName,
      },
    });
    return await modal.present();
  }

  CallCustomer() {
    this.maskCall.CallCustomer(this.OrderData.OrderNumber);
  }

  CopyAddress() {
    Clipboard.write({
      string: getAddress(this.OrderData.Address, true),
    });
    this.toastController
      .create({
        message: 'Address copied to clipboard',
        duration: 2500,
      })
      .then((toast) => toast.present());
  }

  async presentAlert(header: string, message: string, Buttons?: any) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: Buttons || ['OK'],
    });
    await alert.present();
  }

  tutorialComplete(quizNumber): void {
    if (this.recommendedTraining && this.SudsterFirstOrder) {
      this._pointsService
        .addFirstOrderPoints(this.OrderData.OrderNumber, quizNumber)
        .pipe(
          finalize(() => {
            this.incrementTrainingLevel();
          }),
          untilDestroyed(this)
        )
        .subscribe(() => {});
    } else {
      this.incrementTrainingLevel();
    }
  }

  private incrementTrainingLevel(): void {
    this.TrainerLockLevel++;
    this.appRef.tick();
  }

  ConfirmSameDay() {
    this.DeliverabilityConfirmed = true;
  }

  async DeclineSameDay(): Promise<HTMLIonAlertElement> {
    this.DeliverabilityConfirmed = false;
    const alert = await this.alertController.create({
      header: 'Weight Limit',
      message: `If you believe this order is above the ${
        this.isExpressServiceEnabled ? 'Express' : 'Same-Day'
      } weight limit of 60 lb and that you cannot deliver on time, please either message your customer to discuss doing a portion of the order or contact support for assistance.`,
      buttons: [
        {
          text: 'Message Customer',
          handler: () => {
            this.MessageCustomer();
          },
        },
        {
          text: 'Contact Support',
          handler: () => this.ContactSupport(),
        },
        {
          text: 'Close',
          handler: () => {
            alert.dismiss();
          },
        },
      ],
    });
    await alert.present();
    return alert;
  }

  async MessageCustomer(): Promise<HTMLIonModalElement> {
    const modal: HTMLIonModalElement = await this.modalCtrl.create({
      component: MessagesComponent,
      componentProps: {
        SudsterName: this.OrderData.SudsterName,
        OrderNumber: this.OrderData.OrderNumber,
        CustomerName: this.OrderData.CustomerFirstName,
      },
    });
    await modal.present();
    return modal;
  }

  async ContactSupport() {
    if (this.isZendeskLoggedIn) {
      this.unreadChats = 0;
      this.zendeskService.show();
    } else {
      const loading = this.presentLoading();
      this.zendeskService.getIsLoggedIn
        .pipe(takeWhile(() => !this.isZendeskLoggedIn))
        .subscribe((zendeskLoggedIn) => {
          if (zendeskLoggedIn) {
            loading.then(function (ld) {
              ld.dismiss();
            });
            this.unreadChats = 0;
            this.zendeskService.show();
            this.isZendeskLoggedIn = true;
          }
        });
    }
  }

  async ResumePhotoUpload() {
    if (!this.storedPhotoPath) {
      this.presentAlert('Error', 'Missing Photo');
      return;
    }

    const { fileId } = await this.photoService.uploadPhoto(
      this.storedPhotoPath
    );

    this.uploadingPhotoId = fileId;
  }

  async RetakePhoto() {
    if (this.storedPhotoPath) {
      this.photoService.removeSavedPhoto(
        this.storedPhotoPath,
        this.uploadingPhotoId
      );
    }

    // Take the user to take the photo again
    this.storedPhotoPath = '';
    this.storedPhotoSafeUrl = '';
    this.uploadingPhotoId = null;
    this.SectionLevel = 4;
    this.OpenSection = 4;
    this.TakePicDisabled = true;
  }

  checkIsTrainingRequired(currentStep: number, requiresPhoto = false): boolean {
    if (this.recommendedTraining) {
      return requiresPhoto ? this.TakePicDisabled : false;
    }

    return this.TrainerLockLevel <= currentStep && this.SudsterFirstOrder;
  }

  pickupCompleteRequest() {
    const isNewOrderFlow = this.OrderData.IsNewOrderFlow;

    if (isNewOrderFlow) {
      return this.apiService.put(
        `/${this.OrderData.OrderNumber}/status/pickup`,
        {
          bagCount: this.BagCount,
          pickUpPhoto: this.uploadingPhotoId,
          pickUpGPS: this.PhotoGPS,
        },
        {
          baseUrl: `${environment.apiPathV2}/orders`,
        }
      );
    }

    return this.cloudFunctions.httpsCallable('SudsterV3_PickupComplete')({
      OrderNumber: this.OrderData.OrderNumber,
      PickupPhoto: this.uploadingPhotoId,
      BagCount: this.BagCount,
      PickupGPS: this.PhotoGPS,
    });
  }
}
