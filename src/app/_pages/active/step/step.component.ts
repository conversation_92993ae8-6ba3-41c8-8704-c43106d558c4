import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
  selector: 'app-step',
  templateUrl: './step.component.html',
  styleUrls: ['./step.component.scss'],
})
export class StepComponent {
  @Input() index;
  @Input() title;
  @Input() body;
  @Input() disableNext;
  @Input() recommended: boolean;
  @Input() disable = false;

  @Output() NextStep = new EventEmitter();

  ClickNextStep() {
    this.NextStep.emit(this.index);
  }
}
