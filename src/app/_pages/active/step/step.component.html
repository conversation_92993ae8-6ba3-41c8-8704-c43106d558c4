<section class="StepSec">
  <div class="Header">
    <span class="StepNumber">{{ index }}</span>
    <h1 class="StepTitle">{{ title }}</h1>
    <ion-icon
      *ngIf="title !== 'Pre-Weigh'"
      class="StepCheck"
      name="checkmark-circle-outline"
    ></ion-icon>
    <span
      class="recommended-step"
      *ngIf="title === 'Pre-Weigh' && recommended === true"
      >recommended</span
    >
  </div>

  <div class="Body">
    <ng-content></ng-content>

    <ion-button
      *ngIf="title === 'Pre-Weigh' && body === 'true'"
      id="step-complete-button"
      [disabled]="disableNext || disable"
      class="StepCompleteButton"
      (click)="ClickNextStep()"
      expand="full"
    >
      Continue<ion-icon name="arrow-down-outline"></ion-icon>
    </ion-button>

    <ion-button
      *ngIf="title !== 'Pre-Weigh' && (body === undefined || body === 'false')"
      id="step-complete-button"
      [disabled]="disableNext || disable"
      class="StepCompleteButton"
      (click)="ClickNextStep()"
      expand="full"
      >Step Complete<ion-icon name="arrow-down-outline"></ion-icon>
    </ion-button>
  </div>
</section>
