.StepSec {
  width: 100%;
  margin: 0px auto;
  padding: 15px;
  position: relative;
  z-index: 2;
  overflow: hidden;
  max-height: 60px;
  transition: all 0.5s;

  .Header {
    display: block;
    overflow: hidden;

    .StepNumber {
      margin-left: 2px;
      margin-bottom: 2px;
      float: left;
      height: 30px;
      width: 30px;
      font-weight: 800;
      font-size: 16px;
      padding-top: 1px;
      color: white;
      display: block;
      background: var(--BlueGray2);
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      border-radius: 50%;
      border: solid 3px var(--BlueGray1);
    }

    .StepTitle {
      float: left;
      padding-left: 10px;
      margin: 0;
      font-size: 15px;
      text-transform: uppercase;
      font-weight: 700;
      letter-spacing: 1px;
      padding-top: 7px;
      color: var(--BlueGray3);
    }

    .StepCheck {
      float: right;
      display: none;
      font-size: 22px;
    }
  }

  .Body {
    padding-top: 10px;
    padding-bottom: 30px;

    .StepCompleteButton {
      width: 100%;
      max-width: 100%;
      position: absolute;
      left: 0px;
      right: 0px;
      --background: var(--Orange);
      margin-top: 10px;
    }
  }
}

:host(.Open) {
  .StepSec {
    background: white;
    border-radius: 5px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
    margin: 20px auto;
    max-height: unset;

    .Header {
      border-bottom: solid 1px var(--BlueGray2);
      padding-bottom: 10px;

      .StepTitle {
        color: var(--BlueGray4) !important;
        padding-top: 4px !important;
        text-transform: unset !important;
        font-family: 'Fakt-Normal', sans-serif !important;
        font-style: normal !important;
        font-weight: 700 !important;
        font-size: 16px !important;
        line-height: 140% !important;
      }

      .StepNumber {
        background: var(--Orange) !important;
        border-color: #ffe69b !important;
      }
    }
  }
}

:host(.Complete) {
  .StepCheck {
    color: var(--Orange);
    padding-top: 5px;
    display: block !important;
  }
}
.recommended-step {
  background-color: #c2e6ff;
  text-transform: uppercase;
  padding-left: 4px;
  padding-right: 4px;
  border-radius: 15px;
  font-weight: 700;
  font-size: 13px;
  display: flex;
  position: relative;
  width: 119px;
  left: 56px;
  text-align: center;
}
