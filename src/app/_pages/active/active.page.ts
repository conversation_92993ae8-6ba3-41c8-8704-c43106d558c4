import { Component, OnInit, ViewChild } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/auth';
import { AngularFirestore } from '@angular/fire/firestore';
import { ActivatedRoute, Router } from '@angular/router';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>er,
  LoadingController,
  ModalController,
} from '@ionic/angular';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import moment from 'moment';
import {
  ExtensionReasons,
  OrderData,
} from 'src/app/_interfaces/order-data.interface';
import { VIDEOREFS } from 'src/app/_interfaces/videoRefs';
import { AuthidService } from 'src/app/_services/authid.service';
import { DeadlinesService } from 'src/app/_services/deadlines.service';
import { OrderExtensionService } from 'src/app/_services/order-extension.service';
import { PlayVideoService } from 'src/app/_services/play-video.service';
import { StatsigFactoryService } from 'src/app/_services/statsig-factory.service';
import { StatsigService } from 'src/app/_services/statsig.service';
import { environment } from 'src/environments/environment';
import tz_lookup from 'tz-lookup';
import { MessagesComponent } from './messages/messages.component';
import { OrderExtensionComponent } from './order-extension/order-extension.component';
import { WeightService } from '../../service/weight.service';

enum EditingWeightStatus {
  editingWeight = 'editingWeight',
}

@UntilDestroy()
@Component({
  selector: 'app-active',
  templateUrl: './active.page.html',
  styleUrls: ['./active.page.scss'],
})
export class ActivePage implements OnInit {
  StageNumber = 0;
  private statsigService: StatsigService;
  @ViewChild('ScrollDiv', { static: true }) private ScrollDiv;

  OrderNumber = '';
  OrderData: OrderData;
  DoneLoadingPage = false;
  disableEdit = false;

  editWeightMode = false;
  isWeighModeLockedIn = false;
  isOverweightOrder: boolean = false;
  isBagDiscrepancy = false;
  showedExtensionTooltip = true;
  tooltipDismissed = false;
  PreWeightNumber: number = 0;
  isExtended: boolean;
  isExpressServiceEnabled = false;
  orderId = '';
  showChat = false;
  chatOpened = false;

  constructor(
    public firestore: AngularFirestore,
    private AuthID: AuthidService,
    private loadingController: LoadingController,
    private modalController: ModalController,
    private route: ActivatedRoute,
    public afAuth: AngularFireAuth,
    private alertController: AlertController,
    private PlayVideo: PlayVideoService,
    private deadlinesService: DeadlinesService,
    private orderExtensionService: OrderExtensionService,
    private router: Router,
    private statsigFactoryService: StatsigFactoryService,
    private weightService: WeightService
  ) {
    this.OrderNumber = this.route.snapshot.paramMap.get('id');
    this.showChat = this.route.snapshot.paramMap.get('showChat') === 'true';
    this.statsigService = this.statsigFactoryService.getInstance();
  }
  UserID = this.AuthID.getID();
  SameDayService: boolean;
  SameDayPickup: string;
  SameDayDelivery: string;

  ngOnInit() {
    this.statsigService
      .checkGate(environment.statsig.flags.ExpressService)
      .subscribe((expressservice) => {
        this.isExpressServiceEnabled = expressservice;
      });

    const loading = this.presentLoading();

    this.chatOpened = false;

    this.firestore
      .doc<OrderData>(`Orders/${this.OrderNumber}`)
      .valueChanges()
      .pipe(untilDestroyed(this))
      .subscribe(async (doc) => {
        if (doc.SudsterID == this.UserID) {
          const timezone = doc.Timezone;
          doc.OrderNumber = this.OrderNumber;
          this.OrderData = doc;
          this.orderId = this.OrderData.OrderId
            ? this.OrderData.OrderId
            : this.OrderData.OrderNumber;
          this.SameDayService = doc.SameDayService;
          this.SameDayPickup =
            this.deadlinesService.ConvertUnixDeadlineToString(
              doc.PickupDeadline,
              timezone
            );
          this.SameDayDelivery =
            this.deadlinesService.ConvertUnixDeadlineToString(
              doc.DeliveryDeadline,
              timezone
            );
          this.isExtended =
            this.OrderData.OrderExtensionReasons &&
            this.OrderData.OrderExtensionReasons.length > 0;
          if (this.SameDayService && this.OrderData.OrderStatusNumber >= 2) {
            this.SameDayPickup = 'Complete';
          }
          if (this.OrderData.OrderStatusNumber === 1.5) {
            this.StageNumber = 1;
          } else if (this.OrderData.OrderStatusNumber === 2) {
            this.StageNumber = 2;
          } else if (this.OrderData.OrderStatusNumber === 2.5) {
            this.StageNumber = 3;
          } else if (this.OrderData.OrderStatusNumber === 3) {
            this.StageNumber = 4;
          }

          // Lock in edit mode
          // Prevent access to step 4
          if (
            doc.OrderStatus === EditingWeightStatus.editingWeight &&
            this.OrderData.IsNewOrderFlow
          ) {
            this.StageNumber = 3;
            this.isWeighModeLockedIn = true;
          }

          this.setExtendableOrder();
          this.DoneLoadingPage = true;
          loading.then((ld) => ld.dismiss());

          await this.ScrollDiv?.scrollToTop(0);

          if (this.showChat) {
            await this.OpenMessages();
          }
        }
      });
  }

  ionViewDidEnter(): void {
    this.showChat = this.route.snapshot.paramMap.get('showChat') === 'true';

    if (this.OrderData && this.showChat) {
      this.OpenMessages();
    }
  }

  async presentLoading() {
    const loading = await this.loadingController.create({
      showBackdrop: false,
    });
    await loading.present();
    return loading;
  }

  async OpenMessages() {
    if (this.chatOpened) return;

    this.chatOpened = true;

    const modal = await this.modalController.create({
      component: MessagesComponent,
      componentProps: {
        SudsterName: this.OrderData.SudsterName,
        OrderNumber: this.OrderData.OrderNumber,
        CustomerName: this.OrderData.CustomerFirstName,
        isFirstOrder: this.OrderData.FirstOrder,
        orderStatusNumber: this.OrderData.OrderStatusNumber,
      },
    });
    await modal.present();
    await modal.onDidDismiss();

    this.showChat = false;
    this.chatOpened = false;

    if (
      this.OrderData.OrderStatusNumber === 0 ||
      this.OrderData.OrderStatusNumber === 4
    ) {
      this.router.navigate(['home']);
    }
    if (
      this.OrderData.SudsterFirstOrder == true &&
      localStorage.getItem('WatchedMessagingTrainingVideo') == null
    ) {
      const alert = await this.alertController.create({
        header: 'Learn about Messaging',
        message: 'Please watch this short video about messaging.',
        backdropDismiss: false,
        buttons: [
          {
            text: 'Play Video',
            handler: () => {
              localStorage.setItem('WatchedMessagingTrainingVideo', 'true');
              this.PlayVideo.open(VIDEOREFS.inOrder.messaging.ref);
            },
          },
        ],
      });
      await alert.present();
    }
  }

  GetDeadline() {
    if (this.OrderData == null) {
      return '';
    }

    const timezone = this.OrderData.Timezone
      ? this.OrderData.Timezone
      : tz_lookup(this.OrderData.Lat, this.OrderData.Lng);
    let Deadline = this.deadlinesService.ConvertUnixDeadlineToString(
      this.OrderData.PickupDeadline,
      timezone
    );
    if (this.StageNumber >= 2) {
      Deadline = this.deadlinesService.ConvertUnixDeadlineToString(
        this.OrderData.DeliveryDeadline,
        timezone
      );
    }

    return Deadline;
  }

  GetDeadlineSuggestion() {
    if (this.OrderData != null) {
      if (this.OrderData.OrderStatusNumber == 1.5) {
        // Pickup
        const currentPickUpAvailableTime = calcTimeDiffInHrs2(
          new Date().getTime(),
          this.OrderData.PickupDeadline,
          this.OrderData.Timezone
        );
        // sudster need to pick up within the first
        const pickUpDeadLine = moment.tz(
          this.OrderData.PickupDeadline,
          this.OrderData.Timezone
        );
        if (currentPickUpAvailableTime > 24 && !this.OrderData.SameDayService) {
          pickUpDeadLine.subtract(24, 'hours');
          return (
            'Pickup before ' +
            pickUpDeadLine.format('h:mm A') +
            ' today to earn +10pts'
          );
        } else if (
          currentPickUpAvailableTime > 18 &&
          this.OrderData.SameDayService
        ) {
          pickUpDeadLine.subtract(18, 'hours');
          return (
            'Pickup before ' +
            pickUpDeadLine.format('h:mm A') +
            ' today to earn +10pts'
          );
        } else {
          return '';
        }
      }
      return '';
    }

    return '';

    function calcTimeDiffInHrs2(
      actionTime: number,
      actionDeadline: number,
      timezone?: string
    ) {
      const startMoment = timezone
        ? moment(actionTime).tz(timezone)
        : moment(actionTime);
      const endMoment = timezone
        ? moment(actionDeadline).tz(timezone)
        : moment(actionDeadline);

      return endMoment.diff(startMoment, 'hours');
    }
  }

  async presentAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK'],
    });
    await alert.present();
  }

  async editWeight() {
    if (this.OrderData.SudsterWeightChanged) {
      return; // already changed nothing to do
    }

    const alert = await this.alertController.create({
      header: 'Are you sure?',
      message:
        "The customer will be recharged, which will affect the customer's experience and may lead to a lower rating. Once the weight has been updated, it cannot be undone.",
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {},
        },
        {
          text: 'Proceed',
          cssClass: 'danger',
          handler: () => {
            if (this.OrderData.IsNewOrderFlow) {
              this.weightService
                .patchEditWeight(this.OrderNumber, true)
                .subscribe({
                  next: () => {
                    this.editWeightMode = true;
                  },
                  error: () => {
                    this.editWeightMode = false;
                  },
                });
            }
          },
        },
      ],
    });

    await alert.present();
  }

  onEditFinished() {
    this.editWeightMode = false;
  }

  GoBackToDelivery() {
    //If on edit weight, go back to delivery on click:
    if (this.editWeightMode) {
      this.editWeightMode = false;
    }
  }

  setExtendableOrder() {
    if (this.SameDayService) {
      return;
    }
    this.orderExtensionService.canExtendOrder(this.OrderData).then((val) => {
      if (val && val.length === 2) {
        this.isOverweightOrder = val[0];
        this.isBagDiscrepancy = val[1];
        if (this.isOverweightOrder || this.isBagDiscrepancy) {
          this.showedExtensionTooltip = !!localStorage.getItem(
            'showedExtensionTooltip'
          );
        }
      }
    });
  }

  openExtensionModal() {
    if (this.isBagDiscrepancy) {
      this.requestExtension(ExtensionReasons.BagDifference);
    } else {
      this.requestExtension(ExtensionReasons.Overweight);
    }
    this.tooltipDismissed = true;
    this.showedExtensionTooltip = true;
    localStorage.setItem('showedExtensionTooltip', 'true');
  }

  private async requestExtension(reason: ExtensionReasons) {
    const pickupDeadline = new Date(this.OrderData.PickupDeadline);
    const requestDeadline = new Date(pickupDeadline).setHours(
      new Date(pickupDeadline).getHours() + 1
    );
    const modal = await this.modalController.create({
      component: OrderExtensionComponent,
      cssClass: 'order-extension-modal',
      componentProps: {
        sudsterId: this.OrderData.SudsterID,
        orderNumber: this.OrderData.OrderNumber,
        reason: reason,
        isExtended: this.isExtended,
        customerName: this.OrderData.CustomerFirstName,
        deadline: this.OrderData.DeliveryDeadline,
        requestDeadline: requestDeadline,
        PreWeightNumber: this.PreWeightNumber,
      },
      backdropDismiss: false,
    });
    return await modal.present();
  }

  dismissWarning(e) {
    this.isOverweightOrder = e;
  }

  dismissTooltip() {
    this.showedExtensionTooltip = false;
    localStorage.setItem('showedExtensionTooltip', 'false');
  }
  CheckPreWeights(ev: number): void {
    this.PreWeightNumber = ev;
    if (ev >= 100) {
      this.isOverweightOrder = true;
    }
    return;
  }
}
