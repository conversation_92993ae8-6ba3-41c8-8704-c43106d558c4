#AgreeDiv {
  border-bottom: solid 1px var(--BlueGray3);
  padding-bottom: 20px;
  margin-bottom: 10px;

  h1 {
    font-size: 18px;
    font-weight: 400;

    ion-icon {
      font-size: 20px;
      vertical-align: -4px;
    }
  }

  p {
    text-align: justify;
    font-size: 14px;
    line-height: 140%;
    color: var(--BlueGray5);
  }

  #Checkbox {
    font-weight: 800;
    letter-spacing: 1px;
    font-size: 14px;
    color: red;

    ion-checkbox {
      vertical-align: -3px;
      margin-right: 5px;
      --border-color: red;
      --border-color-checked: red;
      --background-checked: red;
    }
  }
}

#MyWeightInput {
  text-align: center;
  font-size: 30px;
  width: 120px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  border: none;
  border-top: solid 3px var(--Orange);
  border-radius: 3px;
  margin-bottom: 15px;
  color: var(--<PERSON><PERSON>ray5);
  padding: 5px 10px;
}

#DecimalPointWarning {
  display: block;
  margin: 15px 0px;
  border-radius: 5px;
  padding: 5px;
  color: var(--BlueGray5);
  font-weight: 600;
  border: solid 2px red;

  ion-icon {
    vertical-align: -4px;
    font-size: 20px;
    color: red;
    margin-right: 5px;
  }
}

#WeighListDiv {
  box-shadow: 0 2px 10px -1px rgba(0, 0, 0, 0.2);
  border-radius: 15px;
  margin-bottom: 20px;
  margin-top: 20px;
  overflow: hidden;

  .ListItem {
    overflow: hidden;
    position: relative;
    text-align: center;
    min-height: 40px;

    .OverlayDiv {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.8);
      text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
      color: white;
      opacity: 0;
      top: 100%;
      text-align: center;
      line-height: 40px;
      font-size: 15px;
      border-top-left-radius: 15px;
      border-top-right-radius: 15px;
      pointer-events: none;
    }

    .OverlayAnimation.OverlayDiv {
      animation: MAOWeighListItemOverlayDivAnimation;
      animation-duration: 5s;
    }

    @keyframes MAOWeighListItemOverlayDivAnimation {
      0% {
        top: 100%;
        opacity: 0;
      }

      10% {
        top: 0%;
        opacity: 1;
      }

      90% {
        top: 0%;
        opacity: 1;
      }

      100% {
        top: 100%;
        opacity: 0;
      }
    }

    label,
    span {
      font-size: 12px;
      color: rgb(180, 180, 180);
      text-transform: uppercase;
      position: absolute;
      left: 15px;
      top: 13px;
    }

    span {
      right: 35px;
      left: auto;
    }

    input {
      border: none;
      font-size: 20px;
      color: black;
      height: 100%;
      width: 100px;
      text-align: center;
      margin: 0 auto;
      display: block;
      margin-top: 5px;
    }

    i {
      height: 100%;
      font-size: 18px;
      color: rgb(220, 220, 220);
      overflow: hidden;
      position: absolute;
      right: 8px;
      top: 11px;
    }

    .ErrorDiv {
      font-size: 12px;
      color: red;
      padding: 10px;
      font-weight: 500;
      overflow: hidden;

      ion-icon {
        font-size: 20px;
        padding-right: 5px;
        vertical-align: -6px;
      }
    }
  }

  ion-button {
    margin-bottom: 0;
    margin-top: 0;
  }
}

.btn-action {
  z-index: 2;
  position: relative;
  margin-top: 30px;
  max-width: 400px;
  background: var(--white);
}

#weight-complete-button {
  z-index: 2;
  position: relative;
  margin-bottom: 30px;
  max-width: 400px;
  font-weight: 600;
}

#OversizedItemsDiv {
  background: var(--BlueGray1);
  border-radius: 5px;

  #InnerDiv {
    padding: 10px;
  }

  #CounterDiv {
    border-radius: 5px;
    width: 150px;
    margin: 0 auto;
    text-align: center;
    height: 40px;
    line-height: 32px;
    margin-top: 10px;
    clear: both;
  }

  #CounterDiv ion-icon {
    font-size: 30px;
    height: 35px;
    width: 35px;
    box-shadow: 0 1px 2px rgba(0, 0, 50, 0.35);
    border-radius: 50%;
    color: var(--Orange);
    background: white;
  }

  .Disabled {
    pointer-events: none;
    color: var(--BlueGray3) !important;
    box-shadow: 0 1px 2px rgba(0, 0, 50, 0.2) !important;
  }

  label {
    font-size: 18px;
  }

  ion-note {
    font-size: 14px;
    display: block;
    text-align: center;
    font-weight: 500;
    padding-top: 10px;
  }

  ion-grid {
    padding: 0;
    padding-top: 10px;

    ion-row:first-child {
      color: var(--BlueGray4);

      b {
        font-weight: 600;
      }
    }

    ion-col {
      padding: 0;
      text-align: left;

      ion-icon {
        font-size: 18px;
        vertical-align: -3px;
      }

      .YesIcon {
        color: var(--Green);
      }

      .NoIcon {
        color: Red;
      }
    }
  }
}
