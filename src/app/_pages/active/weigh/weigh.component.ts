import {
  ApplicationRef,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { AngularFireFunctions } from '@angular/fire/functions';
import { AlertController, IonContent, LoadingController } from '@ionic/angular';
import { OrderData } from 'src/app/_interfaces/order-data.interface';
import { VIDEOREFS } from 'src/app/_interfaces/videoRefs';
import { LegacyApiService } from 'src/app/_services/legacy-api.service';
import { StatsigFactoryService } from 'src/app/_services/statsig-factory.service';
import { StatsigService } from 'src/app/_services/statsig.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-weigh',
  templateUrl: './weigh.component.html',
  styleUrls: ['./weigh.component.scss'],
})
export class WeighComponent implements OnInit {
  @Input() OrderData: Partial<OrderData>;
  @Input() editMode = false;

  @Output() editFinished = new EventEmitter<boolean>();

  OpenSection = 1;
  SectionLevel = 1;
  TrainerLockLevel = 1;
  StepButtonClicked = false;
  SudsterFirstOrder = false;
  OversizedItemsOpened = false;
  PuffyNumber = 0;
  AgreeWeight = false;
  MyWeightInput = '';
  WeighList = [''];

  videoRefs = VIDEOREFS;
  disableClick = false;

  recommendedTraining = true;
  private _statsigService: StatsigService;

  constructor(
    private loadingController: LoadingController,
    private alertController: AlertController,
    private cloudFunctions: AngularFireFunctions,
    private content: IonContent,
    private appRef: ApplicationRef,
    private _statsigFactoryService: StatsigFactoryService,
    private apiService: LegacyApiService
  ) {
    this._statsigService = this._statsigFactoryService.getInstance();
  }

  ngOnInit() {
    this._statsigService
      .checkGate(environment.statsig.flags.lpFirstOrderRecommendedTraining)
      .subscribe((showTraining) => {
        this.recommendedTraining = showTraining;
      });

    this.OversizedItemsOpened =
      this.OrderData.LargeItems?.CustomerSelected || false;

    const StepNumberStorage = parseInt(
      localStorage.getItem(this.OrderData.OrderNumber + '-StepNumber')
    );
    if (StepNumberStorage > 0) {
      this.OpenSection = StepNumberStorage;
      this.SectionLevel = StepNumberStorage;
      this.TrainerLockLevel = StepNumberStorage;
    }

    const MyWeightStorage = localStorage.getItem(
      this.OrderData.OrderNumber + '-MyWeight'
    );
    if (MyWeightStorage !== null) {
      this.MyWeightInput = MyWeightStorage;
    }

    const BagWeightsStorage = localStorage.getItem(
      this.OrderData.OrderNumber + '-BagWeights'
    );
    if (BagWeightsStorage !== null) {
      this.WeighList = JSON.parse(BagWeightsStorage);
    }

    // Editing the order Weight.
    if (
      this.OrderData.StatusHistoryInfo &&
      this.OrderData.StatusHistoryInfo.Done &&
      this.editMode
    ) {
      // Depends if there's available the sudster weight.
      if (this.MyWeightInput) {
        this.AgreeWeight = true;
        // Show the bag weights directly.
        this.OpenSection = 3;
        this.SectionLevel = 3;
        this.TrainerLockLevel = 3;
        this.WeighList = this.computePreviousBagWeights(this.MyWeightInput);
      } else {
        // Request the sudster to input his weight to compute existing bag weights
        this.OpenSection = 2;
        this.SectionLevel = 2;
        this.TrainerLockLevel = 2;
      }
      this.PuffyNumber = this.OrderData.StatusHistoryInfo.Done.PuffyItems || 0;
      this.OversizedItemsOpened = this.PuffyNumber > 0;
    }
  }

  private computePreviousBagWeights(myWeight: string) {
    return this.OrderData.StatusHistoryInfo.Done.WeightArray.split(',').map(
      (w) => (parseFloat(w) + parseFloat(myWeight)).toFixed(1)
    );
  }

  ClickStepSection(index) {
    if (!this.StepButtonClicked) {
      if (index <= this.SectionLevel) {
        this.OpenSection = index;
      }
    }
    this.StepButtonClicked = false;
  }

  StepComplete(index) {
    if (index == this.SectionLevel) {
      this.SectionLevel++;
      this.OpenSection++;
      this.TrainerLockLevel = this.SectionLevel;
    } else {
      this.OpenSection = this.SectionLevel;
    }
    this.StepButtonClicked = true;
    localStorage.setItem(
      this.OrderData.OrderNumber + '-StepNumber',
      this.SectionLevel.toString()
    );
  }

  onCancelUpdateWeigh() {
    this.editFinished.next(true);
  }

  WeighComplete() {
    if (this.disableClick) {
      return;
    }
    const loading = this.presentLoading({
      spinner: 'circular',
      message: 'Saving weights-this may take a moment.',
      cssClass: 'circular-spinner',
    });
    this.disableClick = true;
    localStorage.removeItem(this.OrderData.OrderNumber + '-StepNumber');

    const loadingAndDisable = async () => {
      const ld = await loading;
      await ld.dismiss();
      this.disableClick = false;
    };

    this.WeighCompleteRequest().subscribe({
      next: async () => {
        localStorage.removeItem(this.OrderData.OrderNumber + '-MyWeight');
        localStorage.removeItem(this.OrderData.OrderNumber + '-BagWeights');

        await loadingAndDisable();

        if (this.editMode) {
          this.editFinished.next(undefined);
        }
      },
      error: async (err) => {
        localStorage.setItem(
          this.OrderData.OrderNumber + '-StepNumber',
          this.OpenSection.toString()
        );

        await loadingAndDisable();

        await this.presentAlert(
          'System Error',
          err?.message || 'An error occurred while processing your request.'
        );

        if (this.editMode) {
          this.editFinished.next(false);
        }
      },
    });
  }

  async presentLoading(content = {}) {
    const loading = await this.loadingController.create(content);
    await loading.present();
    return loading;
  }

  async presentAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK'],
    });
    await alert.present();
  }

  tutorialComplete() {
    this.TrainerLockLevel++;
    this.appRef.tick();
  }

  MyWeightInputEvent(event) {
    const MyWeightNumber = parseFloat(event.srcElement.value);
    if (MyWeightNumber < 50) {
      this.presentAlert(
        'Enter YOUR Weight',
        "It looks like you're trying to enter the bag's weight. Please first just enter YOUR body weight and the bag's weight will be calculated later."
      );
    } else {
      this.MyWeightInput = event.srcElement.value.toString();
      localStorage.setItem(
        this.OrderData.OrderNumber + '-MyWeight',
        this.MyWeightInput
      );

      if (this.editMode) {
        // uses the info from the OrderData...Done Obj
        this.WeighList = this.computePreviousBagWeights(this.MyWeightInput);
      }
    }
  }

  WeightItemsInputBlur(index, event) {
    this.WeighList[index] = event.srcElement.value;
    localStorage.setItem(
      this.OrderData.OrderNumber + '-BagWeights',
      JSON.stringify(this.WeighList)
    );
  }

  WeightItemsInputFocus(index) {
    this.content.scrollToPoint(0, 130 + index * 50, 200);
  }

  AddWeightLine() {
    this.WeighList.push('');
    setTimeout(() => {
      document
        .getElementById('ListItemInput-' + (this.WeighList.length - 1))!
        .focus();
    }, 300);
  }
  WeightItemRemove(index) {
    this.WeighList.splice(index, 1);
  }
  CalcBagWeight(index) {
    if (this.WeighList[index] == '') {
      return '00.0';
    } else {
      return (
        parseFloat(this.WeighList[index]) - parseFloat(this.MyWeightInput)
      ).toFixed(1);
    }
  }
  GetBagWeightError(index: number): string {
    const rawWeight = this.WeighList[index];
    const bagWeight = parseFloat(this.CalcBagWeight(index));
    const selfPlusBagWeight = parseInt(rawWeight);
    let errorMessage = '';

    if (isNaN(bagWeight)) {
      errorMessage = 'Invalid weight entry. Please enter a valid number.';
    } else if (bagWeight > 20) {
      errorMessage =
        '20 lbs maximum per bag. Please remove some items and weigh again.';
    } else if (bagWeight < 0) {
      errorMessage =
        'You + bag weight is less than just your weight. Please update your body weight and reweigh.';
    } else if (bagWeight < 1) {
      errorMessage = '1 lb minimum per bag. Please try again.';
    } else if (selfPlusBagWeight < 35) {
      errorMessage =
        "Please weigh yourself while holding the bag. It looks like you just entered the bag's weight.";
    } else if (
      !rawWeight.includes('.') &&
      rawWeight !== '' &&
      rawWeight.length >= 3
    ) {
      errorMessage =
        'Decimal point required. Please weigh precisely and add a decimal point (ex: 150.0).';
    }

    return errorMessage;
  }

  ValidateWeightList() {
    let validated = true;
    for (let i = 0; i < this.WeighList.length; i++) {
      if (this.GetBagWeightError(i) != '') {
        validated = false;
      }
      if (this.WeighList[i] == '') {
        validated = false;
      }
    }
    return validated;
  }

  RTDOM_PlurelBagCount() {
    if (this.PuffyNumber === 1) {
      return '';
    } else {
      return 's';
    }
  }

  WeighCompleteRequest() {
    const isNewOrderFlow = this.OrderData.IsNewOrderFlow;

    const CalcWeighArray = [];
    let TotalWeightNumber = 0;
    for (let i = 0; i < this.WeighList.length; i++) {
      CalcWeighArray.push(this.CalcBagWeight(i));
      TotalWeightNumber += parseFloat(this.CalcBagWeight(i));
    }

    if (isNewOrderFlow) {
      if (this.editMode) {
        // call edit weight endpoint to automatically unblock order

        return this.apiService.patch(
          `/${this.OrderData.OrderNumber}/weight`,
          {
            weightArray: CalcWeighArray,
            totalWeight: TotalWeightNumber,
            puffyNumber: this.PuffyNumber,
          },
          {
            baseUrl: `${environment.apiPathV2}/orders`,
          }
        );
      }

      return this.apiService.put(
        `/${this.OrderData.OrderNumber}/status/weigh`,
        {
          weightArray: CalcWeighArray,
          totalWeight: TotalWeightNumber,
          puffyNumber: this.PuffyNumber,
        },
        {
          baseUrl: `${environment.apiPathV2}/orders`,
        }
      );
    }

    return this.cloudFunctions.httpsCallable('SudsterV3_WeighComplete')({
      OrderNumber: this.OrderData.OrderNumber,
      WeightArray: CalcWeighArray,
      TotalWeight: TotalWeightNumber,
      PuffyNumber: this.PuffyNumber,
    });
  }
}
