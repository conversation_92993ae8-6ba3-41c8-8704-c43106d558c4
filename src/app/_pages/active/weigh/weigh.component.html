<app-step
  index="1"
  [disableNext]="!AgreeWeight"
  title="Weigh Accurately"
  [class.Complete]="SectionLevel > 1"
  [class.Open]="OpenSection == 1"
  (click)="ClickStepSection(1)"
  (NextStep)="StepComplete($event)"
>
  <div id="AgreeDiv">
    <h1>
      <ion-icon name="warning-outline"></ion-icon> Make Sure To Weigh Accurately
    </h1>
    <p>Customers may verify the weight of the bags.</p>

    <div id="Checkbox">
      <ion-checkbox
        (ionChange)="AgreeWeight = $event.detail.checked"
      ></ion-checkbox>
      I AGREE TO WEIGH ACCURATELY
    </div>
  </div>
</app-step>

<app-step
  index="2"
  [disableNext]="
    (!MyWeightInput.includes('.') && MyWeightInput.length >= 3) ||
    MyWeightInput.length < 2
  "
  title="Weigh Yourself"
  [class.Complete]="SectionLevel > 2"
  [class.Open]="OpenSection == 2"
  (click)="ClickStepSection(2)"
  (NextStep)="StepComplete($event)"
>
  <p>Enter just <i>your</i> exact weight.</p>

  <input
    id="MyWeightInput"
    [ngModel]="MyWeightInput"
    type="tel"
    mask="000.0"
    placeholder="0 LBS"
    (blur)="MyWeightInputEvent($event)"
  />

  <label
    id="DecimalPointWarning"
    *ngIf="
      !this.MyWeightInput.includes('.') &&
      this.MyWeightInput !== '' &&
      MyWeightInput.length >= 3
    "
  >
    <ion-icon name="alert-circle-outline"></ion-icon> Decimal point required
    (ex: 155<u>.0</u>)
  </label>
</app-step>

<app-step
  index="3"
  [disableNext]="!ValidateWeightList()"
  title="Weigh & Label"
  [class.Complete]="SectionLevel > 3"
  [class.Open]="OpenSection == 3"
  (click)="ClickStepSection(3)"
  (NextStep)="StepComplete($event)"
>
  <p>
    Enter your weight holding each bag and write its calculated weight on the
    label.
  </p>

  <div id="WeighListDiv">
    <div class="ListItem" *ngFor="let item of WeighList; let i = index">
      <label>YOU + BAG</label>
      <input
        [id]="'ListItemInput-' + i"
        [ngModel]="item"
        type="tel"
        mask="000.0"
        placeholder="000.0"
        (blur)="WeightItemsInputBlur(i, $event)"
        (focus)="WeightItemsInputFocus(i)"
      />
      <i *ngIf="i != 0" class="material-icons" (click)="WeightItemRemove(i)"
        >cancel</i
      >
      <span>{{ CalcBagWeight(i) }} lbs</span>
      <div
        class="OverlayDiv"
        [class.OverlayAnimation]="
          CalcBagWeight(i) > 0 && GetBagWeightError(i) === ''
        "
      >
        Label Bag with Weight: <b>{{ CalcBagWeight(i) }} LBS</b>
      </div>
      <div class="ErrorDiv" *ngIf="GetBagWeightError(i) !== ''">
        <ion-icon name="warning-outline"></ion-icon>
        {{ GetBagWeightError(i) }}
      </div>
    </div>

    <ion-button
      id="add-bag-button"
      color="warning"
      expand="full"
      (click)="AddWeightLine()"
      [disabled]="WeighList[WeighList.length - 2] === ''"
      >Add Bag <i class="material-icons">add</i>
    </ion-button>
  </div>

  <div id="OversizedItemsDiv">
    <ion-button
      id="report-puffy-button"
      (click)="OversizedItemsOpened = true"
      *ngIf="OversizedItemsOpened == false"
      size="small"
      fill="clear"
      expand="block"
      >Add Oversized Items</ion-button
    >
    <div id="InnerDiv" *ngIf="OversizedItemsOpened">
      <ion-note
        >How many "oversized" items?<br /><br />(A "oversized" item is defined
        as one that<br />
        required its own wash/dry cycle)</ion-note
      >
      <div id="CounterDiv">
        <ion-icon
          name="remove"
          style="float: left"
          [class.Disabled]="PuffyNumber < 1"
          (click)="PuffyNumber = PuffyNumber - 1"
        >
        </ion-icon>
        <b
          >{{ PuffyNumber }}<span> item{{ RTDOM_PlurelBagCount() }}</span></b
        >
        <ion-icon
          name="add"
          style="float: right"
          [class.Disabled]="PuffyNumber >= 30"
          (click)="PuffyNumber = PuffyNumber + 1"
        ></ion-icon>
      </div>
      <ion-note>You're paid $6 extra for each large item.</ion-note>
      <ion-button
        id="not-sure-button"
        [routerLink]="['/help']"
        style="margin-top: 0px; margin-bottom: -5px"
        size="small"
        fill="clear"
        >Not sure? Ask support.</ion-button
      >
    </div>
  </div>
</app-step>

<app-step
  index="4"
  [disableNext]="TrainerLockLevel <= 4 && SudsterFirstOrder == true"
  title="Seal Bag"
  [class.Complete]="SectionLevel > 4"
  [class.Open]="OpenSection == 4"
  (click)="ClickStepSection(4)"
  (NextStep)="StepComplete($event)"
>
  <app-tutorial
    [required]="TrainerLockLevel <= 4 && SudsterFirstOrder == true"
    (tutorialComplete)="tutorialComplete()"
    quizQuestion="It's advisable but not required to use a colorful ribbon to tie the top of delivery bags."
    [quizOptions]="['True', 'False', 'Need more info']"
    [quizAnswer]="1"
    [videoLength]="videoRefs.inOrder.sealBag.length"
    [videoLink]="videoRefs.inOrder.sealBag.ref"
    [recommendedTraining]="recommendedTraining"
  >
  </app-tutorial>

  <p>
    Seal each bag from rain and dirt by tying it at the top (ideally with a nice
    ribbon). Make sure each bag is completely sealed.
  </p>
</app-step>

<ion-button
  id="weight-complete-button"
  color="warning"
  size="large"
  expand="block"
  [disabled]="(SectionLevel <= 4 || OpenSection <= 4) && !disableClick"
  (click)="WeighComplete()"
  >{{ editMode ? 'Update Weight' : 'Weight Complete'
  }}<ion-icon name="checkmark-done"></ion-icon>
</ion-button>

<ion-button
  id="cancel-edit-button"
  *ngIf="editMode"
  class="btn-action"
  color="medium"
  size="small"
  expand="block"
  fill="outline"
  (click)="onCancelUpdateWeigh()"
  >Cancel edit<ion-icon name="close"></ion-icon>
</ion-button>
