<!-- Notifications -->
<poplin-notification
  *ngIf="showHoldTightNotification"
  [showNotificationIcon]="true"
  [showLinkCTA]="false"
  notificationHeading="HOLD TIGHT"
  text="Before you can deliver, we're confirming payment from your customer. Please check back shortly."
  notificationIcon="credit_card"
  placement="inline"
  size="small"
  color="info"
></poplin-notification>

<poplin-notification
  *ngIf="showDoNotDeliverNotification || DoNotDeliver"
  [showNotificationIcon]="true"
  [showLinkCTA]="false"
  notificationHeading="DO NOT DELIVER"
  [text]="DO_NOT_DELIVER_TEXT"
  notificationIcon="error_filled"
  placement="inline"
  size="small"
  color="error"
></poplin-notification>

<div *ngIf="!DoNotDeliver" class="position-relative">
  <span
    id="SectionVertLine"
    [class.disabled-line]="
      showHoldTightNotification || showDoNotDeliverNotification || DoNotDeliver
    "
  ></span>
  <span
    *ngIf="
      showHoldTightNotification || showDoNotDeliverNotification || DoNotDeliver
    "
    class="disabled-line-lighter"
  ></span>

  <app-step
    title="GO TO DROP-OFF ADDRESS"
    index="1"
    [disable]="
      showHoldTightNotification || showDoNotDeliverNotification || DoNotDeliver
    "
    [disableNext]="
      showHoldTightNotification || showDoNotDeliverNotification || DoNotDeliver
    "
    [class.Complete]="
      SectionLevel > 1 &&
      !(
        showHoldTightNotification ||
        showDoNotDeliverNotification ||
        DoNotDeliver
      )
    "
    [class.Open]="
      OpenSection == 1 &&
      !(
        showHoldTightNotification ||
        showDoNotDeliverNotification ||
        DoNotDeliver
      )
    "
    (click)="ClickStepSection(1)"
    (NextStep)="StepComplete($event)"
  >
    <label (click)="CopyAddress()">{{ address }}</label>

    <ion-button
      id="directions-button"
      fill="outline"
      color="warning"
      [href]="'https://www.google.com/maps?q=' + address"
      target="_blank"
      >Directions
    </ion-button>

    <small>Proceed to next step for drop-off details</small>
  </app-step>
  <app-step
    title="Deliver Bags"
    index="2"
    [class.Complete]="SectionLevel > 2"
    [class.Open]="OpenSection == 2"
    (click)="ClickStepSection(2)"
    (NextStep)="StepComplete($event)"
  >
    <p style="font-size: 17px">
      Unit Number: <b>{{ addressLine2 }}</b
      ><br />
      Delivery Spot: <b>{{ OrderData.PickupSpot.SimpleSpot }}</b
      ><br />
      <span *ngIf="OrderData.PickupSpot.Instructions !== ''"
        >Instructions: <b>{{ OrderData.PickupSpot.Instructions }}</b></span
      ><br />
    </p>

    <ion-button
      id="call-customer-button"
      (click)="CallCustomer()"
      *ngIf="OrderData.PickupSpot.SimpleSpot === 'Call Me'"
      expand="block"
      fill="clear"
      color="warning"
      >Call Customer</ion-button
    >

    <label style="color: var(--BlueGray3)"
      ><i
        >It’s advisable to deliver the bags to the same spot you picked them up
        from.</i
      ></label
    >
  </app-step>

  <app-step
    [disableNext]="BagCount < 1"
    title="Count Bags"
    index="3"
    [class.Complete]="SectionLevel > 3"
    [class.Open]="OpenSection == 3"
    (click)="ClickStepSection(3)"
    (NextStep)="StepComplete($event)"
  >
    <label>How many bags did you deliver?</label>

    <app-count-selector
      id="bag-count-selector"
      (CountChanged)="BagCount = $event"
      [orderData]="OrderData"
      [orderNumber]="OrderData.OrderNumber"
    ></app-count-selector>
  </app-step>

  <app-step
    [disableNext]="TakePicDisabled"
    title="Take A Photo"
    index="4"
    [class.Complete]="SectionLevel > 4"
    [class.Open]="OpenSection == 4"
    (click)="ClickStepSection(4)"
    (NextStep)="StepComplete($event)"
  >
    <label>Take a clear photo of all the bags together.</label>

    <app-photo-upload
      (uploadStarted)="UploadStarted($event)"
      (pictureStored)="PictureStored($event)"
      [savePhotoPath]="'orderPhotos/' + OrderData.OrderNumber"
      [photoSafeUrl]="storedPhotoSafeUrl"
    >
    </app-photo-upload>
  </app-step>

  <ion-button
    [disabled]="
      SectionLevel <= 4 ||
      OpenSection <= 4 ||
      showHoldTightNotification ||
      showDoNotDeliverNotification ||
      DoNotDeliver
    "
    id="delivery-complete-button"
    color="warning"
    size="large"
    expand="block"
    (click)="DeliveryComplete()"
    >Delivery Complete<ion-icon name="checkmark-done"></ion-icon>
  </ion-button>
</div>
