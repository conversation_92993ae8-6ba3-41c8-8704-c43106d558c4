p {
  font-size: 14px;
  line-height: 150%;
  text-align: left;
}

label {
  display: block;
  margin: 10px 0;
  text-align: center;
  font-weight: 600;
  color: var(--BlueGray5);
  font-size: 15px;
}

h1 {
  font-size: 20px;
  font-weight: 400;
  line-height: 150%;
}

small {
  display: block;
  color: var(--BlueGray3);
  font-weight: 600;
  padding: 5px;
}

#delivery-complete-button {
  z-index: 2;
  position: relative;
  margin-top: 30px;
  margin-bottom: 75px;
  max-width: 400px;
  font-weight: 600;
}

#DoNotDeliver {
  text-align: left;
  z-index: 10;
  border-top: solid 3px red;
}

poplin-notification {
  position: relative;
  z-index: 1;
  margin: 0 auto;
  display: block;
  max-width: 342px;
}

#SectionVertLine {
  display: block;
  position: absolute;
  z-index: 1;
  background: var(--BlueGray2);
  height: calc(100% - 220px);
  width: 2px;
  left: 31px;
  top: 165px;

  &.disabled-line {
    height: 195px;
    top: 50px;
  }
}

.disabled-line-lighter {
  background: var(--gray-1000);
  height: 30px;
  bottom: 55px;
  display: block;
  position: absolute;
  z-index: 1;
  width: 2px;
  left: 31px;
}
