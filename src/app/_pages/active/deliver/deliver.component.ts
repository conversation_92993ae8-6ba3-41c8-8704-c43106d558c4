import {
  After<PERSON><PERSON>wInit,
  ApplicationRef,
  Component,
  EventEmitter,
  Inject,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { AngularFireFunctions } from '@angular/fire/functions';
import {
  <PERSON><PERSON><PERSON>ontroller,
  LoadingController,
  ModalController,
  ToastController,
} from '@ionic/angular';

import { SafeUrl } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Clipboard } from '@capacitor/clipboard';
import { Network } from '@capacitor/network';
import Rollbar from 'rollbar';
import {
  PictureTakenEvent,
  UploadStartedEvent,
} from 'src/app/_components/photo-upload/photo-upload.component';
import { OrderData } from 'src/app/_interfaces/order-data.interface';
import { LegacyApiService } from 'src/app/_services/legacy-api.service';
import { MaskCallService } from 'src/app/_services/mask-call.service';
import { PhotoService } from 'src/app/_services/photo.service';
import { PollingService, WeighStatus } from 'src/app/_services/polling.service';
import { RollbarService } from 'src/app/_services/rollbar/rollbar.service';
import { ToastService } from 'src/app/_services/toast.service';
import { environment } from 'src/environments/environment';
import { getAddress } from '../../../_utils/order-utils';
import { HelpLocateComponent } from '../help-locate/help-locate.component';

@Component({
  selector: 'app-deliver',
  templateUrl: './deliver.component.html',
  styleUrls: ['./deliver.component.scss'],
})
export class DeliverComponent implements OnInit, OnDestroy, AfterViewInit {
  readonly DO_NOT_DELIVER_TEXT = `There was an issue processing your customer's payment. We're working to resolve it. In the meantime, do not deliver the laundry. We'll let you when it's resolved. <br><br>If it's not resolved in 48 hours, you'll receive a <a class='link' href='https://poplin-laundry-pro.zendesk.com/hc/en-us/articles/15462489841307-Customer-Payment-Declined' target='_blank'>$10/day holding fee</a> for up to 10 days.`;

  OpenSection = 1;
  SectionLevel = 1;
  TrainerLockLevel = 1;
  StepButtonClicked = false;

  @Input() OrderData: OrderData;
  @Input() editFinished: boolean | undefined;
  @Output() disableEdit = new EventEmitter();

  TakePicDisabled = true;
  BagCount = 0;
  PhotoGPS;

  DoNotDeliver = false;
  connected: boolean = true;
  address = '';
  addressLine2 = '';

  uploadingPhotoId;
  storedPhotoSafeUrl: SafeUrl = '';
  storedPhotoPath: string;

  showHoldTightNotification = false;
  showDoNotDeliverNotification = false;

  constructor(
    public modalController: ModalController,
    private loadingController: LoadingController,
    private cloudFunctions: AngularFireFunctions,
    private alertController: AlertController,
    private maskCall: MaskCallService,
    private toastController: ToastController,
    private router: Router,
    private appRef: ApplicationRef,
    private toastService: ToastService,
    private photoService: PhotoService,
    private apiService: LegacyApiService,
    private pollingService: PollingService,
    @Inject(RollbarService) private rollbar: Rollbar
  ) {}

  ngOnDestroy() {
    this.pollingService.stop();
  }

  ngOnInit() {
    if (
      this.OrderData.PaymentDeclined > 0 &&
      this.OrderData.OrderStatusNumber == 3
    ) {
      this.DoNotDeliver = true;
    }
    if (this.OpenSection > 2) {
      this.OpenSection = 2;
      this.SectionLevel = 2;
      this.TrainerLockLevel = 2;
    }
    this.address =
      typeof this.OrderData.Address === 'string'
        ? this.OrderData.Address
        : this.OrderData.Address['Full'];
    this.addressLine2 = this.OrderData.AddressLine2
      ? this.OrderData.AddressLine2
      : this.OrderData.Address['Line2'];
  }

  ngAfterViewInit() {
    this.startWeightStatusPolling();
  }

  ClickStepSection(index) {
    if (!this.StepButtonClicked) {
      if (index <= this.SectionLevel) {
        this.OpenSection = index;
      }
    }
    this.StepButtonClicked = false;
  }

  StepComplete(index) {
    localStorage.removeItem('HelpPickup');
    if (index == this.SectionLevel) {
      this.SectionLevel++;
      this.OpenSection++;
      this.TrainerLockLevel = this.SectionLevel;
    } else {
      this.OpenSection = this.SectionLevel;
    }
    this.StepButtonClicked = true;
    localStorage.setItem(
      this.OrderData.OrderNumber + '-StepNumber',
      this.SectionLevel.toString()
    );
  }

  async DeliveryComplete() {
    const loading = this.presentLoading();

    const submitDeliverStep = () => {
      localStorage.removeItem(this.OrderData.OrderNumber + '-StepNumber');

      this.deliveryCompleteRequest().subscribe({
        next: async () => {
          localStorage.removeItem(this.OrderData.OrderNumber + '-StepNumber');
          await this.photoService.removeSavedPhoto(
            this.storedPhotoPath,
            this.uploadingPhotoId
          );
          if (this.OrderData.SudsterFirstOrder) {
            this.logFirstOrder(this.OrderData);
          }
          const ld = await loading;
          await ld.dismiss();

          await this.presentAlert(
            'Order Complete!',
            'Congrats on completing this order! Your payment has been sent to your balance and your rank has been updated based on your performance. You will be notified once your customer has rated & tipped you.'
          );
          await this.router.navigate(['/home'], { replaceUrl: true });
        },
        error: async (err) => {
          const ld = await loading;
          await ld.dismiss();

          await this.presentAlert(
            'System Error',
            err?.message || 'An error occurred while processing your request.'
          );
        },
      });
    };

    const WeighedBags = this.OrderData.StatusHistoryInfo.Done.BagCount;
    if (WeighedBags > this.BagCount) {
      loading.then((ld) => ld.dismiss());
      this.presentAlert(
        'Missing Bags',
        `It looks like you're missing some bags. You weighed ${WeighedBags} bags but only delivered ${this.BagCount}. Please deliver the missing bag before continuing.`
      );
      return;
    } else if (WeighedBags < this.BagCount) {
      loading.then((ld) => ld.dismiss());
      this.presentAlert(
        'Wrong Bags',
        `It looks like you delivered someone elses bags. You weighed only ${WeighedBags} bags but have delivered ${this.BagCount}. Please retrieve the extra bags before continuing.`
      );
      return;
    }

    await Network.getStatus()
      .then(async (status) => {
        if (!status.connected) {
          loading.then((ld) => ld.dismiss());
          const message = `A connection glitch interrupted your request. Keep the app open, find a stronger signal, and try again.<br/> Points are not impacted if the first attempt was before the deadline.`;
          this.toastService.errorToast(
            'Lost Connection',
            message.replace('<br/>', '<br/><br/>')
          );
          return;
        } else {
          {
            setTimeout(() => {
              loading.then((ld) => {
                ld.message =
                  'Taking too long? After 10 seconds, try disabling Wi-Fi.';
              });
            }, 3000);

            // If the photo was not uploaded, start the upload
            if (!this.uploadingPhotoId) {
              await this.ResumePhotoUpload();
            } else if (
              this.photoService.photoHasAlreadyBeenUploaded(
                this.uploadingPhotoId
              )
            ) {
              submitDeliverStep();
              return;
            }

            if (this.uploadingPhotoId) {
              const photoUploadState =
                this.photoService.photoUploadState$.subscribe(
                  async (photoState) => {
                    if (!photoState) {
                      try {
                        throw new Error('photoState is null');
                      } catch (e) {
                        this.rollbar.error(e);
                      }

                      loading.then(function (ld) {
                        ld.dismiss();
                      });
                      this.presentAlert(
                        'Upload Error',
                        'An error occurred while uploading the photo. Please try again.'
                      );

                      this.RetakePhoto();

                      return;
                    }

                    const { photoFileId, uploadState } = photoState;

                    if (photoFileId !== this.uploadingPhotoId) return;

                    if (uploadState === 'complete') {
                      submitDeliverStep();
                      photoUploadState.unsubscribe();
                    }

                    if (uploadState === 'error') {
                      loading.then((ld) => ld.dismiss());
                      this.presentAlert(
                        'Upload Error',
                        'An error occurred while uploading the photo. Please try again.'
                      );
                      photoUploadState.unsubscribe();

                      this.RetakePhoto();
                    }
                  }
                );
            }
          }
        }
      })
      .catch((err) => {
        loading.then((ld) => ld.dismiss());
        return err;
      });
  }

  logFirstOrder(orderData: OrderData) {
    const params = {
      af_order_id: orderData.OrderNumber,
      af_customer_user_id: orderData.CustomerID,
      af_price: orderData.TotalPrice * 0.01,
      af_revenue: (orderData.TotalPrice - orderData.TotalEarned) * 0.01,
      af_city: orderData.Zipcode,
      af_projected_revenue: orderData.AuthAmount * 0.01 * 0.25,
      af_lat: orderData.Lat,
      af_long: orderData.Lng,
      af_user_score: 1,
    };
  }

  async presentLoading(message?: string) {
    const loading = await this.loadingController.create({
      message,
    });
    await loading.present();
    return loading;
  }

  async presentAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK'],
    });
    await alert.present();
  }

  async HelpLocateBags() {
    const modal = await this.modalController.create({
      component: HelpLocateComponent,
      componentProps: {
        isNewOrderFlow: this.OrderData.IsNewOrderFlow,
        StreetAddress: getAddress(this.OrderData.Address),
        PickupSpot: this.OrderData.PickupSpot,
        AddressLine2: this.addressLine2,
        OrderNumber: this.OrderData.OrderNumber,
        SudsterName: this.OrderData.SudsterName,
        CustomerFirstName: this.OrderData.CustomerFirstName,
      },
    });
    return await modal.present();
  }

  CallCustomer() {
    this.maskCall.CallCustomer(this.OrderData.OrderNumber);
  }

  CopyAddress() {
    Clipboard.write({
      string: getAddress(this.OrderData.Address, true),
    });
    this.toastController
      .create({
        message: 'Address copied to clipboard',
        duration: 2500,
      })
      .then((toast) => toast.present());
  }

  UploadStarted(event: UploadStartedEvent) {
    if (!this.uploadingPhotoId) {
      this.uploadingPhotoId = event.photoFileId;
    }
  }

  PictureStored(event: PictureTakenEvent) {
    this.TakePicDisabled = false;
    this.PhotoGPS = event.photoLocation;
    this.storedPhotoPath = event.photoLocalPath;
    this.storedPhotoSafeUrl = event.photoLocalUrl;
    this.uploadingPhotoId = null;
    this.ConfirmLocation(
      this.PhotoGPS.latitude,
      this.PhotoGPS.longitude,
      this.PhotoGPS.accuracy
    );
    this.appRef.tick();
  }

  async ConfirmLocation(lat, lng, accuracy) {
    //Check if user is within 5 meters of accuracy bubble.
    if (
      calcCrow(
        this.OrderData.StatusHistoryInfo.Pickup.GPS.latitude,
        this.OrderData.StatusHistoryInfo.Pickup.GPS.longitude,
        lat,
        lng
      ) >
        accuracy + 15 &&
      this.OrderData.StatusHistoryInfo.Pickup.GPS.latitude != 0 &&
      lat != 0
    ) {
      //Outside delivery bubble
      this.presentAlert(
        'Wrong Location?',
        "Based on your location, it looks like you're at the wrong address/delivery-spot. Please double check and make sure to deliver to the same exact spot you picked it up from. If you move the bags, please take a new picture, otherwise you may proceed to the next step."
      );
    }
  }

  async ResumePhotoUpload() {
    if (!this.storedPhotoPath) {
      this.presentAlert('Error', 'Missing Photo');
      return;
    }

    const { fileId } = await this.photoService.uploadPhoto(
      this.storedPhotoPath
    );

    this.uploadingPhotoId = fileId;
  }

  async RetakePhoto() {
    if (this.storedPhotoPath) {
      this.photoService.removeSavedPhoto(
        this.storedPhotoPath,
        this.uploadingPhotoId
      );
    }

    // Take the user to take the photo again
    this.storedPhotoSafeUrl = '';
    this.storedPhotoPath = '';
    this.uploadingPhotoId = null;
    this.SectionLevel = 4;
    this.OpenSection = 4;
    this.TakePicDisabled = true;
  }

  deliveryCompleteRequest() {
    if (this.OrderData.IsNewOrderFlow) {
      return this.apiService.put(
        `/${this.OrderData.OrderNumber}/status/deliver`,
        {
          deliveryGPS: this.PhotoGPS,
          deliveryPhoto: this.uploadingPhotoId,
          bagCount: this.BagCount,
        },
        {
          baseUrl: `${environment.apiPathV2}/orders`,
        }
      );
    }

    return this.cloudFunctions.httpsCallable('SudsterV3_DeliveryComplete')({
      DeliveryGPS: this.PhotoGPS,
      OrderNumber: this.OrderData.OrderNumber,
      DeliveryPhoto: this.uploadingPhotoId,
      BagCount: this.BagCount,
    });
  }

  private startWeightStatusPolling() {
    if (this.OrderData.IsNewOrderFlow) {
      if (typeof this.editFinished === 'undefined') {
        this.showHoldTightNotification = true;
      }

      this.pollingService
        .pollWeightStatus(this.OrderData.OrderNumber)
        .subscribe({
          next: ({ status }) => {
            this.showHoldTightNotification =
              status === WeighStatus.processingPayment ||
              status === WeighStatus.updatingPayment;
            this.showDoNotDeliverNotification =
              status === WeighStatus.paymentFailed ||
              status === WeighStatus.paymentUpdateFailed;

            if (
              this.showHoldTightNotification ||
              this.showDoNotDeliverNotification
            ) {
              this.disableEdit.next(true);
            }

            const pollingStatues = [
              WeighStatus.processingPayment,
              WeighStatus.editingWeight,
              WeighStatus.paymentFailed,
              WeighStatus.updatingPayment,
            ];

            if (!pollingStatues.includes(status)) {
              this.pollingService.stop();
              this.disableEdit.next(false);
            }
          },
          error: (error) => {
            console.error('Error while polling weight status:', error);
            this.pollingService.stop();
          },
        });
    }
  }
}

function calcCrow(lat1: number, lon1: number, lat2: number, lon2: number) {
  const R = 6371; // km
  const dLat = toRad(lat2 - lat1);
  const dLon = toRad(lon2 - lon1);
  const lat_1 = toRad(lat1);
  const lat_2 = toRad(lat2);

  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.sin(dLon / 2) * Math.sin(dLon / 2) * Math.cos(lat_1) * Math.cos(lat_2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const d = R * c;

  function toRad(Value) {
    return (Value * Math.PI) / 180;
  }

  return d * 1000; //Converts km to meters;
}
