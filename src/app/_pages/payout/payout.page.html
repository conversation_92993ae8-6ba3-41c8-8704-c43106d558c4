<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button defaultHref="/"></ion-back-button>
    </ion-buttons>
    <ion-title>Payout</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="ion-padding poplin-theme">
  <ng-container *ngIf="dataFetched">
    <poplin-information-box
      *ngIf="showCaliforniaRestrictions && payoutDeferred"
      iconName="alert-circle"
      type="warning"
    >
      Payout schedule changes will go into effect {{ payoutDeferredDate }}.
    </poplin-information-box>

    <section [class.disabled]="manualPayoutsValue">
      <ion-label class="section-header">
        <h2>Payout Schedule</h2>
        <p>
          Your money, your schedule—pick when and how you get paid.
          <span *ngIf="isInCalifornia && showCaliforniaRestrictions"
            >In your area, payouts are limited to two per month.</span
          >
        </p>
      </ion-label>
    </section>

    <section class="inline-form-item" [class.disabled]="manualPayoutsValue">
      <ion-label class="section-header">
        <h2>Schedule</h2>
      </ion-label>
      <select-with-slide-up
        #scheduleSelect
        [label]="scheduleDataInitialLabel"
        [options]="scheduleData"
        [disabled]="manualPayoutsValue"
        [selectedValue]="currentPayoutSchedule"
        (valueChanged)="onScheduleChange($event)"
      ></select-with-slide-up>
    </section>

    <section class="inline-form-item" [class.disabled]="manualPayoutsValue">
      <ion-label class="section-header">
        <h2>Pay day</h2>
      </ion-label>
      <select-with-slide-up
        [label]="payDataInitialLabel"
        [options]="payDayData"
        [disabled]="manualPayoutsValue"
        [selectedValue]="payDayData?.selectedValue"
        (valueChanged)="onPayDayChange($event)"
      ></select-with-slide-up>
    </section>

    <section class="manual-payout" [class.disabled]="!isManualPayoutsEnabled">
      <div class="inline-form-item">
        <ion-label class="section-header">
          <h2>Manual Payouts</h2>
          <p>Request payouts anytime with on-demand or express options.</p>
          <p *ngIf="!isManualPayoutsEnabled" class="lock-feature">
            <poplin-icon
              color="--blue-450"
              name="lock_filled"
              [size]="20"
            ></poplin-icon>
            Hit Gold status to unlock
          </p>
        </ion-label>

        <ion-toggle
          [checked]="manualPayoutsValue"
          [disabled]="!isManualPayoutsEnabled"
          (ionChange)="onToggleManualPayouts($event)"
        ></ion-toggle>
      </div>
      <poplin-information-box
        *ngIf="manualPayoutsValue"
        iconName="information-circle"
        type="info"
      >
        'Send Payout' is now available below your balance on the 'My money'
        page.
      </poplin-information-box>
    </section>

    <section id="payout-destination" class="poplin-theme">
      <ion-label class="section-header">
        <h2>Payout Destination</h2>
        <p>This is where your payouts will be deposited.</p>
      </ion-label>

      <ion-item lines="none" id="payout-destination-routing">
        <ion-label position="stacked">Routing</ion-label>
        <ion-input [placeholder]="payoutDestinationName"></ion-input>
      </ion-item>

      <ion-item lines="none">
        <ion-label position="stacked">Account Number</ion-label>
        <ion-input [placeholder]="payoutAccountNumber"></ion-input>
      </ion-item>

      <div>
        <ion-button
          id="change-destination-bth"
          class="poplin-button-outline"
          fill="outline"
          size="default"
          [disabled]="true"
          (click)="changeDestination()"
          >Change Destination</ion-button
        >
      </div>
    </section>
  </ng-container>
</ion-content>
