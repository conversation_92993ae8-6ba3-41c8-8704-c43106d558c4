import { CommonModule } from '@angular/common';
import { Component, CUSTOM_ELEMENTS_SCHEMA, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import {
  IonicModule,
  LoadingController,
  ModalController,
} from '@ionic/angular';
import { catchError, finalize, forkJoin, of, take } from 'rxjs';
import {
  SelectWithSlideUpComponent,
  SlideOptions,
} from 'src/app/_components/form/select-with-slide-up/select-with-slide-up.component';
import { GetSudsterDataService } from 'src/app/_services/get-sudster-data.service';
import { LegacyApiService } from 'src/app/_services/legacy-api.service';
import { StatsigFactoryService } from 'src/app/_services/statsig-factory.service';
import { ToastService } from 'src/app/_services/toast.service';
import { IconComponent } from 'src/app/shared/components/icon/icon.component';
import {
  ConfirmationAction,
  ConfirmationModalComponent,
} from 'src/app/shared/components/modals/confirmation-modal/confirmation-modal.component';
import { PoplinInformationBoxComponent } from 'src/app/shared/components/poplin-information-box/poplin-information-box.component';
import { environment } from 'src/environments/environment';
import {
  BIWEEKLY_PAYOUT_DAY_VALUES,
  PAYOUT_SCHEDULE,
  PAYOUT_WEEKDAY_VALUES,
  PayoutUpdateService,
} from './payout-update.service';

export enum BadgeLevel {
  STARTER = 1,
  BRONZE = 2,
  SILVER = 3,
  GOLD = 4,
  DIAMOND = 5,
  PLATINUM = 6,
  EXECUTIVE = 7,
}

const CALIFORNIA_CODE = 'CA';
@Component({
  selector: 'payout',
  standalone: true,
  templateUrl: './payout.page.html',
  styleUrls: ['./payout.page.scss'],
  imports: [
    CommonModule,
    IonicModule,
    SelectWithSlideUpComponent,
    IconComponent,
    PoplinInformationBoxComponent,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class PayoutPage {
  @ViewChild('scheduleSelect')
  scheduleSelectComponent: SelectWithSlideUpComponent<string>;

  currentPayoutSchedule: PAYOUT_SCHEDULE;
  isManualPayoutsEnabled: boolean = false;
  manualPayoutsValue: boolean = true;
  scheduleData: SlideOptions<string> = null;
  payDayData: SlideOptions<string> = null;
  payDataInitialLabel: string = 'Select day';
  scheduleDataInitialLabel: string = 'Choose a schedule';
  hasNotReachBronze: boolean = false;
  isInCalifornia: boolean = false;

  payoutDestinationName: string = 'Bank of America';
  payoutAccountNumber: string = '•••• 1234';
  dataFetched: boolean = false;

  private readonly initialLoadingPromise = this.presentLoading(
    'Loading Payout Settings...'
  );
  payoutCount: number = 0;
  showCaliforniaRestrictions: boolean = false;
  payoutDeferred: boolean = false;
  payoutDeferredDate: string = '';

  constructor(
    private readonly apiService: LegacyApiService,
    private readonly sudsterService: GetSudsterDataService,
    private readonly loadingController: LoadingController,
    private readonly toasterService: ToastService,
    private readonly router: Router,
    private readonly modalController: ModalController,
    private readonly payoutUpdateService: PayoutUpdateService,
    private readonly statsigFactoryService: StatsigFactoryService
  ) {
    const statsigService = this.statsigFactoryService.getInstance();
    forkJoin([
      this.sudsterService.listenForSudster().pipe(take(1)),
      this.apiService.get(`/payments/payouts/schedule`, {
        baseUrl: `${environment.apiPathV2}`,
      }),
      statsigService.checkGate(
        environment.statsig.flags.californiaPayoutsRestrictions
      ),
    ])
      .pipe(
        finalize(() => {
          if (this.initialLoadingPromise !== null) {
            this.initialLoadingPromise.then((loadingRef) => {
              this.dismissLoading(loadingRef);
            });
          }
        }),
        catchError((err) => {
          return of([null, null]);
        })
      )
      .subscribe(
        async ([
          sudster,
          payoutScheduleRes,
          showCaliforniaPayoutsRestrictions,
        ]) => {
          if (!sudster || !payoutScheduleRes) {
            this.toasterService
              .errorToast(
                'Error Loading Payout Settings',
                'Something went wrong while loading your payout settings. Please try again.'
              )
              .subscribe();
            this.router.navigate(['/']);
            return;
          }

          this.showCaliforniaRestrictions = showCaliforniaPayoutsRestrictions;

          const badgeNumber =
            sudster.Ranking?.BadgeNumber ?? BadgeLevel.STARTER;
          this.isManualPayoutsEnabled = badgeNumber >= BadgeLevel.GOLD;
          this.hasNotReachBronze = badgeNumber < BadgeLevel.BRONZE;
          this.isInCalifornia = sudster.State === CALIFORNIA_CODE;

          const payoutResponseData = (await payoutScheduleRes.data) as Record<
            string,
            any
          >;
          this.manualPayoutsValue =
            payoutResponseData.payoutSchedule === PAYOUT_SCHEDULE.MANUAL;
          this.currentPayoutSchedule = payoutResponseData.payoutSchedule;

          this.payoutCount = payoutResponseData.payoutCount ?? 0;
          this.payoutDeferred = payoutResponseData.deferred ?? false;
          this.payoutDeferredDate = this.getPayoutDeferredDate();

          this.payDayData = this.getPayDayData(this.currentPayoutSchedule);
          if (!this.manualPayoutsValue) {
            const previousPayDayData = this.getPayDayValue(payoutResponseData);
            if (previousPayDayData) {
              this.payDayData.selectedValue = previousPayDayData;
            }
          }
          this.scheduleData = this.getFullScheduleData(
            this.currentPayoutSchedule,
            this.payDayData?.selectedValue
          );
          this.dataFetched = true;
        }
      );
  }

  getPayoutDeferredDate(): string {
    const nextMonth = new Date();
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    const firstDayOfNextMonth = new Date(
      nextMonth.getFullYear(),
      nextMonth.getMonth(),
      1
    );
    const monthName = firstDayOfNextMonth.toLocaleString('en-US', {
      month: 'long',
    });
    return `${monthName} 1st`;
  }

  onScheduleChange($event: any) {
    if (!$event.firstSelection?.value) {
      return;
    }

    this.payDayData = this.getPayDayData($event.firstSelection.value);
    this.currentPayoutSchedule = $event.firstSelection.value;

    if ($event.secondSelection?.value) {
      this.payDayData.selectedValue = $event.secondSelection.value;
    }
    this.savePayoutInformation();
  }

  onPayDayChange($event: any) {
    this.payDayData.selectedValue = $event.firstSelection.value;
    this.savePayoutInformation();
  }

  private getPayDayValue(payoutResponseData: Record<string, any>) {
    switch (payoutResponseData.payoutSchedule) {
      case PAYOUT_SCHEDULE.MONTHLY:
        return payoutResponseData.payoutMonthDay;
      case PAYOUT_SCHEDULE.BIWEEKLY:
        return payoutResponseData.payoutDays;
      case PAYOUT_SCHEDULE.WEEKLY:
        return payoutResponseData.payoutWeekday;
      default:
        return null;
    }
  }

  private async savePayoutInformation() {
    let payload: {
      payoutSchedule: string;
      payoutMonthDay?: string;
      payoutDays?: string;
      payoutWeekday?: string;
    } = {
      payoutSchedule: PAYOUT_SCHEDULE.WEEKLY,
    };

    if (this.isManualPayoutsEnabled && this.manualPayoutsValue) {
      payload.payoutSchedule = PAYOUT_SCHEDULE.MANUAL;
    } else {
      switch (this.currentPayoutSchedule) {
        case PAYOUT_SCHEDULE.MONTHLY:
          payload = {
            payoutSchedule: PAYOUT_SCHEDULE.MONTHLY,
            payoutMonthDay: this.payDayData.selectedValue,
          };
          break;
        case PAYOUT_SCHEDULE.BIWEEKLY:
          payload = {
            payoutSchedule: PAYOUT_SCHEDULE.BIWEEKLY,
            payoutDays: this.payDayData.selectedValue,
          };
          break;
        case PAYOUT_SCHEDULE.WEEKLY:
          payload = {
            payoutSchedule: PAYOUT_SCHEDULE.WEEKLY,
            payoutWeekday: this.payDayData.selectedValue,
          };
          break;
      }
    }

    const loadingRef = await this.presentLoading();
    this.apiService
      .put(`/payments/payouts/schedule`, payload, {
        baseUrl: `${environment.apiPathV2}`,
      })
      .pipe(finalize(() => this.dismissLoading(loadingRef)))
      .subscribe({
        next: () => {
          this.payoutUpdateService.notifyPayoutUpdate({
            type: payload.payoutSchedule as PAYOUT_SCHEDULE,
            weekDay:
              this.currentPayoutSchedule === PAYOUT_SCHEDULE.WEEKLY
                ? (this.payDayData.selectedValue as PAYOUT_WEEKDAY_VALUES)
                : undefined,
            monthDay:
              this.currentPayoutSchedule === PAYOUT_SCHEDULE.MONTHLY
                ? this.payDayData.selectedValue
                : undefined,
            biweeklyDay:
              this.currentPayoutSchedule === PAYOUT_SCHEDULE.BIWEEKLY
                ? (this.payDayData.selectedValue as BIWEEKLY_PAYOUT_DAY_VALUES)
                : undefined,
          });

          this.toasterService
            .successToast('Payout Settings', 'Changes saved successfully')
            .subscribe();
        },
        error: () => {
          this.toasterService
            .errorToast(
              'Error Saving',
              'Something went wrong while saving your changes. Please try again.'
            )
            .subscribe();
        },
      });
  }

  async onToggleManualPayouts(event: any) {
    const newValue = event.detail.checked;
    const isTogglingOnManualPayouts = !this.manualPayoutsValue && newValue;

    const modal = await this.modalController.create({
      component: ConfirmationModalComponent,
      componentProps: {
        title: isTogglingOnManualPayouts
          ? 'Turn on Manual Payouts'
          : 'Turn off Manual Payouts',
        content: isTogglingOnManualPayouts
          ? 'Switching to Manual Payouts will cancel your scheduled payouts. You’ll need to request payouts manually, up to 2 times per month, with on-demand or express options.'
          : 'We’ll help you set up automatic bi-monthly payouts on the dates that work best for you.',
        confirmButtonText: 'CONFIRM & SWITCH',
        cancelButtonText: 'CANCEL',
      },
      cssClass: 'confirmation-modal',
      backdropDismiss: false,
    });

    this.manualPayoutsValue = newValue;

    modal.onDidDismiss().then((modalResult) => {
      if (modalResult.data === ConfirmationAction.CONFIRM) {
        if (this.manualPayoutsValue) {
          this.currentPayoutSchedule = PAYOUT_SCHEDULE.MANUAL;
          this.savePayoutInformation();
        } else {
          // Programmatically triggers the schedule selection slide up
          this.currentPayoutSchedule = PAYOUT_SCHEDULE.BIWEEKLY;
          this.scheduleSelectComponent.selectedValue = PAYOUT_SCHEDULE.BIWEEKLY;
          this.scheduleSelectComponent.disabled = false;
          this.scheduleSelectComponent.openSlideUp();
          this.scheduleSelectComponent.valueChanged
            .pipe(take(1))
            .subscribe((slideModalResult) => {
              if (slideModalResult.modalWasDismissed) {
                // revert back option
                this.manualPayoutsValue = !newValue;
              }
            });
        }
      } else {
        this.manualPayoutsValue = !newValue;
      }
    });
    modal.present();
  }

  changeDestination() {
    // To complete in different ticket
  }

  getFullScheduleData(
    previousSelectedSchedule: string,
    previousSelectedPayDay: string
  ) {
    const scheduleData = this.getScheduleData(this.isInCalifornia);
    const monthlyPayDayData = this.getMonthlyPayDayData();
    const biMonthlyPayDayData = this.getBiMonthlyPayDayData();
    scheduleData.options[0].linkedSelectOptions = monthlyPayDayData;
    scheduleData.options[1].linkedSelectOptions = biMonthlyPayDayData;

    if (!this.isInCalifornia) {
      const weeklyPayDayData = this.getWeeklyPayDayData();
      scheduleData.options[2].linkedSelectOptions = weeklyPayDayData;
    }

    if (previousSelectedSchedule && previousSelectedPayDay) {
      const getIndexOfLinkedOptions = (schedule: string): number => {
        if (schedule === PAYOUT_SCHEDULE.WEEKLY) return 2;
        if (schedule === PAYOUT_SCHEDULE.BIWEEKLY) return 1;
        return 0;
      };

      const indexOfLinkedOptions = getIndexOfLinkedOptions(
        previousSelectedSchedule
      );
      scheduleData.options[
        indexOfLinkedOptions
      ].linkedSelectOptions.selectedValue = previousSelectedPayDay;
    }

    return scheduleData;
  }

  private getScheduleData(isInCalifornia: boolean): SlideOptions<string> {
    const scheduleData = {
      sectionTitle: 'Choose a Schedule',
      sectionDescription: this.hasNotReachBronze
        ? 'Hit Bronze status to unlock Schedules'
        : undefined,
      sectionDescriptionIcon: 'lock_filled',
      selectedValue: this.isInCalifornia
        ? PAYOUT_SCHEDULE.BIWEEKLY
        : PAYOUT_SCHEDULE.WEEKLY,
      options: [
        {
          value: PAYOUT_SCHEDULE.MONTHLY,
          label: 'Monthly',
          description: 'Get paid once a month on the 1st, 15th, or 28th.',
          disabled: this.hasNotReachBronze,
        },
        {
          value: PAYOUT_SCHEDULE.BIWEEKLY,
          label: 'Bi-Monthly',
          description: 'Get paid twice a month on the days you choose.',
          disabled: !this.isInCalifornia && this.hasNotReachBronze,
        },
      ],
    };

    if (!isInCalifornia) {
      scheduleData.options.push({
        value: PAYOUT_SCHEDULE.WEEKLY,
        label: 'Weekly',
        description: 'Get paid every week on the day of your choice.',
        disabled: this.isInCalifornia,
      });
    }

    return scheduleData;
  }

  private getPayDayData(scheduleValue: string): SlideOptions<string> {
    let payDayData: SlideOptions<string>;

    switch (scheduleValue) {
      case PAYOUT_SCHEDULE.MONTHLY:
        payDayData = this.getMonthlyPayDayData();
        break;
      case PAYOUT_SCHEDULE.BIWEEKLY:
        payDayData = this.getBiMonthlyPayDayData();
        break;
      case PAYOUT_SCHEDULE.WEEKLY:
        payDayData = this.getWeeklyPayDayData();
        break;
      default:
        payDayData = null;
    }

    return payDayData;
  }

  private getMonthlyPayDayData() {
    const payDaySlideOptions: SlideOptions<string> = {
      sectionTitle: 'Select a Pay Day',
      sectionDescription: 'Choose when you want to get paid each month',
      sectionDescriptionIcon: '',
      selectedValue: null,
      options: [
        {
          value: '1',
          label: '1st of the month',
          description: '',
          disabled: false,
        },
        {
          value: '15',
          label: '15th of the month',
          description: '',
          disabled: false,
        },
        {
          value: '28',
          label: '28th of the month',
          description: '',
          disabled: false,
        },
      ],
    };

    return payDaySlideOptions;
  }

  private getBiMonthlyPayDayData() {
    const payDaySlideOptions: SlideOptions<string> = {
      sectionTitle: 'Select Pay Days',
      sectionDescription: 'Choose when you want to get paid twice a month',
      sectionDescriptionIcon: '',
      selectedValue: null,
      options: [
        {
          value: BIWEEKLY_PAYOUT_DAY_VALUES.FIRST_AND_FIFTEENTH,
          label: '1st and 15th of the month',
          description: '',
          disabled: false,
        },
        {
          value: BIWEEKLY_PAYOUT_DAY_VALUES.FIFTEENTH_AND_TWENTY_EIGHTH,
          label: '15th and 28th of the month',
          description: '',
          disabled: false,
        },
      ],
    };

    return payDaySlideOptions;
  }

  private getWeeklyPayDayData() {
    const payDaySlideOptions: SlideOptions<string> = {
      sectionTitle: 'Select a Pay Day',
      sectionDescription: this.hasNotReachBronze
        ? 'Hit Bronze status to unlock Pay Days'
        : undefined,
      sectionDescriptionIcon: 'lock_filled',
      selectedValue: this.hasNotReachBronze
        ? PAYOUT_WEEKDAY_VALUES.FRIDAY
        : null,
      options: [
        {
          value: PAYOUT_WEEKDAY_VALUES.MONDAY,
          label: 'Monday',
          description: '',
          disabled: this.hasNotReachBronze,
        },
        {
          value: PAYOUT_WEEKDAY_VALUES.TUESDAY,
          label: 'Tuesday',
          description: '',
          disabled: this.hasNotReachBronze,
        },
        {
          value: PAYOUT_WEEKDAY_VALUES.WEDNESDAY,
          label: 'Wednesday',
          description: '',
          disabled: this.hasNotReachBronze,
        },
        {
          value: PAYOUT_WEEKDAY_VALUES.THURSDAY,
          label: 'Thursday',
          description: '',
          disabled: this.hasNotReachBronze,
        },
        {
          value: PAYOUT_WEEKDAY_VALUES.FRIDAY,
          label: 'Friday',
          description: '',
          disabled: false,
        },
      ],
    };

    return payDaySlideOptions;
  }

  private async presentLoading(message = undefined) {
    const loading = await this.loadingController?.create({
      spinner: 'circular',
      message,
      cssClass: 'circular-spinner',
    });
    loading.present();
    return loading;
  }

  private dismissLoading(loadingRef: HTMLIonLoadingElement): void {
    if (loadingRef) {
      loadingRef.dismiss();
    } else {
      console.error('Loading reference not found');
    }
  }
}
