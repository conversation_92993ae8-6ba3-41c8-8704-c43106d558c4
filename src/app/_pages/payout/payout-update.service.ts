import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export enum PAYOUT_SCHEDULE {
  MANUAL = 'manual',
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  BIWEEKLY = 'biweekly',
}

export enum PAYOUT_WEEKDAY_VALUES {
  MONDAY = 'monday',
  TUESDAY = 'tuesday',
  WEDNESDAY = 'wednesday',
  THURSDAY = 'thursday',
  FRIDAY = 'friday',
  SATURDAY = 'saturday',
  SUNDAY = 'sunday',
}

export enum BIWEEKLY_PAYOUT_DAY_VALUES {
  FIRST_AND_FIFTEENTH = '1-15',
  FIFTEENTH_AND_TWENTY_EIGHTH = '16-28',
}

export interface PayoutUpdate {
  type: PAYOUT_SCHEDULE;
  weekDay?: PAYOUT_WEEKDAY_VALUES;
  monthDay?: string;
  biweeklyDay?: BIWEEKLY_PAYOUT_DAY_VALUES;
}

@Injectable({
  providedIn: 'root',
})
export class PayoutUpdateService {
  private readonly payoutUpdateSubject =
    new BehaviorSubject<PayoutUpdate | null>(null);
  public payoutUpdate$: Observable<PayoutUpdate | null> =
    this.payoutUpdateSubject.asObservable();

  /**
   * Notify subscribers about a payout schedule updates
   * @param update
   */
  notifyPayoutUpdate(update: PayoutUpdate): void {
    this.payoutUpdateSubject.next(update);
  }

  clearPayoutUpdate(): void {
    this.payoutUpdateSubject.next(null);
  }
}
