@import 'type-mixins';
@import 'tokens';

:host {
  width: 100%;
  justify-self: center;
  max-width: 440px;
}

ion-header {
  box-shadow: none;
}

section {
  margin: 24px 0;

  &.manual-payout {
    margin: 32px 0;
  }

  &:first-child {
    margin-top: 0;
  }
}

#payout-destination-routing {
  margin-top: 16px;
}

.inline-form-item {
  display: flex;
  align-items: center;
  justify-content: space-between;

  ion-label {
    margin-right: 30px;
  }
}

.lock-feature {
  color: var(--form-fields-content-message-help);
  margin-top: 6px;

  poplin-icon {
    margin-right: 4px;
    display: inline-block !important;
    line-height: 1.4;
    vertical-align: sub;
  }
}

.poplin-button-outline {
  --border-color: var(--Button-Color-Primary-main);
  --border-radius: var(--Button-Radius-radius-square);
  --border-width: 1px;
  font-size: 12px;
  font-weight: 500;
  line-height: 16px;
  letter-spacing: 1.3px;
  box-shadow: 0px 4px 24px 0px rgba(0, 0, 0, 0.04);
}

.poplin-theme {
  ion-item {
    --border-color: var(--Form-Fields-Border-readonly);
    --border-radius: var(--Button-Radius-radius-square, 8px);
    --border-width: 1px;
    --inner-padding-top: 0px;
    --padding-bottom: 0px;
    background: var(--Form-Fields-Background-readonly);
    line-height: 13px;
    margin-bottom: 12px;
    font-size: 16px;
  }

  ion-input {
    --padding-bottom: 2px;
  }

  ion-button.poplin-button-outline {
    width: 100%;
    max-width: 360px;
  }

  .section-header {
    h2 {
      color: var(--color-content-primary);
      @include m-alt-head;
    }

    p {
      color: var(--color-content-alt);
      @include r-body;
    }
  }

  .disabled {
    h2 {
      color: var(--color-content-med-contrast);
    }
  }
}
