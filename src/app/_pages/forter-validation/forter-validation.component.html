<ion-content class="validation-wrapper">
  <i class="material-icons">security</i>

  <h1 *ngIf="!AccountRejectedForterReason">
    {{ AccountRejected ? 'Access Denied' : 'Access Granted' }}
  </h1>
  <h1 *ngIf="AccountRejectedForterReason && AccountRejectedForterReason !== ''">
    Validation in Progress
  </h1>

  <p
    class="rejected-reason"
    *ngIf="AccountRejected && AccountRejectedForterReason === ''"
  >
    {{ RejectedReason }}
  </p>
  <p *ngIf="AccountRejectedForterReason && AccountRejectedForterReason !== ''">
    Account currently in validation. Check back later.
  </p>

  <ion-button
    id="contact-button"
    href="https://poplin-laundry-pro.zendesk.com/hc/en-us"
    size="small"
    expand="block"
    fill="clear"
    >Contact Us</ion-button
  >
</ion-content>
