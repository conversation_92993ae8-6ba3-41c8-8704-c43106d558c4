import { Component, OnInit } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/auth';
import { AngularFirestore } from '@angular/fire/firestore';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { SudsterData } from 'src/app/_interfaces/sudster-data.interface';
import { AuthidService } from 'src/app/_services/authid.service';

@UntilDestroy()
@Component({
  selector: 'forter-validation',
  templateUrl: './forter-validation.component.html',
  styleUrls: ['./forter-validation.component.scss'],
})
export class ForterValidationComponent implements OnInit {
  UserID = this.AuthID.getID();

  AccountRejected: boolean;
  RejectedReason = '';
  AccountRejectedForterReason: string;

  constructor(
    public afAuth: AngularFireAuth,
    public firestore: AngularFirestore,
    private AuthID: AuthidService
  ) {}

  ngOnInit() {
    this.firestore
      .doc<SudsterData>(`Sudsters/${this.UserID}`)
      .valueChanges()
      .pipe(untilDestroyed(this))
      .subscribe((doc) => {
        this.AccountRejected = doc.AccountRejected;
        this.RejectedReason = doc.AccountRejectedDetails;
        this.AccountRejectedForterReason = doc.AccountRejectedForterReason
          ? doc.AccountRejectedForterReason
          : '';
      });
  }
}
