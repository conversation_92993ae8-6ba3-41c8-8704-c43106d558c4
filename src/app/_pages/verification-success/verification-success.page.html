<ion-header>
  <ion-row>
    <ion-col size="11">
      <ion-title>Security Check </ion-title>
    </ion-col>
    <ion-col size="1">
      <i class="material-icons-outlined help-icon" (click)="contactSupport()"
        >help_outline</i
      >
    </ion-col>
  </ion-row>
</ion-header>

<div class="verification-success-container">
  <div class="size-container">
    <div class="icon">
      <i class="material-icons-outlined check-circle">done</i>
    </div>
    <h2 class="title">Your Security Check is Complete</h2>
    <div class="text">
      <p *ngIf="backgroundCheckActive && IdVerificationActive">
        Identity checked, background as clean as fresh laundry! Now you’re ready
        to accept those orders and start your ascent on the Laundry Pro
        leaderboard.
      </p>
    </div>

    <div class="continue-container">
      <button
        id="continue-button"
        class="continue-button"
        (click)="acceptOrder()"
        expand="block"
        [disabled]="loading"
        color="primary"
      >
        View Available Orders
      </button>
    </div>
  </div>
  <div class="timer-container" *ngIf="timer && timeLeft">
    <div class="timer-text">
      Order reserved, <span id="timer"></span> minutes remaining
    </div>
  </div>
</div>
