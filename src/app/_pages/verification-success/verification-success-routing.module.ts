import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { NgxMaskModule } from 'ngx-mask';

import { IonicModule } from '@ionic/angular';

import { VerificationSuccessPage } from './verification-success.page';

const routes: Routes = [
  {
    path: '',
    component: VerificationSuccessPage,
  },
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes),
    ReactiveFormsModule,
    NgxMaskModule.forRoot(),
  ],
  exports: [RouterModule],
})
export class VerificationSuccessPageRoutingModule {}
