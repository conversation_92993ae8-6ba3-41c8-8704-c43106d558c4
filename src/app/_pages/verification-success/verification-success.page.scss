ion-header {
  height: 60px;
  margin-top: env(safe-area-inset-top);
}
.verification-success-container {
  top: env(safe-area-inset-top);
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  width: 100%;
  background-color: var(--BlueGray1);
}

.size-container {
  margin-top: 40px;
  max-width: 85%;
  min-width: 300px;
  width: 400px;
  background-color: white;
  border-radius: 5px;
  -webkit-box-shadow: 0px 0px 6px 2px rgba(240, 235, 240, 1);
  -moz-box-shadow: 0px 0px 6px 2px rgba(240, 235, 240, 1);
  box-shadow: 0px 0px 6px 2px rgba(240, 235, 240, 1);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
}

.icon {
  background-color: var(--GreenTint);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 10px;
}

.check-circle {
  font-size: 40px;
  font-weight: 900;
  color: White;
}

.title {
  display: flex;
  flex-direction: column;
  align-self: stretch;
  color: var(--content-primary, #000);
  text-align: center;

  /* Heading/Small */
  font-family: 'PitchSans-Medium', 'Helvetica Neue', Helvetica, Arial,
    sans-serif;
  font-size: 24px;
  font-style: normal;
  font-weight: 500;
  line-height: 120%;
  letter-spacing: -0.72px;
}

.text {
  display: flex;
  flex-direction: column;
  align-self: stretch;
  color: var(--content-primary, #000);

  /* Body Text/Small */
  font-family: 'Fakt-Normal' sans-serif;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 140%;
  margin-left: 10px;
}

.continue-container {
  width: 100%;
  margin-bottom: 10px;
}

.timer-container {
  -webkit-box-shadow: 0px 4px 2px 0px rgba(240, 235, 240, 0.54);
  -moz-box-shadow: 0px 4px 2px 0px rgba(240, 235, 240, 0.54);
  box-shadow: 0px 4px 2px 0px rgba(240, 235, 240, 0.54);
  background-color: white;
  border-radius: 50px;
  width: 230px;
  min-width: 230px;
  margin-top: 20px;
  padding: 3px;
}

.timer-text {
  display: flex;
  justify-content: center;
  gap: 5px;
  font-size: 12px;
}
.help-icon {
  position: fixed;
  right: 25px;
}
.continue-button {
  display: flex;
  width: 296px;
  min-height: 48px;
  padding: var(--button-padding-vertical-default, 6px)
    var(--button-padding-horizontal-default, 8px);
  justify-content: center;
  align-items: center;
  border-radius: var(--button-radius-radius-square, 8px);
  background: var(--poplin-pink, #ff6289);

  /* Light/Basic Drop Shadow */
  box-shadow: 0px 4px 24px 0px rgba(0, 0, 0, 0.04);
  color: var(--button-color-primary-alt, #fff);
  text-align: center;

  /* Aux Text/M */
  font-family: 'PitchSans-Medium', 'Helvetica Neue', sans-serif;
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 20px;
  letter-spacing: 1.6px;
  text-transform: uppercase;
  margin: auto;
}
