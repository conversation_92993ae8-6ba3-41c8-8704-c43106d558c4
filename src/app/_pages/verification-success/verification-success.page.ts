import { Component, OnInit } from '@angular/core';
import { AngularFirestore } from '@angular/fire/firestore';
import { Router } from '@angular/router';
import {
  AlertController,
  LoadingController,
  ModalController,
} from '@ionic/angular';
import { untilDestroyed } from '@ngneat/until-destroy';
import { SudsterData } from 'src/app/_interfaces/sudster-data.interface';
import { LegacyApiService } from 'src/app/_services/legacy-api.service';
import { AuthidService } from 'src/app/_services/authid.service';
import { trackEvent } from 'src/app/_utils/track-event';
import { GreetingComponent } from '../home/<USER>/greeting.component';

@Component({
  selector: 'app-verification-success',
  templateUrl: './verification-success.page.html',
  styleUrls: ['./verification-success.page.scss'],
})
export class VerificationSuccessPage implements OnInit {
  IdVerification;
  IdVerificationError;
  IdVerificationDeadline;
  Sudster: SudsterData;
  backgroundCheckActive = true;
  IdVerificationActive = true;
  OrderNumber: string;
  UserID = this.AuthID.getID();

  timer = false;
  loading = false;
  timeLeft: boolean;

  constructor(
    private firestore: AngularFirestore,
    private AuthID: AuthidService,
    private apiService: LegacyApiService,
    private router: Router,
    private alertController: AlertController,
    private loadingController: LoadingController,
    private modalController: ModalController
  ) {}

  ngOnInit() {
    this.firestore
      .doc<SudsterData>(`Sudsters/${this.UserID}`)
      .valueChanges()
      .pipe(untilDestroyed(this))
      .subscribe((doc) => {
        const now = new Date().getTime();
        this.Sudster = doc;
        this.IdVerification = doc.IdVerification;
        this.IdVerificationError = doc.IdVerificationError;
        this.IdVerificationDeadline = doc.IdVerificationDeadline;
        this.countdownTimer(doc.IdVerificationDeadline);
        this.timeLeft = doc.IdVerificationDeadline > now;
        trackEvent({
          eventData: {
            event: 'LPSecurityCheck_Approved',
            userId: this.UserID,
            orderId: this.Sudster?.ReservedOrderId || '',
          },
        });
      });
  }

  async acceptOrder() {
    const loading = this.presentLoading();
    try {
      if (this.Sudster.ReservedOrderId) {
        this.OrderNumber = this.Sudster.ReservedOrderId;
        const body = {
          sudsterId: this.UserID,
          orderNumber: this.OrderNumber,
          acceptedExtension: false,
        };

        const apiResponse = await this.apiService
          .post('UpdateOrder/v1/accept', body)
          .toPromise()
          .catch((err) => {
            return err;
          });
        const response = apiResponse.rawResponse;
        const responseInJson = await apiResponse.data;
        switch (response.status) {
          case 200:
            loading.then((ld) => ld.dismiss());
            this.presentOrderGreeting();
            this.router.navigate(['/active', { id: this.OrderNumber }], {
              replaceUrl: true,
            });
            break;
          default:
            loading.then((ld) => ld.dismiss());
            this.presentAlert('Error', responseInJson.message);
            this.router.navigate(['/home'], { replaceUrl: true });
            break;
        }
        return response;
      } else {
        this.apiService
          .patch('UpdateUserDoc/v1/account/verify/idv', {
            collection: 'Sudsters',
          })
          .toPromise()
          .then(() => {
            loading.then((ld) => ld.dismiss());

            this.router.navigate(['/home'], { replaceUrl: true });
          })
          .catch((err) => {
            return err;
          });
      }
    } catch (error) {
      this.loading = false;
      return this.presentAlert('An error occurred', error);
    }
  }

  countdownTimer(deadline) {
    this.timer = true;

    const x = setInterval(function () {
      const now = new Date().getTime();
      const t = deadline - now;
      const minutes = Math.floor((t % (1000 * 60 * 60)) / (1000 * 60));
      let seconds: number | string = Math.floor((t % (1000 * 60)) / 1000);
      if (seconds < 10) {
        seconds = '0' + seconds;
      }
      const timer = document.getElementById('timer');
      if (!timer) {
        clearInterval(x);
        return;
      }
      timer.innerHTML = minutes + ':' + seconds;
      if (t < 0) {
        clearInterval(x);
        timer.innerHTML = '0';
      }
    }, 1000);
  }

  async presentAlert(
    header: string,
    message: string
  ): Promise<HTMLIonAlertElement> {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK'],
    });
    await alert.present();
    return alert;
  }
  async presentLoading(): Promise<HTMLIonLoadingElement> {
    const loading = await this.loadingController.create({});
    await loading.present();
    return loading;
  }
  async presentOrderGreeting(): Promise<HTMLIonModalElement> {
    const modal = await this.modalController.create({
      component: GreetingComponent,
      cssClass: 'PopupModal',
      componentProps: {
        SudsterFirstName: this.Sudster.FirstName,
        OrderNumber: this.Sudster.ReservedOrderId,
      },
      backdropDismiss: false,
    });
    await modal.present();
    return modal;
  }
  contactSupport() {
    window.open('https://poplin-laundry-pro.zendesk.com/hc/en-us');
  }
}
