import { Component, OnInit } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/auth';
import { ActivatedRoute, Router } from '@angular/router';
import { AlertController, LoadingController } from '@ionic/angular';

@Component({
  selector: 'app-adminlogin',
  templateUrl: './adminlogin.page.html',
  styleUrls: ['./adminlogin.page.scss'],
})
export class AdminloginPage implements OnInit {
  isLoading: HTMLIonLoadingElement;

  constructor(
    private route: ActivatedRoute,
    private afAuth: AngularFireAuth,
    private router: Router,
    private loadingController: LoadingController,
    private alertController: AlertController
  ) {}

  ngOnInit() {
    this.AdminLogin();
  }

  async AdminLogin() {
    const adminToken = this.route.snapshot.paramMap.get('token');

    if (adminToken) {
      this.presentLoading().then(async () => {
        await this.afAuth
          .signOut()
          .then(async () => {
            await this.afAuth
              .signInWithCustomToken(adminToken)
              .then(() => {
                this.isLoading.dismiss();
                this.router.navigate(['']);
              })
              .catch((e) => {
                this.isLoading.dismiss();
                switch (e.code) {
                  case 'auth/invalid-custom-token':
                    this.presentAlertMessage(
                      'Login Error',
                      'Provided token has expired, please use admin app for new one'
                    );
                    break;
                  case 'auth/user-disabled': {
                    this.presentAlertMessage(
                      'Log in Denied',
                      'This account has been disabled from logging into Poplin.'
                    );
                    break;
                  }
                  default:
                    this.presentAlertMessage(
                      'Login Error',
                      `Unexpected issue with login ${e.code}`
                    );
                }
              });
          })
          .catch(() => {
            this.isLoading.dismiss();
            this.presentAlertMessage(
              'Login Failure',
              'Unexpected failure, please try again later'
            );
          });
      });
    }
  }

  async presentLoading() {
    this.isLoading = await this.loadingController.create({
      translucent: true,
      animated: true,
      showBackdrop: true,
    });
    await this.isLoading.present();
  }

  async presentAlertMessage(Title: string, Message: string) {
    const alert = await this.alertController.create({
      header: Title,
      message: Message,
      buttons: ['OK'],
    });

    await alert.present();
  }
}
