ion-segment {
  width: 90%;
  margin: 20px auto;
}

ion-content {
  --padding-bottom: 50px;
}

ion-card {
  margin-top: 20px;

  img {
    height: 200px;
    width: 100%;
    object-fit: cover;
    background: var(--BlueGray1);
  }

  ion-button {
    position: absolute;
    right: 10px;
    bottom: 0px;
  }

  ion-card-content {
    padding-bottom: 35px;
  }

  ion-card-subtitle {
    text-transform: uppercase;
    padding-bottom: 5px;

    ion-chip {
      background: var(--Orange);
      color: white;
      font-weight: 900;
      letter-spacing: 1px;
      height: 25px;
      margin: 0;
      margin-right: 5px;
      vertical-align: 0px;
    }
  }
}

.notice {
  text-align: center;
  display: block;
  color: var(--BlueGray5);

  b {
    font-size: 18px;
    padding-bottom: 10px;
    display: block;
  }
}
