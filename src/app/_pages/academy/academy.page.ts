import { Component, OnInit } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/auth';
import { AngularFirestore } from '@angular/fire/firestore';
import { LoadingController, ModalController } from '@ionic/angular';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { SudsterData } from 'src/app/_interfaces/sudster-data.interface';
import { AuthidService } from 'src/app/_services/authid.service';
import { ArticleComponent } from './article/article.component';

// TODO: Replace once custom-node package v1.10.40 works on the project
export enum FirstOrderQuizzes {
  PREPARE_FOR_PICKUP = 10001,
  TAKE_PHOTO = 10002,
  ADD_DETERGENT = 10003,
  WASH = 10004,
  DRY = 10005,
  FOLD = 10006,
  PACK = 10007,
  LABEL = 10008,
}

@UntilDestroy()
@Component({
  selector: 'app-academy',
  templateUrl: './academy.page.html',
  styleUrls: ['./academy.page.scss'],
})
export class AcademyPage implements OnInit {
  UserID = this.AuthID.getID();

  ShowCompleted = false;
  DaysPassed = 5;
  AcademyArticleCount = 0;
  AcademyInboxLength = 0;
  AcademyArticlesCompleted = [];

  Articles: Array<{
    Title: string;
    Subtitle: string;
    Image: { fileName: string; url: string };
    Body: string;
    Question: string;
    Option1: string;
    Option2: string;
    Option3: string;
    Answer: number;
  }> = [];

  constructor(
    private loadingController: LoadingController,
    private AuthID: AuthidService,
    public firestore: AngularFirestore,
    private modalController: ModalController,
    public afAuth: AngularFireAuth
  ) {}

  async ngOnInit() {
    const loading = this.presentLoading();

    this.firestore
      .doc<any>(`Code/Sudster`)
      .valueChanges()
      .pipe(untilDestroyed(this))
      .subscribe((CodeDoc) => {
        this.firestore
          .doc<SudsterData>(`Sudsters/${this.UserID}`)
          .valueChanges()
          .pipe(untilDestroyed(this))
          .subscribe((doc) => {
            // Filters Completed quizzes from first order
            doc.Academy.Completed = doc.Academy.Completed.filter(
              (completed: number) => !(completed in FirstOrderQuizzes)
            );
            this.AcademyArticleCount = doc.Academy.Count;
            this.AcademyArticlesCompleted = doc.Academy.Completed;
            this.AcademyInboxLength =
              doc.Academy?.Count - doc.Academy?.Completed?.length || 0;

            this.Articles = CodeDoc.AcademyArticles.slice(
              0,
              this.AcademyArticleCount
            );

            loading.then(function (ld) {
              ld.dismiss();
            });
          });
      });
  }

  async presentLoading() {
    const loading = await this.loadingController.create({});
    await loading.present();
    return loading;
  }

  async OpenArticle(index, Earn) {
    const modal = await this.modalController.create({
      component: ArticleComponent,
      componentProps: {
        Title: this.Articles[index].Title,
        Img: this.Articles[index].Image.url,
        Body: this.Articles[index].Body,
        QuizQuestion: this.Articles[index].Question,
        Option1: this.Articles[index].Option1,
        Option2: this.Articles[index].Option2,
        Option3: this.Articles[index].Option3,
        Answer: this.Articles[index].Answer,
        ArticleIndex: index,
        Earn: Earn,
      },
    });
    return await modal.present();
  }
}
