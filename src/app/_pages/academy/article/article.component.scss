ion-content {
  --padding-bottom: 50px;
}

#Header {
  height: calc(250px + env(safe-area-inset-top));
  overflow: hidden;
  position: relative;
  padding-top: env(safe-area-inset-top);

  i {
    position: relative;
    z-index: 4;
    font-size: 30px;
    text-shadow: -1px 1px 0 white, 1px 1px 0 white, 1px -1px 0 white,
      -1px -1px 0 white;
    top: 8px;
    left: 8px;
  }

  img {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    height: 100%;
    width: 100%;
    z-index: 1;
    object-fit: cover;
  }

  h1 {
    position: absolute;
    z-index: 3;
    bottom: 0px;
    left: 0;
    padding: 0px 15px;
    width: 100%;
    font-weight: 800;
    font-size: 45px;
    background: linear-gradient(
      to top,
      white,
      rgba(255, 255, 255, 0.8),
      rgba(255, 255, 255, 0)
    );
    margin: 0;
    padding-top: 50px;
    color: var(--BlueGray5);
  }
}

video {
  border: solid 5px var(--BlueGray1);
  border-radius: 5px;
  margin-top: 10px;
  width: 90%;
  margin: 25px auto;
  display: block;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

pre {
  padding: 20px;
  line-height: 160%;
  font-size: 18px;
  display: block;
  text-align: justify;
  white-space: pre-wrap;
  font-family: 'Roboto', 'Helvetica Neue', sans-serif;
}

#TimerCounter {
  text-align: center;
  display: block;
  padding-top: 5px;
  font-weight: 900;
  color: var(--BlueGray2);
  letter-spacing: 1px;
  font-size: 14px;
}
