<ion-content>
  <div id="Header">
    <i class="material-icons" (click)="dismissModal()">arrow_back</i>
    <img
      alt="Article Image"
      [src]="Img"
      onerror="this.onerror=null; this.src='../../../../assets/logo/pink.png'"
    />
    <h1>{{ Title }}</h1>
  </div>

  <video
    (pause)="ToggleVideoState()"
    (play)="ToggleVideoState()"
    controls
    *ngIf="Body.includes('{video')"
  >
    <source [src]="GetVideoLink(Body)" />
  </video>

  <pre>{{ SectionBody(Body) }}</pre>

  <ion-button
    id="take-quiz-button"
    (click)="TakeQuiz()"
    [disabled]="Timer > 0"
    *ngIf="Earn"
    expand="block"
    fill="outline"
    >Take the quiz & earn 10 pts</ion-button
  >

  <ion-label *ngIf="Earn && Timer > 0" id="TimerCounter"
    >{{
      !VideoInPlayState && VideoIncluded
        ? 'Please watch
    entire video -
    '
        : ''
    }}{{ Timer }}s</ion-label
  >
</ion-content>
