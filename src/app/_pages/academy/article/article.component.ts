import { Component, Input, isDevMode, OnInit } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/auth';
import { AngularFirestore } from '@angular/fire/firestore';
import {
  AlertController,
  LoadingController,
  ModalController,
  ToastController,
} from '@ionic/angular';
import firebase from 'firebase/app';
import 'firebase/firestore';
import { AuthidService } from 'src/app/_services/authid.service';

@Component({
  selector: 'app-article',
  templateUrl: './article.component.html',
  styleUrls: ['./article.component.scss'],
})
export class ArticleComponent implements OnInit {
  @Input() Title = '';
  @Input() Img = '';
  @Input() Body = '';
  @Input() QuizQuestion = '';
  @Input() Option1: string;
  @Input() Option2: string;
  @Input() Option3: string;
  @Input() Answer: number;
  @Input() ArticleIndex: number;
  @Input() Earn = false;

  Timer = 15;
  VideoInPlayState = false;
  VideoIncluded = false;

  UserID = this.AuthID.getID();

  constructor(
    private modalCtrl: ModalController,
    private AuthID: AuthidService,
    private alertController: AlertController,
    private toastController: ToastController,
    public afAuth: AngularFireAuth,
    public firestore: AngularFirestore,
    private loadingController: LoadingController
  ) {}

  ngOnInit() {
    if (this.Body.includes('{video:')) {
      this.VideoIncluded = true;
    }

    setInterval(() => {
      if (!this.VideoIncluded || this.VideoInPlayState || isDevMode()) {
        this.Timer--;
      }
    }, 1000);

    this.SetTimer();
  }

  dismissModal() {
    this.modalCtrl.dismiss();
  }

  SectionBody(body) {
    return body.replace(/{video:.*}/g, '');
  }

  GetVideoLink(body) {
    return body.split('{video:')[1].split('}')[0];
  }

  SetTimer() {
    if (isDevMode()) {
      this.Timer = 2;
    } else if (this.VideoIncluded) {
      this.Timer =
        parseInt(this.GetVideoLink(this.Body).split('&length=')[1]) - 5;
    } else {
      this.Timer = 15;
    }
  }

  ToggleVideoState() {
    this.VideoInPlayState = !this.VideoInPlayState;
  }

  async TakeQuiz() {
    const alert = await this.alertController.create({
      header: 'Quiz',
      subHeader: this.QuizQuestion,
      inputs: [
        {
          name: this.Option1,
          type: 'radio',
          label: this.Option1,
          value: 1,
        },
        {
          name: this.Option2,
          type: 'radio',
          label: this.Option2,
          value: 2,
        },
        {
          name: this.Option3,
          type: 'radio',
          label: this.Option3,
          value: 3,
        },
      ],
      buttons: [
        {
          text: 'Close',
          role: 'cancel',
          cssClass: 'secondary',
          handler: () => {},
        },
        {
          text: 'Submit',
          handler: (selection) => {
            if (selection == this.Answer) {
              const loading = this.presentLoading();

              this.firestore
                .doc('Sudsters/' + this.UserID)
                .set(
                  {
                    Academy: {
                      Completed: firebase.firestore.FieldValue.arrayUnion(
                        this.ArticleIndex
                      ),
                    },
                    Ranking: {
                      Points: firebase.firestore.FieldValue.increment(10),
                    },
                  },
                  { merge: true }
                )
                .then(() => {
                  this.toastController
                    .create({
                      message: 'Correct! You earned 10 pts!',
                      duration: 4000,
                      color: 'success',
                    })
                    .then((toast) => {
                      toast.present();
                      loading.then(function (ld) {
                        ld.dismiss();
                      });
                      this.dismissModal();
                    });
                });
            } else {
              this.SetTimer();
              this.toastController
                .create({
                  message: 'Please review the post and try again.',
                  duration: 4000,
                })
                .then((toast) => {
                  toast.present();
                });
            }
          },
        },
      ],
    });

    await alert.present();
  }

  async presentLoading() {
    const loading = await this.loadingController.create({});
    await loading.present();
    return loading;
  }
}
