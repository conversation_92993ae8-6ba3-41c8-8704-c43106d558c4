<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button
        id="back-button"
        defaultHref="home"
        text=""
        icon="arrow-back"
        color="black"
      ></ion-back-button>
    </ion-buttons>
    <ion-title>Pro Tips</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content>
  <ion-segment
    mode="ios"
    value="earn"
    (ionChange)="ShowCompleted = !ShowCompleted"
  >
    <ion-segment-button id="new-button" value="earn">
      <ion-label>New ({{AcademyInboxLength}})</ion-label>
    </ion-segment-button>
    <ion-segment-button id="completed-button" value="all">
      <ion-label>Completed</ion-label>
    </ion-segment-button>
  </ion-segment>

  <ng-container *ngIf="!ShowCompleted">
    <ng-container *ngFor="let article of Articles; let i = index">
      <ion-card
        *ngIf="!AcademyArticlesCompleted.includes(i)"
        (click)="OpenArticle(i, true)"
      >
        <img
          alt="Academy Image"
          [src]="article.Image.url"
          onerror="this.onerror=null; this.src='../../../assets/logo/pink.png'"
        />
        <ion-card-header>
          <ion-card-subtitle>
            <ion-chip>
              <ion-label>Learn & Earn</ion-label>
            </ion-chip>
            +10 pts
          </ion-card-subtitle>
          <ion-card-title>{{article.Title}}</ion-card-title>
          <ion-button id="view-button" fill="outline" slot="end"
            >View</ion-button
          >
        </ion-card-header>

        <ion-card-content>
          {{article.Subtitle}}
          <ion-button fill="clear" size="small">
            View <ion-icon name="chevron-forward-outline"></ion-icon>
          </ion-button>
        </ion-card-content>
      </ion-card>
    </ng-container>
  </ng-container>

  <ion-list *ngIf="ShowCompleted">
    <ng-container *ngFor="let article of Articles; let i = index">
      <ion-item
        *ngIf="AcademyArticlesCompleted.includes(i)"
        (click)="OpenArticle(i, false)"
      >
        <ion-label>
          <h2>{{article.Title}}</h2>
          <p>{{article.Subtitle}}</p>
        </ion-label>
        <ion-icon slot="end" name="chevron-forward-outline"></ion-icon>
      </ion-item>
    </ng-container>
  </ion-list>

  <ion-label
    *ngIf="ShowCompleted && AcademyArticlesCompleted.length == 0"
    class="notice"
    >You have not completed any topics yet.</ion-label
  >

  <ion-label *ngIf="!ShowCompleted && AcademyInboxLength == 0" class="notice"
    ><b style="font-size: 18px; padding-bottom: 10px">You're all caught up!</b
    >Check back later for more<br />
    or review completed topics.</ion-label
  >
</ion-content>
