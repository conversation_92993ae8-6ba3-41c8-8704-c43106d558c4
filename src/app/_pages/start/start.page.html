<ion-content>
  <div id="LogoDiv" class="ion-padding">
    <ion-icon
      name="chevron-back"
      routerDirection="back"
      [routerLink]="['/intro']"
      id="BackButton"
    ></ion-icon>
    <img src="assets/logo/white.png" id="Logo" />
    <a href="https://poplin-laundry-pro.zendesk.com/hc/en-us" id="HelpButton">
      <ion-icon name="help-circle-outline"></ion-icon>
    </a>
  </div>

  <div id="IndentCurveDiv">
    <div id="IndentCurveCenter"></div>
    <div id="IndentCurveBackground"></div>
  </div>

  <div class="ion-padding">
    <div id="BreadcrumbDiv">
      <i class="material-icons" [class.active]="Step >= 1">email</i>
      <i class="material-icons" [class.active]="Step >= 2">account_circle</i>
      <i class="material-icons" [class.active]="Step >= 3">monetization_on</i>
      <i class="material-icons" [class.active]="Step >= 4">verified_user</i>
      <i class="material-icons" [class.active]="Step >= 5"
        >play_circle_filled</i
      >
      <i class="material-icons" [class.active]="Step >= 6">assignment</i>
    </div>
    <label
      [ngClass]="
    Step === 1 ? 'LabelStep1':
    Step === 2 ? 'LabelStep2':
    Step === 3 ? 'LabelStep3' : ''
    "
      >{{Title}}</label
    >
    <p>{{Subtitle}}</p>

    <app-signin *ngIf="Step == 1" (NextStep)="NextStep($event)"></app-signin>

    <app-three-steps
      *ngIf="Step == 2"
      (NextStep)="NextStep($event)"
    ></app-three-steps>

    <app-account *ngIf="Step == 3" (NextStep)="NextStep($event)"></app-account>

    <app-payoutsetup
      *ngIf="Step == 4"
      (NextStep)="NextStep($event)"
      [IsSignup]="true"
    ></app-payoutsetup>

    <app-idcheck *ngIf="Step == 5" (NextStep)="NextStep($event)"></app-idcheck>

    <app-training
      *ngIf="Step == 6"
      (NextStep)="NextStep($event)"
    ></app-training>

    <app-test *ngIf="Step == 7" (NextStep)="NextStep($event)"></app-test>

    <app-ready *ngIf="Step == 8" (NextStep)="NextStep($event)"></app-ready>
  </div>
</ion-content>
