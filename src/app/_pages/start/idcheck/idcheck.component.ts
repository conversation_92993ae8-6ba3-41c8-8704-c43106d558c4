import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { AngularFireRemoteConfig } from '@angular/fire/remote-config';
import {
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { SafeUrl } from '@angular/platform-browser';
import { <PERSON><PERSON><PERSON>ontroller, LoadingController } from '@ionic/angular';
import { SignupSteps } from 'src/app/_services/analytics/analytics-event.entity';
import { AnalyticsLogService } from 'src/app/_services/analytics/analytics-log.service';
import { AuthidService } from 'src/app/_services/authid.service';
import { environment } from 'src/environments/environment';
import { SubmitIdService } from '../../../_services/submit-id/submit-id.service';

// Import the new service that calls the /v1/sudster/submit-id endpoint

declare const Stripe: any;
const stripe = Stripe(environment.Stripe);

@Component({
  selector: 'app-idcheck',
  templateUrl: './idcheck.component.html',
  styleUrls: ['./idcheck.component.scss'],
})
export class IdcheckComponent implements OnInit {
  // Reactive form for ID data
  IDForm: UntypedFormGroup;

  // Event emitter to signal next step after success
  @Output() NextStep = new EventEmitter();

  dlFrontImg: SafeUrl = '';
  dlBackImg: SafeUrl = '';
  dlFront = '';
  dlBack = '';

  userFaceVerified = false;
  verifying = false;
  faceVerificationPlaceholder = 'Biometric Face Verification';
  faceVerification = false;

  // Retrieve the user ID
  UserID = this.AuthID.getID();

  constructor(
    private fb: UntypedFormBuilder,
    private AuthID: AuthidService,
    public loadingController: LoadingController,
    public alertController: AlertController,
    remoteConfig: AngularFireRemoteConfig,
    private logService: AnalyticsLogService,
    private submitIdService: SubmitIdService
  ) {
    // Subscribe to remote config for face verification feature flag
    remoteConfig.booleans.FaceVerification.subscribe((value) => {
      this.faceVerification = value;
    });
  }

  ngOnInit() {
    // Build the form with required validations
    this.IDForm = this.fb.group({
      firstName: ['', Validators.required],
      lastName: ['', Validators.required],
      dob: ['', [Validators.required, Validators.minLength(8)]],
      ssn: ['', [Validators.required, Validators.minLength(4)]],
      gender: ['', Validators.required],
    });
  }

  /**
   * Next() is called when the user submits ID info.
   * It checks if the user is over 18, and if so, calls submitIdService (Observable).
   */
  Next() {
    const loading = this.presentLoading();

    // Parse DOB to check if the user is above 18
    const DOBArray = this.IDForm.value.dob.split('/');
    const DOBMonth = parseInt(DOBArray[0], 10);
    const DOBDay = parseInt(DOBArray[1], 10);
    const DOBYear = DOBArray[2];
    const DOBMinAge = 18;
    const DOBdate = new Date();
    DOBdate.setFullYear(DOBYear, DOBMonth - 1, DOBDay);
    const currdate = new Date();
    const setDate = new Date();
    setDate.setFullYear(
      DOBdate.getFullYear() + DOBMinAge,
      DOBMonth - 1,
      DOBDay
    );

    // If user is above 18
    if (currdate.getTime() - setDate.getTime() > 0) {
      // Call the new service instead of cloudFunctions.httpsCallable
      this.submitIdService
        .submitId({
          ...this.IDForm.value,
        })
        .subscribe({
          next: () => {
            // On success, emit next step and track analytics
            this.NextStep.emit();
            this.logService.logAccountCreationAccountInfo(
              this.UserID,
              'Identity Verification',
              SignupSteps.IdVerification
            );
            // Dismiss the loading spinner
            loading.then((ld) => {
              ld.dismiss();
            });
          },
          error: (err) => {
            // On error, dismiss loading and show alert
            loading.then((ld) => {
              ld.dismiss();
            });
            this.presentAlert('Error', err.message);
          },
        });
    } else {
      // Under 18
      loading.then(function (ld) {
        ld.dismiss();
      });
      this.presentAlert(
        'Age Limit',
        "We're sorry but you must be at least 18 years old to be a Laundry Pro. Please register again after your 18th birthday."
      );
    }
  }

  /**
   * Presents a loading spinner and returns the loading instance (Promise).
   */
  async presentLoading() {
    const loading = await this.loadingController.create({});
    await loading.present();
    return loading;
  }

  /**
   * Presents an alert with the given title and message.
   */
  async presentAlert(Title: string, Message: string) {
    const alert = await this.alertController.create({
      header: Title,
      message: Message,
      buttons: ['OK'],
    });
    await alert.present();
  }
}
