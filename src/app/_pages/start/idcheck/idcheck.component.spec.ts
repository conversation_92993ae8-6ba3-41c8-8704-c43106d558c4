import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { IdcheckComponent } from './idcheck.component';

describe('IdcheckComponent', () => {
  let component: IdcheckComponent;
  let fixture: ComponentFixture<IdcheckComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [IdcheckComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(IdcheckComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
