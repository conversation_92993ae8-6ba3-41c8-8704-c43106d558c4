<form [formGroup]="IDForm" style="margin-top: 20px">
  <ion-segment class="InputSegment" mode="ios" formControlName="gender">
    <ion-segment-button id="male-button" mode="ios" value="male">
      <ion-label>Male</ion-label>
    </ion-segment-button>
    <ion-segment-button id="female-button" mode="ios" value="female">
      <ion-label>Female</ion-label>
    </ion-segment-button>
  </ion-segment>
  <div class="InputDiv" id="first-name-div">
    <i class="material-icons">account_circle</i>
    <input
      id="first-name-input"
      type="text"
      placeholder="Full Legal First Name"
      formControlName="firstName"
    />
  </div>
  <div class="InputDiv" id="last-name-div">
    <i class="material-icons">people</i>
    <input
      id="last-name-input"
      type="text"
      placeholder="Full Legal Last Name"
      formControlName="lastName"
    />
  </div>
  <div class="InputDiv" id="dob-div">
    <i class="material-icons">date_range</i>
    <input
      id="dob-input"
      type="text"
      [dropSpecialCharacters]="false"
      placeholder="Date of Birth (mm/dd/yyyy)"
      mask="00/00/0000"
      formControlName="dob"
    />
  </div>
  <div class="InputDiv" id="ssn-div">
    <i class="material-icons">lock</i>
    <input
      id="ssn-input"
      type="text"
      placeholder="Social Security Number"
      mask="***********"
      formControlName="ssn"
    />
  </div>
  <ion-button
    id="next-button"
    [disabled]="IDForm.invalid"
    (click)="Next()"
    expand="block"
    >Next <ion-icon name="arrow-forward"> </ion-icon>
  </ion-button>
</form>
