import { AccountComponent } from './account/account.component';
import { PayoutsetupComponent } from './payoutsetup/payoutsetup.component';

import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';

import { IonicModule } from '@ionic/angular';

import { StartPage } from './start.page';

import { NgxMaskModule } from 'ngx-mask';
import { LegalInfoComponent } from 'src/app/_components/legal-info/legal-info.component';
import { ThreeStepsComponent } from '../three-steps/three-steps.component';
import { IdcheckComponent } from './idcheck/idcheck.component';
import { PhoneComponent } from './phone/phone.component';
import { ReadyComponent } from './ready/ready.component';
import { SigninComponent } from './signin/signin.component';
import { TestComponent } from './test/test.component';
import { TrainingComponent } from './training/training.component';
import { AddressInfoComponent } from '../../_components/address-info/address-info.component';
import { ContactInfoComponent } from '../../_components/contact-info/contact-info.component';

const routes: Routes = [
  {
    path: '',
    component: StartPage,
  },
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes),
    NgxMaskModule.forRoot(),
    ReactiveFormsModule,
    LegalInfoComponent,
    AddressInfoComponent,
    ContactInfoComponent,
  ],
  providers: [],
  declarations: [
    StartPage,
    SigninComponent,
    AccountComponent,
    PayoutsetupComponent,
    IdcheckComponent,
    TrainingComponent,
    TestComponent,
    ReadyComponent,
    PhoneComponent,
    ThreeStepsComponent,
  ],
})
export class StartPageModule {}
