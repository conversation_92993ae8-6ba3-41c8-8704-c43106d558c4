#BackButton {
  font-size: 20px;
  color: white;
}

#HelpButton {
  font-size: 20px;
  color: white;
  position: absolute;
  top: calc(env(safe-area-inset-top) + 20px);

  right: 15px;
  opacity: 0.7;
}

#LogoDiv {
  background: var(--pink-core);
  padding-top: calc(env(safe-area-inset-top) + 15px);
}

#Logo {
  width: 130px;
  display: block;
  text-align: center;
  margin: -15px auto;
  padding-bottom: 10px;
}

#InnerDiv {
  width: 100%;
  border-radius: 5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  background: white;
  padding: 10px 15px;
  padding-bottom: 5px;
}

#IndentCurveDiv {
  position: relative;
  overflow: hidden;
  height: 25px;
}

#IndentCurveCenter {
  width: 100%;
  position: absolute;
  top: -100%;
  left: 0px;
  right: 0px;
  background: var(--pink-core);
  height: 50px;
  z-index: 2;
  border-radius: 50%;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

#IndentCurveBackground {
  width: 100%;
  position: absolute;
  bottom: 0px;
  left: 0px;
  right: 0px;
  background: white;
  height: 50px;
  z-index: 1;
}

ion-button {
  position: absolute;
  bottom: 0px;
  left: 0;
  right: 0;
}

#BreadcrumbDiv {
  text-align: center;
  position: relative;
  margin-top: -35px;
  margin-bottom: 20px;

  i {
    height: 35px;
    width: 35px;
    background: var(--BlueGray1);
    color: var(--BlueGray4);
    border-radius: 50%;
    text-align: center;
    padding-top: 5px;
    font-size: 20px;
    margin: 0 5px;
    position: relative;
    z-index: 2;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    box-sizing: border-box;
    border: solid 2px var(--BlueGray1);
    transform: scale(0.8);
  }

  i.active {
    background: var(--pink-core);
    color: white;
    border-color: white;
    box-shadow: none;
    transform: scale(1);
  }

  b {
    position: absolute;
    height: 2px;
    width: 200px;
    left: calc(50% - 100px);
    z-index: 1;
    top: 15px;
    background: var(--BlueGray1);
  }
}

label {
  display: block;
  text-transform: uppercase;
  font-size: 13px;
  letter-spacing: 1px;
  color: var(--BlueGray4);
  font-weight: 700;
  text-align: center;
}

.LabelStep1 {
  font-size: 24px;
  white-space: pre-wrap;
  font-family: 'PitchSans-Medium', Helvetica, sans-serif;
  color: var(--black);
  font-weight: 400;
}
.LabelStep2 {
  display: flex;
  width: 324px;
  flex-direction: column;
  justify-content: center;
  flex-shrink: 0;
  color: #000;
  text-align: center;
  font-family: Pitch Sans;
  font-size: 24px;
  font-style: normal;
  font-weight: 500;
  line-height: 120%;
  letter-spacing: -0.72px;
  font-family: 'PitchSans-Medium', Helvetica, sans-serif;
  text-transform: none;
  margin: auto;
}

.LabelStep3 {
  color: var(--black);
}

p {
  font-size: 13px;
  margin: 0 auto;
  max-width: 275px;
  color: var(--BlueGray3);
  text-align: center;
  margin-top: 5px;
  line-height: 130%;
}

.comp {
  max-width: 300px;
  margin: 0 auto;
}

ion-button {
  --background: var(--viridian-core);
}
