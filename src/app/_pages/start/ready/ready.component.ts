import { Component, OnInit } from '@angular/core';
import { AngularFireAnalytics } from '@angular/fire/analytics';
import { AngularFireAuth } from '@angular/fire/auth';
import { AngularFirestore } from '@angular/fire/firestore';
import { Router } from '@angular/router';
import { Alert<PERSON>ontroller, LoadingController } from '@ionic/angular';
import { SignupSteps } from 'src/app/_services/analytics/analytics-event.entity';
import { AnalyticsLogService } from 'src/app/_services/analytics/analytics-log.service';
import { AuthidService } from 'src/app/_services/authid.service';
import { SignUpStepsService } from '../../../_services/signup.service';

@Component({
  selector: 'app-ready',
  templateUrl: './ready.component.html',
  styleUrls: ['./ready.component.scss'],
})
export class ReadyComponent implements OnInit {
  UserID = this.AuthID.getID();
  NotificationsEnabled = true;
  welcomeMessage = 'Welcome to the Poplin team.';

  constructor(
    public afAuth: AngularFireAuth,
    private AuthID: AuthidService,
    public firestore: AngularFirestore,
    private loadingController: LoadingController,
    private router: Router,
    private alertController: AlertController,
    private analytics: AngularFireAnalytics,
    private logService: AnalyticsLogService,
    private signUpStepsService: SignUpStepsService
  ) {}

  ngOnInit() {
    if (
      !this.signUpStepsService.isSkipped() &&
      this.signUpStepsService.canSkip()
    ) {
      this.welcomeMessage = '500 points have been added to your account.';
    }
  }

  async Continue() {
    const loading = this.presentLoading();

    await this.firestore
      .doc('Sudsters/' + this.UserID)
      .update({ SignupStepNumber: 0 });

    this.analytics.logEvent('sudster_signup_complete');
    this.logService.logAccountCreationAccountInfo(
      this.UserID,
      'Get Started',
      SignupSteps.Completed
    );

    this.router.navigate(['home']);

    loading.then(function (ld) {
      ld.dismiss();
    });
  }

  async presentLoading() {
    const loading = await this.loadingController.create({});
    await loading.present();
    return loading;
  }

  async presentAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK'],
    });
    await alert.present();
  }
}
