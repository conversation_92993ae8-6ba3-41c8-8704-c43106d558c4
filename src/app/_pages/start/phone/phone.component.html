<label class="Title">{{
  VerifyStage ? 'Enter the 6-digit code ' : 'What is your phone number?'
}}</label>

<input
  *ngIf="!VerifyStage"
  [(ngModel)]="Phone"
  type="tel"
  id="phone-input"
  prefix="+1 "
  mask="(*************"
  placeholder="+1 (*************"
/>

<input
  *ngIf="VerifyStage"
  style="text-align: center"
  [(ngModel)]="VerificationCode"
  type="tel"
  id="phone-input"
  mask="000-000"
  placeholder="000-000"
/>
<div class="sms-confirm-main" *ngIf="!VerifyStage">
  <div class="sms-confirm-checkbox-div">
    <ion-checkbox
      slot="end"
      [(ngModel)]="acceptNotificationSms"
      (ionChange)="handleCheckbox($event)"
    ></ion-checkbox>
    <ion-label class="sms-confirm-label"
      >I want to receive account notifications via text message.
    </ion-label>
  </div>
</div>

<ion-button
  id="send-code-button"
  *ngIf="!VerifyStage"
  (click)="SendCode()"
  [disabled]="Phone?.length < 10"
  expand="block"
  >Next
  <ion-icon slot="end" name="arrow-forward-outline"></ion-icon>
</ion-button>

<p
  *ngIf="!VerifyStage"
  style="
    font-size: 12px;
    color: gray;
    width: 330px;
    margin: 10px auto;
    display: block;
    text-align: center;
    font-style: italic;
  "
>
  Message and data rates may apply. Message frequency varies. Reply HELP for
  help or STOP to cancel. By continuing, you agree to Poplins's

  <span *ngIf="ShowTermsAndPrivacyPolicy">
    <a
      style="text-decoration: underline; color: gray"
      href="https://poplin.co/terms-of-service"
      >Terms of Service</a
    >
    and
    <a
      style="text-decoration: underline; color: gray"
      href="https://poplin.co/privacy-policy"
      >Privacy Policy</a
    >
  </span>
</p>

<ion-button
  id="verify-code-button"
  *ngIf="VerifyStage"
  (click)="VerifyCode()"
  expand="block"
  >Verify Code
</ion-button>

<ion-button
  id="resend-code-button"
  *ngIf="VerifyStage"
  (click)="VerifyStage = false; detectChanges()"
  expand="block"
  size="small"
  fill="clear"
>
  Resend code
</ion-button>
