import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  OnInit,
  Output,
} from '@angular/core';
import { AngularFireAuth } from '@angular/fire/auth';
import { AngularFirestore } from '@angular/fire/firestore';
import { AngularFireFunctions } from '@angular/fire/functions';
import { <PERSON><PERSON><PERSON><PERSON>roller, LoadingController } from '@ionic/angular';
import { SignupSteps } from 'src/app/_services/analytics/analytics-event.entity';
import { AnalyticsLogService } from 'src/app/_services/analytics/analytics-log.service';
import { AttentiveService } from 'src/app/_services/attentive/attentive.service';
import { AuthidService } from 'src/app/_services/authid.service';
import { GetSudsterDataService } from 'src/app/_services/get-sudster-data.service';
import { trackEvent } from 'src/app/_utils/track-event';
import { StatsigFactoryService } from '../../../_services/statsig-factory.service';
import { StatsigService } from '../../../_services/statsig.service';

@Component({
  selector: 'app-phone',
  templateUrl: './phone.component.html',
  styleUrls: ['./phone.component.scss'],
})
export class PhoneComponent implements OnInit {
  Phone: string = null;
  VerificationCode: string = null;
  VerifyStage = false;
  Phone_E164 = '';
  ShowTermsAndPrivacyPolicy: boolean = false;

  @Output() verified = new EventEmitter();

  UserID = this.AuthID.getID();
  Email: string = null;
  acceptNotificationSms = true;

  private statsigService: StatsigService;

  constructor(
    public afAuth: AngularFireAuth,
    public alertController: AlertController,
    public loadingController: LoadingController,
    private changeDetectorRef: ChangeDetectorRef,
    private firestore: AngularFirestore,
    private cloudFunctions: AngularFireFunctions,
    private AuthID: AuthidService,
    private logService: AnalyticsLogService,
    private sudsterService: GetSudsterDataService,
    private attentiveService: AttentiveService,
    private statsigFactoryService: StatsigFactoryService
  ) {
    this.sudsterService.listenForSudster().subscribe((sudster) => {
      if (sudster) {
        this.Email = sudster.ContactEmail;
      }
      this.statsigService = this.statsigFactoryService.getInstance('v4');
    });
  }

  ngOnInit() {
    this.loadTermsAndPoliciesFeatureFlag();
  }

  handleCheckbox(event: CustomEvent): void {
    this.acceptNotificationSms = event.detail.checked;
  }

  SendCode() {
    this.Phone_E164 = '+1' + this.Phone;
    const loading = this.presentLoading();
    this.cloudFunctions
      .httpsCallable('SudsterV3_VerifyPhone')({ phone: this.Phone_E164 })
      .toPromise()
      .then((res) => {
        this.VerifyStage = true;
        loading.then(function (ld) {
          ld.dismiss();
        });
        this.changeDetectorRef.detectChanges();
        this.logService.logAccountCreation(
          this.UserID,
          'Verify Phone',
          SignupSteps.AccountInfo,
          this.Email
        );
      })
      .catch((err) => {
        loading.then(function (ld) {
          ld.dismiss();
        });
        this.presentAlert(
          'ERROR',
          err?.message || 'An error occurred while processing your request.'
        );
      });
  }

  VerifyCode() {
    if (this.VerificationCode?.length < 6) {
      this.presentAlert(
        'Error',
        'Please enter the full 6-digit verification code sent to your phone.'
      );
    } else {
      const loading = this.presentLoading();

      this.cloudFunctions
        .httpsCallable('SudsterV3_VerifyPhone')({
          code: this.VerificationCode,
          phone: this.Phone_E164,
        })
        .toPromise()
        .then((res) => {
          if (res == 'verified') {
            this.firestore
              .doc('Sudsters/' + this.UserID)
              .set(
                {
                  Phone: this.Phone_E164,
                  GDPRCompliance: {
                    AcceptNotificationSms: this.acceptNotificationSms || false,
                  },
                },
                { merge: true }
              )
              .then(() => {
                loading.then(function (ld) {
                  ld.dismiss();
                });
                if (this.acceptNotificationSms) {
                  this.attentiveService.subscribeUser(
                    this.Phone,
                    this.Email,
                    this.UserID
                  );
                }
                this.verified.emit(true);
                this.changeDetectorRef.detectChanges();
                this.logService.logAccountCreation(
                  this.UserID,
                  'Phone Verified',
                  SignupSteps.AccountInfo,
                  this.Email
                );
                trackEvent({
                  eventData: {
                    event: 'LPOnboarding_PhoneVerified',
                    userId: this.UserID,
                  },
                });
              });
          } else {
            throw new Error(res);
          }
        })
        .catch((error) => {
          loading.then(function (ld) {
            ld.dismiss();
          });
          this.presentAlert('ERROR', error);
        });
    }
  }

  async presentAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK'],
    });

    await alert.present();
  }

  async presentLoading() {
    const loading = await this.loadingController.create({});
    await loading.present();
    return loading;
  }

  detectChanges() {
    this.changeDetectorRef.detectChanges();
  }

  async loadTermsAndPoliciesFeatureFlag() {
    await this.statsigService.initializeStatsig('', {} as any);
    this.statsigService.setStatsigInitialized();

    const config = await this.statsigService.getConfigPromise(
      'require-laundrypro-terms-and-policies'
    );

    this.ShowTermsAndPrivacyPolicy = config.get(
      'explicityRequiredNewLP',
      false
    );
  }
}
