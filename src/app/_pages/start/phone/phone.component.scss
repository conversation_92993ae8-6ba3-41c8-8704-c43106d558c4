input {
  margin: 25px auto;
  width: 313px;
  display: block;
  font-size: 25px;
  border: none;
  border-bottom: solid 2px black;
  padding-bottom: 5px;
  text-align: center;
}

input:focus {
  border-color: var(--Blue);
}

small {
  display: block;
  font-size: 10px;
  color: var(--BlueGray3);
  width: 270px;
  text-align: center;
  margin: 0 auto;
  margin-top: 10px;

  a {
    color: var(--BlueGray3);
  }
}

.Title {
  display: block;
  text-align: center;
  margin: 20px;
  font-weight: 600;
  font-size: 18px;
  color: var(--BlueGray5);
}
.sms-confirm-main {
  width: 100%;
}
.sms-confirm-checkbox-div {
  width: 300px;
  display: flex;
  margin: auto;
}

ion-checkbox {
  --background-checked: #285652;
  --border-color-checked: #285652;
  --size: 28px;
  width: 40px;
  margin: 0px 8px 0px;
}
.sms-confirm-label {
  margin: 0px 0px 20px 8px;
  color: var(--Checkbox-Text-label-unchecked, #4b4b4b);
  font-family: 'Fakt-Normal' sans-serif;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 18.904px; /* 135.026% */
}
#verify-code-button,
#send-code-button {
  --background: var(--viridian-core);
  display: flex;
  width: 300px;
  height: 46px;
  margin: 24px auto;
  justify-content: center;
  align-items: center;
  gap: 12px;
}

#resend-code-button {
  --color: #0065ad;
}
