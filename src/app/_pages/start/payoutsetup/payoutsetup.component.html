<br />
<ion-segment
  *ngIf="ShowPayoutOptions"
  (ionChange)="SwitchType($event)"
  value="BA"
>
  <ion-segment-button id="debit-card-button" value="DC">
    <ion-label>Debit Card</ion-label>
  </ion-segment-button>
  <ion-segment-button id="bank-account-button" value="BA">
    <ion-label>Bank Account</ion-label>
  </ion-segment-button>
</ion-segment>
<br />
<div *ngIf="PayoutMethod === 'DC'">
  <div *ngIf="this.ranking >= 3">
    <label id="DebitCardWarning"
      >Get paid through your bank debit card.<i
        >Please note that any non-bank debit cards (e.g. Pre-paid, CashApp,
        etc.) will not work.</i
      ></label
    >

    <div id="stripe-element"></div>
  </div>
  <div *ngIf="this.ranking < 3">
    <p class="blocked-debit-card-text">
      Debit Card not available until you reach Gold status
    </p>
  </div>
</div>

<div *ngIf="PayoutMethod === 'BA'">
  <ion-item>
    <ion-label position="floating">Routing Number</ion-label>
    <ion-input [(ngModel)]="RoutingNumber" type="text"></ion-input>
  </ion-item>
  <ion-item>
    <ion-label position="floating">Account Number</ion-label>
    <ion-input [(ngModel)]="AccountNumber" type="text"></ion-input>
  </ion-item>

  <ion-button
    id="more-info-button"
    style="margin-top: 15px"
    (click)="ShowCheckHelp = !ShowCheckHelp"
    size="small"
    expand="block"
    fill="clear"
    >Where can I find this info?</ion-button
  >

  <img src="assets/img/start/CheckHelp.jpg" *ngIf="ShowCheckHelp" />
</div>

<ion-button
  id="next-button"
  style="margin-top: 15px; min-height: 35px"
  [disabled]="ValidateForm()"
  (click)="Next()"
  expand="block"
  >Next <ion-icon name="arrow-forward"> </ion-icon>
</ion-button>
