import {
  AfterViewInit,
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import {
  Alert<PERSON>ontroller,
  LoadingController,
  ModalController,
} from '@ionic/angular';
import { AngularFirestore } from '@angular/fire/firestore';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';

import { SudsterData } from 'src/app/_interfaces/sudster-data.interface';
import { SignupSteps } from 'src/app/_services/analytics/analytics-event.entity';
import { AnalyticsLogService } from 'src/app/_services/analytics/analytics-log.service';
import { AuthidService } from 'src/app/_services/authid.service';
import { trackEvent } from 'src/app/_utils/track-event';
import { environment } from 'src/environments/environment';
import { PayoutSetupService } from 'src/app/_services/payout-setup/payout-setup.service';
import { ErrorHandlingService } from 'src/app/_services/error-handling/error-handling.service';
import { PayoutMethodData } from '../../../_interfaces/submit-payout.interface';

// Stripe
declare const Stripe: any;
const stripe = Stripe(environment.Stripe);
const elements = stripe.elements();

@UntilDestroy()
@Component({
  selector: 'app-payoutsetup',
  templateUrl: './payoutsetup.component.html',
  styleUrls: ['./payoutsetup.component.scss'],
})
export class PayoutsetupComponent implements AfterViewInit, OnInit, OnDestroy {
  PayoutMethod = 'BA';
  ShowCheckHelp = false;
  card: any;
  RoutingNumber = '';
  AccountNumber = '';
  ranking = 0;
  CCBuilt = false;
  userId: string;

  @Output() NextStep = new EventEmitter();

  @Input() ShowPayoutOptions = false;
  @Input() IsSignup = false;

  constructor(
    public loadingController: LoadingController,
    public alertController: AlertController,
    private modalCtrl: ModalController,
    private AuthidService: AuthidService,
    public firestore: AngularFirestore,
    private logService: AnalyticsLogService,
    private payoutSetupService: PayoutSetupService,
    private errorHandlingService: ErrorHandlingService
  ) {}

  ngOnInit() {
    this.userId = this.AuthidService.getID();
    this.firestore
      .doc<SudsterData>(`Sudsters/${this.userId}`)
      .valueChanges()
      .pipe(untilDestroyed(this))
      .subscribe((doc) => {
        if (doc?.Ranking?.BadgeNumber) {
          this.ranking = doc.Ranking.BadgeNumber;
        }
      });
  }

  ngAfterViewInit() {
    if (this.PayoutMethod === 'DC') {
      this.CreateCCField();
    }
  }

  ngOnDestroy() {
    if (this.card) {
      this.card.unmount();
      this.card.destroy();
    }
  }

  async Next() {
    if (this.PayoutMethod === 'DC') {
      await this.handleDebitCardSelection();
      return;
    }

    await this.handleBankAccountSelection();
  }

  async handleBankAccountSelection() {
    const loading = await this.presentLoading();

    const body: PayoutMethodData = {
      PayoutMethod: 'BA',
      RoutingNumber: this.RoutingNumber,
      AccountNumber: this.AccountNumber,
    };

    await this.payoutSetupService.submitPayoutMethod(body).subscribe({
      next: () => {
        loading.dismiss();
        this.NextStep.emit();

        if (this.IsSignup) {
          this.logService.logAccountCreationAccountInfo(
            this.userId,
            'Connect Bank',
            SignupSteps.ConnectBank
          );
          trackEvent({
            eventData: {
              event: 'LPOnboarding_BankConnected',
              userId: this.userId,
            },
          });
          return;
        }

        this.logService.logPayoutMethodUpdate('Bank Account');
        this.dismissModal();
      },
      error: (err: Error | HttpErrorResponse) => {
        loading.dismiss();
        this.errorHandlingService.handleError(err);
        this.presentAlert('Error', err.message);
      },
    });
  }

  async handleDebitCardSelection() {
    const loading = await this.presentLoading();

    await stripe.createToken(this.card, { currency: 'usd' }).then((result) => {
      if (result.error) {
        loading.dismiss();
        this.errorHandlingService.handleError(result.error);
        this.presentAlert('Card Error', result.error.message);
        return;
      }

      const body: PayoutMethodData = {
        PayoutMethod: 'DC',
        Token: result.token.id,
      };

      this.payoutSetupService.submitPayoutMethod(body).subscribe({
        next: () => {
          loading.dismiss();
          this.NextStep.emit();
          this.logService.logPayoutMethodUpdate('Debit Card');
          this.dismissModal();
        },
        error: (err: Error | HttpErrorResponse) => {
          loading.dismiss();
          this.errorHandlingService.handleError(err);
          this.presentAlert('Error', err.message);
        },
      });
    });
  }

  dismissModal() {
    this.modalCtrl
      .dismiss()
      .catch((err) => console.warn('Error dismissing modal', err));
  }

  ValidateForm() {
    if (this.PayoutMethod === 'BA') {
      return !(this.AccountNumber && this.RoutingNumber.length === 9);
    }
  }

  async presentLoading() {
    const loading = await this.loadingController.create({});
    await loading.present();
    return loading;
  }

  async presentAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK'],
    });
    await alert.present();
  }

  SwitchType(ev: any) {
    this.PayoutMethod = ev.detail.value;
    if (this.PayoutMethod === 'DC') {
      if (this.card) {
        this.card.unmount();
        this.card.destroy();
      }
      setTimeout(() => {
        this.CreateCCField();
      }, 200);
    }
  }

  CreateCCField() {
    if (this.PayoutMethod === 'BA' || !this.ranking || this.ranking < 3) {
      return;
    }
    const style = {
      base: {
        color: '#32325d',
        lineHeight: '18px',
        fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
        fontSmoothing: 'antialiased',
        fontSize: '16px',
        '::placeholder': {
          color: '#aab7c4',
        },
      },
      invalid: {
        color: '#fa755a',
        iconColor: '#fa755a',
      },
    };
    this.card = elements.create('card', { style });
    this.card.mount('#stripe-element');
  }
}
