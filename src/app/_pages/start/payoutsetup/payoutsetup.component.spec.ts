import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { PayoutsetupComponent } from './payoutsetup.component';

describe('PayoutsetupComponent', () => {
  let component: PayoutsetupComponent;
  let fixture: ComponentFixture<PayoutsetupComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [PayoutsetupComponent],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PayoutsetupComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
