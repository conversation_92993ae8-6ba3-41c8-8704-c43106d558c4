<app-phone
  *ngIf="!PhoneVerified"
  (verified)="PhoneVerified = true; changeDetectorRef.detectChanges()"
></app-phone>

<div class="account-container maxwidth" *ngIf="PhoneVerified">
  <form [formGroup]="AccountForm" style="margin-top: 20px">
    <div class="InputDiv">
      <label>Legal Name </label>
      <app-legal-info [legalForm]="AccountForm"></app-legal-info>
    </div>
    <div class="InputDiv">
      <label>Home Address </label>
      <app-address-info [addressForm]="AccountForm"></app-address-info>
    </div>

    <div class="InputDiv">
      <div class="header">
        <label>Alternate Contact</label>
        <i class="material-icons" (click)="presentICEInfo()">info</i>
      </div>
      <app-contact-info [contactInfoForm]="AccountForm"></app-contact-info>
    </div>
    <br />
    <ion-button
      id="next-button"
      (click)="Next()"
      [disabled]="AccountForm.invalid"
      expand="block"
    >
      Next <ion-icon name="arrow-forward"></ion-icon>
    </ion-button>
  </form>
</div>
