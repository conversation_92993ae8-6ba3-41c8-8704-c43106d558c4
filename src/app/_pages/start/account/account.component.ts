import {
  Component,
  EventEmitter,
  OnInit,
  Output,
  ViewEncapsulation,
} from '@angular/core';
import { AngularFirestore } from '@angular/fire/firestore';
import {
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from '@angular/forms';
import { <PERSON><PERSON><PERSON>ontroller, LoadingController } from '@ionic/angular';
import 'firebase/firestore';
import Geohash from 'latlon-geohash';
import { first } from 'rxjs/operators';

import { AngularFireRemoteConfig } from '@angular/fire/remote-config';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import firebase from 'firebase';
import { SudsterData } from 'src/app/_interfaces/sudster-data.interface';
import { SignupSteps } from 'src/app/_services/analytics/analytics-event.entity';
import { AnalyticsLogService } from 'src/app/_services/analytics/analytics-log.service';
import { AuthidService } from 'src/app/_services/authid.service';
import { LegacyApiService } from 'src/app/_services/legacy-api.service';
import { globalPhoneNumberValidator } from 'src/app/_utils/custom-validators';
import { emailPattern } from 'src/app/_utils/input.utils';
import { trackEvent } from 'src/app/_utils/track-event';

declare const google;
@UntilDestroy()
@Component({
  selector: 'app-account',
  templateUrl: './account.component.html',
  styleUrls: ['./account.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class AccountComponent implements OnInit {
  AccountForm: UntypedFormGroup;
  loading;

  GoogleMapsService;
  geocoder;

  PhoneVerified = false;

  UserID = this.AuthID.getID();
  AddressFailAttempts = 0;

  UserPhone = '';

  ForterAPI: boolean = false;
  @Output() NextStep = new EventEmitter();

  constructor(
    private fb: UntypedFormBuilder,
    private AuthID: AuthidService,
    private firestore: AngularFirestore,
    public loadingController: LoadingController,
    public alertController: AlertController,
    remoteConfig: AngularFireRemoteConfig,
    private apiService: LegacyApiService,
    private logService: AnalyticsLogService
  ) {
    this.GoogleMapsService = new google.maps.places.AutocompleteService();
    this.geocoder = new google.maps.Geocoder();

    remoteConfig.booleans.ForterAPI.subscribe((value) => {
      this.ForterAPI = value;
    });
  }
  appInfo: {
    appVersion: string;
    platform: string;
  };

  Email: string;

  ngOnInit() {
    this.firestore
      .doc<SudsterData>(`Sudsters/${this.UserID}`)
      .valueChanges()
      .pipe(untilDestroyed(this))
      .subscribe((doc) => {
        if (doc.Phone != null) {
          this.PhoneVerified = true;
          this.UserPhone = doc.Phone;
        }
        if (doc.appInfo) {
          this.appInfo = {
            appVersion: doc.appInfo.appVersion,
            platform: doc.appInfo.platform,
          };
        }
        if (doc.ContactEmail !== null) {
          this.Email = doc.ContactEmail;
        }
      });

    this.AccountForm = this.fb.group({
      FirstName: ['', [Validators.required, Validators.maxLength(32)]],
      LastName: [
        '',
        [
          Validators.required,
          Validators.minLength(2),
          Validators.maxLength(32),
        ],
      ],
      StreetAddress: ['', Validators.required],
      AddressLine2: '',
      City: ['', Validators.required],
      State: [
        '',
        [Validators.required, Validators.minLength(2), Validators.maxLength(2)],
      ],
      Zipcode: [
        '',
        [Validators.required, Validators.minLength(5), Validators.maxLength(5)],
      ],
      iceFirstName: ['', [Validators.required, Validators.maxLength(32)]],
      iceLastName: ['', [Validators.required, Validators.maxLength(32)]],
      iceRelationship: ['', Validators.required],
      iceEmail: [
        '',
        [
          Validators.required,
          Validators.email,
          Validators.pattern(emailPattern),
        ],
      ],
      icePhone: ['', [Validators.required, globalPhoneNumberValidator()]],
    });
  }

  async Next(overrideAddress: boolean) {
    if (this.AccountForm.value.icePhone == this.UserPhone.substring(2)) {
      this.presentAlert(
        'Alternate Contact Person Required',
        'You cannot enter yourself as your alternate contact person.'
      );
      return;
    }
    this.presentLoading();

    this.AccountForm.value.iceEmail =
      this.AccountForm.value.iceEmail.toLowerCase();

    this.AccountForm.value.icePhone =
      '+1' + this.AccountForm.value.icePhone.replace(/\D/g, '');

    await this.apiService
      .post(`SudsterVerification/v1/sudster/ice-verification`, {
        contactEmail: this.AccountForm.value.iceEmail,
        phoneNumber: this.AccountForm.value.icePhone,
      })
      .toPromise()
      .catch((err) => {
        console.log('error:', err);
      });

    new Promise((resolve, reject) => {
      this.firestore
        .doc<any>(`Code/WorkArea`)
        .valueChanges()
        .pipe(first())
        .subscribe((doc) => {
          const AvailableWorkArea = doc.Circles;
          this.geocoder.geocode(
            {
              address:
                this.AccountForm.value.StreetAddress +
                ' ' +
                this.AccountForm.value.City +
                ', ' +
                this.AccountForm.value.State +
                ', ' +
                this.AccountForm.value.Zipcode +
                ', USA',
            },
            (data, status) => {
              if (overrideAddress != true) {
                if (status == 'ZERO_RESULTS') {
                  reject(
                    'Your home address could not be found. Please check it and try again.'
                  );
                  return;
                } else if (data.length > 2) {
                  reject(
                    'Your home address seems to be incomplete. Please check and try again.'
                  );
                  return;
                }

                let setLocality = false;
                let setNeighborhood = false;
                let HouseNumber;
                let StreetAddress;
                let City;
                let State;
                let Zipcode;
                for (const component of data[0].address_components) {
                  for (const type of component.types) {
                    if (type === 'street_number') {
                      HouseNumber = component.long_name;
                    }
                    if (type === 'route') {
                      StreetAddress = component.short_name;
                    }
                    if (type === 'locality') {
                      City = component.long_name;
                      setLocality = true;
                    }
                    if (type === 'neighborhood' && !setLocality) {
                      City = component.long_name;
                      setNeighborhood = true;
                    }
                    if (
                      (type === 'sublocality_level_1' ||
                        type === 'sublocality') &&
                      !setLocality &&
                      !setNeighborhood
                    ) {
                      City = component.long_name;
                    }
                    if (type === 'administrative_area_level_1') {
                      State = component.short_name;
                    }
                    if (type === 'postal_code') {
                      Zipcode = component.long_name;
                    }
                  }
                }

                if (
                  !HouseNumber ||
                  !StreetAddress ||
                  !City ||
                  !State ||
                  !Zipcode
                ) {
                  reject(
                    'Your home address seems to be incomplete. Please check and try again.'
                  );
                  return;
                }

                this.AccountForm.value.StreetAddress = `${HouseNumber} ${StreetAddress}`;
                this.AccountForm.value.City = City;
                this.AccountForm.value.State = State;
                this.AccountForm.value.Zipcode = Zipcode;
              }

              let InArea = false;
              for (let i = 0; i < AvailableWorkArea.length; i++) {
                if (
                  getDistance(
                    data[0].geometry.location.lat(),
                    data[0].geometry.location.lng(),
                    AvailableWorkArea[i].Center.latitude,
                    AvailableWorkArea[i].Center.longitude
                  ) <=
                  AvailableWorkArea[i].Radius + 5
                ) {
                  InArea = true;
                }
              }

              if (InArea) {
                const GeoHashCenter = Geohash.encode(
                  data[0].geometry.location.lat(),
                  data[0].geometry.location.lng(),
                  4
                );
                const GeoHashNeighbours = Geohash.neighbours(GeoHashCenter);
                const GeoHashArray = Object.keys(GeoHashNeighbours).map(
                  function (n) {
                    return GeoHashNeighbours[n];
                  }
                );
                GeoHashArray.push(GeoHashCenter);

                this.firestore
                  .doc('Sudsters/' + this.UserID)
                  .set(
                    Object.assign(this.AccountForm.value, {
                      SignupStepNumber: 4,
                      WorkAreaCenter: new firebase.firestore.GeoPoint(
                        data[0].geometry.location.lat(),
                        data[0].geometry.location.lng()
                      ),
                      AvgWorkAreaLat: data[0].geometry.location.lat(),
                      WorkAreaRadius: 24140,
                      GeoHash: GeoHashCenter,
                    }),
                    { merge: true }
                  )
                  .then(() => {
                    resolve(true);
                    this.logService.logAccountCreationAccountInfo(
                      this.UserID,
                      'Account Info',
                      SignupSteps.AccountInfo,
                      this.AccountForm.value.FirstName,
                      this.AccountForm.value.LastName,
                      this.AccountForm.value.City,
                      this.AccountForm.value.State
                    );
                    trackEvent({
                      eventData: {
                        event: 'LPOnboarding_ProfileCompleted',
                        userId: this.UserID,
                      },
                    });
                  });
              } else {
                //Not in service area
                reject(
                  "Sorry, we don't service your area yet. But we'll be there soon!"
                );
                return;
              }
            }
          );
        });

      function getDistance(lat1, lon1, lat2, lon2) {
        const R = 6371; // Radius of the earth in km
        const dLat = deg2rad(lat2 - lat1); // deg2rad below
        const dLon = deg2rad(lon2 - lon1);
        const a =
          Math.sin(dLat / 2) * Math.sin(dLat / 2) +
          Math.cos(deg2rad(lat1)) *
            Math.cos(deg2rad(lat2)) *
            Math.sin(dLon / 2) *
            Math.sin(dLon / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        const d = R * c; // Distance in km
        const dm = d * 0.621371; //Distance in Mi
        return dm;

        function deg2rad(deg) {
          return deg * (Math.PI / 180);
        }
      }
    })
      .then((result) => {
        this.loading.dismiss();
        if (this.ForterAPI && this.AuthID.latestForterToken.token !== '') {
          this.AuthID.callForterAPI(
            this.UserID,
            'SignUp',
            this.AccountForm.value.FirstName,
            this.AccountForm.value.LastName,
            this.Email,
            'ACTIVE',
            'USER SIGNUP',
            'END_USER'
          );
        } else {
          this.NextStep.emit();
        }
      })
      .catch((err) => {
        this.loading.dismiss();

        if (err.includes('Your home address')) {
          this.AddressFailAttempts++;

          if (this.AddressFailAttempts >= 3) {
            this.alertController
              .create({
                header: 'Address Invalid',
                message:
                  "We're sorry, but we can't seem to find your home address on our maps. Please check your address for accuracy and try again. If you're sure that everything is accurate, you can override the verification below.",
                buttons: [
                  {
                    text: 'Override',
                    cssClass: 'secondary',
                    handler: () => {
                      this.Next(true);
                    },
                  },
                  {
                    text: 'Go Back',
                    role: 'cancel',
                  },
                ],
              })
              .then((alert) => {
                alert.present();
              });
          } else {
            this.presentAlert(
              'Error',
              err?.message || 'An error occurred while processing your request.'
            );
          }
        } else {
          this.presentAlert(
            'Error',
            err?.message || 'An error occurred while processing your request.'
          );
        }
      });
  }

  async presentLoading() {
    this.loading = await this.loadingController.create({});
    await this.loading.present();
  }

  presentICEInfo() {
    this.presentAlert(
      'Alternate Contact',
      'In the rare event we can’t reach you, having an Alternate Contact on file ensures we can handle time-sensitive situations smoothly. Your Alternate Contact will only be contacted in urgent situations as a last resort after all other attempts to reach you have been made.<br><br> Rest assured, your Alternate Contact will never receive marketing materials from us—just support when it’s truly needed. '
    );
    return;
  }
  async presentAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      cssClass: 'customClassAlert',
      buttons: ['OKAY'],
    });
    await alert.present();
  }

  formName: Record<string, string> = {
    FirstName: 'First Name',
    LastName: 'Last Name',
    StreetAddress: 'Street Address',
    AddressLine2: 'Address Line 2',
    City: 'City',
    State: 'State',
    Zipcode: 'Zipcode',
    iceFirstName: 'ICE First Name',
    iceLastName: 'ICE Last Name',
    iceRelationship: 'ICE Relationship',
    iceEmail: 'ICE Email',
  };
}
