.account-container {
  ion-item ion-item {
    --border-color: white;
  }
  ion-button {
    --background: var(--viridian-core);
  }
  #last-name-input {
    display: block;
    margin-bottom: 5px;
  }

  #error-last-name {
    color: var(--Red);
    font-weight: bold;
    font-size: 13px;
    margin-left: 30px;
    display: block;
  }

  .InputDiv {
    width: 100%;
    max-width: 500px;
    padding: 5px 10px;
    border-bottom: none;

    > label,
    > .header > label {
      font-size: 14px;
      font-weight: 700;
      line-height: 20px;
      text-align: left;
      color: var(--ion-color-dark-shade);
    }

    i {
      display: flex;
      position: relative;
      top: 2.5px;
      font-size: 18px;
      color: var(--black);
      cursor: pointer;
    }

    .ng-invalid.ng-touched {
      border: 1px solid var(--Form-Fields-Border-error);
    }
  }
}

.customClassAlert {
  .alert-wrapper {
    &.sc-ion-alert-md,
    &.sc-ion-alert-ios {
      border-radius: 10px;
    }
  }

  .alert-message {
    color: var(--content-alt);
    font-family: 'Fakt-Normal';
    font-size: 16px;
    font-weight: 400;
    line-height: 22px;
    text-align: left;

    &.sc-ion-alert-md,
    &.sc-ion-alert-ios {
      max-height: 22rem;
    }
  }

  .alert-head {
    &.sc-ion-alert-md {
      padding: 2rem 0rem 0.3rem 1.5rem;
    }

    &.sc-ion-alert-ios {
      text-align: left;
      padding: 2rem 0rem 0.3rem 1rem;
    }
  }

  .alert-title {
    &.sc-ion-alert-md,
    &.sc-ion-alert-ios {
      color: var(--black);
      font-size: max(17px, 1.0625rem);
      font-weight: 700;
      font-family: 'Fakt-Normal';
      line-height: 28px;
      font-size: 20px;
    }
  }

  .alert-button {
    &.sc-ion-alert-md,
    &.sc-ion-alert-ios {
      display: inline-flex;
      background-color: var(--Button-Color-Primary-main);
      color: var(--white);
      font-family: 'PitchSans-Medium';
      text-transform: uppercase;
      border-radius: 10px;
      font-size: 16px;
      cursor: pointer;
      width: 100%;
      height: 48px;
      font-weight: 700;

      &:hover {
        background-color: var(--Button-Color-Primary-main);
      }
      &:focus {
        outline: none;
        box-shadow: 0 0 0 3px var(--Button-Color-Primary-main);
      }
      &:active {
        background-color: var(--Button-Color-Primary-main);
      }
    }
  }

  .alert-button-inner {
    &.sc-ion-alert-md,
    &.sc-ion-alert-ios {
      justify-content: center;
    }
  }

  .alert-button-group {
    &.sc-ion-alert-md {
      padding: 0rem 1rem 2rem 1.5rem;
    }
    &.sc-ion-alert-ios {
      padding: 0rem 0.8rem 2rem;
    }
  }
}
