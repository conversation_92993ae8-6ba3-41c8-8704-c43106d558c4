<div
  id="QuestionDiv"
  *ngFor="let test of TestArray; let i = index"
  [hidden]="i != SectionNumber || ShowCorrectTransition"
>
  <h1>{{ test.Question }}</h1>

  <ion-button
    id="first-option-button"
    (click)="ClickAnswer(i, 1)"
    size="small"
    expand="block"
    fill="outline"
    class="ion-text-wrap"
    ><span id="first-option-button-span" class="button-span">{{
      test.Option1
    }}</span></ion-button
  >
  <ion-button
    id="second-option-button"
    (click)="ClickAnswer(i, 2)"
    size="small"
    expand="block"
    fill="outline"
    class="ion-text-wrap"
    ><span id="second-option-button-span" class="button-span">{{
      test.Option2
    }}</span></ion-button
  >
  <ion-button
    id="third-option-button"
    (click)="ClickAnswer(i, 3)"
    size="small"
    expand="block"
    fill="outline"
    class="ion-text-wrap"
    ><span id="third-option-button-span" class="button-span">{{
      test.Option3
    }}</span></ion-button
  >

  <ion-button
    *ngIf="skippable"
    id="skip-training-button"
    (click)="confirmSkip()"
    size="small"
    expand="block"
    fill="clear"
    >Skip</ion-button
  >
</div>

<div
  class="checkmarkDiv"
  *ngIf="ShowCorrectTransition"
  style="margin-top: 25px"
>
  <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
    <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
    <path
      class="checkmark__check"
      fill="none"
      d="M14.1 27.2l7.1 7.2 16.7-16.8"
    />
  </svg>
</div>

<ion-progress-bar [value]="ProgressValue()"></ion-progress-bar>
