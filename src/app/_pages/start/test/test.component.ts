import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/auth';
import { AngularFirestore } from '@angular/fire/firestore';
import { AlertController, ModalController } from '@ionic/angular';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { take } from 'rxjs';
import { SkipModalComponent } from 'src/app/_components/skip-modal/skip-modal.component';
import { SignupSteps } from 'src/app/_services/analytics/analytics-event.entity';
import { AnalyticsLogService } from 'src/app/_services/analytics/analytics-log.service';
import { AuthidService } from 'src/app/_services/authid.service';
import { SignUpStepsService } from 'src/app/_services/signup.service';
import { trackEvent } from 'src/app/_utils/track-event';

@UntilDestroy()
@Component({
  selector: 'app-test',
  templateUrl: './test.component.html',
  styleUrls: ['./test.component.scss'],
})
export class TestComponent implements OnInit {
  TestArray = [];
  SectionNumber = 0;
  ShowCorrectTransition = false;
  ShowWrongTransition = false;
  WrongAnswers = 0;
  skippable = false;

  UserID = this.AuthID.getID();

  @Output() NextStep = new EventEmitter();

  constructor(
    public firestore: AngularFirestore,
    private AuthID: AuthidService,
    public alertController: AlertController,
    public afAuth: AngularFireAuth,
    private logService: AnalyticsLogService,
    private signUpStepsService: SignUpStepsService,
    private modalController: ModalController
  ) {}

  ngOnInit() {
    this.firestore
      .doc<any>(`Code/Sudster`)
      .valueChanges()
      .pipe(untilDestroyed(this))
      .pipe(take(1))
      .subscribe((doc) => {
        this.TestArray = doc.Test;

        if (this.signUpStepsService.isSkipped()) {
          this.Skip();
        }
      });

    this.skippable = this.signUpStepsService.canSkip();
  }

  Skip() {
    const index = this.TestArray.length - 1;

    this.signUpStepsService.skip();
    this.SectionNumber = this.TestArray.length;

    this.NextStep.emit(7);
    this.ClickAnswer(index, this.TestArray[index].Answer);
  }

  async confirmSkip() {
    const modal = await this.modalController.create({
      component: SkipModalComponent,
      cssClass: 'SmallPopupModal',
    });

    await modal.present();

    const { data, role } = await modal.onWillDismiss();

    if (role === 'skip') {
      this.Skip();
    }

    return modal;
  }

  ClickAnswer(index, AnswerNumber) {
    if (AnswerNumber == this.TestArray[index].Answer) {
      //Correct
      this.ShowCorrectTransition = true;
      setTimeout(() => {
        this.ShowCorrectTransition = false;
        this.SectionNumber++;
        if (this.SectionNumber >= this.TestArray.length) {
          //Finished Test
          if (this.WrongAnswers <= 3) {
            //Pass - complete account setup
            const points =
              !this.signUpStepsService.isSkipped() &&
              this.signUpStepsService.canSkip()
                ? 500
                : 0;

            this.firestore.doc('Sudsters/' + this.UserID).set(
              {
                SignupStepNumber: this.signUpStepsService.isSkipped() ? 0 : 8,
                Balance: 0,
                PendingBalance: 0,
                AverageRating: {
                  Count: 1,
                  Current: 3,
                },
                SignupTime: new Date().getTime(),
                WorkRadius: 20,
                Ranking: {
                  BadgeNumber: 1,
                  Points: points,
                },
                LifetimeTotal: 0,
              },
              { merge: true }
            );
            this.NextStep.emit();

            //V3 Event
            this.logService.logAccountCreationAccountInfo(
              this.UserID,
              'Test Complete',
              SignupSteps.Test
            );
            // New Event
            trackEvent({
              eventData: {
                event: `LPOnboarding_TestCompleted`,
                userId: this.UserID,
              },
            });
          } else {
            //Fail
            this.presentAlert(
              'Try Again',
              'It looks like you got more than 3 answers wrong, please review the best practices and take the quiz again.',
              'Review Best Practices'
            );
            this.firestore
              .doc('Sudsters/' + this.UserID)
              .update({ SignupStepNumber: 6 });
            this.NextStep.emit(-1);
          }
        }
      }, 1500);
    } else {
      this.WrongAnswers++;
      this.presentAlert(
        this.TestArray[index].ExplanationTitle,
        this.TestArray[index].ExplanationBody,
        'Try Again'
      );
    }
  }

  ProgressValue() {
    if (this.TestArray != null) {
      return this.SectionNumber / this.TestArray.length;
    } else {
      return 0;
    }
  }

  async presentAlert(header: string, message: string, btnText: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: [btnText],
    });
    await alert.present();
  }
}
