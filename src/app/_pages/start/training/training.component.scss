ion-progress-bar {
  left: 10px;
  right: 10px;
  width: calc(100% - 20px);
}

.Section {
  margin-top: 20px;
  padding-bottom: 75px;

  h1 {
    font-size: 22px;
    color: var(--BlueGray5);
    margin-top: 0px;

    i {
      vertical-align: -3px;
      padding-right: 10px;
    }
  }

  div {
    font-size: 14px;
    color: var(--BlueGray5);
    text-align: left;
  }

  video {
    width: 100%;
    border: solid 5px var(--BlueGray1);
    border-radius: 5px;
    margin-top: 10px;
  }

  pre {
    display: block;
    line-height: 140%;
    font-size: 15px;
    text-align: left;
    white-space: pre-wrap;
    font-family: 'Roboto', 'Helvetica Neue', sans-serif;
  }
}

#ButtonDiv {
  left: 10px;
  right: 10px;
  margin-top: 30px;

  ion-icon {
    color: var(--BlueGray4);
    z-index: 3;
    float: left;
    margin-right: 5px;
    font-size: 18px;
    padding-top: 4px;
    display: inline-block;
  }

  ion-button {
    max-width: 300px;
    margin: 0 auto;
    width: calc(100% - 25px);
    display: inline-block;
  }
}
#take-test-button {
  --background: var(--viridian-core);
}

#TestAttempLabel {
  display: block;
  text-align: center;
  font-size: 13px;
  color: var(--BlueGray3);
  padding-top: 5px;
}

#review-training-button {
  --color: #0065ad;
}

#skip-training-button {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  z-index: 1;
  --background: var(--white);
}

ion-progress-bar {
  bottom: 50px;
}
