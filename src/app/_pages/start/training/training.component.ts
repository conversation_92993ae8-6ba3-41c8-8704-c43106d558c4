import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/auth';
import { AngularFirestore } from '@angular/fire/firestore';
import { Alert<PERSON>ontroller, ModalController } from '@ionic/angular';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import firebase from 'firebase/app';
import 'firebase/firestore';
import { SkipModalComponent } from 'src/app/_components/skip-modal/skip-modal.component';
import { SignupSteps } from 'src/app/_services/analytics/analytics-event.entity';
import { AnalyticsLogService } from 'src/app/_services/analytics/analytics-log.service';
import { AuthidService } from 'src/app/_services/authid.service';
import { SignUpStepsService } from 'src/app/_services/signup.service';
import { trackEvent } from 'src/app/_utils/track-event';
import { environment } from 'src/environments/environment';

@UntilDestroy()
@Component({
  selector: 'app-training',
  templateUrl: './training.component.html',
  styleUrls: ['./training.component.scss'],
})
export class TrainingComponent implements OnInit {
  Training;
  SectionNumber = 0;
  TimeCounter = 0;
  hasLoggedStart = false;
  hasLoggedCompletion = false;

  UserID = this.AuthID.getID();

  TestAttemptsCount = 3;

  skippable = false;

  skippableIntroContent = {
    Icon: null,
    Title: 'Rank up and earn more',
    Body: 'As a Poplin Laundry Pro, points help you rank up-which means you get notified of orders before others, giving you the best shot at growing your earnings.\n\nThis Best Practices section is optional, but passing the short quiz at the end instantly adds 500 points to your score-boosting your rank and giving you confidence for your first order.\n\nMore points = Higher rank = More orders.\nReady to level up?',
  };

  @Output() NextStep = new EventEmitter();

  constructor(
    public firestore: AngularFirestore,
    private AuthID: AuthidService,
    public afAuth: AngularFireAuth,
    public alertController: AlertController,
    private logService: AnalyticsLogService,
    private signUpStepsService: SignUpStepsService,
    private modalController: ModalController
  ) {}

  ngOnInit() {
    this.firestore
      .doc<any>(`Code/Sudster`)
      .valueChanges()
      .pipe(untilDestroyed(this))
      .subscribe((doc) => {
        if (!this.Training) {
          this.Training = doc.Training;
        }
      });

    this.firestore
      .doc<any>(`Sudsters/${this.UserID}`)
      .valueChanges()
      .pipe(untilDestroyed(this))
      .subscribe((doc) => {
        this.TestAttemptsCount = doc.TestAttempsRemaining;

        this.signUpStepsService.setState(doc.State);
        this.skippable = this.signUpStepsService.canSkip();

        if (this.skippable) {
          this.setSkippableIntroContent();
        }
      });

    setInterval(() => {
      this.TimeCounter++;
    }, 1000);
  }

  PrevSection() {
    this.SectionNumber--;
  }

  Skip() {
    this.SectionNumber = this.Training.length - 1;
    this.signUpStepsService.skip();

    this.NextSection();
    this.Next();
  }

  async confirmSkip() {
    const modal = await this.modalController.create({
      component: SkipModalComponent,
      cssClass: 'SmallPopupModal',
    });

    await modal.present();

    const { data, role } = await modal.onWillDismiss();

    if (role === 'skip') {
      this.Skip();
    }

    return modal;
  }

  BackToTraining() {
    this.signUpStepsService.undoSkip();
    this.SectionNumber = 0;
  }

  NextSection() {
    if (this.SectionNumber === 0 && !this.hasLoggedStart) {
      this.logService.logAccountCreationAccountInfo(
        this.UserID,
        'Training Started',
        SignupSteps.Training
      );
      this.hasLoggedStart = true;
    }

    this.TimeCounter = 0;
    this.SectionNumber++;

    if (
      this.SectionNumber >= this.Training.length &&
      !this.hasLoggedCompletion
    ) {
      this.logService.logAccountCreationAccountInfo(
        this.UserID,
        'Training Completed',
        SignupSteps.Training
      );
      trackEvent({
        eventData: {
          event: 'LPOnboarding_TrainingCompleted',
          userId: this.UserID,
        },
      });
      this.hasLoggedCompletion = true;
    }
  }

  SectionBody(body) {
    return body.replace(/{video:.*}/g, '');
  }

  ProgressValue() {
    if (this.Training != null) {
      return this.SectionNumber / this.Training.length;
    } else {
      return 0;
    }
  }

  Next() {
    if (this.TestAttemptsCount >= 1) {
      this.firestore.doc('Sudsters/' + this.UserID).update({
        SignupStepNumber: 7,
        TestAttempsRemaining: firebase.firestore.FieldValue.increment(-1),
      });
      this.NextStep.emit();
      this.logService.logAccountCreationAccountInfo(
        this.UserID,
        'Test Started',
        SignupSteps.Test
      );
    } else {
      this.presentAlert(
        'No Attempts Left',
        "We're sorry, but it looks like you don't have any more test attempts left."
      );
    }
  }

  async presentAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK'],
    });
    await alert.present();
  }

  GetVideoLink(body) {
    return body.split('{video:')[1].split('}')[0];
  }

  GetSectionWaitTime(index) {
    if (!environment.production) {
      return 0;
    }
    if (this.Training[index].Body.includes('{video')) {
      return parseInt(
        this.GetVideoLink(this.Training[index].Body).split('&length=')[1]
      );
    }
  }

  private setSkippableIntroContent() {
    this.Training[0] = this.skippableIntroContent;
  }
}
