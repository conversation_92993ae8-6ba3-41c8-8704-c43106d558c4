<div
  class="Section maxwidth"
  *ngFor="let section of Training; let i = index"
  [hidden]="i !== SectionNumber"
>
  <h1>
    <i class="material-icons">{{ section.Icon }}</i
    >{{ section.Title }}
  </h1>
  <pre>{{ SectionBody(section.Body) }}</pre>
  <video controls *ngIf="section.Body.includes('{video')" id="video-element">
    <source [src]="GetVideoLink(section.Body)" />
  </video>

  <!-- SKIPPABLE CONTENT BUTTON -->
  <div *ngIf="SectionNumber === 0 && skippable">
    <ion-button
      id="next-section-button-skippable"
      (click)="NextSection()"
      expand="block"
      fill="solid"
    >
      Next Section <ion-icon name="arrow-forward"></ion-icon>
    </ion-button>
  </div>

  <div id="ButtonDiv" *ngIf="!skippable || SectionNumber > 0">
    <ion-icon
      name="arrow-back"
      *ngIf="SectionNumber > 0"
      (click)="PrevSection()"
    ></ion-icon>
    <ion-button
      id="next-section-button"
      [disabled]="TimeCounter < GetSectionWaitTime(i)"
      (click)="NextSection()"
      size="small"
      expand="block"
      fill="solid"
    >
      Next Section
    </ion-button>
  </div>
  <div>
    <ion-button
      id="skip-training-button"
      (click)="confirmSkip()"
      size="small"
      expand="block"
      fill="clear"
      >Skip</ion-button
    >
  </div>
</div>

<div
  class="Section"
  *ngIf="Training != null && SectionNumber >= Training.length && !skippable"
>
  <br /><br />
  <ion-button
    id="take-test-button"
    (click)="Next()"
    expand="block"
    size="large"
  >
    Start Quiz
  </ion-button>
  <label id="TestAttempLabel"
    >Quiz Attempts Remaining: <b>{{ TestAttemptsCount }}</b></label
  >
  <ion-button
    id="review-training-button"
    style="margin-top: 10px"
    (click)="BackToTraining()"
    size="small"
    expand="block"
    fill="clear"
  >
    Review Best Practices <ion-icon name="refresh"></ion-icon>
  </ion-button>
</div>

<!-- SKIPPABLE CONTENT (Test callout) -->
<div
  class="Section maxwidth"
  *ngIf="Training != null && SectionNumber >= Training.length && skippable"
>
  <h1>Boost your rank and build confidence</h1>
  <pre>
Pass this quick quiz to instantly earn 500 points-helping you rank up faster and see more orders before others. It's a simple way to gain confidence for your first order and set yourself up for success.
  </pre>
  <pre>
You can always review best practices before getting started. Ready to go?
  </pre>

  <ion-row>
    <ion-col>
      <ion-button
        id="start-quiz-button"
        (click)="Next()"
        expand="block"
        fill="solid"
      >
        Start Quiz
      </ion-button>
    </ion-col>

    <ion-col>
      <ion-button
        id="back-to-training-button"
        (click)="BackToTraining()"
        size="small"
        expand="block"
        fill="clear"
      >
        Review Best Practices
      </ion-button>
    </ion-col>
  </ion-row>
  <div>
    <ion-button
      id="skip-training-button"
      (click)="confirmSkip()"
      size="small"
      expand="block"
      fill="clear"
      >Skip</ion-button
    >
  </div>
</div>

<ion-progress-bar [value]="ProgressValue()"></ion-progress-bar>
