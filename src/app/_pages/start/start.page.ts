import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/auth';
import { AngularFirestore } from '@angular/fire/firestore';
import { Router } from '@angular/router';
import { AlertController } from '@ionic/angular';
import { <PERSON><PERSON><PERSON><PERSON> } from '@ngneat/until-destroy';
import { ReplaySubject, takeUntil } from 'rxjs';
import { SudsterData } from 'src/app/_interfaces/sudster-data.interface';
import { SignUpStepsService } from 'src/app/_services/signup.service';

@UntilDestroy()
@Component({
  selector: 'app-start',
  templateUrl: './start.page.html',
  styleUrls: ['./start.page.scss'],
})
export class StartPage implements OnInit {
  Step = 0;
  Title = '';
  Subtitle = '';
  private destroyed$: ReplaySubject<boolean> = new ReplaySubject(1);

  constructor(
    private changeDetector: ChangeDetectorRef,
    private firestore: AngularFirestore,
    private afAuth: Ang<PERSON><PERSON><PERSON><PERSON><PERSON>,
    public alertController: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    private router: Router,
    private signUpStepsService: SignUpStepsService
  ) {}

  ngOnInit() {
    this.afAuth.user.subscribe((user) => {
      if (user) {
        if (user.uid != null) {
          this.firestore
            .doc<SudsterData>('Sudsters/' + user.uid)
            .valueChanges()
            .pipe(takeUntil(this.destroyed$))
            .subscribe((doc) => {
              this.Step = doc.SignupStepNumber;

              this.signUpStepsService.setState(doc.State);

              if (doc.SignupStepNumber === 0) {
                this.router.navigate(['home']);
              } else if (doc.SignupStepNumber == null) {
                this.Step == 1;
              }

              this.SetStep();
            });
        }
      } else {
        this.Step = 1;
        this.SetStep();
      }
    });
  }

  NextStep(event) {
    if (!event) {
      this.Step++;
      this.SetStep();
    } else if (event == -1) {
      this.Step--;
      this.SetStep();
    } else if (event >= 1) {
      this.Step = event;
      this.SetStep();
    }
  }

  SetStep() {
    if (this.Step == 1) {
      this.Title = 'Become a Poplin\nLaundry Pro Today';
    } else if (this.Step == 2) {
      this.Title = 'Get started as a Laundry Pro in 3 easy steps';
    } else if (this.Step == 3) {
      this.Title = 'Account Info';
      this.Subtitle = '';
    } else if (this.Step == 4) {
      this.Title = 'Connect Bank';
      this.Subtitle = 'How would you like to get paid?';
    } else if (this.Step == 5) {
      this.Title = 'Identity Verification';
      this.Subtitle =
        'The information you provide here is required by federal banking regulations to verify your identity and ensure secure payouts. All information is 100% secure and confidential.';
    } else if (this.Step == 6) {
      this.Title = 'Best Practices';
      this.Subtitle = '';
    } else if (this.Step == 7) {
      this.Title = this.signUpStepsService.isSkipped() ? 'Skipping...' : 'Quiz';
    } else if (this.Step == 8) {
      this.Title = '';
    }

    this.changeDetector.detectChanges();
  }

  // Angular OnDestroy is not getting triggered and keeps the subscription open
  // so we have to close it via takeUntil on ionic lifecycle ionViewDidLeave
  ionViewDidLeave(): void {
    this.destroyed$.next(true);
    this.destroyed$.complete();
  }

  async presentAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK'],
    });

    await alert.present();
  }
}
