import { FormControl } from '@angular/forms';

export const getFormErrorMessage = (formControl: FormControl): string => {
  if (formControl.hasError('required')) {
    return 'This field is required.';
  }

  if (formControl.hasError('minlength')) {
    const minLength = formControl.getError('minlength').requiredLength;
    return `Please enter at least ${minLength} characters.`;
  }

  if (formControl.hasError('email')) {
    return 'Please enter a valid email address.';
  }

  if (formControl.hasError('pattern')) {
    return 'The email format is invalid. \nPlease use a valid <NAME_EMAIL>';
  }

  if (formControl.hasError('maxlength')) {
    const maxLength = formControl.getError('maxlength').requiredLength;
    return `Please don't exceed ${maxLength} characters.`;
  }

  if (formControl.hasError('invalidPhoneNumber')) {
    return 'Please enter a valid phone number.';
  }

  return '';
};
