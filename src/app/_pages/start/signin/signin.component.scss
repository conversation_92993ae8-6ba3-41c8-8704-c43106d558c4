small {
  display: block;
  width: 270px;
  text-align: center;
  margin: 0 auto;
  margin-top: 10px;

  a {
    text-decoration: underline;
  }
}

#TOS {
  font-weight: 400;
  font-size: 14px;
  line-height: 140%;
  color: var(--<PERSON>Gray5);
  margin: 0 auto;
  margin-top: 10px;

  a {
    color: var(--BlueGray5);
  }
}

#login {
  font-weight: 400;
  font-size: 16px;
  line-height: 140%;
  color: var(--black);
  a {
    color: var(--ion-color-primary);
    cursor: pointer;
  }
}

.suggestion {
  width: 90%;
  max-width: 300px;
  margin: 10px auto;
  overflow: hidden;
  text-align: right;
  font-size: 13px;
  color: var(--BlueGray4);

  span {
    color: var(--Blue);
    font-size: 14px;
    margin: 0 3px;
    text-decoration: underline;
  }
}

#create-account-button {
  font-family: 'PitchSans-Medium', Helvetica, sans-serif;
  font-size: 16px;
  letter-spacing: 0.15em;
  --background: var(--viridian-core);
}

#create-account {
  padding-top: 6px;
}

#spacer {
  height: calc(100vh - 620px);
}
#continue-button {
  --background: var(--viridian-core);
}

#signin-link,
#reset-password-link {
  color: #0065ad !important;
}
