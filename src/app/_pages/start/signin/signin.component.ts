import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/auth';
import { AngularFirestore } from '@angular/fire/firestore';
import { Router } from '@angular/router';
import { Device } from '@capacitor/device';
import {
  Alert<PERSON>ontroller,
  LoadingController,
  ModalController,
} from '@ionic/angular';
import firebase from 'firebase';
import 'firebase/auth';
import { TermsAndPoliciesComponent } from 'src/app/_components/terms-and-policies/terms-and-policies.component';
import { SudsterData } from 'src/app/_interfaces/sudster-data.interface';
import { SignupSteps } from 'src/app/_services/analytics/analytics-event.entity';
import { AnalyticsLogService } from 'src/app/_services/analytics/analytics-log.service';
import { LaundryProService } from 'src/app/_services/api/laundry-pro.service';
import { TermsAndPoliciesService } from 'src/app/_services/api/terms-and-policies.service';
import { AuthidService } from 'src/app/_services/authid.service';
import { StatsigFactoryService } from 'src/app/_services/statsig-factory.service';
import { StatsigService } from 'src/app/_services/statsig.service';
import { AuthErrors } from 'src/app/_utils/enum';
import { trackEvent } from 'src/app/_utils/track-event';
import { environment } from 'src/environments/environment';
// eslint-disable-next-line @typescript-eslint/no-require-imports
const mailcheck = require('mailcheck');

@Component({
  selector: 'app-signin',
  templateUrl: './signin.component.html',
  styleUrls: ['./signin.component.scss'],
})
export class SigninComponent implements OnInit {
  requireTermsOfService: boolean = true;
  private termsAndPoliciesAgreement: {
    termsVersion: string;
    policyVersion: string;
  };
  Email: string;
  Password: string;
  SuggestedEmail: string;
  VerifyEmailSent = false;
  errorMap = new Map([
    [AuthErrors.EMAIL_IN_USE, 'Account exists, please login or reset password'],
    [
      AuthErrors.EMAIL_ALREADY_EXISTS,
      'Account exists, please login or reset password',
    ],
    [
      AuthErrors.WEAK_PASSWORD,
      'Please enter a stronger password, try with special characters',
    ],
    [
      AuthErrors.INVALID_PASSWORD,
      'Please enter a stronger password, try with special characters',
    ],
    [AuthErrors.INVALID_EMAIL, 'Please enter a valid email address'],
    [
      AuthErrors.NO_EMAIL_OR_PASSWORD,
      'Please enter an email address and password',
    ],
  ]);
  Months = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];

  @Output() NextStep = new EventEmitter();
  platform: string;
  private statsigService: StatsigService;

  constructor(
    public afAuth: AngularFireAuth,
    public alertController: AlertController,
    public loadingController: LoadingController,
    public firestore: AngularFirestore,
    private router: Router,
    private authID: AuthidService,
    private statsigFactoryService: StatsigFactoryService,
    private logService: AnalyticsLogService,
    private modalController: ModalController,
    private termsAndPoliciesService: TermsAndPoliciesService,
    private laundryProService: LaundryProService
  ) {
    this.statsigService = this.statsigFactoryService.getInstance();
  }

  ngOnInit(): void {
    this.afAuth.user.subscribe((user) => {
      if (user?.uid) {
        this.firestore
          .doc('Sudsters/' + user.uid)
          .get()
          .subscribe((sudsterDoc) => {
            const sudster = sudsterDoc.data() as SudsterData;

            if (user?.emailVerified && sudster.SignupStepNumber === 1) {
              const loading = this.presentLoading();
              this.firestore
                .doc('Sudsters/' + user.uid)
                .update({ SignupStepNumber: 2 })
                .then(() => {
                  loading.then(function (ld) {
                    ld.dismiss();
                  });
                  this.NextStep.emit(user);
                  this.logService.logAccountCreation(
                    user.uid,
                    'Email Verified',
                    SignupSteps.GetStarted,
                    this.Email
                  );
                  trackEvent({
                    eventData: {
                      event: 'LPOnboarding_EmailVerified',
                      userId: user.uid,
                    },
                  });
                });
            } else if (user?.emailVerified == false) {
              this.Email = user.email;
              this.VerifyEmailSent = true;
            }
          });
      }
    });
    Device.getInfo().then((device) => {
      this.platform =
        device && device.platform ? device.platform : 'not available';
    });
    this.forceStatsigReinitialization();
  }

  async loadTermsAndPoliciesFeatureFlag() {
    await this.statsigService.initializeStatsig('', {} as any);
    this.statsigService.setStatsigInitialized();
    const config = await this.statsigService.getConfigPromise(
      'require-laundrypro-terms-and-policies'
    );

    this.requireTermsOfService = config.get('explicityRequiredNewLP', false);
  }

  validateEmail(): void {
    const validated = mailcheck.run({
      email: this.Email,
      suggested: function (suggestion) {
        return suggestion.full;
      },
      empty: function () {
        return '';
      },
    });

    this.SuggestedEmail = validated;
  }

  acceptSuggestion(): void {
    this.Email = this.SuggestedEmail;
    this.SuggestedEmail = '';
  }

  private handleAfAuthError(err: any) {
    const errorMessage =
      this.errorMap.get(err.code) ?? `Unexpected error: ${err.code}`;

    this.presentAlert('Sign up', errorMessage);
  }

  private async forceStatsigReinitialization(): Promise<void> {
    this.statsigFactoryService.shutdownAll();
    this.statsigService = this.statsigFactoryService.getInstance('v4');
    await this.loadTermsAndPoliciesFeatureFlag();
  }

  async SignUp(): Promise<void> {
    await this.forceStatsigReinitialization();

    if (this.requireTermsOfService) {
      await this.SigUpWithTermOfServices();
    } else {
      await this.SignUpWithoutTermOfService();
    }
  }

  async SigUpWithTermOfServices(): Promise<void> {
    const loading = await this.loadingController.create({
      message: 'Loading terms and policies',
      keyboardClose: false,
      showBackdrop: true,
      backdropDismiss: false,
    });
    await loading.present();
    const termsAndPolicies =
      await this.termsAndPoliciesService.getCurrentTermsAndPolicy();

    loading.dismiss();
    const modal = await this.modalController.create({
      component: TermsAndPoliciesComponent,
      componentProps: {
        termsAndPolicies: {
          terms: termsAndPolicies.terms,
          policies: termsAndPolicies.policy,
        },
      },
      cssClass: 'modal-fullscreen',
      backdropDismiss: false,
    });
    modal.present();
    const confirmTermsAndPolicies = await modal.onDidDismiss();
    if (confirmTermsAndPolicies.role === 'confirm') {
      const loading = await this.presentLoading();
      try {
        this.termsAndPoliciesAgreement = confirmTermsAndPolicies.data;
        const trimmedEmail = this.Email.trim();
        const trimmedPassword = this.Password.trim();
        await this.newSudster(loading, {
          email: trimmedEmail,
          password: trimmedPassword,
        });
      } catch (e) {
        this.handleAfAuthError(e);
      } finally {
        loading.dismiss();
      }
    }
  }

  async SignUpWithoutTermOfService(): Promise<void> {
    const trimmedEmail = this.Email.trim();
    const trimmedPassword = this.Password.trim();

    const loading = await this.presentLoading();

    this.afAuth
      .createUserWithEmailAndPassword(trimmedEmail, trimmedPassword)
      .then(async (res) => {
        await this.newSudsterLegacy(loading, res);
      })
      .catch((e) => {
        loading.dismiss();
        switch (e.code) {
          case AuthErrors.EMAIL_IN_USE: {
            this.presentAlert(
              'Sign up',
              'Account exists, please login or reset password'
            );
            break;
          }
          case AuthErrors.WEAK_PASSWORD: {
            this.presentAlert(
              'Sign up',
              'Please enter a stronger password, try with special characters'
            );
            break;
          }
          case AuthErrors.INVALID_EMAIL: {
            this.presentAlert('Sign up', 'Please enter a valid email address');
            break;
          }

          case AuthErrors.NO_EMAIL_OR_PASSWORD: {
            this.presentAlert(
              'Sign up',
              'Please enter an email address and password'
            );
            break;
          }
          default: {
            this.presentAlert('Sign up', `Unexpected error: ${e.code}`);
          }
        }
      });
  }

  async SignIn(): Promise<HTMLIonAlertElement> {
    const alert = await this.alertController.create({
      header: 'Laundry Pro Login',
      inputs: [
        {
          name: 'email',
          type: 'email',
          placeholder: 'Email',
          id: 'login-email-input',
        },
        {
          name: 'password',
          type: 'password',
          placeholder: 'Password',
          id: 'login-password-input',
        },
      ],
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel',
          id: 'login-cancel-button',
          cssClass: 'secondary',
          handler: () => {},
        },
        {
          text: 'Login',
          id: 'login-submit-button',
          handler: async (event) => {
            const loading = await this.presentLoading();
            await this.emailLogin(loading, event.email, event.password);
          },
        },
      ],
    });
    await alert.present();
    return alert;
  }

  async emailLogin(
    ld: HTMLIonLoadingElement,
    email: string,
    password: string
  ): Promise<void> {
    this.afAuth
      .signInWithEmailAndPassword(email, password)
      .then(async (res) => {
        ld.dismiss();
        if (await this.checkFirestore(res.user.uid)) {
          const sudster = await this.firestore
            .doc<SudsterData>(`Sudsters/${res.user.uid}`)
            .get()
            .toPromise()
            .then((res) => {
              return res.data();
            });

          this.validateLogin();
          this.logService.logUserLoginEvent(
            res.user.uid,
            email,
            sudster.Ranking.BadgeNumber,
            sudster.AverageRating?.Current || 0,
            sudster.Ranking.Points
          );
          trackEvent({
            eventData: {
              event: 'LPAccountLoggedIn',
              loginMethod: 'Email',
              userId: res.user.uid,
            },
          });
          this.router.navigate(['home']);
        } else {
          this.presentAlert(
            'Error',
            'Invalid account information, please contact support'
          );
        }
      })
      .catch((e) => {
        ld.dismiss();
        switch (e.code) {
          case AuthErrors.USER_NOT_FOUND: {
            this.presentAlert(
              'Login',
              'No account found, please create one to sign-in'
            );
            break;
          }
          case AuthErrors.WRONG_PASSWORD: {
            this.presentAlert('Login', 'Incorrect password, please try again');
            break;
          }
          case AuthErrors.USER_DISABLED: {
            this.presentAlert(
              'Log in Denied',
              'This account has been disabled from logging into Poplin.'
            );
            break;
          }
          default: {
            this.presentAlert('Login', `Unexpected error: ${e.code}`);
          }
        }
      });
  }

  async resetPassword(): Promise<void> {
    const alert = await this.alertController.create({
      header: 'Reset Password',
      inputs: [
        {
          name: 'emailAddress',
          placeholder: 'Email',
        },
      ],
      buttons: [
        {
          text: 'Cancel',
          cssClass: 'secondary',
        },
        {
          text: 'Send',
          handler: (data) => {
            this.afAuth
              .sendPasswordResetEmail(data.emailAddress)
              .then(() => {
                this.presentAlert(
                  'Reset',
                  'Please check your email for a reset email'
                );
              })
              .catch((e) => {
                switch (e.code) {
                  case 'auth/user-not-found':
                    this.presentAlert(
                      'Reset',
                      'No account found, please create one to sign-in'
                    );
                    break;
                  default:
                    this.presentAlert('Reset', `Unexpected error: ${e.code}`);
                }
              });
          },
        },
      ],
    });
    await alert.present();
  }

  async checkFirestore(userId: string): Promise<boolean> {
    return this.firestore
      .collection('Sudsters')
      .doc(userId)
      .get()
      .toPromise()
      .then(async (x) => {
        if (x.exists) {
          await this.MigrateV2Sudsters(userId, x.data() as SudsterData);
          return true;
        } else {
          return false;
        }
      });
  }

  async MigrateV2Sudsters(userId: string, docData: SudsterData): Promise<void> {
    if (docData.Ranking == null) {
      const currentRating = docData.AverageRating.Current;
      const SyncPoints = Math.ceil(
        docData.AverageRating.Current * 20 * (docData.AverageRating.Count / 5)
      );
      let SyncBadgeNumber = 1;

      if (SyncPoints > 20000 && currentRating >= 4.9) {
        SyncBadgeNumber = 7;
      } else if (SyncPoints > 10000 && currentRating >= 4.8) {
        SyncBadgeNumber = 6;
      } else if (SyncPoints > 5000 && currentRating >= 4.7) {
        SyncBadgeNumber = 5;
      } else if (SyncPoints > 2500 && currentRating >= 4.6) {
        SyncBadgeNumber = 4;
      } else if (SyncPoints > 1000 && currentRating >= 4.5) {
        SyncBadgeNumber = 3;
      } else if (SyncPoints > 200 && currentRating >= 4) {
        SyncBadgeNumber = 2;
      }

      this.firestore.doc(`Sudsters/${userId}`).set(
        {
          Ranking: {
            BadgeNumber: SyncBadgeNumber,
            Points: SyncPoints,
          },
          LifetimeTotal: 0,
        },
        { merge: true }
      );
    }
  }

  private async logAccountCreation(
    lpUserId: string,
    lpUserEmail: string,
    policyVersion: string = undefined,
    termsVersion: string = undefined
  ): Promise<void> {
    await this.logService.logAccountCreation(
      lpUserId,
      'Create Login',
      SignupSteps.GetStarted,
      lpUserEmail
    );
    await trackEvent({
      eventData: {
        event: 'LPOnboarding_EmailSubmitted',
        userId: lpUserId,
        policyVersion,
        termsVersion,
      },
    });
  }

  async newSudsterLegacy(
    ld: HTMLIonLoadingElement,
    user: firebase.auth.UserCredential
  ): Promise<void> {
    const date = new Date();
    this.firestore
      .doc('Sudsters/' + user.user.uid)
      .set(
        {
          ContactEmail: user.user.email,
          SignupStepNumber: 1,
          SinceMonth: this.Months[date.getMonth()] + ' ' + date.getFullYear(),
          AccountRejected: false,
          AccountRejectedDetails: '',
          AlertsOnLp: true,
          AlertsOn: true,
          WorkRadius: 20,
          SignupUnixTime: date.getTime(),
          LastTOSAcceptanceDate: date,
          TestAttempsRemaining: 3,
        },
        { merge: true }
      )
      .then(() => {
        user.user
          .sendEmailVerification()
          .then(() => {
            ld.dismiss();
            this.VerifyEmailSent = true;

            this.logAccountCreation(user.user.uid, user.user.email);
          })
          .catch((e) => {
            ld.dismiss();
            this.presentAlert('SignUp', `Unexpected error: ${e.code}`);
          });
      });
  }

  async newSudster(
    ld: HTMLIonLoadingElement,
    user: {
      email: string;
      password: string;
    }
  ): Promise<void> {
    const date = new Date();
    const { policyVersion, termsVersion } = this.termsAndPoliciesAgreement;

    const landryProPayload = {
      password: user.password,
      email: user.email,
      sinceMonth: this.Months[date.getMonth()] + ' ' + date.getFullYear(),
      signupUnixTime: date.getTime(),
      lastTOSAcceptanceDate: date,
      termsAndPoliciesAgreement: {
        policyVersion,
        termsVersion,
      },
    };

    await this.laundryProService.createLaundryProUser(landryProPayload);

    const userCredentials = await this.afAuth.signInWithEmailAndPassword(
      user.email,
      user.password
    );
    const userLogged = userCredentials.user;

    await userLogged.sendEmailVerification();

    const lpId = userLogged.uid;
    const lpEmail = userLogged.email;

    this.VerifyEmailSent = true;

    await this.logAccountCreation(lpId, lpEmail, policyVersion, termsVersion);

    await trackEvent({
      eventData: {
        event: 'LPTermsAndConditionsAccepted',
        lpId: lpId,
        policyVersion,
        termsVersion,
      },
    });
  }

  Reload(): void {
    location.reload();
  }

  Logout(): void {
    this.Reload();
    this.afAuth.signOut();
  }

  async presentAlert(Title: string, Message: string): Promise<void> {
    const alert = await this.alertController.create({
      header: Title,
      message: Message,
      buttons: ['OK'],
    });
    return await alert.present();
  }

  async presentLoading(): Promise<HTMLIonLoadingElement> {
    const loading = await this.loadingController.create({
      message: 'Finishing settings, please wait.',
      keyboardClose: false,
      showBackdrop: true,
    });

    await loading.present();

    return loading;
  }

  private validateLogin(): void {
    this.statsigService
      .checkGate(environment.statsig.flags.forterLoginApi)
      .subscribe((flagIsTrue) => {
        if (flagIsTrue) {
          this.authID.validateLogin().subscribe();
        }
      });
  }
}
