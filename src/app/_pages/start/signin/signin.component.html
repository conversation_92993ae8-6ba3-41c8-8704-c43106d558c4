<div *ngIf="!VerifyEmailSent">
  <div class="InputDiv">
    <i class="material-icons">email</i>
    <input
      id="email-input"
      [(ngModel)]="Email"
      type="email"
      placeholder="Email"
      (blur)="validateEmail()"
    />
  </div>
  <div class="suggestion" *ngIf="SuggestedEmail">
    Did you mean <span (click)="acceptSuggestion()">{{ SuggestedEmail }}</span
    >?
  </div>
  <div class="InputDiv">
    <i class="material-icons">lock</i>
    <input
      id="password-input"
      [(ngModel)]="Password"
      type="password"
      placeholder="Create Password"
    />
  </div>
  <ion-button
    id="create-account-button"
    (click)="SignUp()"
    [disabled]="!Password || !Email"
    expand="block"
    >Create Account</ion-button
  >
</div>

<ion-card *ngIf="VerifyEmailSent">
  <ion-card-header>
    <ion-card-subtitle>ACTION REQUIRED</ion-card-subtitle>
    <ion-card-title
      >Confirmation Email Sent to <strong>{{ Email }}</strong></ion-card-title
    >
  </ion-card-header>

  <ion-card-content>
    A confirmation link was just sent to your email. Please go to your emails
    and click the confirmation link. Once verified, click the button below to
    continue.
    <br /><br />
    <ion-button id="continue-button" (click)="Reload()" expand="block"
      >CONTINUE</ion-button
    >
    <ion-button
      id="back-button"
      (click)="Logout()"
      size="small"
      expand="block"
      fill="clear"
      >Back</ion-button
    >
  </ion-card-content>
</ion-card>

<small id="TOS" *ngIf="!requireTermsOfService"
  >By continuing, you agree to our
  <a id="tos-link" href="https://poplin.co/terms-of-service/" target="_blank"
    >Terms of Service</a
  >
  and
  <a
    id="privacy-policy-link"
    href="https://poplin.co/privacy-policy/"
    target="_blank"
    >Privacy Policy</a
  >.</small
>
<ng-container *ngIf="!VerifyEmailSent">
  <div id="spacer"></div>
  <small id="login">
    Already a Poplin Laundry Pro?
    <br />
    <a id="signin-link" (click)="SignIn()">Log In</a>
    or
    <a id="reset-password-link" (click)="resetPassword()">Reset Password</a>
  </small>
</ng-container>
