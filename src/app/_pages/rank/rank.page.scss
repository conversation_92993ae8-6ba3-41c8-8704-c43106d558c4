#InnerDiv {
  max-width: 450px;
  margin: 0 auto;
}

#VideoButton {
  position: absolute;
  right: 12px;
  font-size: 13px;
  z-index: 3;
}

.TimeRangeSelect {
  width: 125px;
  float: right;
  font-size: 12px;
}

.StatusBadge {
  padding: 3px 10px;
  border-radius: 20px;
  display: block;
  margin-top: 10px;
  color: white;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  margin-bottom: 10px;
  float: right;
  font-size: 13px;
  text-transform: uppercase;
  font-weight: 700;
  text-align: center;
  line-height: 20px;
  width: 130px;
  height: 25px;

  i {
    font-size: 18px;
    float: left;
  }

  ion-icon {
    padding-left: 3px;
    float: right;
    margin-top: 3px;
  }
}

#DifferenceChange {
  font-size: 13px;
  font-weight: 700;
  letter-spacing: 1px;
  color: var(--BlueGray3);
  vertical-align: -6px;
  line-height: 0px;
}

.TabSection {
  padding-top: 20px;
}

.HistoryItem {
  b {
    float: left;
    padding: 5px 5px;
    background: var(--Blue);
    color: white;
    border-radius: 5px;
    font-size: 18px;
    min-width: 80px;
    text-align: center;
    border: solid 2px white;
  }

  h3 {
    float: left;
    font-size: 20px;
    margin-left: 15px;

    small {
      display: block;
      font-size: 13px;
    }
  }
}

.EmptyAlert {
  text-align: center;
  display: block;
  color: var(--BlueGray4);
}

ion-chip {
  padding: 0px 8px;
  height: 22px;
  font-size: 13px;
  font-weight: 500;
  border: solid 1px red;
  color: red;
  background: white;
}

ion-chip.Compliment {
  background: var(--Green);
  color: white;
  border: none;
}

#PerformanceList {
  ion-item-divider {
    padding-top: 20px;

    ion-label {
      font-weight: 900;
      letter-spacing: 1px;
      text-transform: uppercase;
      font-size: 14px;
      font-family: 'PitchSans-Medium', Helvetica, sans-serif;
    }
  }

  ion-item {
    ion-label {
      font-family: 'PitchSans-Medium', Helvetica, sans-serif;
      border-left: solid 2px red;
      padding-left: 7px;
    }

    ion-note {
      font-family: 'PitchSans-Medium', Helvetica, sans-serif;
      font-size: 16px;
      padding: 0;
      line-height: 52px;

      b {
        font-weight: 900;
      }
    }
  }

  .complimentItem ion-label {
    border-color: var(--Green);
  }
}

.progress-bar {
  background: linear-gradient(
    90deg,
    var(--Red) 15%,
    var(--Yellow),
    var(--GreenTint) 85%
  );
  width: 150px;
  height: 8px;
  position: relative;
  border-radius: 20px;
  // margin-top: 20px;
  // margin-bottom: 10px;
  margin: 5px 0px;

  span {
    top: -4px;
    position: absolute;
    border-radius: 20px;
    background: var(--BlueGray4);
    padding: 0px 5px;
    color: white;
    transform: translateX(-50%);
  }
}

.additional-info {
  margin-top: 10px;
  display: flex;
  width: 100%;
  font-size: 14px;
  font-weight: 600;

  .info {
    font-size: 16px;
    vertical-align: bottom;
    height: 17px;
  }

  div {
    flex-basis: 50%;
    flex-grow: 1;
  }

  .top {
    text-align: right;
  }
}
