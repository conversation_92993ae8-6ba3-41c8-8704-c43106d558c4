<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button
        color="black"
        defaultHref="home"
        icon="arrow-back"
      ></ion-back-button>
    </ion-buttons>
    <ion-title>My Rank</ion-title>

    <ion-buttons slot="end">
      <ion-button (click)="openReputation()">
        <ion-icon name="people-circle-outline" slot="icon-only"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content>
  <div id="InnerDiv">
    <ion-card mode="ios">
      <ion-button
        (click)="playVideoClick()"
        fill="clear"
        id="VideoButton"
        size="small"
        >How My Rank Works
        <ion-icon name="play-circle-outline"></ion-icon>
      </ion-button>

      <ion-card-content>
        <ion-card-subtitle
          >{{ ratingAvg }}
          <i
            class="material-icons"
            style="font-size: 13px; vertical-align: -2px"
            >star</i
          ></ion-card-subtitle
        >
        <ion-card-title>
          {{ rankPoints | thousandFormat }} pts
          <label
            (click)="openBadges()"
            [style.background]="rankColorName"
            class="StatusBadge"
            mode="ios"
            ><i class="material-icons">military_tech</i>{{ rankStatus }}
            <ion-icon name="chevron-down-outline"></ion-icon>
          </label>
        </ion-card-title>
        <div class="progress-bar">
          <span [style.left.px]="sliderPixelsLeft"
            >{{ retentionScore| number:'1.2-2' }}</span
          >
        </div>
        <ion-card-subtitle class="additional-info">
          <div (click)="showRetentionInfo()">
            RETENTION SCORE
            <ion-icon class="info" name="information-circle-outline"></ion-icon>
          </div>
          <div *ngIf="rankNumber >= 3" class="top">
            Top {{ rankPercentage }}% this year
          </div>
        </ion-card-subtitle>
        <canvas #barChart></canvas>

        <label id="DifferenceChange"
          >{{ addSymbolToPoints(pointDifference) }} pts</label
        >

        <ion-select
          (ionChange)="timeRange = $event.srcElement.value; loadRankHistory();"
          [value]="timeRange"
          class="TimeRangeSelect"
        >
          <ion-select-option value="week">Last 7 days</ion-select-option>
          <ion-select-option value="month">Last 30 days</ion-select-option>
          <ion-select-option value="quarter">Last 3 months</ion-select-option>
          <ion-select-option value="all">All time</ion-select-option>
        </ion-select>
      </ion-card-content>
    </ion-card>

    <div class="ion-padding" style="padding-top: 0px">
      <ion-segment
        (ionChange)="selectedTab = $event.srcElement.value;"
        value="points"
      >
        <ion-segment-button value="points">
          <ion-label>Points</ion-label>
        </ion-segment-button>
        <ion-segment-button value="reviews">
          <ion-label>Reviews</ion-label>
        </ion-segment-button>
        <ion-segment-button value="performance">
          <ion-label>Performance</ion-label>
        </ion-segment-button>
      </ion-segment>

      <section *ngIf="selectedTab === 'performance'" class="TabSection">
        <div *ngIf="feedbackCount == 0">
          <h4 style="text-align: center; color: gray">
            You have not received any customer feedback yet.
          </h4>
        </div>

        <div *ngIf="feedbackCount > 0">
          <app-slide-graph
            PointTitle="Performance Score"
            PopupInfo="This score is based on your customer reviews and is a major factor in your rank."
            [PointLevel]="getPerformanceLevel()"
            [PointNumber]="performanceScore + '%'"
            [Segments]="[{Label:'Poor',ColorGrade:1},{Label:'',ColorGrade:2},{Label:'',ColorGrade:3},{Label:'',ColorGrade:4},{Label:'Great',ColorGrade:5}]"
          >
          </app-slide-graph>

          <ion-list id="PerformanceList">
            <ion-item-divider>
              <ion-label style="color: var(--Green)"> Compliments</ion-label>
            </ion-item-divider>

            <ion-item
              *ngFor="let compliment of compliments"
              class="complimentItem"
            >
              <ion-label>{{ compliment.title }}</ion-label>
              <ion-note
                [style.color]="convertPercentToColor(+getPerformanceListPercentage(compliment.count))"
                slot="end"
              >
                <b>{{ getPerformanceListPercentage(compliment.count) }}%</b>
                ({{ compliment.count }})
              </ion-note>
            </ion-item>

            <ion-item-divider>
              <ion-label style="color: red"> Complaints</ion-label>
            </ion-item-divider>

            <ion-item *ngFor="let complaint of complaints">
              <ion-label>{{ complaint.title }}</ion-label>
              <ion-note
                [style.color]="convertPercentToColor(+getPerformanceListPercentage(complaint.count),true)"
                slot="end"
              >
                <b>{{ getPerformanceListPercentage(complaint.count) }}%</b>
                ({{ complaint.count }})
              </ion-note>
            </ion-item>
          </ion-list>
        </div>
      </section>

      <section *ngIf="selectedTab === 'reviews'" class="TabSection">
        <label *ngIf="reviewList.length == 0" class="EmptyAlert"
          >No reviews to show.</label
        >

        <ion-list>
          <ion-item *ngFor="let review of reviewList">
            <ion-label text-wrap>
              <h2>
                <b [style.color]="getRatingColor(review.Number)"
                  >{{ review.Number }}/5</b
                >
                - {{ review.Name }} ({{ review.Date }})
              </h2>
              <p>{{ review.Note }}</p>
              <ion-chip
                *ngFor="let compliment of review.Compliments"
                class="Compliment"
                ><h2 class="poplin-font">{{ compliment }}</h2></ion-chip
              >
              <ion-chip *ngFor="let complaint of review.Complaints"
                >{{ complaint }}
              </ion-chip>
            </ion-label>
          </ion-item>
        </ion-list>
      </section>

      <section *ngIf="selectedTab === 'points'" class="TabSection">
        <label *ngIf="historyList.length == 0" class="EmptyAlert"
          >No rank changes to show.</label
        >

        <ion-list>
          <ion-item
            (click)="openPointsOverview(historyItem)"
            *ngFor="let historyItem of historyList"
          >
            <ion-label class="HistoryItem">
              <b
                [style.background-color]="historyItem.Pending ? 'white' : convertPointsToColor(historyItem.TotalPoints,historyItem.Pending)"
                [style.border-color]="convertPointsToColor(historyItem.TotalPoints, historyItem.Pending)"
                [style.color]="!historyItem.Pending ? 'white' : convertPointsToColor(historyItem.TotalPoints, historyItem.Pending)"
                >{{ addSymbolToPoints(historyItem.TotalPoints) }} pts</b
              >
              <h3>
                {{ historyItem.Private ? '[Anonymous]' : (historyItem.Name +
                '(#' + historyItem.Order + ')') }}
                <small *ngIf="historyItem.Private != true"
                  >Updated {{ convertTimeToRelative(historyItem.Time) }}</small
                >
                <small *ngIf="historyItem.Private == true"
                  >Customer name hidden by request.</small
                >
              </h3>
            </ion-label>
          </ion-item>
        </ion-list>

        <br />
        <ion-infinite-scroll
          (ionInfinite)="loadMoreReviews($event)"
          threshold="100px"
        >
          <ion-infinite-scroll-content
            loadingSpinner="crescent"
            loadingText="Loading more..."
          >
          </ion-infinite-scroll-content>
        </ion-infinite-scroll>
      </section>
    </div>
  </div>
</ion-content>
