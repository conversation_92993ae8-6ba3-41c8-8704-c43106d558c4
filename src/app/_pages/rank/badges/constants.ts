import { RelationshipOption } from 'src/app/_components/contact-info/contact-info.component';

export const badgesList = (isExpressServiceEnabled: boolean) => [
  {
    name: 'Starter',
    rankNumber: 1,
    points: 0,
    minRating: '',
    privs: [
      { title: '1 Active Order', popup: '' },
      { title: 'Standard Payouts', popup: '' },
    ],
  },
  {
    name: 'Bronze',
    rankNumber: 2,
    points: 200,
    minRating: '4.4',
    privs: [
      { title: '3 Active Orders', popup: '' },
      {
        title: 'Scheduled Payouts',
        popup:
          'Set a payout schedule that works best for you. You can choose between weekly and monthly payouts and pick which day to receive them.',
      },
      {
        title: `Access to ${
          isExpressServiceEnabled ? 'Express' : 'Same-Day'
        } Orders`,
        popup: '',
      },
      {
        title: '<PERSON><PERSON><PERSON> Supplies Discount via Quill',
        popup:
          'Thanks to our partner Quill, get exclusive access to discounts on detergent, bags, and more! Save an average of 16% per load.',
      },
    ],
  },
  {
    name: '<PERSON>',
    rankNumber: 3,
    points: 1000,
    minRating: '4.5',
    privs: [
      { title: '5 Active Orders', popup: '' },
      {
        title: '$25 Merch Gift Card',
        popup:
          'We will send you a $25 gift card to our online store where you can pick out your favorite apparel and accessories for free.',
      },
    ],
  },
  {
    name: 'Gold',
    rankNumber: 4,
    points: 2500,
    minRating: '4.6',
    privs: [
      { title: '7 Active Orders', popup: '' },
      {
        title: 'On-Demand Payouts',
        popup:
          'Send payouts to your bank whenever you want. No need to wait for payout day.',
      },
      {
        title: '50% Keeper Discount',
        popup:
          'An expense tracking and tax filing service designed with 1099 gig workers in mind.',
      },
      {
        title: '3 Free Months of Gridwise Plus',
        popup:
          'Mileage tracking, gas discounts and access to discounted auto-maintenance, health coverage, legal support, and more.',
      },
    ],
  },
  {
    name: 'Diamond',
    rankNumber: 5,
    points: 5000,
    minRating: '4.7',
    privs: [
      { title: '10 Active Orders', popup: '' },
      {
        title: 'Instant Payouts',
        popup: 'Send your available balance to your debit card instantly.',
      },
    ],
  },
  {
    name: 'Platinum',
    rankNumber: 6,
    points: 10000,
    minRating: '4.8',
    privs: [
      { title: 'Unlimited Active Orders', popup: '' },
      {
        title: 'Advanced Instant Payouts',
        popup: 'Some payments may become available for instant payout earlier.',
      },
    ],
  },
  {
    name: 'Executive',
    rankNumber: 7,
    points: 20000,
    minRating: '4.9',
    privs: [
      {
        title: '$100 Boost to Your Account',
        popup:
          '$100 will be deposited in your Laundry Pro account within 7-10 business days of you reaching Executive. We hope you spend this on yourself, not on laundry supplies!',
      },
      {
        title: 'Meet the Poplin Team',
        popup: 'Your chance to tell Poplin how we can improve your experience.',
      },
    ],
  },
];

export const contactInfoRelationshipOptions: RelationshipOption[] = [
  { value: 'Spouse', label: 'Spouse' },
  { value: 'Partner', label: 'Partner' },
  { value: 'Parent', label: 'Parent' },
  { value: 'Daughter', label: 'Daughter' },
  { value: 'Son', label: 'Son' },
  { value: 'Sibling', label: 'Sibling' },
  { value: 'Friend', label: 'Friend' },
  { value: 'Other', label: 'Other' },
];
