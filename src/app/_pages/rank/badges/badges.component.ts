import { Component, Input, OnInit } from '@angular/core';
import { <PERSON>ert<PERSON>ontroller, ModalController } from '@ionic/angular';
import { StatsigFactoryService } from 'src/app/_services/statsig-factory.service';
import { StatsigService } from 'src/app/_services/statsig.service';
import { environment } from 'src/environments/environment';
import { badgesList } from './constants';

@Component({
  selector: 'app-badges',
  templateUrl: './badges.component.html',
  styleUrls: ['./badges.component.scss'],
})
export class BadgesComponent implements OnInit {
  @Input() RankNumber = 0;

  isExpressServiceEnabled = false;
  badgesList: any[] = [];
  private statsigService: StatsigService;
  constructor(
    private modalCtrl: ModalController,
    private alertController: AlertController,
    private statsigFactoryService: StatsigFactoryService
  ) {
    this.statsigService = this.statsigFactoryService.getInstance();
  }

  ngOnInit() {
    this.statsigService
      .checkGate(environment.statsig.flags.ExpressService)
      .subscribe((expressService) => {
        this.isExpressServiceEnabled = expressService;
      });

    this.badgesList = badgesList(this.isExpressServiceEnabled);
  }

  rankColor(rankNumber: number): string {
    const rankColors: Record<number, string> = {
      7: 'linear-gradient(to right,#0cd2fd,#0cfdb0,#ffc107, #ed02c4)',
      6: 'linear-gradient(to right,#444443,#a09f9e,#444443)',
      5: 'linear-gradient(to right,#fd25d7,#fe92eb,#fd25d7)',
      4: 'linear-gradient(to right,#ffb700,#FFD700,#ffb700)',
      3: 'linear-gradient(to right,#999999,#C0C0C0,#999999)',
      2: 'linear-gradient(to right,#cd7f32,#dfac7a,#cd7f32)',
      1: 'linear-gradient(to right,#25d7fd,#7ce7fd,#25d7fd)',
    };

    return rankColors[rankNumber];
  }

  async presentAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK'],
    });
    await alert.present();
  }

  async dismissModal() {
    await this.modalCtrl.dismiss();
  }
}
