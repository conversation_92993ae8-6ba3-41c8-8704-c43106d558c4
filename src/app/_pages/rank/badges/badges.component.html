<div class="ModalHeader">
  <ion-icon name="close" (click)="dismissModal()"></ion-icon>
  <label>My Badges</label>
</div>
<ion-content>
  <div style="padding: 15px">
    <div
      class="BadgeItem"
      *ngFor="let badgeItem of badgesList"
      [style.opacity]="RankNumber >= badgeItem.rankNumber ? '1' : '0.3'"
    >
      <div class="Line" style="top: 10px"></div>
      <label
        class="StatusBadge"
        style="float: left"
        [style.background]="
          RankNumber >= badgeItem.rankNumber
            ? rankColor(badgeItem.rankNumber)
            : 'var(--BlueGray3)'
        "
        >{{ badgeItem.name }}</label
      >
      <label *ngIf="badgeItem.rankNumber != 1" class="Points">
        <span *ngIf="badgeItem.rankNumber != 2">
          {{ badgeItem.points | thousandFormat }} pts
        </span>
        <span *ngIf="badgeItem.rankNumber === 2" style="line-height: 25px">
          {{ badgeItem.points | thousandFormat }} pts and Completed 3
          Orders</span
        >
        <span class="Rating"
          >(minimum {{ badgeItem.minRating
          }}<i class="material-icons">star</i>)</span
        ></label
      >
      <div class="PrivsDiv">
        <label *ngFor="let privItem of badgeItem.privs"
          ><i class="material-icons activeIcon">{{
            RankNumber >= badgeItem.rankNumber ? 'check_circle' : 'lock'
          }}</i>
          {{ privItem.title }}
          <i
            *ngIf="privItem.popup !== ''"
            class="material-icons infoIcon"
            (click)="presentAlert(privItem.title, privItem.popup)"
            >info_outline</i
          ></label
        >
      </div>
    </div>
  </div>
</ion-content>
