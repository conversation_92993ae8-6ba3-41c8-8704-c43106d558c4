import { Component, Input, OnInit } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { StatsigFactoryService } from 'src/app/_services/statsig-factory.service';
import { StatsigService } from 'src/app/_services/statsig.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-points-overview',
  templateUrl: './points-overview.component.html',
  styleUrls: ['./points-overview.component.scss'],
})
export class PointsOverviewComponent implements OnInit {
  @Input() Points: {
    Delivery: number;
    LastUpdated: Date;
    Pickup: number;
    Rating: number;
    Messaging: number;
    Preferred: number;
    Tip: number;
    Review: number;
    BonusPoints?: number;
    BonusMultiplier?: number;
  };
  @Input() RatingNumber: number;
  @Input() SameDayService?: boolean;
  @Input() OrderWindow?: string;

  isExpressServiceEnabled = false;

  constructor(
    private modalCtrl: ModalController,
    private statsigFactoryService: StatsigFactoryService
  ) {
    this.statsigService = this.statsigFactoryService.getInstance();
  }
  private statsigService: StatsigService;
  PointTotal = 0;
  Pending = true;

  ngOnInit() {
    this.statsigService
      .checkGate(environment.statsig.flags.ExpressService)
      .subscribe((expressservice) => {
        this.isExpressServiceEnabled = expressservice;
      });

    const orderPoints =
      (this.Points.Delivery || 0) +
      (this.Points.Pickup || 0) +
      (this.Points.Rating || 0) +
      (this.Points.Review || 0) +
      (this.Points.Preferred || 0) +
      (this.Points.Tip || 0) +
      (this.Points.Messaging || 0) +
      (this.Points.BonusPoints || 0);
    let multiplier = this.Points.BonusMultiplier || 1;
    if (orderPoints < 0) {
      multiplier = 1;
    }

    this.PointTotal = multiplier * orderPoints;

    if (this.Points.Rating != 0) {
      this.Pending = false;
    }
  }

  AddPlusToPositive(number) {
    if (number > 0) {
      return '+' + number;
    } else {
      return number;
    }
  }

  GetPointLevel(Points, Type) {
    if (Type == 'Pickup' || Type == 'Delivery') {
      if (Points >= 10) {
        return 5;
      } else if (Points >= 5) {
        return 4;
      } else if (Points >= 0) {
        return 3;
      } else if (Points >= -10) {
        return 2;
      } else {
        return 1;
      }
    } else if (Type == 'Messaging') {
      if (Points >= 5) {
        return 5;
      } else if (Points >= 3) {
        return 4;
      } else if (Points >= 1) {
        return 3;
      } else if (Points >= -5) {
        return 2;
      } else {
        return 1;
      }
    } else if (Type == 'Rating') {
      if (Points >= 10) {
        return 5;
      } else if (Points >= -25) {
        return 4;
      } else if (Points >= -50) {
        return 3;
      } else if (Points >= -75) {
        return 2;
      } else {
        return 1;
      }
    } else if (Type == 'Review') {
      if (Points >= 15) {
        return 5;
      } else if (Points >= 5) {
        return 4;
      } else if (Points >= 0) {
        return 3;
      } else if (Points >= -20) {
        return 2;
      } else {
        return 1;
      }
    } else if (Type == 'Tip') {
      if (Points >= 15) {
        return 5;
      } else if (Points >= 10) {
        return 4;
      } else if (Points >= 5) {
        return 3;
      } else if (Points >= 3) {
        return 2;
      } else {
        return 1;
      }
    } else if (Type == 'Total' && this.Pending) {
      //Pending total
      if (Points >= 15) {
        return 5;
      } else if (Points >= 6) {
        return 4;
      } else if (Points >= 4) {
        return 3;
      } else if (Points >= 0) {
        return 2;
      } else {
        return 1;
      }
    } else if (Type == 'Total' && !this.Pending) {
      //Completed total
      if (Points >= 40) {
        return 5;
      } else if (Points >= 25) {
        return 4;
      } else if (Points >= 15) {
        return 3;
      } else if (Points >= 0) {
        return 2;
      } else {
        return 1;
      }
    }
  }

  getPopupInfoPickUp(): string {
    if (this.SameDayService) {
      return '<b>Express Pickup:</b><br><br>+10 points: By pickup deadline<br>-10 points: After pickup deadline<br><br>(+10 extra points: For orders placed after 3:00 pm, pickup by 8:00 pm on the same day.)<br>';
    } else {
      return '<b>Standard Pickup:</b><br><br>+10 points: By pickup deadline<br>-10 points: After pickup deadline<br><br>(+10 extra points: For orders placed after 3:00 pm, pickup by 8:00 pm on the same day.)<br>';
    }
  }

  getPopupInfoDelivery(): string {
    if (this.SameDayService) {
      return '<b>Express Delivery:</b><br><br>+10 points: By delivery deadline<br>-20 points: After delivery deadline<br>';
    } else {
      return '<b>Standard Delivery:</b><br><br>+10 points: By delivery deadline<br>-10 points: After delivery deadline<br>';
    }
  }

  async DismissModal() {
    await this.modalCtrl.dismiss();
  }
}
