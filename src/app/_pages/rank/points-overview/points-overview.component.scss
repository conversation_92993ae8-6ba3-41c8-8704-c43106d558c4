app-slide-graph {
  border-top: solid 1px var(--BlueGray3);
  display: block;
  margin-top: 20px;
}

#OverallGraph {
  border: none !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  padding: 10px;
  padding-top: 0px;
  overflow: hidden;
  border-radius: 5px;
  margin-bottom: 30px;
  background: var(--BlueGray5);

  h1 {
    margin-top: 15px;
    font-size: 22px !important;
  }
}

:host #OverallGraph ::ng-deep h1 {
  color: white !important;
  margin-top: 15px;
  font-size: 22px !important;
}

#IncompleteLabel {
  text-align: center;
  display: block;
  font-size: 20px;
  color: var(--BlueGray4);
  margin-top: 30px;
  line-height: 140%;
  font-weight: 600;
}

.DetailedBonus {
  display: block;
  text-align: center;
  width: 280px;
  margin: 0 auto 5px;
  background: var(--BlueGray1);
  color: var(--<PERSON><PERSON>ray5);
  padding: 6px;
  border-radius: 20px;
  font-weight: 500;

  b {
    font-weight: 900;
    color: var(--Green);
  }
}

.DoubleBonus {
  border: 1px solid #f9b00d;
  color: var(--<PERSON>Gray6);

  b {
    color: #f9b00d;
  }
}
