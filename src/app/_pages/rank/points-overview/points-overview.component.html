<div class="ModalHeader">
  <ion-icon name="close" (click)="DismissModal()"></ion-icon>
  <label></label>
</div>
<ion-content>
  <div style="padding: 15px">
    <app-slide-graph
      id="OverallGraph"
      [Segments]="[
        { Label: 'Bad', ColorGrade: 1 },
        { Label: 'Poor', ColorGrade: 2 },
        { Label: 'Good', ColorGrade: 3 },
        { Label: 'Great', ColorGrade: 4 },
        { Label: 'Outstanding', ColorGrade: 5 }
      ]"
      [PointTitle]="'Total' + (RatingNumber === 0 ? ' (pending)' : '')"
      [PointNumber]="AddPlusToPositive(PointTotal)"
      [PointLevel]="GetPointLevel(PointTotal, 'Total')"
      [doubleBadge]="Points.BonusMultiplier == 2"
    >
    </app-slide-graph>

    <ion-label class="DetailedBonus" *ngIf="Points.Preferred > 0"
      >Customer Request Boost: <b>+{{ Points.Preferred }} pts</b>
    </ion-label>

    <ion-label class="DetailedBonus" *ngIf="Points.BonusPoints > 0"
      >Order Boost Points: <b>+{{ Points.BonusPoints }} pts</b>
    </ion-label>

    <ion-label
      class="DetailedBonus DoubleBonus"
      *ngIf="Points.BonusMultiplier == 2 && PointTotal > 0"
      >Double Points Boost:
      <b>+{{ PointTotal / Points.BonusMultiplier }} pts</b>
    </ion-label>

    <app-slide-graph
      [pending]="RatingNumber === 0"
      [Segments]="[
        { Label: '1/5', ColorGrade: 1 },
        { Label: '2/5', ColorGrade: 1 },
        { Label: '3/5', ColorGrade: 2 },
        { Label: '4/5', ColorGrade: 3 },
        { Label: '5/5', ColorGrade: 5 }
      ]"
      PointTitle="Customer Rating"
      [PointNumber]="AddPlusToPositive(Points.Rating)"
      [PointLevel]="GetPointLevel(Points.Rating, 'Rating')"
      [PopupInfo]="
        Points.Rating === -25 || Points.Rating === 0
          ? 'Happy customers always rate 5/5, so if you only got a 4/5, it usually means that something small was wrong.'
          : ''
      "
    >
    </app-slide-graph>

    <app-slide-graph
      *ngIf="Points.Review != null"
      [pending]="RatingNumber === 0"
      [Segments]="[
        { Label: 'Poor', ColorGrade: 1 },
        { Label: '', ColorGrade: 2 },
        { Label: 'Good', ColorGrade: 3 },
        { Label: '', ColorGrade: 4 },
        { Label: 'Outstanding', ColorGrade: 5 }
      ]"
      [PointTitle]="Points.Review >= 0 ? 'Compliments' : 'Complaints'"
      [PointNumber]="AddPlusToPositive(Points.Review)"
      [PointLevel]="GetPointLevel(Points.Review, 'Review')"
      PopupInfo="This is calculated automatically based on different indications from your customer rating. Your customer does not directly choose this score."
    >
    </app-slide-graph>

    <app-slide-graph
      [pending]="RatingNumber === 0"
      [Segments]="[
        { Label: '$0+', ColorGrade: 1 },
        { Label: '', ColorGrade: 2 },
        { Label: '', ColorGrade: 3 },
        { Label: '', ColorGrade: 4 },
        { Label: '$15+', ColorGrade: 5 }
      ]"
      PointTitle="Tip"
      [PointNumber]="AddPlusToPositive(Points.Tip)"
      [PointLevel]="GetPointLevel(Points.Tip, 'Tip')"
      PopupInfo="Usually, customers tip based on your performance. You're rewarded 1 point per tip dollar. You will never lose points based on the tip."
    >
    </app-slide-graph>

    <app-slide-graph
      [Segments]="[
        { Label: 'Very Late', ColorGrade: 1 },
        { Label: 'Late', ColorGrade: 2 },
        { Label: 'On Time', ColorGrade: 3 },
        { Label: 'Fast', ColorGrade: 4 },
        { Label: 'Very Fast', ColorGrade: 5 }
      ]"
      PointTitle="Pickup"
      [PointNumber]="AddPlusToPositive(Points.Pickup)"
      [PointLevel]="GetPointLevel(Points.Pickup, 'Pickup')"
      [PopupInfo]="getPopupInfoPickUp()"
    >
    </app-slide-graph>

    <app-slide-graph
      [Segments]="[
        { Label: 'Very late', ColorGrade: 1 },
        { Label: 'Late', ColorGrade: 2 },
        { Label: 'On Time', ColorGrade: 3 },
        { Label: 'Early', ColorGrade: 4 },
        { Label: 'Very Early', ColorGrade: 5 }
      ]"
      PointTitle="Delivery"
      [PointNumber]="AddPlusToPositive(Points.Delivery)"
      [PointLevel]="GetPointLevel(Points.Delivery, 'Delivery')"
      [PopupInfo]="getPopupInfoDelivery()"
    >
    </app-slide-graph>

    <app-slide-graph
      [Segments]="[
        { Label: 'Poor', ColorGrade: 1 },
        { Label: '', ColorGrade: 2 },
        { Label: 'Good', ColorGrade: 3 },
        { Label: '', ColorGrade: 4 },
        { Label: 'Super', ColorGrade: 5 }
      ]"
      PointTitle="Messaging"
      [PointNumber]="AddPlusToPositive(Points.Messaging)"
      [PointLevel]="GetPointLevel(Points.Messaging, 'Messaging')"
      PopupInfo="The faster you respond to your customer’s messages (plus the friendlier and more professional you are), the more points you get.<br><br>Respond to all messages within 1 hour and always make sure to be the last to respond, even if there's nothing important to say. Your last message might be... <br><br>'Thank you.'<br>'Got it.'<br>'Have a nice day!'"
    >
    </app-slide-graph>
  </div>
</ion-content>
