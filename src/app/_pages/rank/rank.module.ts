import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { RankPageRoutingModule } from './rank-routing.module';

import { RankPage } from './rank.page';
import { PointsOverviewComponent } from './points-overview/points-overview.component';
import { SlideGraphComponent } from 'src/app/_components/slide-graph/slide-graph.component';
import { ReputationComponent } from './reputation/reputation.component';
import { BadgesComponent } from './badges/badges.component';
import { ThousandFormatPipe } from '../../_pipes/thousand-format.pipe';

@NgModule({
  imports: [CommonModule, FormsModule, IonicModule, RankPageRoutingModule],
  declarations: [
    RankPage,
    PointsOverviewComponent,
    SlideGraphComponent,
    ReputationComponent,
    BadgesComponent,
    ThousandFormatPipe,
  ],
})
export class RankPageModule {}
