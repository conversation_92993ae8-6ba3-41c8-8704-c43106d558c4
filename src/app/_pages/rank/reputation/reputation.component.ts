import { Component, OnInit } from '@angular/core';
import { AngularFirestore } from '@angular/fire/firestore';
import { AngularFireFunctions } from '@angular/fire/functions';
import { LoadingController, ModalController } from '@ionic/angular';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { SudsterData } from 'src/app/_interfaces/sudster-data.interface';
import { AuthidService } from 'src/app/_services/authid.service';

@UntilDestroy()
@Component({
  selector: 'app-reputation',
  templateUrl: './reputation.component.html',
  styleUrls: ['./reputation.component.scss'],
})
export class ReputationComponent implements OnInit {
  ListData: Array<{
    Name: string;
    City: string;
    Signup: string;
    Points: number;
    Badge: number;
  }> = [];
  LocalListData: Array<{
    Name: string;
    City: string;
    Signup: string;
    Points: number;
    Badge: number;
  }> = [];
  LastDoc;

  constructor(
    private modalCtrl: ModalController,
    private cloudFunctions: AngularFireFunctions,
    private loadingController: LoadingController,
    private AuthID: AuthidService,
    public firestore: AngularFirestore
  ) {}

  SudsterData: SudsterData;
  UserID = this.AuthID.getID();
  state: string | undefined; // check if better way later
  LeaderboardSetting = 'national';

  // TODO move this array to Interfaces

  sudShareStates = [
    {
      name: 'Alabama',
      initials: 'AL',
    },
    {
      name: 'Arizona',
      initials: 'AZ',
    },
    {
      name: 'Arkansas',
      initials: 'AR',
    },
    {
      name: 'California',
      initials: 'CA',
    },
    {
      name: 'Colorado',
      initials: 'CO',
    },
    {
      name: 'Connecticut',
      initials: 'CT',
    },
    {
      name: 'District of Columbia',
      initials: 'DC',
    },
    {
      name: 'Florida',
      initials: 'FL',
    },
    {
      name: 'Georgia',
      initials: 'GA',
    },
    {
      name: 'Idaho',
      initials: 'ID',
    },
    {
      name: 'Illinois',
      initials: 'IL',
    },
    {
      name: 'Indiana',
      initials: 'IN',
    },
    {
      name: 'Iowa',
      initials: 'IA',
    },
    {
      name: 'Kansas',
      initials: 'KS',
    },
    {
      name: 'Kentucky',
      initials: 'KY',
    },
    {
      name: 'Louisiana',
      initials: 'LA',
    },
    {
      name: 'Maryland',
      initials: 'MD',
    },
    {
      name: 'Massachusetts',
      initials: 'MA',
    },
    {
      name: 'Michigan',
      initials: 'MI',
    },
    {
      name: 'Minnesota',
      initials: 'MN',
    },
    {
      name: 'Mississippi',
      initials: 'MS',
    },
    {
      name: 'Missouri',
      initials: 'MO',
    },
    {
      name: 'Nebraska',
      initials: 'NE',
    },
    {
      name: 'Nevada',
      initials: 'NV',
    },
    {
      name: 'New Jersey',
      initials: 'NJ',
    },
    {
      name: 'New Mexico',
      initials: 'NM',
    },
    {
      name: 'New York',
      initials: 'NY',
    },
    {
      name: 'North Carolina',
      initials: 'NC',
    },
    {
      name: 'North Dakota',
      initials: 'ND',
    },
    {
      name: 'Ohio',
      initials: 'OH',
    },
    {
      name: 'Oklahoma',
      initials: 'OK',
    },
    {
      name: 'Oregon',
      initials: 'OR',
    },
    {
      name: 'Pennsylvania',
      initials: 'PA',
    },
    {
      name: 'South Carolina',
      initials: 'SC',
    },
    {
      name: 'South Dakota',
      initials: 'SD',
    },
    {
      name: 'Tennessee',
      initials: 'TN',
    },
    {
      name: 'Texas',
      initials: 'TX',
    },
    {
      name: 'Utah',
      initials: 'UT',
    },
    {
      name: 'Virginia',
      initials: 'VA',
    },
    {
      name: 'Washington',
      initials: 'WA',
    },
    {
      name: 'West Virginia',
      initials: 'WV',
    },
    {
      name: 'Wisconsin',
      initials: 'WI',
    },
  ];
  ngOnInit() {
    this.LoadSudsterList();
    this.firestore
      .doc<SudsterData>(`Sudsters/${this.UserID}`)
      .valueChanges()
      .pipe(untilDestroyed(this))
      .subscribe(async (doc) => {
        // if state !== state.toUpperCase() --> update user with new state to uppercase
        if (doc.State !== doc.State.toUpperCase()) {
          await this.firestore
            .doc(`Sudsters/${this.UserID}`)
            .update({ State: doc.State.toUpperCase() });
        }
        this.state = doc.State.toUpperCase();
        this.LoadLocalSudsterList();
      });
  }

  LoadSudsterList() {
    const loading = this.presentLoading();

    this.cloudFunctions
      .httpsCallable('SudsterV3_GetReputationList')({ LastItem: this.LastDoc })
      .toPromise()
      .then((res) => {
        this.LastDoc = res.LastItem;
        this.ListData = this.ListData.concat(res.data);
        loading.then(function (ld) {
          ld.dismiss();
        });
      })
      .catch((err) => {
        loading.then(function (ld) {
          ld.dismiss();
        });
      });
  }

  LoadLocalSudsterList() {
    const loading = this.presentLoading();
    this.cloudFunctions
      .httpsCallable('SudsterV3_GetReputationList')({
        LastItem: this.LastDoc,
        State: this.state,
      })
      .toPromise()
      .then((res) => {
        this.LastDoc = res.LastItem;
        this.LocalListData = res.data;
        loading.then(function (ld) {
          ld.dismiss();
        });
      })
      .catch((err) => {
        loading.then(function (ld) {
          ld.dismiss();
        });
      });
  }

  handleStateChange(e) {
    this.state = e.currentTarget.value;
    this.LoadLocalSudsterList();
  }
  async DismissModal() {
    await this.modalCtrl.dismiss();
  }

  numberWithCommas(x) {
    return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }

  async presentLoading() {
    const loading = await this.loadingController.create({});
    await loading.present();
    return loading;
  }

  segmentChanged(e: any) {
    this.LeaderboardSetting = e.currentTarget.value;
  }
}
