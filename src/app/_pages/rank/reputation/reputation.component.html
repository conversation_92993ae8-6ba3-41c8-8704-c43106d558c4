<ion-content>
  <div class="ModalHeader">
    <ion-icon name="close" (click)="DismissModal()"></ion-icon>
    <label>Leader Board</label>
  </div>
  <div class="leaderboard-segment">
    <ion-segment
      mode="ios"
      value="national"
      (ionChange)="segmentChanged($event)"
    >
      <ion-segment-button value="national">
        <ion-label>National</ion-label>
      </ion-segment-button>
      <ion-segment-button value="local">
        <ion-label>Local ({{ state }})</ion-label>
      </ion-segment-button>
    </ion-segment>
  </div>

  <ion-list *ngIf="LeaderboardSetting === 'national'">
    <ion-item *ngFor="let item of ListData; let i = index">
      <b>#{{ i + 1 }}</b>
      <ion-label>
        <h1>{{ item.Name }}</h1>
        <p>Since {{ item.Signup }}</p>
      </ion-label>
      <ion-note slot="end">
        {{ numberWithCommas(item.Points) }} <small>pts</small>
      </ion-note>
    </ion-item>
  </ion-list>

  <ion-list *ngIf="LeaderboardSetting === 'local'">
    <ion-item class="leaderboard-dropdown">
      <!-- <ion-label>{{this.state}}</ion-label> -->
      <ion-select
        placeholder="Select State"
        (ionChange)="handleStateChange($event)"
        mode="ios"
      >
        <ion-select-option
          *ngFor="let state of sudShareStates"
          value="{{ state.initials }}"
          >{{ state.name }}</ion-select-option
        >
      </ion-select>
    </ion-item>

    <ion-item *ngFor="let item of LocalListData; let i = index">
      <b>#{{ i + 1 }}</b>
      <ion-label>
        <h1>{{ item.Name }}</h1>
        <p>Since {{ item.Signup }}</p>
      </ion-label>
      <ion-note slot="end">
        {{ numberWithCommas(item.Points) }} <small>pts</small>
      </ion-note>
    </ion-item>
  </ion-list>

  <!-- <ion-infinite-scroll threshold="100px" (ionInfinite)="loadData($event)">
    <ion-infinite-scroll-content
      loadingSpinner="bubbles"
      loadingText="Loading more data...">
    </ion-infinite-scroll-content>
  </ion-infinite-scroll> -->
</ion-content>
