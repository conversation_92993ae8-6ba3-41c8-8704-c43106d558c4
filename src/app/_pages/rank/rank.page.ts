import { Component, OnInit, ViewChild } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/auth';
import { AngularFirestore } from '@angular/fire/firestore';
import { registerPlugin } from '@capacitor/core';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>er,
  IonInfiniteScroll,
  ModalController,
} from '@ionic/angular';
import { untilDestroyed } from '@ngneat/until-destroy';
import { Chart } from 'chart.js';
import { distinctUntilChanged } from 'rxjs';
import { debounceTime } from 'rxjs/operators';
import { OrderData } from 'src/app/_interfaces/order-data.interface';
import { SudsterData } from 'src/app/_interfaces/sudster-data.interface';
import { VIDEOREFS } from 'src/app/_interfaces/videoRefs';
import { AuthidService } from 'src/app/_services/authid.service';
import { PlayVideoService } from 'src/app/_services/play-video.service';
import { TimeRange } from '../../_utils/enum';
import { BadgesComponent } from './badges/badges.component';
import { PointsOverviewComponent } from './points-overview/points-overview.component';
import { ReputationComponent } from './reputation/reputation.component';

const AppRequestReview: any = registerPlugin('AppRequestReview');

@Component({
  selector: 'app-rank',
  templateUrl: './rank.page.html',
  styleUrls: ['./rank.page.scss'],
})
export class RankPage implements OnInit {
  @ViewChild('barChart') barChart;
  @ViewChild(IonInfiniteScroll) infiniteScroll: IonInfiniteScroll;
  bars: any;
  rankColorName: string;
  rankPercentage: string;
  sliderPixelsLeft = 0;
  rankStatus = '';
  rankPoints = 0;
  ratingCount = 0;
  ratingAvg = '0';
  rankNumber = 0;
  pointDifference = 0;
  selectedTab = 'points';
  timeRange = 'week';
  firstLoadTimeRange = true;
  compliments: Array<{ count: number; title: string }>;
  complaints: Array<{ count: number; title: string }>;
  feedbackCount = 0;
  retentionScore = 4;
  hasRetention = false;
  performanceScore: number;
  sudsterData: SudsterData;
  userID = this.authID.getID();
  reviewList = [];
  historyList = [];

  constructor(
    private alertController: AlertController,
    public firestore: AngularFirestore,
    public afAuth: AngularFireAuth,
    private modalController: ModalController,
    private playVideo: PlayVideoService,
    private authID: AuthidService
  ) {}

  ngOnInit() {
    setInterval(this.rankStatus, 1000);

    this.firestore
      .doc<SudsterData>(`Sudsters/${this.userID}`)
      .valueChanges()
      .pipe(debounceTime(300), distinctUntilChanged(), untilDestroyed(this))
      .subscribe((doc) => {
        this.rankPoints = doc.Ranking.Points;
        this.rankNumber = doc.Ranking.BadgeNumber;
        this.rankColorName = this.rankColor(this.rankNumber);
        this.rankPercentage = this.getRankPercentage();
        const averageRating = doc.AverageRating.Current.toString();
        this.ratingAvg = averageRating.slice(0, averageRating.indexOf('.') + 3);
        this.ratingCount = doc.AverageRating.Count;
        this.compliments = doc.AverageRating.Compliments?.sort((a, b) =>
          a.count < b.count ? 1 : b.count < a.count ? -1 : 0
        );
        this.complaints = doc.AverageRating.Complaints?.sort((a, b) =>
          a.count < b.count ? 1 : b.count < a.count ? -1 : 0
        );
        this.feedbackCount = doc.AverageRating.FeedbackCount || 0;
        this.performanceScore = doc.PerformanceScore || 100;
        this.sudsterData = doc;

        this.setRetentionScore(doc);
        this.convertLevelToRankBadge();
      });

    this.loadRankHistory();
    this.loadReviews();
  }

  getPerformanceListPercentage(count: number | string) {
    if (count) {
      return Math.min((+count / this.feedbackCount) * 100, 100).toFixed(0);
    }

    return 0;
  }

  convertPercentToColor(percent: number, isNegative = false): string {
    const adjustedPercent = isNegative ? 100 - percent * 5 : percent;

    const thresholds = [
      { limit: 80, color: '#2ecc71' },
      { limit: 60, color: '#16a085' },
      { limit: 40, color: '#f1c40f' },
      { limit: 20, color: '#e67e22' },
      { limit: 0, color: '#e74c3c' },
    ];

    // find color
    return (
      thresholds.find(({ limit }) => adjustedPercent >= limit)?.color ??
      '#e74c3c'
    );
  }

  getPerformanceLevel(): number {
    const thresholds = [
      { limit: 80, level: 5 },
      { limit: 60, level: 4 },
      { limit: 40, level: 3 },
      { limit: 20, level: 2 },
      { limit: 1, level: 1 },
    ];

    return (
      thresholds.find(({ limit }) => this.performanceScore >= limit)?.level ?? 0
    );
  }

  loadReviews() {
    this.firestore
      .collection<OrderData>('Orders', (ref) =>
        ref
          .where('SudsterID', '==', this.userID)
          .orderBy('Rating.Time', 'desc')
          .limit(50)
      )
      .valueChanges({ idField: 'OrderNumber' })
      .subscribe((collection) => {
        this.reviewList = [];

        collection.forEach((doc) => {
          if (doc.Rating.Private != true && doc.Rating.Number) {
            this.reviewList.push({
              Number: doc.Rating.Number,
              Name: doc.CustomerFirstName,
              Date: doc.Rating.Time.toDate().toDateString(),
              Note: doc.Rating.Note,
              Compliments: doc.Rating.Compliments,
              Complaints: doc.Rating.Complaints,
            });
          }
        });
        setTimeout(() => {
          if (parseFloat(this.ratingAvg) > 4.5 && this.ratingCount > 3) {
            AppRequestReview.requestReview();
          }
        }, 3000);
      });
  }

  loadRankHistory() {
    const TimeQueryRange = new Date();
    if (this.timeRange === TimeRange.week) {
      TimeQueryRange.setDate(TimeQueryRange.getDate() - 8);
    } else if (this.timeRange === TimeRange.month) {
      TimeQueryRange.setDate(TimeQueryRange.getDate() - 30);
    } else if (this.timeRange === TimeRange.quarter) {
      TimeQueryRange.setDate(TimeQueryRange.getDate() - 90);
    } else if (this.timeRange === TimeRange.all) {
      TimeQueryRange.setTime(0);
    }

    this.firestore
      .collection<OrderData>('Orders', (ref) =>
        ref
          .where('SudsterID', '==', this.userID)
          .where('Points.LastUpdated', '>=', TimeQueryRange)
          .orderBy('Points.LastUpdated', 'desc')
          .limit(1000)
      )
      .valueChanges({ idField: 'OrderNumber' })
      .subscribe((collection) => {
        collection = collection.reverse();

        if (collection.length < 2 && this.firstLoadTimeRange == true) {
          if (this.timeRange === TimeRange.week) {
            this.timeRange = TimeRange.month;
          } else if (this.timeRange === TimeRange.month) {
            this.timeRange = TimeRange.quarter;
          } else if (this.timeRange === TimeRange.quarter) {
            this.timeRange = TimeRange.all;
            this.firstLoadTimeRange = false;
          }

          this.loadRankHistory();
          return;
        } else {
          this.firstLoadTimeRange = false;
          this.pointDifference = 0;
          let lastDateAdded: number;

          const GraphData = [];
          collection.forEach((doc) => {
            this.pointDifference +=
              (doc.Points.BonusMultiplier || 1) *
              ((doc.Points.Delivery || 0) +
                (doc.Points.Pickup || 0) +
                (doc.Points.Rating || 0) +
                (doc.Points.Review || 0) +
                (doc.Points.Preferred || 0) +
                (doc.Points.Tip || 0) +
                (doc.Points.Messaging || 0) +
                (doc.Points.BonusPoints || 0));
          });

          collection.forEach((doc) => {
            let orderPoints =
              (doc.Points.Delivery || 0) +
              (doc.Points.Pickup || 0) +
              (doc.Points.Rating || 0) +
              (doc.Points.Review || 0) +
              (doc.Points.Preferred || 0) +
              (doc.Points.Tip || 0) +
              (doc.Points.Messaging || 0) +
              (doc.Points.BonusPoints || 0);
            orderPoints = orderPoints * (doc.Points.BonusMultiplier || 1);
            if (lastDateAdded === doc.Points.LastUpdated.toDate().getDate()) {
              GraphData[GraphData.length - 1].y =
                GraphData[GraphData.length - 1].y + orderPoints;
            } else {
              lastDateAdded = doc.Points.LastUpdated.toDate().getDate();
              GraphData.push({
                x: doc.Points.LastUpdated.toDate(),
                y: orderPoints,
              });
            }
          });

          this.createBarChart(GraphData);

          // Rank History
          this.historyList = [];
          collection.forEach((doc) => {
            const orderPoints =
              (doc.Points.Delivery || 0) +
              (doc.Points.Pickup || 0) +
              (doc.Points.Rating || 0) +
              (doc.Points.Review || 0) +
              (doc.Points.Preferred || 0) +
              (doc.Points.Tip || 0) +
              (doc.Points.Messaging || 0) +
              (doc.Points.BonusPoints || 0);
            let multiplier = doc.Points.BonusMultiplier || 1;
            if (orderPoints < 0) {
              multiplier = 1;
            }
            this.historyList.push({
              TotalPoints: multiplier * orderPoints,
              Points: doc.Points,
              Name: doc.CustomerFirstName,
              Order: doc.OrderId ? doc.OrderId : doc.OrderNumber,
              Time: doc.Points.LastUpdated.toDate().getTime(),
              Pending: doc.Rating.Number === 0,
              Private: doc.Rating.Private,
              Rating: doc.Rating.Number,
              SameDayService: doc.SameDayService,
              OrderWindow: doc.OrderWindow,
            });
          });
          this.historyList.reverse();
        }
      });
  }

  loadMoreReviews(event) {
    setTimeout(() => {
      event.target.complete();
      // App logic to determine if all data is loaded
      // and disable the infinite scroll
      if (this.reviewList.length == 1000) {
        event.target.disabled = true;
      }
    }, 500);
  }

  createBarChart(data) {
    let dataReduced = [];

    if (data.length > 14) {
      let SkipCount = 0;
      for (let i = 0; i < data.length; i++) {
        if (i == SkipCount) {
          SkipCount += Math.floor(data.length / 14);
          dataReduced.push(data[i]);
        }
      }
    } else {
      dataReduced = data;
    }

    this.bars = new Chart(this.barChart.nativeElement, {
      type: 'bar',
      data: {
        datasets: [
          {
            // data: testData,
            data: dataReduced,
            backgroundColor: '#fd7ce7',
            borderColor: '#fd7ce7',
            borderWidth: 2,
          },
        ],
      },
      options: {
        legend: {
          display: false,
        },
        scales: {
          xAxes: [
            {
              type: 'time',
              time: {
                unit: 'day',
              },
              ticks: {
                autoSkipPadding: 30,
              },
            },
          ],
          yAxes: [
            {
              ticks: {
                autoSkipPadding: 40,
                beginAtZero: true,
              },
            },
          ],
        },
      },
    });

    // this.bars = new Chart(this.barChart.nativeElement, {
    //   type: 'line',
    //   data: {
    //     datasets: [{
    //       data: [{
    //         x: new Date(),
    //         y: 1
    //       }, {
    //         t: new Date(),
    //         y: 10
    //       }],
    //       backgroundColor: '#fee9fb',
    //       borderColor: '#fd25d7',
    //       borderWidth: 2
    //     }],
    //   },
    //   options: {
    //     legend: {
    //       display: false
    //     },
    //     scales: {
    //       yAxes: [{
    //         ticks: {
    //           autoSkipPadding: 50
    //         }
    //       }]
    //     }
    //   },
    // });
  }

  convertLevelToRankBadge(): void {
    const rankMap: Record<number, string> = {
      7: 'Executive',
      6: 'Platinum',
      5: 'Diamond',
      4: 'Gold',
      3: 'Silver',
      2: 'Bronze',
      1: 'Starter',
    };

    this.rankStatus = rankMap[this.rankNumber] ?? '';
  }

  rankColor(RankNumber: number): string {
    const rankColors: Record<number, string> = {
      7: 'linear-gradient(to right,#0cd2fd,#0cfdb0,#ffc107, #ed02c4)',
      6: 'linear-gradient(to right,#444443,#a09f9e,#444443)',
      5: 'linear-gradient(to right,#fd25d7,#fe92eb,#fd25d7)',
      4: 'linear-gradient(to right,#ffb700,#FFD700,#ffb700)',
      3: 'linear-gradient(to right,#999999,#C0C0C0,#999999)',
      2: 'linear-gradient(to right,#cd7f32,#dfac7a,#cd7f32)',
      1: 'linear-gradient(to right,#25d7fd,#7ce7fd,#25d7fd)',
    };

    return rankColors[RankNumber];
  }

  getRankPercentage(): string {
    const rankPercentages: Record<number, string> = {
      7: '0.1',
      6: '1',
      5: '5',
      4: '10',
      3: '20',
      2: '30',
    };

    return rankPercentages[this.rankNumber];
  }

  async presentAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK'],
    });
    await alert.present();
  }

  getRatingColor(Rating: number): string {
    const ratingColors: Record<number, string> = {
      5: '#2ecc71',
      4: '#16a085',
      3: '#f1c40f',
      2: '#e67e22',
      1: '#e74c3c',
    };

    return ratingColors[Rating];
  }

  addSymbolToPoints(value: number) {
    if (value >= 1) {
      return '+' + value;
    }

    return value;
  }

  convertTimeToRelative(time: number) {
    const msPerMinute = 60 * 1000;
    const msPerHour = msPerMinute * 60;
    const msPerDay = msPerHour * 24;
    const msPerMonth = msPerDay * 30;
    const msPerYear = msPerDay * 365;

    const elapsed = new Date().getTime() - time;

    if (elapsed < msPerMinute) {
      return Math.round(elapsed / 1000) + ' seconds ago';
    } else if (elapsed < msPerHour) {
      return Math.round(elapsed / msPerMinute) + ' minutes ago';
    } else if (elapsed < msPerDay) {
      return Math.round(elapsed / msPerHour) + ' hours ago';
    } else if (elapsed < msPerMonth) {
      return '' + Math.round(elapsed / msPerDay) + ' days ago';
    } else if (elapsed < msPerYear) {
      return '' + Math.round(elapsed / msPerMonth) + ' months ago';
    } else {
      return '' + Math.round(elapsed / msPerYear) + ' years ago';
    }
  }

  convertPointsToColor(points: number, pending: boolean) {
    let PendingSubtract = 0;

    if (pending) {
      PendingSubtract = 15;
    }

    if (points >= 35 - PendingSubtract) {
      return '#1abc9c';
    } else if (points >= 25 - PendingSubtract) {
      return '#2ecc71';
    } else if (points >= 15 - PendingSubtract) {
      return '#f1c40f';
    } else if (points >= 0 - PendingSubtract) {
      return '#e67e22';
    }
    return '#e74c3c';
  }

  async openPointsOverview(Order: OrderData) {
    const modal = await this.modalController.create({
      component: PointsOverviewComponent,
      componentProps: {
        Points: Order.Points,
        RatingNumber: Order.Rating,
        SameDayService: Order.SameDayService,
        OrderWindow: Order.OrderWindow,
      },
    });

    return await modal.present();
  }

  async openReputation() {
    const modal = await this.modalController.create({
      component: ReputationComponent,
      componentProps: {
        SudsterData: this.sudsterData,
      },
    });

    return await modal.present();
  }

  async openBadges() {
    const modal = await this.modalController.create({
      component: BadgesComponent,
      componentProps: {
        RankNumber: this.rankNumber,
      },
    });

    return await modal.present();
  }

  playVideoClick() {
    this.playVideo.open(VIDEOREFS.rankPage.ref);
  }

  setRetentionScore(sudster: SudsterData) {
    this.hasRetention = true;
    this.retentionScore = sudster.Retention?.Score || 4;
    this.sliderPixelsLeft = 150 * ((this.retentionScore - 1) / 4);
  }

  showRetentionInfo() {
    this.presentAlert(
      'Retention Score',
      `Your Customer Retention score indicates how likely your customers are to continue to use Poplin. It's an important signal that impacts how we determine your eligibility for orders.<br/>
    <br/>
   <h5>NEW LAUNDRY PRO:</h5>
    It takes time for Retention Scores to calibrate in new accounts. In the meantime, your score will default at 4.00. <br/>
    <br/>
    <h5>IT’S RELATIVE:</h5>
    The algorithm looks at your customer retention relative to the average customer retention in the network. Your Laundry Pro rank will only be negatively affected if your customer retention is abnormal, in other words, if it's below the average in the network, and/or, if it detects that you're losing the type of customer that should be remaining on the platform. This really only occurs in two situations. One, if a Laundry Pro is providing a below average service. And two, if a Laundry Pro is misusing the platform and trying to take customers off it. <br/>
    <br/><h5>HOTEL GUESTS:</h5>
    The algorithm knows which customers are hotel guests and takes into account that they are likely to be short term customers. Servicing hotel guests does not risk your customer rank. In fact, we know these addresses, know that they are often not long term customers, and the algorithm actually favors Laundry Pros who service hotel guests. <br/>
    <br/><h5>WHAT IF I DON’T TAKE ORDERS ALL THE TIME:</h5>
    Not accepting orders doesn’t  affect your Laundry Pro rank or your Customer Retention score. Your customers, assuming they've been serviced well, will continue with the platform using other Laundry Pros and your retention rating will remain intact. Your Laundry Pro rank is not negatively impacted if your customers stay on the platform but are serviced by other Laundry Pros. In other words, your customer retention rating is not a function of whether the customer continues to use YOU, it's a function of whether or not the customer continues to use Poplin, the laundry service platform. `
    );
  }
}
