import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  CUSTOM_ELEMENTS_SCHEMA,
  Component,
  ElementRef,
  HostBinding,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
  ViewChild,
  reflectComponentType,
} from '@angular/core';
import { BaseCustomComponent } from '../utils/base-custom.component';
import { componentIdFnFactory } from '../utils/define-component-id';

@Component({
  selector: 'poplin-icon',
  standalone: true,
  imports: [CommonModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './icon.component.html',
  styleUrls: ['./icon.component.scss'],
})
export class IconComponent
  extends BaseCustomComponent
  implements OnInit, AfterViewInit, OnChanges
{
  protected override _defineId = componentIdFnFactory(
    reflectComponentType(IconComponent)?.selector
  );

  @HostBinding('style.display') display = 'block';
  @HostBinding('style.height') height = '36px';
  @HostBinding('style.width') width = '36px';

  @Input() name = '';
  @Input() size = 36;
  @Input() color = '--color-content-primary';

  @ViewChild('iconContainer') iconContainer!: ElementRef;

  iconMarkup!: string;

  ngOnInit(): void {
    this._defineId(this);
    this.setIconSize();
  }

  ngAfterViewInit(): void {
    if (this.name) {
      this.importIcon();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (
      changes['size'] &&
      changes['size'].currentValue !== changes['size'].previousValue &&
      !changes['size'].firstChange
    ) {
      this.setIconSize();
    }
    if (
      changes['color'] &&
      changes['color'].currentValue !== changes['color'].previousValue &&
      !changes['color'].firstChange
    ) {
      this.setIconColor();
    }
    if (
      changes['name'] &&
      changes['name'].currentValue !== changes['name'].previousValue &&
      !changes['name'].firstChange
    ) {
      this.importIcon();
    }
  }

  async setIcon(): Promise<void> {
    if (this.iconContainer) {
      if (!this.iconMarkup) {
        return;
      }

      const svgElement = new DOMParser().parseFromString(
        this.iconMarkup,
        'image/svg+xml'
      ).documentElement;
      this.iconContainer.nativeElement.innerHTML = svgElement.outerHTML;
      this.setIconSize();
      this.setIconColor();
    }
  }

  setIconSize(): void {
    this.height = `${this.size}px`;
    this.width = `${this.size}px`;
  }

  setIconColor(): void {
    const svgElement = this.iconContainer.nativeElement.firstChild;
    if (svgElement) {
      svgElement.setAttribute('color', `var(${this.color})`);
    }
  }

  async importIcon(): Promise<void> {
    try {
      const fileRes = await fetch(`assets/svgs/${this.name}.svg`);
      this.iconMarkup = await fileRes.text();
      this.setIcon();
    } catch (err) {
      throw new Error(`No icon with name ${this.name} and error ${err}`);
    }
  }
}
