@import 'type-mixins';
@import 'tokens';

.poplin-notification,
.poplin-notification * {
  box-sizing: border-box;
}

.poplin-notification {
  &.color-default {
    --notification-color-icon: var(--notification-color-icon-default);
    --notification-color-content: var(--notification-color-content-default);
    --notification-color-background: var(
      --notification-color-background-default
    );
  }
  &.color-error {
    --notification-color-icon: var(--notification-color-icon-error);
    --notification-color-content: var(--notification-color-content-error);
    --notification-color-background: var(--notification-color-background-error);

    .notification-icon {
      background-color: var(--pink-700);

      poplin-icon {
        color: white;
      }
    }
  }
  &.color-success {
    --notification-color-icon: var(--notification-color-icon-success);
    --notification-color-content: var(--notification-color-content-success);
    --notification-color-background: var(
      --notification-color-background-success
    );
  }
  &.color-info {
    --notification-color-icon: var(--blue-shade);
    --notification-color-content: var(--blue-contrast);
    --notification-color-background: var(--blue-tint);
  }
  &.color-caution {
    --notification-color-icon: var(--notification-color-icon-caution);
    --notification-color-content: var(--notification-color-content-caution);
    --notification-color-background: var(
      --notification-color-background-caution
    );
  }
  &.color-warning {
    --notification-color-icon: var(--notification-color-icon-warning);
    --notification-color-content: var(--notification-color-content-warning);
    --notification-color-background: var(
      --notification-color-background-warning
    );
  }

  // sizes
  &.size-default {
    --notification-border-radius: var(--border-radius-default);
    --notification-icon-size: 28px;
    padding: 12px 16px;
    gap: 16px;

    .notification-text {
      gap: 16px;
    }

    .notification-heading-and-description {
      h1 {
        margin: 0 0 4px;
      }
      p {
        @include m-body;
      }
    }

    .notification-link-cta button {
      @include m-body;
    }
  }
  &.size-small {
    --notification-border-radius: var(--border-radius-small);
    --notification-icon-size: 20px;
    padding: 8px 12px;
    gap: 12px;

    .notification-text {
      gap: 12px;
    }

    .notification-heading-and-description {
      h1 {
        margin: 0;
      }
      p {
        @include s-body;
      }
    }

    .notification-link-cta button {
      @include s-body;
    }
  }

  // placements
  // placement-inline
  &.placement-toast {
    max-width: 440px;
    margin: 0 auto;
    box-shadow: var(--primary-box-shadow);
    position: absolute;
    bottom: 1rem;
    left: 1rem;
    right: 1rem;
    z-index: 10;
  }
  &.placement-main-banner {
    --notification-border-radius: 0;
  }

  // styles
  background: var(--notification-color-background);
  color: var(--notification-color-content);
  border-radius: var(--notification-border-radius);

  display: flex;
  flex-direction: column;

  .notification-text {
    display: flex;
  }

  .notification-icon {
    height: calc(var(--notification-icon-size) + 8px);
    width: calc(var(--notification-icon-size) + 8px);
    flex-basis: calc(var(--notification-icon-size) + 8px);
    padding: 4px;
    border-radius: var(--border-radius-round);
    background: var(--white);
    color: var(--notification-color-icon);

    poplin-icon {
      color: currentColor;
      display: block;
      height: var(--notification-icon-size) !important;
      width: var(--notification-icon-size) !important;
    }
  }

  .notification-heading-and-description {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    text-align: start;

    h1 {
      padding: 0;
      @include s-subhead;
    }
    p {
      margin: 0;
      padding: 0;
    }
  }

  .notification-button-cta {
    display: flex;
    justify-content: center;
  }

  .notification-link-cta {
    display: flex;
    justify-content: flex-end;

    button {
      background: none;
      border: none;
      padding: 0;
      margin: 0;
      display: inline-flex;
      align-items: center;
      gap: 4px;
      color: var(--notification-color-content);
      border-radius: var(--border-radius-small);
      text-decoration: underline;
      cursor: pointer;
      transition: box-shadow var(--transition-speed-hover),
        opacity var(--transition-speed-hover);

      &:hover {
        opacity: 0.6;
      }
      &:active {
        opacity: 0.75;
      }
      &:focus-visible {
        outline: none;
        box-shadow: 0 0 0 2px var(--notification-color-content);
      }

      poplin-icon {
        color: inherit;
        display: block;
        height: 20px !important;
        width: 20px !important;
      }
    }
  }
}
