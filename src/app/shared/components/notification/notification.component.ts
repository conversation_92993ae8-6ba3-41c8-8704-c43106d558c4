import { CommonModule } from '@angular/common';
import {
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { ButtonComponent } from '../button/button.component';
import { IconComponent } from '../icon/icon.component';

type Placement = 'toast' | 'inline' | 'main-banner';
type Color = 'default' | 'error' | 'success' | 'info' | 'caution' | 'warning';

export const NotificationEventType = {
  CTAClicked: 'Notification CTA Clicked',
} as const;

export interface NotificationEvent {
  eventType: (typeof NotificationEventType)[keyof typeof NotificationEventType];
}

@Component({
  selector: 'poplin-notification',
  standalone: true,
  imports: [CommonModule, ButtonComponent, IconComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './notification.component.html',
  styleUrls: ['./notification.component.scss'],
})
export class NotificationComponent implements OnInit {
  id = new Date().getTime().toString(36);

  @Input() showHeading = true;
  @Input() notificationHeading = '';
  @Input() text = '';
  @Input() showNotificationIcon = true;
  @Input() notificationIcon = 'person';
  @Input() notificationIconColor = 'currentColor';
  @Input() showButtonCTA = false;
  @Input() buttonCTAText = 'Click Here';
  @Input() buttonCTAVariant:
    | 'primary'
    | 'monochrome'
    | 'inverse-monochrome'
    | 'black'
    | 'white'
    | 'round-arrow' = 'black';
  @Input() showLinkCTA = true;
  @Input() linkCTAText = 'Click Here';
  @Input() placement: Placement = 'toast';
  @Input() color: Color = 'default';
  @Input() size: 'small' | 'default' = 'default';

  @Output() eventEmitter = new EventEmitter<NotificationEvent>();

  public get classes(): string[] {
    return [
      `placement-${this.placement}`,
      `color-${this.color}`,
      `size-${this.size}`,
    ];
  }

  ngOnInit(): void {
    // set button cta variant
    switch (this.color) {
      case 'info':
      case 'warning':
        this.buttonCTAVariant = 'monochrome';
        break;
      case 'default':
        this.buttonCTAVariant = 'inverse-monochrome';
        break;
      default:
        this.buttonCTAVariant = 'black';
        break;
    }
    // hide link CTA if button
    if (this.showButtonCTA) {
      this.showLinkCTA = false;
    }
  }

  handleCTAClick(): void {
    this.eventEmitter.emit({ eventType: NotificationEventType.CTAClicked });
  }
}
