<aside [id]="id" class="poplin-notification" [ngClass]="classes" tabindex="0">
  <div class="notification-text">
    <div class="notification-icon" *ngIf="showNotificationIcon">
      <poplin-icon
        [name]="notificationIcon"
        [size]="size === 'small' ? 20 : 28"
        [color]="notificationIconColor"
      ></poplin-icon>
    </div>
    <div class="notification-heading-and-description">
      <h1
        *ngIf="showHeading && notificationHeading"
        [innerHTML]="notificationHeading"
        [attr.aria-label]="showHeading ?? notificationHeading"
      ></h1>
      <p [innerHTML]="text" [attr.aria-label]="text ?? text"></p>
    </div>
  </div>
  <ng-content></ng-content>
  <div class="notification-button-cta" *ngIf="showButtonCTA">
    <poplin-button
      [showIcon]="true"
      iconSlot="end"
      size="small"
      shape="round"
      fill="outline"
      icon="keyboard_arrow_right"
      [label]="buttonCTAText"
      [color]="buttonCTAVariant"
      (click)="handleCTAClick()"
    ></poplin-button>
  </div>
  <div class="notification-link-cta" *ngIf="showLinkCTA && linkCTAText">
    <button (click)="handleCTAClick()">
      {{ linkCTAText }}
      <poplin-icon
        name="keyboard_arrow_right"
        [size]="20"
        [color]="notificationIconColor"
      ></poplin-icon>
    </button>
  </div>
</aside>
