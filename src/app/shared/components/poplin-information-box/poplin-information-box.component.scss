@import 'type-mixins';
@import 'tokens';

:host {
  display: block;
  width: 100%;
  height: auto;
}

.information-container {
  display: flex;
  align-items: center;
  justify-content: start;

  @include body-text;
  @include s-body;
  font-size: 14px;
  line-height: 20px;

  width: 100%;
  padding: 8px 12px;
  margin: 16px 0 0 0;

  border-radius: var(--border-radius-small);
  box-shadow: 0px 4px 24px 0px rgba(0, 0, 0, 0.04);

  p {
    margin: 0;
    flex-grow: 1;
  }

  .icon-wrapper {
    width: 28px;
    height: 28px;
    margin-right: 12px;
    flex-shrink: 0;
    flex-grow: 0;
    align-self: flex-start;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  ion-icon {
    width: 36px;
    height: 36px;
    flex-grow: 0;
    flex-shrink: 0;
  }

  &.type-info {
    background-color: var(--blue-150);
    .icon-wrapper {
      background-color: #54b8ff;
    }
    ion-icon {
      color: var(--white);
    }
  }

  &.type-warning {
    background-color: #ff936f;
    .icon-wrapper {
      background-color: var(--white);
    }
    ion-icon {
      color: #ff5d26;
      width: 20px;
      height: 20px;
    }
  }

  &.type-danger {
    background-color: var(--pink-450);
    .icon-wrapper {
      background-color: var(--pink-700);
    }
    ion-icon {
      color: var(--white);
    }
  }
}
