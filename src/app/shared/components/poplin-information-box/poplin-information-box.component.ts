import { CommonModule } from '@angular/common';
import { Component, CUSTOM_ELEMENTS_SCHEMA, Input } from '@angular/core';
import { IconComponent } from '../icon/icon.component';

@Component({
  selector: 'poplin-information-box',
  templateUrl: './poplin-information-box.component.html',
  styleUrls: ['./poplin-information-box.component.scss'],
  standalone: true,
  imports: [IconComponent, CommonModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class PoplinInformationBoxComponent {
  @Input() iconName: string = 'information-circle';
  @Input() type: 'info' | 'warning' | 'danger' = 'info';

  get typeClass(): string {
    return `type-${this.type}`;
  }
}
