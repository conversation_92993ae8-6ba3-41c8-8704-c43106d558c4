import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { IonicModule, ModalController } from '@ionic/angular';

export enum ConfirmationAction {
  CONFIRM = 'confirm',
  CANCEL = 'cancel',
}

@Component({
  selector: 'confirmation-modal',
  standalone: true,
  imports: [CommonModule, IonicModule],
  templateUrl: './confirmation-modal.component.html',
  styleUrls: ['./confirmation-modal.component.scss'],
})
export class ConfirmationModalComponent {
  @Input() title: string = '';
  @Input() content: string = '';
  @Input() contentHtml: string = '';
  @Input() confirmButtonText: string = 'Confirm';
  @Input() cancelButtonText: string = 'Cancel';

  readonly ConfirmationAction = ConfirmationAction;

  constructor(private readonly modalCtrl: ModalController) {}

  onAction(action: ConfirmationAction) {
    this.modalCtrl.dismiss(action);
  }
}
