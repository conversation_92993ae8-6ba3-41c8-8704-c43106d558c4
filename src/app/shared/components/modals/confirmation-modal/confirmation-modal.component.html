<div class="ion-content">
  <ion-title>{{ title }}</ion-title>

  <div *ngIf="content" class="content content-text">{{ content }}</div>
  <div
    *ngIf="contentHtml"
    class="content content-html"
    [innerHTML]="contentHtml"
  ></div>

  <div class="action-buttons">
    <ion-button
      expand="block"
      (click)="onAction(ConfirmationAction.CONFIRM)"
      class="poplin-theme v2"
    >
      {{ confirmButtonText }}
    </ion-button>
    <ion-button
      expand="block"
      fill="outline"
      (click)="onAction(ConfirmationAction.CANCEL)"
      class="poplin-theme v2"
    >
      {{ cancelButtonText }}
    </ion-button>
  </div>
</div>
