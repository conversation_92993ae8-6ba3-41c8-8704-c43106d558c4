<ion-button
  [id]="id"
  [ngClass]="classes"
  [type]="buttonType"
  (click)="onClick($event)"
  [disabled]="isDisabled"
  [attr.aria-label]="ariaLabel || iconSlot === 'icon-only' ? icon : label"
  role="button"
>
  <poplin-icon
    *ngIf="showIcon && iconSlot === 'start'"
    [color]="iconColor"
    [size]="iconSize"
    [name]="icon"
  ></poplin-icon>

  <span *ngIf="iconSlot !== 'icon-only'" [id]="'buttonLabel-' + id">{{
    label
  }}</span>

  <poplin-icon
    *ngIf="showIcon && iconSlot === 'end'"
    [color]="iconColor"
    [size]="iconSize"
    [name]="icon"
  ></poplin-icon>

  <poplin-icon
    *ngIf="showIcon && iconSlot === 'icon-only'"
    [color]="iconColor"
    [size]="iconSize"
    [name]="icon"
  ></poplin-icon>

  <div *ngIf="color === 'round-arrow'" class="arrow-icon">
    <poplin-icon
      [color]="'--color-round-arrow-button-background'"
      [size]="48"
      [name]="'arrow_right_custom'"
    ></poplin-icon>
  </div>
</ion-button>
