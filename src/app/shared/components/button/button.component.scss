@import 'src/theme/type-mixins.scss';

/* Base button styling */
/* ==================================================== */
button {
  padding: 0;
}
.pop-button::part(native) {
  line-height: unset;
  border-width: 0;
  transition: background-color var(--transition-speed-hover),
    color var(--transition-speed-hover),
    box-shadow var(--transition-speed-hover),
    transform var(--transition-speed-hover);
}

:host {
  min-height: 0;
  display: block;
  width: auto;
}

.pop-button {
  min-height: 0;
  transition: background-color var(--transition-speed-hover),
    color var(--transition-speed-hover),
    box-shadow var(--transition-speed-hover);
  margin: 0;
  align-items: center;
  display: block;
  --background-activated-opacity: 1;
  --background-focused-opacity: 1;
  --background-hover-opacity: 1;

  &.button-disabled {
    opacity: 1;
    pointer-events: none;
  }

  // Button Variant Property
  &.primary {
    --button-color-alt: var(--color-button-primary-alt);
    --button-color-main: var(--color-button-primary-main);
    --button-color-disabled: var(--color-button-primary-disabled);
    --button-color-hover: var(--color-button-primary-hover);
    --button-color-active: var(--color-button-primary-active);
    --button-color-wash: var(--color-button-primary-wash);
  }
  &.monochrome {
    --button-color-alt: var(--color-button-monochrome-alt);
    --button-color-main: var(--color-button-monochrome-main);
    --button-color-disabled: var(--color-button-monochrome-disabled);
    --button-color-hover: var(--color-button-monochrome-hover);
    --button-color-active: var(--color-button-monochrome-active);
    --button-color-wash: var(--color-button-monochrome-wash);
  }
  &.inverse-monochrome {
    --button-color-alt: var(--color-button-inverse-monochrome-alt);
    --button-color-main: var(--color-button-inverse-monochrome-main);
    --button-color-disabled: var(--color-button-inverse-monochrome-disabled);
    --button-color-hover: var(--color-button-inverse-monochrome-hover);
    --button-color-active: var(--color-button-inverse-monochrome-active);
    --button-color-wash: var(--color-button-inverse-monochrome-wash);
  }
  &.black {
    --button-color-alt: var(--color-button-black-alt);
    --button-color-main: var(--color-button-black-main);
    --button-color-disabled: var(--color-button-black-disabled);
    --button-color-hover: var(--color-button-black-hover);
    --button-color-active: var(--color-button-black-active);
    --button-color-wash: var(--color-button-black-wash);
  }
  &.white {
    --button-color-alt: var(--color-button-white-alt);
    --button-color-main: var(--color-button-white-main);
    --button-color-disabled: var(--color-button-white-disabled);
    --button-color-hover: var(--color-button-white-hover);
    --button-color-active: var(--color-button-white-active);
    --button-color-wash: var(--color-button-white-wash);
  }
  &.red {
    --button-color-alt: var(--color-button-red-alt);
    --button-color-main: var(--color-button-red-main);
    --button-color-disabled: var(--color-button-red-disabled);
    --button-color-hover: var(--color-button-red-hover);
    --button-color-active: var(--color-button-red-active);
    --button-color-wash: var(--color-button-red-wash);
  }

  // Button Size Property
  &.default {
    --padding-top: 6px;
    --padding-end: 8px;
    --padding-bottom: 6px;
    --padding-start: 8px;
    --button-stroke: 2px;

    @include m-aux;

    span {
      padding: 8px 18px;
    }

    // temp until icon is fixed
    poplin-icon {
      height: 36px !important;
      width: 36px !important;
    }
  }

  &.small {
    --padding-top: 6px;
    --padding-end: 4px;
    --padding-bottom: 6px;
    --padding-start: 4px;
    --button-stroke: 1px;

    @include s-aux;

    span {
      padding: 4px 12px;
    }

    // temp until icon is fixed
    poplin-icon {
      height: 24px !important;
      width: 24px !important;
    }
  }

  // Button Fill Property
  &.solid {
    --color: var(--button-color-alt);
    color: var(--button-color-alt);
    --background: var(--button-color-main);
    --box-shadow: var(--primary-box-shadow);

    --background-hover: var(--button-color-hover);
    --background-activated: var(--button-color-active);
    --background-focused: var(--button-color-hover);

    @media (hover: hover) {
      &:not(.button-disabled):not(:active):hover {
        --background: var(--button-color-hover);
      }
    }

    &:not(.button-disabled):active {
      --background: var(--button-color-active);
    }

    &.button-disabled {
      --background: var(--button-color-disabled);
    }
  }
  &.outline {
    --color: var(--button-color-main);
    color: var(--button-color-main);
    --background: transparent;
    --box-shadow: inset 0 0 0 var(--button-stroke) var(--button-color-main);

    --color-hover: var(--button-color-hover);
    --color-activated: var(--button-color-active);

    --background-hover: transparent;
    --background-activated: transparent;

    @media (hover: hover) {
      &:not(.button-disabled):hover {
        --color: var(--button-color-hover);
        color: var(--button-color-hover);
        --box-shadow: inset 0 0 0 var(--button-stroke) var(--button-color-hover);
      }
    }

    &:not(.button-disabled):active {
      --background: var(--button-color-wash);
      --color: var(--button-color-active);
      color: var(--button-color-active);
      --box-shadow: inset 0 0 0 var(--button-stroke) var(--button-color-active);
    }

    &.button-disabled {
      --color: var(--button-color-disabled);
      color: var(--button-color-disabled);
      --box-shadow: inset 0 0 0 var(--button-stroke)
        var(--button-color-disabled);
    }
  }
  &.clear {
    --color: var(--button-color-main);
    color: var(--button-color-main);
    --background: transparent;

    --color-hover: var(--button-color-hover);
    --color-activated: var(--button-color-active);

    --background-hover: transparent;
    --background-activated: transparent;

    @media (hover: hover) {
      &:not(.button-disabled):hover {
        --color: var(--button-color-hover);
        color: var(--button-color-hover);
      }
    }

    &:not(.button-disabled):active {
      --background: var(--button-color-wash);
      --color: var(--button-color-active);
      color: var(--button-color-active);
    }

    &.button-disabled {
      --color: var(--button-color-disabled);
      color: var(--button-color-disabled);
    }
  }

  &.auth-button {
    letter-spacing: 0;
    --font-size: 20px;
    font: 500 20px/24px 'Roboto-Normal', sans-serif;
    text-transform: none;
    --padding-top: 6px;
    --padding-end: 8px;
    --padding-bottom: 6px;
    --padding-start: 8px;
    --button-stroke: 2px;
    poplin-icon {
      height: 24px !important;
      width: 24px !important;
      padding: 0 0 0 8px;
    }
    span {
      padding: 6px 8px 6px 15px;
    }

    // take

    --color: var(--color-auth-button-text);
    color: var(--color-auth-button-text);
    --background: var(--color-auth-button-background);
    --box-shadow: none;

    --color-hover: var(--color-auth-button-text-hover);
    --color-activated: var(--color-auth-button-text-active);
    --color-focused: var(--color-auth-button-text-hover);

    --background-focused: transparent;
    --background-hover: transparent;
    --background-activated: transparent;

    @media (hover: hover) {
      &:not(.button-disabled):hover {
        --background: var(--color-auth-button-background);
        --color: var(--color-auth-button-text-hover);
        color: var(--color-auth-button-text-hover);
        --box-shadow: none;
      }
    }

    &:not(.button-disabled):active {
      --background: var(--color-auth-button-background);
      --color: var(--color-auth-button-text-active);
      color: var(--color-auth-button-text-active);
      --box-shadow: none;
    }

    &.button-disabled {
      --background: var(--color-auth-button-background);
      --color: var(--color-auth-button-disabled);
      color: var(--color-auth-button-disabled);
      --box-shadow: none;
      poplin-icon {
        filter: grayscale(1);
      }
    }
  }

  // Button Shape
  &.square {
    --border-radius: var(--border-radius-default);
  }
  &.round {
    --border-radius: var(--border-radius-round);
  }

  // Round Arrow Button
  &.round-arrow {
    --padding-top: 8px;
    --padding-end: 8px;
    --padding-bottom: 8px;
    --padding-start: 8px;

    @include m-aux;

    span {
      padding: 14px 26px 14px 18px;
      flex: 1 0 auto;
    }

    --color: var(--color-round-arrow-button-text);
    color: var(--color-round-arrow-button-text);
    --background: var(--color-round-arrow-button-background);
    --color-hover: var(--color-round-arrow-button-text-hover);
    --box-shadow: var(--primary-box-shadow);
    --border-radius: var(--border-radius-round);

    .arrow-icon {
      background: var(--color-round-arrow-button-icon);
      border-radius: var(--border-radius-round);
      height: 48px;
      width: 48px;

      // temp until icon is fixed
      poplin-icon {
        height: 48px !important;
        width: 48px !important;
      }
    }

    @media (hover: hover) {
      &:not(.button-disabled):hover {
        color: var(--color-round-arrow-button-text-hover);
        --box-shadow: var(--heavy-box-shadow);
        transform: scale(1.02);

        .arrow-icon poplin-icon {
          animation-name: animateContinueArrow;
          animation-duration: 1s;
          animation-iteration-count: 2;
          animation-timing-function: linear;
        }
      }
    }

    &:not(.button-disabled):active {
      color: var(--color-round-arrow-button-text-active);
    }

    &.button-disabled {
      --background: var(--color-round-arrow-button-disabled);
    }
  }

  // Icon-Only Buttons
  &.show-icon.icon-only {
    &.default {
      --padding-end: 6px;
      --padding-start: 6px;
      width: 48px;
      height: 48px;
    }
    &.small {
      --padding-top: 4px;
      --padding-bottom: 4px;
      width: 36px;
      height: 36px;

      // temp until icon is fixed
      poplin-icon {
        height: 28px !important;
        width: 28px !important;
      }
    }
  }
}

@keyframes animateContinueArrow {
  0% {
    opacity: 1;
    transform: translateX(0px);
  }
  25% {
    opacity: 0;
    transform: translateX(10px);
  }
  26% {
    opacity: 0;
    transform: translateX(-10px);
  }
  55% {
    opacity: 1;
    transform: translateX(0px);
  }
}
