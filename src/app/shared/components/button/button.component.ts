import { CommonModule } from '@angular/common';
import {
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  EventEmitter,
  Input,
  OnInit,
  Output,
  reflectComponentType,
} from '@angular/core';
import { IconComponent } from '../icon/icon.component';
import { BaseCustomComponent } from '../utils/base-custom.component';
import { componentIdFnFactory } from '../utils/define-component-id';

@Component({
  selector: 'poplin-button',
  standalone: true,
  imports: [CommonModule, IconComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './button.component.html',
  styleUrls: ['./button.component.scss'],
})
export class ButtonComponent extends BaseCustomComponent implements OnInit {
  protected override _defineId = componentIdFnFactory(
    reflectComponentType(ButtonComponent)?.selector
  );

  @Input() label = 'Button';
  @Output() eventEmitter = new EventEmitter<Event>();
  @Input() buttonType = 'button';

  @Input() isDisabled = false;

  @Input() showIcon = false;
  @Input() iconSlot: 'start' | 'end' | 'icon-only' = 'end';

  @Input() isAuthBtn = false;
  @Input() monoChrome = false;
  @Input() monoChromeInverse = false;
  @Input() black = false;
  @Input() white = false;
  @Input() color:
    | 'primary'
    | 'monochrome'
    | 'inverse-monochrome'
    | 'black'
    | 'white'
    | 'red'
    | 'round-arrow' = 'primary';
  @Input() size: 'small' | 'default' = 'default';
  @Input() fill: 'clear' | 'outline' | 'solid' = 'solid';
  @Input() shape: 'square' | 'round' = 'square';
  @Input() nonBlockBtn = false;

  @Input() icon = '';
  @Input() iconSize = 36;
  @Input() iconColor = '';
  @Input() ariaLabel = '';

  public get classes(): string[] {
    const iconBtn = this.showIcon ? `show-icon ${this.iconSlot}` : '';
    const authBtn = this.isAuthBtn ? 'auth-button' : '';

    if (this.size == 'small') {
      if (this.iconSlot == 'icon-only') {
        this.iconSize = 28;
      } else {
        this.iconSize = 24;
      }
    }

    return [
      'pop-button',
      `${this.fill}`,
      `${this.color}`,
      `${this.size}`,
      `${this.shape}`,
      this.isDisabled ? 'button-disabled' : '',
      this.nonBlockBtn ? 'non-block' : '',
      iconBtn,
      authBtn,
    ];
  }

  onClick(event: Event): void {
    if (!this.isDisabled) {
      this.eventEmitter.emit(event);
    }
  }

  ngOnInit(): void {
    this._defineId(this);
  }
}
