export interface OrderContractAddress {
  city: string;
  state: string;
  zipCode: string;
  distance: {
    miles: string;
    estimatedMinutes: string;
  };
}

export interface OrderContractTiming {
  pickup: {
    timeMessage: string;
    time: string;
    place: string;
    instructions?: string;
  };
  delivery: {
    timeMessage: string;
    time: string;
  };
}

export interface OrderContractPrice {
  amount: number;
  oldAmount?: number;
  weight?: number;
  oldWeight?: number;
  bags: {
    quantity: number;
    bagSize: string;
    oversized: number;
  };
}

export interface OrderContractData {
  orderNumber: string;
  customerName: string;
  isBusinessCustomer: boolean;
  canProposeRate: boolean;
  hasPendingRateProposal?: boolean;
  isPreferred: boolean;
  isExpress: boolean;
  hasBoost: boolean;
  boostAmount?: number;
  isNewCustomer: boolean;
  timing: OrderContractTiming;
  price: OrderContractPrice;
  address: OrderContractAddress;
  detergent: string;
  isHangDry: boolean;
  isDelicate: boolean;
  hasSpecialNotes?: boolean;
  specialNotes?: string;
  acknowledgements: {
    expressOrder: boolean;
    hypoallergenicDetergent: boolean;
    deadline?: boolean;
  };
}
