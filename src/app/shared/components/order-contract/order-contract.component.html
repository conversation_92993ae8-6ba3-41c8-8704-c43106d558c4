<div class="order-contract">
  <header class="contract-header title">
    <div>Order Contract</div>
    <div (click)="onOrderNumberClick()">#{{ contractData.orderNumber }}</div>
  </header>
  <div class="tags" *ngIf="tags.length > 0">
    <poplin-tag
      *ngFor="let tag of tags"
      [label]="tag.name"
      [color]="tag.color"
      [icon]="tag.icon || ''"
    >
    </poplin-tag>
  </div>

  <section class="customer-info">
    <poplin-icon-text
      [icon]="'person'"
      [texts]="[
        {
          content:
            contractData.customerName +
            (contractData.isPreferred ? ' is requesting you!' : '')
        }
      ]"
    >
    </poplin-icon-text>
  </section>

  <section class="timing-info">
    <poplin-icon-text
      [icon]="'directions_car'"
      [texts]="[
        { content: contractData.timing.pickup.timeMessage },
        {
          content: contractData.timing.delivery.timeMessage,
          variant: TextVariant.Primary
        }
      ]"
    >
    </poplin-icon-text>
  </section>

  <section class="price-info">
    <poplin-icon-text
      [icon]="'dollar_custom'"
      [texts]="[{ content: formatPrice() }, { content: formatBags() }]"
    >
    </poplin-icon-text>
  </section>

  <section class="address-info">
    <poplin-icon-text [icon]="'location'" [texts]="formatAddress()">
    </poplin-icon-text>
  </section>

  <section class="business-indicator" *ngIf="contractData.isBusinessCustomer">
    <poplin-icon-text
      [icon]="'business'"
      [texts]="[
        { content: 'Customer is a business', variant: TextVariant.Secondary }
      ]"
    >
    </poplin-icon-text>
  </section>

  <section class="pickup-info" *ngIf="contractData.timing.pickup.place">
    <poplin-icon-text [icon]="'laundry_custom'" [texts]="[formatPickupPlace()]">
    </poplin-icon-text>
  </section>

  <section
    class="pickup-instructions"
    *ngIf="contractData.timing.pickup.instructions"
  >
    <poplin-icon-text
      [icon]="'comment_custom'"
      [texts]="[formatPickupInstructions()]"
    >
    </poplin-icon-text>
  </section>

  <section
    class="acknowledgements"
    *ngIf="
      contractData.acknowledgements.expressOrder &&
      (!viewMode || contractData.hasPendingRateProposal)
    "
  >
    <poplin-checkbox
      size="small"
      [isDisabled]="!!contractData.hasPendingRateProposal"
      [parentForm]="form"
      [formControlName]="'expressOrder'"
      label="I acknowledge this is an Express order and cannot be extended"
    >
    </poplin-checkbox>
  </section>

  <section
    class="acknowledgements"
    *ngIf="
      contractData.acknowledgements.deadline &&
      (!viewMode || contractData.hasPendingRateProposal)
    "
  >
    <poplin-checkbox
      size="small"
      [isDisabled]="!!contractData.hasPendingRateProposal"
      [parentForm]="form"
      [formControlName]="'deadline'"
      [label]="formatDeadlineLabel()"
    >
    </poplin-checkbox>
  </section>

  <section class="laundry-care">
    <div class="title">Laundry Care</div>
    <div class="instructions">
      <poplin-icon-text
        *ngFor="let instruction of instructions"
        [icon]="instruction.icon || ''"
        [texts]="[
          { content: instruction.text || '', variant: TextVariant.Secondary }
        ]"
      >
      </poplin-icon-text>
    </div>
  </section>

  <section
    class="acknowledgements"
    *ngIf="
      contractData.acknowledgements.hypoallergenicDetergent &&
      (!viewMode || contractData.hasPendingRateProposal)
    "
  >
    <poplin-checkbox
      size="small"
      [isDisabled]="!!contractData.hasPendingRateProposal"
      [parentForm]="form"
      [formControlName]="'hypoallergenicDetergent'"
      label="I acknowledge this customer required unscented hypoallergenic detergent"
    >
    </poplin-checkbox>
  </section>

  <footer class="contract-actions">
    <poplin-button
      *ngIf="contractData.canProposeRate && !viewMode"
      label="Propose my rate"
      (click)="onProposeRateClick()"
      fill="outline"
      shape="round"
    >
    </poplin-button>

    <poplin-button
      *ngIf="contractData.isPreferred && !viewMode"
      label="Decline Request"
      (click)="onDeclineClick()"
      fill="outline"
      shape="round"
    >
    </poplin-button>

    <poplin-swipe-button
      *ngIf="!viewMode"
      label="Swipe to Accept"
      [isDisabled]="acceptButtonDisabled"
      (eventEmitter)="handleSwipeEvent($event)"
    >
    </poplin-swipe-button>

    <poplin-button
      *ngIf="viewMode"
      label="Close"
      (click)="onCloseClick()"
      fill="outline"
    >
    </poplin-button>

    <div class="warning" *ngIf="!viewMode">
      <poplin-icon
        [name]="'info_filled'"
        [size]="22"
        [color]="'--orange-600'"
      ></poplin-icon>
      <span>Once you accept an order, you CANNOT cancel it.</span>
    </div>
  </footer>
</div>
