import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ButtonComponent } from '../button/button.component';
import { CheckboxComponent } from '../checkbox/checkbox.component';
import { IconTextComponent } from '../icon-text/icon-text.component';
import { TextVariant } from '../icon-text/icon-text.model';
import { IconComponent } from '../icon/icon.component';
import {
  SwipeButtonComponent,
  SwipeButtonEvent,
  SwipeButtonEventType,
} from '../swipe-button/swipe-button.component';
import { TagComponent } from '../tag/tag.component';
import { Tag } from '../tag/tag.model';
import { OrderContractData } from './order-contract.model';

interface LaundryInstruction {
  icon: string;
  text: string;
}

@Component({
  selector: 'poplin-order-contract',
  templateUrl: './order-contract.component.html',
  styleUrls: ['./order-contract.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    TagComponent,
    IconTextComponent,
    ButtonComponent,
    SwipeButtonComponent,
    CheckboxComponent,
    IconComponent,
  ],
})
export class OrderContractComponent implements OnInit {
  @ViewChild(SwipeButtonComponent) swipeButton!: SwipeButtonComponent;

  protected TextVariant = TextVariant;

  @Input() contractData!: OrderContractData;
  @Input() viewMode = false;

  @Output() acceptContract = new EventEmitter<void>();
  @Output() proposeRate = new EventEmitter<void>();
  @Output() declineContract = new EventEmitter<void>();
  @Output() closeContract = new EventEmitter<void>();
  @Output() orderNumberClick = new EventEmitter<void>();

  public form: FormGroup = new FormGroup({
    expressOrder: new FormControl(false),
    hypoallergenicDetergent: new FormControl(false),
    deadline: new FormControl(false),
  });

  public instructions!: Partial<LaundryInstruction>[];
  public tags!: Tag[];
  public acceptButtonDisabled = true;

  ngOnInit(): void {
    this.instructions = this.getLaundryInstructions();
    this.tags = this.getTags();

    this.form.valueChanges.subscribe(() => {
      this.acceptButtonDisabled = !this.form.valid;
    });

    this.updateFormValidators();
  }

  formatPrice(): string {
    const oldPrice = this.contractData.price.oldAmount
      ? `<span><s>$${this.contractData.price.oldAmount} Est. (${this.contractData.price.oldWeight}/lb)</span></s> `
      : '';

    return `${oldPrice}$${
      this.contractData.price.amount
    } Est. (${this.contractData.price.weight.toFixed(2)}/lb)`;
  }

  formatBags(): string {
    const { quantity, bagSize, oversized } = this.contractData.price.bags;

    return `${quantity} ${bagSize} Bag${quantity > 1 ? 's' : ''} ${
      oversized ? ` + ${oversized} Oversized` : ''
    }`;
  }

  formatAddress(): { content: string }[] {
    const { city, state, zipCode, distance } = this.contractData.address;

    return [
      { content: `${city}, ${state} ${zipCode}` },
      {
        content: `${distance.miles} mile drive est. (~${distance.estimatedMinutes} min)`,
      },
    ];
  }

  formatPickupPlace(): { content: string; variant: TextVariant } {
    return {
      content: `Pickup at ${this.contractData.timing.pickup.place}`,
      variant: TextVariant.Secondary,
    };
  }

  formatPickupInstructions(): { content: string; variant: TextVariant } {
    return {
      content: this.contractData.timing.pickup.instructions || '',
      variant: TextVariant.Tertiary,
    };
  }

  formatDeadlineLabel(): string {
    const pickupTime = this.contractData.timing.pickup.time;
    const deliveryTime = this.contractData.timing.delivery.time;

    return `I acknowledge the customer deadlines of ${pickupTime} pickup and ${deliveryTime} delivery`;
  }

  handleSwipeEvent(event: SwipeButtonEvent): void {
    if (event.eventType === SwipeButtonEventType.SwipeComplete) {
      this.onSwipeAccept();
    }
  }

  onSwipeAccept(): void {
    this.acceptContract.emit();
  }

  onDeclineClick(): void {
    this.declineContract.emit();
  }

  onCloseClick(): void {
    this.closeContract.emit();
  }

  onProposeRateClick(): void {
    this.proposeRate.emit();
  }

  onOrderNumberClick(): void {
    this.orderNumberClick.emit();
  }

  private getTags(): Tag[] {
    const tags = {
      hasPendingRateProposal: {
        name: 'Pending Proposal',
        color: 'var(--blue-150)',
        icon: 'alarm',
      },
      isExpress: {
        name: 'Express',
        color: 'var(--green-200)',
        icon: 'bolt',
      },
      hasBoost: {
        name: `+ $${this.contractData.boostAmount} Boost`,
        color: 'var(--orange-200)',
      },
      isNewCustomer: {
        name: 'New to Poplin',
        color: 'var(--yellow-tint)',
        icon: 'waving_hand_filled',
      },
    };

    return Object.keys(tags)
      .filter((key) => this.contractData[key as keyof OrderContractData])
      .map((item) => ({
        ...tags[item as keyof typeof tags],
      })) as Tag[];
  }

  private getLaundryInstructions(): LaundryInstruction[] {
    const instructions = {
      detergent: {
        icon: 'bubbles_custom',
        text: this.contractData.detergent,
      },
      isHangDry: {
        icon: 'hanger_custom',
        text: 'Hang-dry Included',
      },
      isDelicate: {
        icon: 'flower',
        text: 'Delicate Cycle Items',
      },
      hasSpecialNotes: {
        icon: 'comment_custom',
        text: this.contractData.specialNotes,
      },
    };

    return Object.keys(instructions)
      .filter((key) => this.contractData[key as keyof OrderContractData])
      .map((item) => ({
        ...instructions[item as keyof typeof instructions],
      })) as LaundryInstruction[];
  }

  private updateFormValidators(): void {
    const expressControl = this.form.get('expressOrder');
    const hypoallergenicControl = this.form.get('hypoallergenicDetergent');
    const deadlineControl = this.form.get('deadline');

    if (this.contractData.hasPendingRateProposal) {
      expressControl?.setValue(true);
      hypoallergenicControl?.setValue(true);
      deadlineControl?.setValue(true);
    }

    if (this.contractData.acknowledgements.expressOrder) {
      expressControl?.setValidators([Validators.requiredTrue]);
    } else {
      expressControl?.clearValidators();
    }
    expressControl?.updateValueAndValidity();

    if (this.contractData.acknowledgements.hypoallergenicDetergent) {
      hypoallergenicControl?.setValidators([Validators.requiredTrue]);
    } else {
      hypoallergenicControl?.clearValidators();
    }
    hypoallergenicControl?.updateValueAndValidity();

    if (this.contractData.acknowledgements.deadline) {
      deadlineControl?.setValidators([Validators.requiredTrue]);
    } else {
      deadlineControl?.clearValidators();
    }
    deadlineControl?.updateValueAndValidity();
  }

  resetSwipeButton(): void {
    this.swipeButton?.resetState();
  }
}
