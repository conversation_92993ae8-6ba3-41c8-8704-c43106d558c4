.order-contract {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .contract-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }

  .title {
    color: var(--color-content-med-contrast);

    font-family: 'PitchSans-Medium';
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 17px;
    letter-spacing: 2.1px;
    text-transform: uppercase;
  }

  .tags {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
    margin-bottom: 16px;
  }

  .laundry-care {
    margin-top: 16px;
    margin-bottom: 16px;

    .title {
      margin-bottom: 12px;
    }

    .instructions {
      display: flex;
      flex-direction: column;
      gap: 5px;
    }
  }

  .acknowledgements {
    background: var(--notification-color-background-info);
    border-radius: 8px;
    padding: 9px 12px;
    margin-top: 4px;
  }

  .contract-actions {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-top: 16px;

    .warning {
      display: flex;
      color: var(--gray-800);
      font-size: 14px;
      justify-content: center;

      span {
        padding-top: 1px;
        padding-left: 4px;
      }
    }
  }

  .price-info {
    ::ng-deep span {
      font-weight: 400;
      margin-right: 4px;
      color: var(--gray-800);
    }
  }

  ::ng-deep .icon-text {
    ion-col {
      &:first-of-type {
        padding: 0 8px 5px 5px !important;
      }

      &.content-wrapper {
        padding-top: 0px !important;
        padding-left: 0px !important;
        padding-right: 0px !important;
        p {
          margin: 0 0 2px 0 !important;
        }
      }
    }
  }
}
