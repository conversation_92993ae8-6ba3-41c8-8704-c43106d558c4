<div [id]="id">
  <div
    class="checkbox-div"
    [ngClass]="{
      'size-small': size === 'small',
      'size-default': size === 'default',
      'no-label': !showLabel
    }"
    [class.disabled]="isDisabled"
  >
    <ion-checkbox
      label-placement="end"
      [disabled]="isDisabled"
      [attr.aria-labelledby]="showLabel ? 'checkboxLabel' + id : null"
      [attr.aria-describedby]="subLabel ? 'checkboxSublabel' + id : null"
      role="checkbox"
      [attr.aria-checked]="isChecked ? 'true' : 'false'"
      [checked]="isChecked"
      [indeterminate]="isIndeterminate"
      (ionChange)="handleChange($event)"
      [ngClass]="{ checked: isChecked }"
      [formControlName]="formControlName"
      id="checkbox-{{ id }}"
      [ngClass]="{
        'checkbox-checked': isChecked,
        'checkbox-error-checked': showError && isChecked,
        'checkbox-error-unchecked': showError && !isChecked
      }"
    ></ion-checkbox>

    <div class="label-container" *ngIf="showLabel">
      <label
        *ngIf="showLabel"
        [id]="'checkboxLabel' + id"
        [ngClass]="{
          'label-checked': isChecked,
          'label-unchecked': !isChecked && !isDisabled,
          'label-disabled': isDisabled,
          'label-error-checked': isChecked && showError,
          'label-error-unchecked': !isChecked && showError
        }"
        for="checkbox-{{ id }}"
        (click)="handleCheckboxClick()"
      >
        {{ label }}
      </label>

      <div
        [ngClass]="{
          'sub-label-checked': isChecked,
          'sub-label-unchecked': !isChecked && !isDisabled,
          'sub-label-disabled': isDisabled,
          'sub-label-error-checked': isChecked && showError,
          'sub-label-error-unchecked': !isChecked && showError
        }"
        class="description"
        *ngIf="subLabel"
        [id]="'checkboxSublabel' + id"
      >
        {{ subLabel }}
      </div>
    </div>
  </div>
</div>
