import { CommonModule } from '@angular/common';
import {
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  EventEmitter,
  Input,
  OnInit,
  Output,
  reflectComponentType,
} from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { BaseCustomComponent } from '../utils/base-custom.component';
import { componentIdFnFactory } from '../utils/define-component-id';

@Component({
  selector: 'poplin-checkbox',
  standalone: true,
  imports: [CommonModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './checkbox.component.html',
  styleUrls: ['./checkbox.component.scss'],
})
export class CheckboxComponent extends BaseCustomComponent implements OnInit {
  protected override _defineId = componentIdFnFactory(
    reflectComponentType(CheckboxComponent)?.selector
  );

  @Input() ngId = '';
  @Input() formControlName = 'checkbox';
  @Input() parentForm: FormGroup = new FormGroup({
    checkbox: new FormControl(false),
  });
  @Input() isDisabled = false;
  @Input() isIndeterminate = false;
  @Input() label = 'Default';
  @Input() subLabel = '';
  @Input() showError = false;
  @Input() showLabel = true;
  @Input() size: 'small' | 'default' = 'default';

  @Output() eventEmitter = new EventEmitter<boolean>();

  get isChecked(): boolean {
    return this.parentForm?.controls[this.formControlName]?.value || false;
  }

  handleChange(ev: Event): void {
    const value = (ev.target as HTMLIonCheckboxElement).checked;
    if (this.parentForm?.controls[this.formControlName]) {
      this.parentForm.controls[this.formControlName].setValue(value);
      this.eventEmitter.emit(value);
    }
  }

  public get classes(): string[] {
    return [
      'pop-checkbox',
      `${this.size}`,
      `${this.isChecked}`,
      `${this.isDisabled ? 'is-disabled' : 'not-disabled'}`,
      `${this.isIndeterminate}`,
      `${this.subLabel == '' ? 'no-subLabel' : 'subLabel'}`,
      `${this.showError ? 'show-error' : 'no-error'}`,
    ];
  }

  handleCheckboxClick(): void {
    if (this.parentForm?.controls[this.formControlName]) {
      this.parentForm.controls[this.formControlName].setValue(!this.isChecked);
      this.eventEmitter.emit(this.isChecked);
    }
  }

  ngOnInit(): void {
    this._defineId(this);

    if (!this.parentForm.contains(this.formControlName)) {
      this.parentForm.addControl(this.formControlName, new FormControl(false));
    }
  }
}
