// ion-checkbox documentation : https://ionicframework.com/docs/api/checkbox#css-shadow-parts
@import 'type-mixins.scss';

// basic checkbox positioning
.checkbox-div {
  display: flex;
  align-items: flex-start;
  cursor: pointer;

  &.disabled {
    cursor: default;
    pointer-events: none;

    ion-checkbox {
      opacity: 1;
    }
  }

  .label-container {
    display: flex;
    flex-direction: column;
    min-width: 0;
    flex: 1 1 auto;
  }

  label {
    display: block;
    cursor: pointer;
  }

  // label and description

  label {
    color: var(--color-content-alt);

    &.label-checked {
      color: var(--color-content-primary);
    }

    &.label-disabled {
      color: var(--color-content-alt);
    }
  }

  .description {
    color: var(--color-content-alt);
    cursor: pointer;
  }
}

// checkbox sizes
.checkbox-div {
  &.size-default {
    gap: 12px;
    padding: 8px 0;

    ion-checkbox {
      --size: 32px;
      width: 32px;
      height: 32px;

      poplin-icon {
        width: 32px !important;
        height: 32px !important;
      }
    }

    .label-container {
      padding: 2px 0;
      gap: 4px;
    }

    label {
      @include l-body;
    }

    .description {
      @include m-body;
    }
  }

  &.size-small {
    gap: 8px;
    padding: 6px 0;

    ion-checkbox {
      --size: 24px;
      width: 24px;
      height: 24px;

      poplin-icon {
        width: 24px !important;
        height: 24px !important;
      }
    }

    .label-container {
      padding: 1px 0;
    }

    label {
      @include m-body;
    }

    .description {
      @include s-body;
    }
  }

  &.no-label {
    padding: 0;
  }
}

// checkbox styles

.checkbox-div ion-checkbox {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  box-shadow: var(--primary-box-shadow);
  transition: border var(--transition-speed-hover);

  --border-radius: var(--border-radius-small);
  --border-color: var(--checkbox-icon-border-unchecked);
  --border-color-checked: var(--checkbox-icon-border-checked);
  --checkbox-background: var(--checkbox-icon-background-unchecked);
  --checkbox-background-checked: var(--checkbox-icon-background-checked);
  --checkmark-color: var(--checkbox-icon-checked);
  --checkmark-width: 1.5px;

  &.checkbox-disabled {
    --border-color: var(--checkbox-icon-border-disabled);
    --border-color-checked: var(--checkbox-icon-border-disabled);
    --checkbox-background: var(--checkbox-icon-background-disabled);
    --checkbox-background-checked: var(--checkbox-icon-background-disabled);
    --checkmark-color: var(--checkbox-icon-disabled);
    opacity: 1;
  }

  &.checkbox-error-unchecked {
    --border-color: var(--checkbox-icon-border-error);
    --checkbox-background: var(--checkbox-icon-background-unchecked);
    --checkmark-color: transparent;
  }

  &.checkbox-error-checked {
    --border-color-checked: var(--checkbox-icon-border-error);
    --checkbox-background-checked: var(
      --checkbox-icon-background-checked-error
    );
    --checkmark-color: var(--checkbox-icon-checked);
  }
}
