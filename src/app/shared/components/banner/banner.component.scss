.banner {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: 358px;
  max-width: 600px;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: var(--border-radius-small);
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 9999;
  line-height: 17px;
  letter-spacing: 15%;

  &--alert {
    background-color: var(--ion-color-danger-tint);
  }

  &--vacation {
    background-color: var(--blue-150);
  }

  &-content {
    display: flex;
    gap: 10px;
    flex-grow: 1;

    &__icon {
      height: 28px;
      width: 35px;
      border-radius: 50%;
      background-color: var(--white);

      &--position {
        margin: 3px 5px;
      }
    }

    &__text {
      display: flex;
      flex-direction: column;
    }

    &__title {
      font-family: 'PitchSans-Medium', sans-serif;
      font-weight: 700;
      font-size: 14px;
      line-height: 17px;
      letter-spacing: 0.15em;
      text-transform: uppercase;
      color: var(--black);
    }

    &__message {
      font-family: 'Fakt-Normal' sans-serif;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      letter-spacing: 0%;
      color: var(--black);
    }
  }
}
