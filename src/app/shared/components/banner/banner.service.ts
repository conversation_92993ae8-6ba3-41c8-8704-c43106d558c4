import { Injectable } from '@angular/core';
import { ReplaySubject } from 'rxjs';
import { BannerState } from 'src/app/_interfaces/banner.interface';

@Injectable({
  providedIn: 'root',
})
export class BannerService {
  private bannerSubject = new ReplaySubject<BannerState | null>(1);
  bannerState$ = this.bannerSubject.asObservable();

  showBanner(banner: BannerState) {
    if (banner) {
      this.bannerSubject.next(banner);
    }
  }

  hideBanner() {
    this.bannerSubject.next(null);
  }
}
