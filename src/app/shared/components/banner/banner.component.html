<ng-container *ngIf="bannerState$ | async as banner">
  <div
    class="banner"
    [ngClass]="'banner--' + banner.type"
    (click)="onBannerClick(banner.type)"
    (keydown.enter)="onBannerClick(banner.type)"
    (keydown.space)="onBannerClick(banner.type)"
    tabindex="0"
  >
    <div class="banner-content">
      <div class="banner-content__icon">
        <poplin-icon
          class="banner-content__icon--position"
          [name]="banner.icon"
          [color]="banner.iconColor"
          size="20"
        >
        </poplin-icon>
      </div>
      <div class="banner-content__text">
        <span class="banner-content__title">{{ banner.title }}</span>
        <span class="banner-content__message">{{ banner.message }}</span>
      </div>
    </div>
  </div>
</ng-container>
