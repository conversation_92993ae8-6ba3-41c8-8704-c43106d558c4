import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Output } from '@angular/core';
import { BannerTypeEnum } from 'src/app/_constants/banner.constant';
import { IconComponent } from '../icon/icon.component';
import { BannerService } from './banner.service';

@Component({
  selector: 'poplin-banner',
  standalone: true,
  imports: [CommonModule, IconComponent],
  templateUrl: './banner.component.html',
  styleUrls: ['./banner.component.scss'],
})
export class BannerComponent {
  @Output() bannerClicked = new EventEmitter<BannerTypeEnum>();
  bannerState$ = this.bannerService.bannerState$;

  constructor(private bannerService: BannerService) {}

  closeBanner() {
    this.bannerService.hideBanner();
  }

  onBannerClick(type: BannerTypeEnum) {
    this.bannerClicked.emit(type);
  }
}
