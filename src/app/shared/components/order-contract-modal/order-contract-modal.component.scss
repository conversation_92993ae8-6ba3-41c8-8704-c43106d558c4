ion-modal {
  --backdrop-opacity: 0.25;
  --border-radius: var(--border-radius-large) var(--border-radius-large) 0 0;
  --border-width: 0;
  --background: var(--color-background-alt);
  --background: transparent;
  --height: auto;
  --max-width: 440px;
  --modal-icon-color: var(--color-content-primary);

  .modal-content {
    border-radius: var(--border-radius-large) var(--border-radius-large) 0 0;
    background: var(--color-background-alt);
    color: var(--color-content-alt);
    padding: 32px 16px;
    max-height: calc(100vh - 40px);
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch;

    &::-webkit-scrollbar {
      display: none;
    }
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}
