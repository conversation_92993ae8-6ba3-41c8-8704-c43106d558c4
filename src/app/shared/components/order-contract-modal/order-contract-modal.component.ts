import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  Output,
  ViewChild,
} from '@angular/core';
import { IonModal } from '@ionic/angular/standalone';
import { OrderContractComponent } from '../order-contract/order-contract.component';
import { OrderContractData } from '../order-contract/order-contract.model';

@Component({
  selector: 'poplin-order-contract-modal',
  templateUrl: './order-contract-modal.component.html',
  styleUrls: ['./order-contract-modal.component.scss'],
  standalone: true,
  imports: [IonModal, CommonModule, OrderContractComponent],
})
export class OrderContractModalComponent implements OnDestroy {
  @ViewChild('modal') modal!: IonModal;
  @ViewChild(OrderContractComponent)
  orderContractComponent!: OrderContractComponent;

  @Input() contractData!: OrderContractData;
  @Input() viewMode = false;
  @Input() isOpen = false;

  @Output() acceptContract = new EventEmitter<void>();
  @Output() proposeRate = new EventEmitter<void>();
  @Output() declineContract = new EventEmitter<void>();
  @Output() closeContract = new EventEmitter<void>();
  @Output() orderNumberClick = new EventEmitter<void>();

  public breakpoints: number[] = [0, 1];
  public initialBreakpoint = 1;

  resetSwipeButton(): void {
    this.orderContractComponent.resetSwipeButton();
  }

  didDismiss(): void {
    this.closeContract.emit();
  }

  onAcceptContract(): void {
    this.acceptContract.emit();
  }

  onProposeRate(): void {
    this.proposeRate.emit();
  }

  onDeclineContract(): void {
    this.declineContract.emit();
  }

  onCloseContract(): void {
    this.isOpen = false;
    this.closeContract.emit();
  }

  onOrderNumberClick(): void {
    this.orderNumberClick.emit();
  }

  ngOnDestroy(): void {
    this.modal.dismiss();
  }
}
