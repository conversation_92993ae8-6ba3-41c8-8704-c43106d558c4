<div>
  <ion-modal
    #modal
    [isOpen]="isOpen"
    [initialBreakpoint]="initialBreakpoint"
    [breakpoints]="breakpoints"
    class="modal-container"
    (didDismiss)="didDismiss()"
    [showBackdrop]="isOpen"
  >
    <ng-template>
      <div class="modal-content">
        <poplin-order-contract
          [contractData]="contractData"
          [viewMode]="viewMode"
          (acceptContract)="onAcceptContract()"
          (proposeRate)="onProposeRate()"
          (declineContract)="onDeclineContract()"
          (closeContract)="onCloseContract()"
          (orderNumberClick)="onOrderNumberClick()"
        ></poplin-order-contract>
      </div>
    </ng-template>
  </ion-modal>
</div>
