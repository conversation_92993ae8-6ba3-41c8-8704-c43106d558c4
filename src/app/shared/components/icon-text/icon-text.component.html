<div class="icon-text">
  <ion-row>
    <ion-col size="auto">
      <poplin-icon
        *ngIf="icon"
        [name]="icon"
        [color]="iconColor"
        [size]="size"
      ></poplin-icon>
    </ion-col>

    <ion-col class="content-wrapper">
      <p
        *ngFor="let text of texts; let i = index"
        [ngClass]="[getVariant(text, i), text.style || '']"
        [innerHTML]="text.content"
      ></p>
    </ion-col>
  </ion-row>
</div>
