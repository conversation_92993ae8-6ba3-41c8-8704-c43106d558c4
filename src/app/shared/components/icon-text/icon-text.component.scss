.icon-text {
  ion-row {
    align-items: flex-start;

    ion-col:first-of-type {
      margin: 0;
      padding-top: 4px;
      padding-left: 0;
      padding-right: 0px;

      poplin-icon {
        margin-top: 0;
        margin-left: 0;
        align-self: flex-start;
      }
    }

    ion-col.content-wrapper {
      padding-bottom: 0px;

      p {
        margin: 0px;
        line-height: 20px;

        &.primary {
          font-size: 16px;
          font-weight: 700;
          color: var(--color-content-primary);

          &.default {
            font-weight: 400;
          }
        }

        &.secondary {
          font-size: 14px;
          font-weight: 400;
          color: var(--color-content-primary);
        }

        &.tertiary {
          font-size: 14px;
          font-weight: 400;
          color: var(--color-content-alt);
        }
      }
    }
  }
}
