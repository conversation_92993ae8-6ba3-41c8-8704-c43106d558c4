import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { IonCol, IonRow } from '@ionic/angular/standalone';
import { IconComponent } from '../icon/icon.component';
import { TextData, TextVariant } from './icon-text.model';

@Component({
  selector: 'poplin-icon-text',
  templateUrl: './icon-text.component.html',
  styleUrls: ['./icon-text.component.scss'],
  standalone: true,
  imports: [IonCol, IonRow, IconComponent, CommonModule],
})
export class IconTextComponent {
  @Input() texts: TextData[] = [];
  @Input() icon = '';
  @Input() iconColor = '--color-content-primary';
  @Input() size = 20;

  getVariant(text: TextData, index: number): TextVariant {
    if (text.variant) {
      return text.variant;
    }
    return index === 0 ? TextVariant.Primary : TextVariant.Tertiary;
  }
}
