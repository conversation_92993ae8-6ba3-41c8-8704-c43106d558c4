<div class="swipe-container" [class.animating]="isAnimating">
  <div
    [ngClass]="{
      background: true,
      'background-disabled': isDisabled,
      'background-animating': isAnimating
    }"
  ></div>
  <div *ngIf="value < max && !isDisabled && !isAnimating" class="pulse"></div>

  <div [ngClass]="{ 'loader-container': true, show: showLoader }">
    <ion-spinner name="circular"></ion-spinner>
  </div>

  <ion-range
    [value]="value"
    [min]="min"
    [max]="max"
    [disabled]="isDisabled || isAnimating"
    (ionChange)="handleSwipeChange($event)"
    [label]="label"
  >
  </ion-range>
</div>
