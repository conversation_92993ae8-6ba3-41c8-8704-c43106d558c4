.swipe-container {
  position: relative;

  &.animating {
    ion-range {
      &::part(knob) {
        display: none;
      }

      opacity: 0;
      transition: opacity 0.5s ease;
    }
  }

  .pulse {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background-size: 250% !important;

    background: linear-gradient(
      90deg,
      transparent 0%,
      transparent 35%,
      white 50%,
      transparent 65%,
      transparent 100%
    );

    opacity: 0.35;
    filter: blur(8px);
    -webkit-filter: blur(8px);

    pointer-events: none;
    -webkit-animation: bg-pan-right 1.5s ease infinite;
    animation: bg-pan-right 1.5s ease infinite;
  }

  .background {
    border-radius: var(--border-radius-round);
    background: linear-gradient(
        90deg,
        rgba(0, 0, 0, 0.2) 34%,
        rgba(0, 0, 0, 0) 100%
      ),
      var(--viridian-800);
    box-shadow: 0px 4px 24px 0px rgba(0, 0, 0, 0.04);
    width: 100%;
    height: 48px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: -3px;

    &.background-animating {
      width: 48px;
      height: 48px;
      border-radius: var(--border-radius-round);
      background: var(--viridian-800);
      transition: width 0.5s ease;
    }

    &.background-disabled {
      background: var(--gray-650);
    }
  }

  .loader-container {
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
    width: 36px;
    height: 36px;
    background-color: var(--white);
    border-radius: var(--border-radius-round);
    display: flex;
    align-items: center;
    justify-content: center;
    left: calc(100% - 24px);
    visibility: hidden;
    padding: 6px;

    &.show {
      visibility: visible;
      left: 50%;
      transition: left 0.5s ease;
    }

    .spinner {
      width: 24px;
      height: 24px;
      background-image: url('/assets/images/loading.gif');
      background-size: contain;
      background-position: center;
      background-repeat: no-repeat;
    }
  }
}

ion-spinner {
  color: var(--viridian-800) !important;
}

ion-range {
  --knob-size: 36px;
  --knob-background: var(--white);

  width: calc(100% - 48px);
  left: 50%;
  transform: translate(-50%, 0%);
  transition: opacity 0.5s ease;

  &::part(label) {
    position: absolute;
    width: 100%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: -1;

    color: var(--white);
    text-align: center;

    font-family: 'PitchSans-Medium';
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 20px;
    letter-spacing: 1.6px;
    text-transform: uppercase;
  }

  &::part(bar) {
    display: none;
  }

  &::part(bar-active) {
    display: none;
  }

  &::part(knob) {
    transform: scale(1);
    outline: 0;
  }

  &::part(knob)::before {
    display: none;
  }

  &::part(knob)::after {
    content: '';
    position: absolute;
    width: 36px;
    height: 36px;
    background-color: var(--viridian-800);
    mask-image: url('/assets/svgs/arrow_right_custom.svg');
    -webkit-mask-image: url('/assets/svgs/arrow_right_custom.svg');
    mask-repeat: no-repeat;
    mask-position: center;
    mask-size: contain;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
  }

  &.range-disabled {
    opacity: 1;

    &::part(knob)::after {
      background-color: var(--gray-650);
    }
  }
}

@keyframes bg-pan-right {
  0% {
    background-position: 100% center;
  }
  100% {
    background-position: 0% center;
  }
}

@-webkit-keyframes bg-pan-right {
  0% {
    background-position: 100% center;
  }
  100% {
    background-position: 0% center;
  }
}
