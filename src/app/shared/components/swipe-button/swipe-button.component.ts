import { CommonModule } from '@angular/common';
import {
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  EventEmitter,
  Input,
  Output,
} from '@angular/core';

export const SwipeButtonEventType = {
  SwipeComplete: 'swipeComplete',
  SwipeReset: 'swipeReset',
} as const;

export type SwipeButtonEventTypes =
  (typeof SwipeButtonEventType)[keyof typeof SwipeButtonEventType];

export interface SwipeButtonEvent {
  eventType: SwipeButtonEventTypes;
}

@Component({
  selector: 'poplin-swipe-button',
  standalone: true,
  imports: [CommonModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './swipe-button.component.html',
  styleUrls: ['./swipe-button.component.scss'],
})
export class SwipeButtonComponent {
  @Input() label = '';
  @Input() isDisabled = false;

  @Output() eventEmitter = new EventEmitter<SwipeButtonEvent>();

  public value = 0;
  public min = 0;
  public max = 100;
  public isAnimating = false;
  public showLoader = false;

  handleSwipeChange(event: any): void {
    if (this.isAnimating) return;

    this.value = event.detail.value;

    if (this.value >= this.max) {
      this.startAcceptAnimation();

      setTimeout(() => {
        this.eventEmitter.emit({
          eventType: SwipeButtonEventType.SwipeComplete,
        });
      }, 1000);

      return;
    }
  }

  private startAcceptAnimation(): void {
    this.isAnimating = true;
    this.showLoader = true;
  }

  public resetState(): void {
    this.value = this.min;
    this.isAnimating = false;
    this.showLoader = false;

    this.eventEmitter.emit({ eventType: SwipeButtonEventType.SwipeReset });
  }
}
