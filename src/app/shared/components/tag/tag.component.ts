import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { IconComponent } from '../icon/icon.component';

@Component({
  selector: 'poplin-tag',
  templateUrl: './tag.component.html',
  styleUrls: ['./tag.component.scss'],
  imports: [IconComponent, CommonModule],
  standalone: true,
})
export class TagComponent implements OnInit {
  @Input() label = '';
  @Input() color = '';
  @Input() icon = '';

  words: string[] = [];

  ngOnInit(): void {
    this.words = this.label.split(' ');
  }
}
