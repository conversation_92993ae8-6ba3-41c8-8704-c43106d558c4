import { ElementRef, inject } from '@angular/core';
import { BaseCustomComponent } from './base-custom.component';

export type DefineIdFn = (base: BaseCustomComponent) => void;

export const componentIdFnFactory = (
  tagName: string | undefined
): DefineIdFn => {
  const _elementRef = inject(ElementRef);

  return (base: BaseCustomComponent): void => {
    const defaultId = tagName
      ? tagName.toLowerCase().split('poplin-').join('')
      : 'poplin-unknown';
    const { id } = base;

    if (_elementRef.nativeElement) {
      if (id?.trim()) {
        _elementRef.nativeElement.id = `${id.trim()}-wrapper`;
      } else {
        base.id = `${defaultId}-element`;
        _elementRef.nativeElement.id = `${base.id}-wrapper`;
      }
    }
  };
};
