.select-container {
  position: relative;
  display: inline-block;
  width: 100%;

  &__select {
    width: 100%;
    padding: 4px 16px 2px 15px;
    border: 1px solid var(--gray-250);
    border-radius: 8px;
    font-size: 14px;
    outline: none;
    transition: border 0.3s;
    background-color: var(--white);
  }

  &__select:focus {
    border: 1px solid var(--Form-Fields-Border-focus);
  }

  &__label {
    position: absolute;
    top: 12px;
    left: 12px;
    font-size: 14px;
    color: var(--gray-700);
    pointer-events: none;
    transition: all 0.3s;
    background: var(--white);
    padding: 0 4px;
    z-index: 500;
  }

  &__label--focused {
    top: -6px;
    font-size: 12px;
    color: var(--Form-Fields-Border-focus);
  }

  ion-select {
    --placeholder-color: var(--Form-Fields-Content-label);
    --placeholder-opacity: none;

    &::part(icon) {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      pointer-events: none;
      font-size: 14px;
      font-weight: 400;
    }
  }
}
