import { NgForOf } from '@angular/common';
import { Component, Input } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { getFormErrorMessage } from 'src/app/_pages/start/constant/form-error.constant';

@Component({
  selector: 'pop-sudster-dropdown',
  standalone: true,
  templateUrl: './select.component.html',
  styleUrls: ['./select.component.scss'],
  imports: [IonicModule, ReactiveFormsModule, NgForOf],
})
export class SelectComponent {
  @Input() control!: FormControl;
  @Input() label!: string;
  @Input() options: { value: string; label: string }[] = [];
  @Input() placeholder: string = 'Select an option';
  @Input() interfaceType: 'action-sheet' | 'popover' | 'alert' = 'popover';
  @Input() mode: 'ios' | 'md' = 'md';

  isError = false;
  isFocused = false;
  message = '';

  onFocus() {
    this.isFocused = true;
    this.isError = false;
  }

  onBlur() {
    this.isFocused = false;
    this.message = getFormErrorMessage(this.control);
    this.isError = this.control.errors && this.control.dirty;
  }
}
