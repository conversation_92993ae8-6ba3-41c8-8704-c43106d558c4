<div class="input-container" [class.input-container--error]="isError">
  <input
    #inputField
    [inputUpperCase]="isUpperCase"
    autocomplete="on"
    type="text"
    [formControl]="control"
    [id]="label"
    (focus)="onFocus()"
    (blur)="onBlur()"
    class="input-container__form-input"
  />
  <label
    [for]="label"
    class="input-container__form-label"
    [class.input-container__form-label--focused]="isFocused || control.value"
  >
    {{ label }}
  </label>
  <ng-container *ngIf="isError">
    <span class="input-container__form-input--error">
      <i class="material-icons">info</i>
      <label>{{ message }}</label>
    </span>
  </ng-container>
</div>
