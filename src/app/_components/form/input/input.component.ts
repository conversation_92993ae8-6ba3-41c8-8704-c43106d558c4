import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { UpperCaseDirective } from 'src/app/_directive/upper-case.directive';
import { getFormErrorMessage } from 'src/app/_pages/start/constant/form-error.constant';

@Component({
  selector: 'pop-sudster-input',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, UpperCaseDirective],
  templateUrl: './input.component.html',
  styleUrls: ['./input.component.scss'],
})
export class InputComponent {
  @Input() control = new FormControl();
  @Input() label = '';
  @Input() isUpperCase = false;
  isFocused = false;
  isError = false;
  message = '';

  onFocus() {
    this.isFocused = true;
    this.isError = false;
  }

  onBlur() {
    this.isFocused = false;
    this.message = getFormErrorMessage(this.control);
    this.isError = this.control.errors && this.control.dirty;
  }
}
