.input-container {
  position: relative;
  display: inline-block;
  width: 100%;

  &--error {
    &__form-input {
      border: 1px solid var(--Form-Fields-Border-error);
    }
  }

  &__form-input {
    width: 100%;
    padding: var(--Form-Fields-Padding-top-vertical)
      var(--Form-Fields-Padding-horizontal-small)
      var(--Form-Fields-Padding-bottom-vertical)
      var(--Form-Fields-Padding-horizontal-small);
    border: 1px solid var(--gray-200);
    border-radius: 8px;
    font-size: 16px;
    outline: none;
    transition: border 0.3s;

    &:focus {
      border: 1px solid var(--Form-Fields-Border-focus);
    }

    &--error {
      display: flex;
      width: fit-content;
      i {
        display: flex;
        justify-content: center;
        align-items: center;
        width: fit-content;
        position: relative;
        color: var(--Form-Fields-Border-error);
      }
      label {
        font-size: 16px;
        font-weight: 400;
      }
    }
  }

  &__form-label {
    position: absolute;
    top: 12px;
    left: 10px;
    color: var(--Form-Fields-Content-label) !important;
    pointer-events: none;
    transition: all 0.3s;
    background: var(--white);
    padding: 0 4px;
    font-size: 16px;
    font-weight: 400;
    line-height: 22px;

    &--focused {
      top: 2px;
      font-size: 12px !important;
    }
  }
}
