<ion-header>
  <ion-toolbar>
    <ion-buttons *ngIf="canNavigateBack" slot="start">
      <ion-button (click)="navigateBack()">
        <ion-icon slot="icon-only" name="arrow-back" size="small"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title class="slide-up-title">
      {{ currentData.sectionTitle }}
    </ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="closeModal()">
        <ion-icon slot="icon-only" name="close" size="small"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>

  <p *ngIf="currentData.sectionDescription" class="description">
    <poplin-icon
      *ngIf="currentData.sectionDescriptionIcon"
      color="--blue-450"
      [name]="currentData.sectionDescriptionIcon"
      [size]="20"
    ></poplin-icon>
    {{ currentData.sectionDescription }}
  </p>
</ion-header>

<div class="content-wrapper">
  <swiper
    [initialSlide]="slideOpts.initialSlide"
    [speed]="slideOpts.speed"
    [autoHeight]="true"
    [allowTouchMove]="false"
    #slider
  >
    <ng-template swiperSlide>
      <ion-list>
        <ion-radio-group
          slot="fixed"
          (ionChange)="onOptionSelected($event)"
          [value]="currentSelectedOptionValue"
        >
          <ion-item
            *ngFor="let option of data.options"
            lines="none"
            detail="false"
          >
            <ion-label [class.disabled-label]="option.disabled">
              {{ option.label }}
              <p *ngIf="option.description" class="option-description">
                {{ option.description }}
              </p>
            </ion-label>
            <ion-radio
              slot="start"
              [value]="option.value"
              [disabled]="option.disabled"
            ></ion-radio>
          </ion-item>
        </ion-radio-group>
      </ion-list>
    </ng-template>

    <ng-template swiperSlide>
      <ion-list>
        <ion-radio-group
          (ionChange)="onOptionSelectedSecondPage($event)"
          [value]="currentSelectedOptionValue"
        >
          <ion-item
            *ngFor="let option of linkedOptions"
            lines="none"
            detail="false"
          >
            <ion-label [class.disabled-label]="option.disabled">
              {{ option.label }}
              <p *ngIf="option.description" class="option-description">
                {{ option.description }}
              </p>
            </ion-label>
            <ion-radio
              slot="start"
              [value]="option.value"
              [disabled]="option.disabled"
            ></ion-radio>
          </ion-item>
        </ion-radio-group>
      </ion-list>
    </ng-template>
  </swiper>

  <div class="actions">
    <ion-button
      [disabled]="actionButtonDisabled"
      (click)="clickBtnAction()"
      color="primary"
    >
      <span class="action-btn">{{ actionButtonLabel }}</span>
    </ion-button>
    <div *ngIf="isThereAnyPagination" class="slide-progress">
      <div class="step" [class.active]="currentSlideIndex >= 0"></div>
      <div class="step" [class.active]="currentSlideIndex >= 1"></div>
    </div>
  </div>
</div>
