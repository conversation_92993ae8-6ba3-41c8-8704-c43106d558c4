import { CommonModule } from '@angular/common';
import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { IonicModule, ModalController } from '@ionic/angular';
import { IconComponent } from 'src/app/shared/components/icon/icon.component';
import SwiperCore, { Pagination } from 'swiper';
import { SwiperComponent, SwiperModule } from 'swiper/angular';
import { SelectOption, SlideOptions } from '../select-with-slide-up.component';

SwiperCore.use([Pagination]);

enum ActionButtonLabel {
  Continue = 'Continue',
  Save = 'Save',
}

@Component({
  selector: 'select-options-renderer',
  standalone: true,
  templateUrl: './select-options-renderer.component.html',
  styleUrls: ['./select-options-renderer.component.scss'],
  imports: [IonicModule, CommonModule, IconComponent, SwiperModule],
})
export class SelectOptionsRendererComponent<T> implements OnInit {
  @Input() data: SlideOptions<T> = null;
  @ViewChild('slider', { static: false }) slider: SwiperComponent;

  readonly FINAL_SLIDE_INDEX = 1;

  // to store first selected value on multi view modal
  firstSelectionOption: SelectOption<T> = null;
  currentSelectedOptionValue: T = null;

  currentData: SlideOptions<T> = null;
  linkedOptions: SelectOption<T>[] = [];

  currentSlideIndex = 0;
  actionButtonLabel: ActionButtonLabel = ActionButtonLabel.Save;
  actionButtonDisabled = true;
  canNavigateBack = false;
  isThereAnyPagination = false;

  readonly slideOpts = {
    initialSlide: 0,
    speed: 400,
  };
  isOptionEverChanged: boolean = false;

  constructor(private readonly modalController: ModalController) {}

  ngOnInit(): void {
    this.currentData = this.data;
    this.currentSelectedOptionValue = this.data.selectedValue;
    this.isThereAnyPagination = this.data?.options.some(
      (option) =>
        !option.disabled && !!option.linkedSelectOptions?.options?.length
    );
    this.updateNavigationState();
  }

  closeModal(): void {
    this.modalController.dismiss(null);
  }

  onOptionSelected(event: CustomEvent): void {
    this.currentSelectedOptionValue = event.detail.value;
    this.updateNavigationState();
    this.isOptionEverChanged = true;
  }

  onOptionSelectedSecondPage(event: CustomEvent): void {
    this.currentSelectedOptionValue = event.detail.value;
    this.actionButtonDisabled = false;
    this.actionButtonLabel = ActionButtonLabel.Save;
    this.isOptionEverChanged = true;
  }

  async clickBtnAction(): Promise<void> {
    const firstPageSelectedOption = this.getOptionByValue(
      this.currentSelectedOptionValue
    );

    if (!firstPageSelectedOption) {
      return;
    }

    if (this.actionButtonLabel === ActionButtonLabel.Continue) {
      this.firstSelectionOption = firstPageSelectedOption;
      this.currentSelectedOptionValue = null;
      this.prepareSecondSlide(firstPageSelectedOption);
      this.slider.swiperRef.slideNext();

      return;
    }

    // Save btn action
    const isMultiSlide = this.currentSlideIndex === this.FINAL_SLIDE_INDEX;
    const returnData = {
      firstSelection: isMultiSlide
        ? this.firstSelectionOption
        : firstPageSelectedOption,
      secondSelection: isMultiSlide ? firstPageSelectedOption : undefined,
    };

    this.modalController.dismiss(returnData);
  }

  async navigateBack(): Promise<void> {
    if (!this.canNavigateBack) {
      this.closeModal();
      return;
    }

    this.currentSelectedOptionValue = this.firstSelectionOption?.value ?? null;
    this.currentSlideIndex = 0;
    this.currentData = this.data;
    this.linkedOptions = [];
    this.actionButtonLabel = ActionButtonLabel.Continue;
    this.actionButtonDisabled = false;
    this.canNavigateBack = false;

    this.slider.swiperRef.slidePrev();
  }

  private prepareSecondSlide(selectedOption: SelectOption<T>): void {
    this.linkedOptions = selectedOption.linkedSelectOptions?.options ?? [];
    this.currentData = selectedOption.linkedSelectOptions!;
    // If no change has ever been made, we need to use the selected value from external data
    if (!this.isOptionEverChanged) {
      this.currentSelectedOptionValue =
        selectedOption.linkedSelectOptions.selectedValue;
    }
    this.currentSlideIndex = this.FINAL_SLIDE_INDEX;
    this.actionButtonLabel = ActionButtonLabel.Save;
    this.actionButtonDisabled = true;
    this.canNavigateBack = true;
  }

  private updateNavigationState(): void {
    const selectedOption = this.getOptionByValue(
      this.currentSelectedOptionValue
    );

    this.actionButtonDisabled = !selectedOption;
    if (!selectedOption) return;

    const hasNextSlide = !!selectedOption.linkedSelectOptions?.options?.length;
    this.actionButtonLabel = hasNextSlide
      ? ActionButtonLabel.Continue
      : ActionButtonLabel.Save;
  }

  private getOptionByValue(value: T): SelectOption<T> | undefined {
    return this.currentData?.options.find((option) => option.value === value);
  }
}
