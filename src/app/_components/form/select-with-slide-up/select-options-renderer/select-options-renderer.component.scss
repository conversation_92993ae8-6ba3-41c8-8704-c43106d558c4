@import 'type-mixins';
@import 'tokens';

:host {
  height: auto !important;
}

.content-wrapper {
  padding: 0 16px 16px;
}

swiper {
  margin: 0;
  padding: 0;
  width: 100%;

  ::ng-deep .swiper-wrapper {
    height: auto !important;
  }
}

ion-header {
  box-shadow: none;

  ion-title:first-child {
    margin-left: 16px;
  }
}

p.description {
  margin: 6px 16px 0;
  padding: 0;
  color: var(--form-fields-content-message-help);
  @include s-body;

  poplin-icon {
    margin-right: 4px;
    display: inline-block !important;
    line-height: 1.4;
    vertical-align: sub;
  }
}

ion-content {
  padding: 0 16px;
}

ion-list {
  margin-top: 0;
  padding: 0;

  ion-item {
    --padding-start: 5px;
    --padding-end: 5px;
    --inner-padding-end: 0;
    --inner-padding-start: 0;
    --border-radius: 0;
    --ripple-color: transparent;

    position: relative;
    overflow: hidden;

    .disabled-label {
      opacity: 1;
      color: var(--form-fields-content-label);
      pointer-events: none;
    }

    ion-radio {
      align-self: self-start;
      margin: 13px 12px 0 0;

      &.radio-disabled {
        --color: var(--Radio-Icon-background-disabled, #d1d1d1);
        background-color: var(--Radio-Icon-background-disabled, #d1d1d1);
        border-radius: 50%;
        opacity: 1;
        pointer-events: none;
      }
    }
  }
}

.actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding-top: 20px;

  ion-button {
    width: 100%;
    max-width: 380px;
  }
}

.slide-progress {
  display: flex;
  justify-content: center;
  margin-top: 10px;
  gap: 8px;

  .step {
    height: 2px;
    width: 12px;
    background-color: var(--wizard-unselected-color);
    transition: background-color 0.3s;

    &.active {
      background-color: var(--wizard-active-color);
    }
  }
}
