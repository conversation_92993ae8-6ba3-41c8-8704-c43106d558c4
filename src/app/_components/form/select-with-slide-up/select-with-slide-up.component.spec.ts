import { ComponentFixture, TestBed } from '@angular/core/testing';

import { SelectWithSlideUpComponent } from './select-with-slide-up.component';

describe('SelectWithSlideUpComponent', () => {
  let component: SelectWithSlideUpComponent;
  let fixture: ComponentFixture<SelectWithSlideUpComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [SelectWithSlideUpComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(SelectWithSlideUpComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
