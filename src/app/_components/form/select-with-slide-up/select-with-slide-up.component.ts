import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  SimpleChanges,
} from '@angular/core';
import { IonicModule, ModalController } from '@ionic/angular';
import { SelectOptionsRendererComponent } from './select-options-renderer/select-options-renderer.component';

export interface SelectOption<T> {
  value: T;
  description: string;
  label: string;
  disabled?: boolean;
  linkedSelectOptions?: SlideOptions<T>;
}

export interface SlideOptions<T> {
  sectionTitle: string;
  sectionDescription: string;
  sectionDescriptionIcon: string;
  selectedValue: T;
  options: SelectOption<T>[];
}

export interface SelectedOptionsResult<T> {
  firstSelection: SelectOption<T>;
  secondSelection?: SelectOption<T>;
  modalWasDismissed?: boolean;
}
@Component({
  selector: 'select-with-slide-up',
  standalone: true,
  imports: [IonicModule, CommonModule],
  templateUrl: './select-with-slide-up.component.html',
  styleUrls: ['./select-with-slide-up.component.scss'],
})
export class SelectWithSlideUpComponent<T> implements OnChanges {
  @Input() label: string = 'Select';
  @Input() options: SlideOptions<T> = null;
  @Input() disabled: boolean = false;
  @Input() selectedValue: T | null = null;

  @Output() valueChanged = new EventEmitter<SelectedOptionsResult<T>>();

  selectedOption: SelectOption<T> | null = null;

  constructor(private readonly modalController: ModalController) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.options?.currentValue && changes.selectedValue?.currentValue) {
      this.selectedOption = this.options.options.find(
        (option) => option.value === changes.selectedValue.currentValue
      );
      if (this.selectedOption) {
        this.label = this.selectedOption.label;
      }
    }
  }

  public async openSlideUp() {
    if (this.disabled) {
      return;
    }

    const modal = await this.modalController.create({
      component: SelectOptionsRendererComponent,
      componentProps: {
        data: {
          ...this.options,
          selectedValue: this.selectedValue,
        },
      },
      cssClass: 'poplin-theme-dynamic-height',
      backdropDismiss: false,
    });

    modal.onDidDismiss().then(
      (result: {
        data?: {
          firstSelection: SelectOption<T>;
          secondSelection: SelectOption<T>;
        };
      }) => {
        const selectedOptions = this.handleModalDismiss(result.data);
        this.valueChanged.emit(selectedOptions);
      }
    );

    await modal.present();
  }

  private handleModalDismiss(data?: {
    firstSelection: SelectOption<T>;
    secondSelection?: SelectOption<T>;
  }): SelectedOptionsResult<T> {
    if (!data) {
      return {
        firstSelection: null,
        secondSelection: null,
        modalWasDismissed: true,
      };
    }

    const selectedOptions = data as SelectedOptionsResult<T>;
    this.selectedOption = selectedOptions.firstSelection;
    this.label = selectedOptions.firstSelection?.label ?? this.label;

    return selectedOptions;
  }
}
