@import 'type-mixins';
@import 'poplin-styles';

.select-container {
  display: flex;
  flex-direction: column;
  padding: 10px 12px;
  cursor: pointer;
  position: relative;
  min-height: 48px;

  border-radius: var(--border-radius-default);
  border: 1px solid var(--Form-Fields-Border-default);
  background: var(--Form-Fields-Background-default);
  box-shadow: 0px 4px 24px 0px rgba(0, 0, 0, 0.04);

  overflow: hidden;
  color: var(--color-content-primary);
  text-overflow: ellipsis;
  white-space: nowrap;

  @include r-body;
  @include body-text;
  line-height: 22px;
  font-size: 16px;
}

.value {
  margin: 2px 28px 0 0;
}

ion-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 20px;
  color: var(--color-content-primary);
  --stroke-width: 32px;
}

.select-container.disabled {
  color: var(--color-content-primary);
  background: var(--form-fields-background-disabled);
  cursor: not-allowed;
}
