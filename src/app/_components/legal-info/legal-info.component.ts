import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import {
  AbstractControl,
  ReactiveFormsModule,
  UntypedFormGroup,
} from '@angular/forms';
import { InputComponent } from '../form/input/input.component';
export interface FormData {
  data: Record<string, string>;
  isValid: boolean;
}

@Component({
  selector: 'app-legal-info',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, InputComponent],
  templateUrl: './legal-info.component.html',
})
export class LegalInfoComponent {
  @Input() public legalForm: UntypedFormGroup;

  get firstName(): AbstractControl {
    return this.legalForm.get('FirstName');
  }

  get lastName(): AbstractControl {
    return this.legalForm.get('LastName');
  }

  get email(): AbstractControl {
    return this.legalForm.get('Email');
  }
}
