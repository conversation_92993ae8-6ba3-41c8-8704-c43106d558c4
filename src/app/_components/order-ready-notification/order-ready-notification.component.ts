import { Component, inject, Input, OnInit } from '@angular/core';
import { InputComponent } from '../form/input/input.component';
import { IconComponent } from '../../shared/components/icon/icon.component';
import { IonicModule, ModalController } from '@ionic/angular';
import { NgForOf, NgIf } from '@angular/common';
import { SwiperModule } from 'swiper/angular';
import { Router } from '@angular/router';
import {
  formatDeadline,
  getDeadline,
  getStage,
} from '../../_utils/order-utils';
import { Stages } from '../../_interfaces/order-data.interface';
import { LocationService } from '../../_services/location.service';
import { ApiService } from '../../_services/api.service';

interface Metadata {
  CustomerFirstName: string;
  OrderStatus: string;
  OrderSize: string;
  actions: any[];
  orderNumber: string;
  Address: string;
  EstimatedDriveTime: string;
}

@Component({
  selector: 'app-order-ready-notification',
  standalone: true,
  templateUrl: './order-ready-notification.component.html',
  styleUrls: ['./order-ready-notification.component.scss'],
  imports: [
    InputComponent,
    IconComponent,
    IonicModule,
    NgForOf,
    NgIf,
    SwiperModule,
  ],
})
export class OrderReadyNotificationComponent implements OnInit {
  modalCtrl = inject(ModalController);
  router = inject(Router);
  locationService = inject(LocationService);
  apiService = inject(ApiService);

  @Input() notificationData: {
    id: string;
    metadata: Metadata;
  };

  data!: Metadata;

  deadline = '';
  address = '';

  ngOnInit() {
    this.data = this.notificationData.metadata;
    const stage = getStage(this.data as any);
    this.deadline =
      (stage == Stages.PickUp ? 'Pickup by ' : 'Deliver by ') +
      formatDeadline(getDeadline(this.data as any));
    this.address = this.locationService.getAreaName(this.data.Address);
  }

  async closeModal() {
    this.apiService
      .patch(`LpInboxMessages/v1/sudster/inbox/${this.notificationData.id}`, {})
      .subscribe(async () => await this.modalCtrl.dismiss(null));
  }

  async viewOrder() {
    await this.closeModal();
    await this.router.navigate(
      [this.data.actions[1].route, this.data.actions[1].routeParams],
      {
        replaceUrl: true,
      }
    );
  }
}
