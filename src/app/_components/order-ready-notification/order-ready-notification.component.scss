ion-title {
  margin-left: -4px;
}

ion-col {
  padding-left: 0;
  padding-right: 0;
}

.title-description,
.content-wrapper {
  padding: 0 16px 16px;
}

.title {
  color: var(--color-content-primary);
  font-size: 20px;
  font-weight: 700;
  line-height: 28px;
}

h5 {
  color: var(--color-content-alt);
  font-family: 'PitchSans-Bold', serif;
  font-size: 13px;
  font-style: normal;
  font-weight: 700;
  line-height: 16px;
  letter-spacing: 1.3px;
  text-transform: uppercase;
  margin: 0 0 8px;
}

p {
  color: var(--color-content-alt);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  margin: 0;
}

.actions {
  ion-button {
    height: 48px;
    width: 100%;
    margin: 24px 0 8px;
  }
}
