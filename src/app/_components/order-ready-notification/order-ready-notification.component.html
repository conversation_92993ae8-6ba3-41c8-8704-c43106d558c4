<ion-header>
  <ion-toolbar>
    <ion-title class="title"> Ready for delivery </ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="closeModal()">
        <poplin-icon name="close" [size]="36"></poplin-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>

  <p class="title-description">
    Issue resolved! You may now prepare this customer’s order for delivery.
  </p>
</ion-header>

<div class="content-wrapper" *ngIf="data">
  <h5>ORDER #{{ data.orderNumber }}</h5>

  <ion-row>
    <ion-col size="1"
      ><poplin-icon name="person" [size]="20"></poplin-icon
    ></ion-col>
    <ion-col size="11"
      ><b>{{ data.CustomerFirstName }}</b></ion-col
    >
  </ion-row>
  <ion-row>
    <ion-col size="1"
      ><poplin-icon name="alarm" [size]="20"></poplin-icon
    ></ion-col>
    <ion-col size="11"
      ><b>{{ deadline }}</b> <br /><span
        >{{ data.OrderSize }} bags</span
      ></ion-col
    >
  </ion-row>
  <ion-row>
    <ion-col size="1"
      ><poplin-icon name="location" [size]="20"></poplin-icon
    ></ion-col>
    <ion-col size="11">
      <p>{{ data.EstimatedDriveTime }} min drive | {{ address }}</p></ion-col
    >
  </ion-row>

  <div class="actions">
    <ion-button (click)="viewOrder()" color="primary">
      <span class="action-btn">View Order</span>
    </ion-button>
  </div>
</div>
