import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  Input,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import { ModalController } from '@ionic/angular';
import {
  IonButton,
  IonButtons,
  IonCheckbox,
  IonContent,
  IonFooter,
  IonItem,
  IonLabel,
  IonTitle,
  IonToolbar,
} from '@ionic/angular/standalone';
import { Until<PERSON><PERSON>roy } from '@ngneat/until-destroy';

@UntilDestroy()
@Component({
  selector: 'app-terms-and-policies',
  standalone: true,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [
    CommonModule,
    IonToolbar,
    IonTitle,
    IonButtons,
    IonButton,
    IonContent,
    IonItem,
    IonFooter,
    IonCheckbox,
    IonLabel,
  ],
  templateUrl: './terms-and-policies.component.html',
  styleUrls: ['./terms-and-policies.component.scss'],
  // to be able to inject the html and load the css.
  encapsulation: ViewEncapsulation.None,
})
export class TermsAndPoliciesComponent implements AfterViewInit {
  @Input() termsAndPolicies: {
    terms: { content: string; version: string };
    policies: { content: string; version: string };
  };
  @ViewChild(IonContent) contentComponent: IonContent;

  private scrollElement: HTMLElement;

  isScrolledToBottom = false;
  accepted = false;

  /**
   * https://github.com/ionic-team/ionic-framework/issues/12014#issue-235134678
   * On the ionic version we are using the `ionScrollEnd` still have this event bug on Android devices,
   * where the function will be called before the real finished of the scroll,
   * sent the information before the user reach the bottom of the page, leads to a inconsistent scroll position value.
   * I didn't find an updated suggestion to solve that issue, so a use this alternative solution by subtracting a
   * portion of the value make possible to match the value of the end of the page.
   */
  private readonly nativeDeviceAvgError = 10;

  constructor(private modalCtrl: ModalController) {}

  cancel() {
    return this.modalCtrl.dismiss(null, 'cancel');
  }

  confirm() {
    return this.modalCtrl.dismiss(
      {
        accepted: this.accepted,
        policyVersion: this.termsAndPolicies.policies.version,
        termsVersion: this.termsAndPolicies.terms.version,
      },
      'confirm'
    );
  }

  onTermsChanged() {
    this.accepted = !this.accepted;
  }

  async handleScrollEnd() {
    if (this.isScrolledToBottom) {
      return;
    }

    const {
      scrollHeight = 0,
      clientHeight = 0,
      scrollTop: currentScrollDepth = 0,
    } = this.scrollElement || {};
    const maxScrollDepth =
      scrollHeight - clientHeight - this.nativeDeviceAvgError;

    const isScrolledToBottom = currentScrollDepth >= maxScrollDepth;

    if (isScrolledToBottom) {
      this.isScrolledToBottom = true;
    }
  }

  async ngAfterViewInit() {
    this.scrollElement = await this.contentComponent.getScrollElement();
  }
}
