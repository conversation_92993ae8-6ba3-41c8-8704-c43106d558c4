.terms-title {
  padding-right: 0;
  text-align: center;
  -webkit-font-smoothing: antialiased; /* Helps smooth text rendering in Safari */
  backface-visibility: hidden; /* Helps prevent rendering issues */
}

.terms-footer {
  ion-button {
    height: 38px;
    width: 301px;
    font-size: 16px;
    font-weight: 600; /* Adjusted */
    line-height: 20px; /* 125% */
    letter-spacing: 1px; /* Adjusted */
    text-transform: uppercase;
    -webkit-font-smoothing: antialiased;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
  }
}

.terms-of-services-content {
  .terms-of-service-text {
    border-bottom: 1px solid #e1e1e1;
    padding-bottom: 20px;
    max-width: 100%;

    -webkit-font-smoothing: antialiased; /* Helps smooth text rendering */
    backface-visibility: hidden; /* Helps prevent rendering issues */
  }
  &::part(native) {
    padding: 25px 25px 0px 25px;
    background: var(--themes-light-background-alt, #f9f9f9);
  }
}

.privacy-policy-content {
  .privacy-policy {
    padding-top: 15px;
  }
  h1,
  h2,
  h3,
  h4,
  h5 {
    font-family: 'Fakt-Normal', 'Roboto', Helvetica, sans-serif;
    text-align: center;

    -webkit-font-smoothing: antialiased; /* Helps prevent blurry text */
    backface-visibility: hidden; /* Helps prevent blurry rendering in Safari */
  }

  li {
    p {
      margin: 0;

      -webkit-font-smoothing: antialiased; /* Helps smooth text rendering */
      backface-visibility: hidden; /* Helps prevent blurring */
    }
  }
  &::part(native) {
    padding: 0px 25px 25px 25px;
    background: var(--themes-light-background-alt, #f9f9f9);
  }
}

.terms-of-service-text {
  // Entire block copied from marketing site css
  .no-list-markers li::marker {
    content: '';
    font-size: 0; /* This will hide the marker for all li elements */
    /* Prevent potential rendering issues */
    -webkit-font-smoothing: antialiased;
    backface-visibility: hidden;
  }

  .lst-kix_list_14-1 > li:before {
    content: ' ';
  }
  .lst-kix_list_14-3 > li:before {
    content: ' ';
  }
  .lst-kix_list_14-0 > li:before {
    content: '' counter(lst-ctn-kix_list_14-0, decimal) '. ';
  }
  .lst-kix_list_14-4 > li:before {
    content: ' ';
  }
  .lst-kix_list_6-1 > li {
    counter-increment: lst-ctn-kix_list_6-1;
  }
  .lst-kix_list_14-5 > li:before {
    content: ' ';
  }
  .lst-kix_list_14-7 > li:before {
    content: ' ';
  }
  ol.lst-kix_list_15-0 {
    list-style-type: none;
  }
  .lst-kix_list_14-6 > li:before {
    content: ' ';
  }
  ul.lst-kix_list_9-3 {
    list-style-type: none;
  }
  ul.lst-kix_list_9-4 {
    list-style-type: none;
  }
  ul.lst-kix_list_9-1 {
    list-style-type: none;
  }
  ul.lst-kix_list_9-2 {
    list-style-type: none;
  }
  ul.lst-kix_list_9-7 {
    list-style-type: none;
  }
  .lst-kix_list_13-0 > li {
    counter-increment: lst-ctn-kix_list_13-0;
  }
  ul.lst-kix_list_9-8 {
    list-style-type: none;
  }
  .lst-kix_list_17-0 > li {
    counter-increment: lst-ctn-kix_list_17-0;
  }
  ul.lst-kix_list_9-5 {
    list-style-type: none;
  }
  ul.lst-kix_list_9-6 {
    list-style-type: none;
  }
  ul.lst-kix_list_9-0 {
    list-style-type: none;
  }
  .lst-kix_list_14-2 > li:before {
    content: ' ';
  }
  ol.lst-kix_list_12-0.start {
    counter-reset: lst-ctn-kix_list_12-0 0;
  }
  ol.lst-kix_list_17-1.start {
    counter-reset: lst-ctn-kix_list_17-1 0;
  }
  .lst-kix_list_14-8 > li:before {
    content: ' ';
  }
  .lst-kix_list_5-0 > li:before {
    content: '\0025cf   ';
  }
  ol.lst-kix_list_6-0 {
    list-style-type: none;
  }
  ol.lst-kix_list_6-1 {
    list-style-type: none;
  }
  ol.lst-kix_list_16-5 {
    list-style-type: none;
  }
  ol.lst-kix_list_16-6 {
    list-style-type: none;
  }
  ol.lst-kix_list_16-7 {
    list-style-type: none;
  }
  ol.lst-kix_list_16-8 {
    list-style-type: none;
  }
  .lst-kix_list_5-3 > li:before {
    content: '\0025aa   ';
  }
  ol.lst-kix_list_16-1 {
    list-style-type: none;
  }
  ol.lst-kix_list_16-2 {
    list-style-type: none;
  }
  .lst-kix_list_5-2 > li:before {
    content: '\0025aa   ';
  }
  ol.lst-kix_list_16-3 {
    list-style-type: none;
  }
  ol.lst-kix_list_16-4 {
    list-style-type: none;
  }
  .lst-kix_list_5-1 > li:before {
    content: 'o  ';
  }
  ol.lst-kix_list_18-0.start {
    counter-reset: lst-ctn-kix_list_18-0 0;
  }
  ol.lst-kix_list_16-0 {
    list-style-type: none;
  }
  .lst-kix_list_5-7 > li:before {
    content: '\0025aa   ';
  }
  ul.lst-kix_list_8-4 {
    list-style-type: none;
  }
  ul.lst-kix_list_8-5 {
    list-style-type: none;
  }
  .lst-kix_list_5-6 > li:before {
    content: '\0025aa   ';
  }
  .lst-kix_list_5-8 > li:before {
    content: '\0025aa   ';
  }
  ul.lst-kix_list_8-2 {
    list-style-type: none;
  }
  ul.lst-kix_list_8-3 {
    list-style-type: none;
  }
  ul.lst-kix_list_8-8 {
    list-style-type: none;
  }
  ul.lst-kix_list_8-6 {
    list-style-type: none;
  }
  ul.lst-kix_list_8-7 {
    list-style-type: none;
  }
  ol.lst-kix_list_6-6 {
    list-style-type: none;
  }
  ol.lst-kix_list_6-7 {
    list-style-type: none;
  }
  .lst-kix_list_5-4 > li:before {
    content: '\0025aa   ';
  }
  ol.lst-kix_list_6-8 {
    list-style-type: none;
  }
  .lst-kix_list_5-5 > li:before {
    content: '\0025aa   ';
  }
  ol.lst-kix_list_6-2 {
    list-style-type: none;
  }
  ul.lst-kix_list_8-0 {
    list-style-type: none;
  }
  ol.lst-kix_list_6-3 {
    list-style-type: none;
  }
  ul.lst-kix_list_8-1 {
    list-style-type: none;
  }
  ol.lst-kix_list_6-4 {
    list-style-type: none;
  }
  ol.lst-kix_list_16-4.start {
    counter-reset: lst-ctn-kix_list_16-4 0;
  }
  ol.lst-kix_list_6-5 {
    list-style-type: none;
  }
  .lst-kix_list_6-1 > li:before {
    content: '' counter(lst-ctn-kix_list_6-1, lower-latin) '. ';
  }
  .lst-kix_list_6-3 > li:before {
    content: '(' counter(lst-ctn-kix_list_6-3, decimal) ') ';
  }
  .lst-kix_list_6-5 > li {
    counter-increment: lst-ctn-kix_list_6-5;
  }
  .lst-kix_list_6-8 > li {
    counter-increment: lst-ctn-kix_list_6-8;
  }
  .lst-kix_list_6-0 > li:before {
    content: '' counter(lst-ctn-kix_list_6-0, decimal) '. ';
  }
  .lst-kix_list_6-4 > li:before {
    content: '(' counter(lst-ctn-kix_list_6-4, lower-latin) ') ';
  }
  .lst-kix_list_6-2 > li:before {
    content: '' counter(lst-ctn-kix_list_6-2, lower-roman) '. ';
  }
  ol.lst-kix_list_15-0.start {
    counter-reset: lst-ctn-kix_list_15-0 0;
  }
  .lst-kix_list_6-8 > li:before {
    content: '' counter(lst-ctn-kix_list_6-8, lower-roman) '. ';
  }
  .lst-kix_list_6-5 > li:before {
    content: '(' counter(lst-ctn-kix_list_6-5, lower-roman) ') ';
  }
  .lst-kix_list_6-7 > li:before {
    content: '' counter(lst-ctn-kix_list_6-7, lower-latin) '. ';
  }
  .lst-kix_list_6-6 > li:before {
    content: '' counter(lst-ctn-kix_list_6-6, decimal) '. ';
  }
  ol.lst-kix_list_17-8 {
    list-style-type: none;
  }
  .lst-kix_list_7-4 > li:before {
    content: ' ';
  }
  .lst-kix_list_7-6 > li:before {
    content: ' ';
  }
  ol.lst-kix_list_17-4 {
    list-style-type: none;
  }
  ol.lst-kix_list_17-5 {
    list-style-type: none;
  }
  ol.lst-kix_list_6-2.start {
    counter-reset: lst-ctn-kix_list_6-2 0;
  }
  ol.lst-kix_list_17-6 {
    list-style-type: none;
  }
  ol.lst-kix_list_17-7 {
    list-style-type: none;
  }
  ol.lst-kix_list_17-0 {
    list-style-type: none;
  }
  ol.lst-kix_list_17-1 {
    list-style-type: none;
  }
  ol.lst-kix_list_17-2 {
    list-style-type: none;
  }
  .lst-kix_list_7-2 > li:before {
    content: ' ';
  }
  ol.lst-kix_list_17-3 {
    list-style-type: none;
  }
  .lst-kix_list_13-7 > li:before {
    content: ' ';
  }
  .lst-kix_list_7-8 > li:before {
    content: ' ';
  }
  .lst-kix_list_15-5 > li:before {
    content: ' ';
  }
  .lst-kix_list_4-1 > li:before {
    content: 'o  ';
  }
  .lst-kix_list_15-7 > li:before {
    content: ' ';
  }
  .lst-kix_list_17-7 > li {
    counter-increment: lst-ctn-kix_list_17-7;
  }
  .lst-kix_list_4-3 > li:before {
    content: '\0025aa   ';
  }
  .lst-kix_list_4-5 > li:before {
    content: '\0025aa   ';
  }
  .lst-kix_list_15-1 > li:before {
    content: ' ';
  }
  .lst-kix_list_15-3 > li:before {
    content: ' ';
  }
  .lst-kix_list_16-2 > li {
    counter-increment: lst-ctn-kix_list_16-2;
  }
  .lst-kix_list_6-4 > li {
    counter-increment: lst-ctn-kix_list_6-4;
  }
  ol.lst-kix_list_16-7.start {
    counter-reset: lst-ctn-kix_list_16-7 0;
  }
  ol.lst-kix_list_18-0 {
    list-style-type: none;
  }
  .lst-kix_list_12-3 > li:before {
    content: ' ';
  }
  .lst-kix_list_12-1 > li:before {
    content: ' ';
  }
  .lst-kix_list_16-3 > li {
    counter-increment: lst-ctn-kix_list_16-3;
  }
  .lst-kix_list_13-3 > li:before {
    content: ' ';
  }
  .lst-kix_list_13-5 > li:before {
    content: ' ';
  }
  .lst-kix_list_12-5 > li:before {
    content: ' ';
  }
  ul.lst-kix_list_18-8 {
    list-style-type: none;
  }
  ul.lst-kix_list_18-7 {
    list-style-type: none;
  }
  ul.lst-kix_list_18-6 {
    list-style-type: none;
  }
  ul.lst-kix_list_18-5 {
    list-style-type: none;
  }
  .lst-kix_list_12-7 > li:before {
    content: ' ';
  }
  ul.lst-kix_list_18-4 {
    list-style-type: none;
  }
  ul.lst-kix_list_18-3 {
    list-style-type: none;
  }
  ul.lst-kix_list_18-2 {
    list-style-type: none;
  }
  ol.lst-kix_list_6-0.start {
    counter-reset: lst-ctn-kix_list_6-0 0;
  }
  ul.lst-kix_list_18-1 {
    list-style-type: none;
  }
  .lst-kix_list_13-1 > li:before {
    content: ' ';
  }
  .lst-kix_list_3-0 > li:before {
    content: '\0025cf   ';
  }
  ul.lst-kix_list_5-7 {
    list-style-type: none;
  }
  ul.lst-kix_list_5-8 {
    list-style-type: none;
  }
  ul.lst-kix_list_5-5 {
    list-style-type: none;
  }
  ul.lst-kix_list_5-6 {
    list-style-type: none;
  }
  .lst-kix_list_16-0 > li {
    counter-increment: lst-ctn-kix_list_16-0;
  }
  ul.lst-kix_list_5-0 {
    list-style-type: none;
  }
  .lst-kix_list_3-4 > li:before {
    content: '\0025aa   ';
  }
  ul.lst-kix_list_5-3 {
    list-style-type: none;
  }
  .lst-kix_list_3-3 > li:before {
    content: '\0025aa   ';
  }
  ul.lst-kix_list_5-4 {
    list-style-type: none;
  }
  ul.lst-kix_list_5-1 {
    list-style-type: none;
  }
  .lst-kix_list_8-0 > li:before {
    content: '\0025cf   ';
  }
  ul.lst-kix_list_5-2 {
    list-style-type: none;
  }
  .lst-kix_list_8-7 > li:before {
    content: ' ';
  }
  .lst-kix_list_3-8 > li:before {
    content: '\0025aa   ';
  }
  .lst-kix_list_8-3 > li:before {
    content: ' ';
  }
  ul.lst-kix_list_13-5 {
    list-style-type: none;
  }
  ul.lst-kix_list_13-4 {
    list-style-type: none;
  }
  ul.lst-kix_list_13-3 {
    list-style-type: none;
  }
  ul.lst-kix_list_13-2 {
    list-style-type: none;
  }
  ul.lst-kix_list_13-1 {
    list-style-type: none;
  }
  .lst-kix_list_3-7 > li:before {
    content: '\0025aa   ';
  }
  .lst-kix_list_8-4 > li:before {
    content: ' ';
  }
  ul.lst-kix_list_13-8 {
    list-style-type: none;
  }
  .lst-kix_list_17-1 > li {
    counter-increment: lst-ctn-kix_list_17-1;
  }
  .lst-kix_list_11-1 > li:before {
    content: ' ';
  }
  ul.lst-kix_list_13-7 {
    list-style-type: none;
  }
  ul.lst-kix_list_13-6 {
    list-style-type: none;
  }
  .lst-kix_list_11-0 > li:before {
    content: '\0025cf   ';
  }
  .lst-kix_list_8-8 > li:before {
    content: ' ';
  }
  .lst-kix_list_16-8 > li:before {
    content: '' counter(lst-ctn-kix_list_16-8, lower-roman) '. ';
  }
  .lst-kix_list_16-7 > li:before {
    content: '' counter(lst-ctn-kix_list_16-7, lower-latin) '. ';
  }
  .lst-kix_list_17-8 > li {
    counter-increment: lst-ctn-kix_list_17-8;
  }
  .lst-kix_list_4-8 > li:before {
    content: '\0025aa   ';
  }
  .lst-kix_list_4-7 > li:before {
    content: '\0025aa   ';
  }
  .lst-kix_list_17-0 > li:before {
    content: '' counter(lst-ctn-kix_list_17-0, decimal) '. ';
  }
  ul.lst-kix_list_4-8 {
    list-style-type: none;
  }
  .lst-kix_list_16-0 > li:before {
    content: '' counter(lst-ctn-kix_list_16-0, decimal) '. ';
  }
  ul.lst-kix_list_4-6 {
    list-style-type: none;
  }
  ul.lst-kix_list_4-7 {
    list-style-type: none;
  }
  ol.lst-kix_list_12-0 {
    list-style-type: none;
  }
  ul.lst-kix_list_4-0 {
    list-style-type: none;
  }
  .lst-kix_list_16-4 > li:before {
    content: '(' counter(lst-ctn-kix_list_16-4, lower-latin) ') ';
  }
  ul.lst-kix_list_4-1 {
    list-style-type: none;
  }
  .lst-kix_list_16-3 > li:before {
    content: '(' counter(lst-ctn-kix_list_16-3, decimal) ') ';
  }
  ul.lst-kix_list_4-4 {
    list-style-type: none;
  }
  ul.lst-kix_list_4-5 {
    list-style-type: none;
  }
  ul.lst-kix_list_4-2 {
    list-style-type: none;
  }
  ul.lst-kix_list_4-3 {
    list-style-type: none;
  }
  ul.lst-kix_list_12-6 {
    list-style-type: none;
  }
  ul.lst-kix_list_12-5 {
    list-style-type: none;
  }
  .lst-kix_list_17-7 > li:before {
    content: '' counter(lst-ctn-kix_list_17-7, lower-latin) '. ';
  }
  ul.lst-kix_list_12-4 {
    list-style-type: none;
  }
  ul.lst-kix_list_12-3 {
    list-style-type: none;
  }
  ul.lst-kix_list_12-2 {
    list-style-type: none;
  }
  .lst-kix_list_16-7 > li {
    counter-increment: lst-ctn-kix_list_16-7;
  }
  ul.lst-kix_list_12-1 {
    list-style-type: none;
  }
  .lst-kix_list_17-8 > li:before {
    content: '' counter(lst-ctn-kix_list_17-8, lower-roman) '. ';
  }
  .lst-kix_list_17-3 > li:before {
    content: '(' counter(lst-ctn-kix_list_17-3, decimal) ') ';
  }
  .lst-kix_list_17-4 > li:before {
    content: '(' counter(lst-ctn-kix_list_17-4, lower-latin) ') ';
  }
  ul.lst-kix_list_12-8 {
    list-style-type: none;
  }
  ul.lst-kix_list_12-7 {
    list-style-type: none;
  }
  .lst-kix_list_7-0 > li:before {
    content: '\0025cf   ';
  }
  .lst-kix_list_16-5 > li {
    counter-increment: lst-ctn-kix_list_16-5;
  }
  .lst-kix_list_2-4 > li:before {
    content: '\0025aa   ';
  }
  .lst-kix_list_2-8 > li:before {
    content: '\0025aa   ';
  }
  ol.lst-kix_list_13-0 {
    list-style-type: none;
  }
  .lst-kix_list_6-6 > li {
    counter-increment: lst-ctn-kix_list_6-6;
  }
  .lst-kix_list_7-3 > li:before {
    content: ' ';
  }
  ul.lst-kix_list_7-5 {
    list-style-type: none;
  }
  .lst-kix_list_10-0 > li:before {
    content: '\0025cf   ';
  }
  ul.lst-kix_list_7-6 {
    list-style-type: none;
  }
  ul.lst-kix_list_7-3 {
    list-style-type: none;
  }
  ul.lst-kix_list_7-4 {
    list-style-type: none;
  }
  .lst-kix_list_13-8 > li:before {
    content: ' ';
  }
  .lst-kix_list_18-3 > li:before {
    content: ' ';
  }
  .lst-kix_list_18-7 > li:before {
    content: ' ';
  }
  ul.lst-kix_list_7-7 {
    list-style-type: none;
  }
  ul.lst-kix_list_7-8 {
    list-style-type: none;
  }
  ul.lst-kix_list_7-1 {
    list-style-type: none;
  }
  ul.lst-kix_list_7-2 {
    list-style-type: none;
  }
  ul.lst-kix_list_7-0 {
    list-style-type: none;
  }
  .lst-kix_list_7-7 > li:before {
    content: ' ';
  }
  .lst-kix_list_15-4 > li:before {
    content: ' ';
  }
  .lst-kix_list_10-4 > li:before {
    content: ' ';
  }
  .lst-kix_list_10-8 > li:before {
    content: ' ';
  }
  .lst-kix_list_4-0 > li:before {
    content: '\0025cf   ';
  }
  ul.lst-kix_list_15-3 {
    list-style-type: none;
  }
  ul.lst-kix_list_15-2 {
    list-style-type: none;
  }
  .lst-kix_list_15-0 > li:before {
    content: '' counter(lst-ctn-kix_list_15-0, decimal) '. ';
  }
  ul.lst-kix_list_15-1 {
    list-style-type: none;
  }
  .lst-kix_list_15-8 > li:before {
    content: ' ';
  }
  .lst-kix_list_4-4 > li:before {
    content: '\0025aa   ';
  }
  ul.lst-kix_list_15-8 {
    list-style-type: none;
  }
  ul.lst-kix_list_15-7 {
    list-style-type: none;
  }
  ul.lst-kix_list_15-6 {
    list-style-type: none;
  }
  .lst-kix_list_9-3 > li:before {
    content: ' ';
  }
  ul.lst-kix_list_15-5 {
    list-style-type: none;
  }
  ul.lst-kix_list_15-4 {
    list-style-type: none;
  }
  .lst-kix_list_9-7 > li:before {
    content: ' ';
  }
  .lst-kix_list_11-4 > li:before {
    content: ' ';
  }
  ol.lst-kix_list_14-0 {
    list-style-type: none;
  }
  .lst-kix_list_12-4 > li:before {
    content: ' ';
  }
  .lst-kix_list_1-0 > li:before {
    content: '\0025cf   ';
  }
  .lst-kix_list_11-8 > li:before {
    content: ' ';
  }
  .lst-kix_list_12-0 > li:before {
    content: '' counter(lst-ctn-kix_list_12-0, decimal) '. ';
  }
  .lst-kix_list_17-3 > li {
    counter-increment: lst-ctn-kix_list_17-3;
  }
  .lst-kix_list_1-4 > li:before {
    content: '\0025aa   ';
  }
  .lst-kix_list_13-0 > li:before {
    content: '' counter(lst-ctn-kix_list_13-0, decimal) '. ';
  }
  ul.lst-kix_list_14-4 {
    list-style-type: none;
  }
  ul.lst-kix_list_14-3 {
    list-style-type: none;
  }
  ol.lst-kix_list_13-0.start {
    counter-reset: lst-ctn-kix_list_13-0 0;
  }
  ul.lst-kix_list_14-2 {
    list-style-type: none;
  }
  .lst-kix_list_13-4 > li:before {
    content: ' ';
  }
  ul.lst-kix_list_14-1 {
    list-style-type: none;
  }
  ul.lst-kix_list_14-8 {
    list-style-type: none;
  }
  ul.lst-kix_list_14-7 {
    list-style-type: none;
  }
  .lst-kix_list_2-0 > li:before {
    content: '\0025cf   ';
  }
  ul.lst-kix_list_14-6 {
    list-style-type: none;
  }
  ul.lst-kix_list_14-5 {
    list-style-type: none;
  }
  .lst-kix_list_1-8 > li:before {
    content: '\0025aa   ';
  }
  .lst-kix_list_12-8 > li:before {
    content: ' ';
  }
  ol.lst-kix_list_17-7.start {
    counter-reset: lst-ctn-kix_list_17-7 0;
  }
  ul.lst-kix_list_1-0 {
    list-style-type: none;
  }
  .lst-kix_list_15-0 > li {
    counter-increment: lst-ctn-kix_list_15-0;
  }
  ol.lst-kix_list_6-6.start {
    counter-reset: lst-ctn-kix_list_6-6 0;
  }
  ul.lst-kix_list_1-3 {
    list-style-type: none;
  }
  ul.lst-kix_list_1-4 {
    list-style-type: none;
  }
  ul.lst-kix_list_1-1 {
    list-style-type: none;
  }
  ul.lst-kix_list_1-2 {
    list-style-type: none;
  }
  ul.lst-kix_list_1-7 {
    list-style-type: none;
  }
  ul.lst-kix_list_1-8 {
    list-style-type: none;
  }
  ul.lst-kix_list_1-5 {
    list-style-type: none;
  }
  ul.lst-kix_list_1-6 {
    list-style-type: none;
  }
  ol.lst-kix_list_16-3.start {
    counter-reset: lst-ctn-kix_list_16-3 0;
  }
  .lst-kix_list_17-2 > li {
    counter-increment: lst-ctn-kix_list_17-2;
  }
  ol.lst-kix_list_17-2.start {
    counter-reset: lst-ctn-kix_list_17-2 0;
  }
  .lst-kix_list_18-0 > li:before {
    content: '' counter(lst-ctn-kix_list_18-0, decimal) '. ';
  }
  .lst-kix_list_18-1 > li:before {
    content: ' ';
  }
  .lst-kix_list_18-2 > li:before {
    content: ' ';
  }
  .lst-kix_list_16-1 > li {
    counter-increment: lst-ctn-kix_list_16-1;
  }
  .lst-kix_list_2-7 > li:before {
    content: '\0025aa   ';
  }
  .lst-kix_list_2-5 > li:before {
    content: '\0025aa   ';
  }
  .lst-kix_list_17-5 > li {
    counter-increment: lst-ctn-kix_list_17-5;
  }
  ol.lst-kix_list_17-0.start {
    counter-reset: lst-ctn-kix_list_17-0 0;
  }
  .lst-kix_list_18-6 > li:before {
    content: ' ';
  }
  ul.lst-kix_list_3-7 {
    list-style-type: none;
  }
  ul.lst-kix_list_3-8 {
    list-style-type: none;
  }
  .lst-kix_list_10-1 > li:before {
    content: ' ';
  }
  .lst-kix_list_18-4 > li:before {
    content: ' ';
  }
  .lst-kix_list_18-8 > li:before {
    content: ' ';
  }
  ul.lst-kix_list_3-1 {
    list-style-type: none;
  }
  .lst-kix_list_17-6 > li {
    counter-increment: lst-ctn-kix_list_17-6;
  }
  ul.lst-kix_list_3-2 {
    list-style-type: none;
  }
  ul.lst-kix_list_3-0 {
    list-style-type: none;
  }
  ul.lst-kix_list_3-5 {
    list-style-type: none;
  }
  ul.lst-kix_list_3-6 {
    list-style-type: none;
  }
  ul.lst-kix_list_3-3 {
    list-style-type: none;
  }
  ul.lst-kix_list_3-4 {
    list-style-type: none;
  }
  .lst-kix_list_10-7 > li:before {
    content: ' ';
  }
  .lst-kix_list_10-5 > li:before {
    content: ' ';
  }
  .lst-kix_list_10-3 > li:before {
    content: ' ';
  }
  ul.lst-kix_list_11-7 {
    list-style-type: none;
  }
  ul.lst-kix_list_11-6 {
    list-style-type: none;
  }
  ul.lst-kix_list_11-5 {
    list-style-type: none;
  }
  ul.lst-kix_list_11-4 {
    list-style-type: none;
  }
  ul.lst-kix_list_11-3 {
    list-style-type: none;
  }
  ul.lst-kix_list_11-2 {
    list-style-type: none;
  }
  ul.lst-kix_list_11-1 {
    list-style-type: none;
  }
  ul.lst-kix_list_11-0 {
    list-style-type: none;
  }
  .lst-kix_list_9-2 > li:before {
    content: ' ';
  }
  ol.lst-kix_list_14-0.start {
    counter-reset: lst-ctn-kix_list_14-0 0;
  }
  ul.lst-kix_list_11-8 {
    list-style-type: none;
  }
  .lst-kix_list_9-0 > li:before {
    content: '\0025cf   ';
  }
  .lst-kix_list_16-8 > li {
    counter-increment: lst-ctn-kix_list_16-8;
  }
  .lst-kix_list_9-6 > li:before {
    content: ' ';
  }
  .lst-kix_list_9-4 > li:before {
    content: ' ';
  }
  .lst-kix_list_11-3 > li:before {
    content: ' ';
  }
  .lst-kix_list_6-3 > li {
    counter-increment: lst-ctn-kix_list_6-3;
  }
  ul.lst-kix_list_2-8 {
    list-style-type: none;
  }
  .lst-kix_list_11-5 > li:before {
    content: ' ';
  }
  ol.lst-kix_list_6-1.start {
    counter-reset: lst-ctn-kix_list_6-1 0;
  }
  ul.lst-kix_list_2-2 {
    list-style-type: none;
  }
  ul.lst-kix_list_2-3 {
    list-style-type: none;
  }
  ul.lst-kix_list_2-0 {
    list-style-type: none;
  }
  ul.lst-kix_list_2-1 {
    list-style-type: none;
  }
  .lst-kix_list_9-8 > li:before {
    content: ' ';
  }
  ul.lst-kix_list_2-6 {
    list-style-type: none;
  }
  .lst-kix_list_1-1 > li:before {
    content: 'o  ';
  }
  ul.lst-kix_list_2-7 {
    list-style-type: none;
  }
  .lst-kix_list_11-7 > li:before {
    content: ' ';
  }
  ul.lst-kix_list_2-4 {
    list-style-type: none;
  }
  ul.lst-kix_list_2-5 {
    list-style-type: none;
  }
  ul.lst-kix_list_10-0 {
    list-style-type: none;
  }
  .lst-kix_list_1-3 > li:before {
    content: '\0025aa   ';
  }
  ol.lst-kix_list_16-8.start {
    counter-reset: lst-ctn-kix_list_16-8 0;
  }
  ul.lst-kix_list_10-8 {
    list-style-type: none;
  }
  ul.lst-kix_list_10-7 {
    list-style-type: none;
  }
  .lst-kix_list_1-7 > li:before {
    content: '\0025aa   ';
  }
  ul.lst-kix_list_10-6 {
    list-style-type: none;
  }
  ul.lst-kix_list_10-5 {
    list-style-type: none;
  }
  ul.lst-kix_list_10-4 {
    list-style-type: none;
  }
  ul.lst-kix_list_10-3 {
    list-style-type: none;
  }
  .lst-kix_list_1-5 > li:before {
    content: '\0025aa   ';
  }
  ul.lst-kix_list_10-2 {
    list-style-type: none;
  }
  ul.lst-kix_list_10-1 {
    list-style-type: none;
  }
  .lst-kix_list_2-1 > li:before {
    content: 'o  ';
  }
  .lst-kix_list_2-3 > li:before {
    content: '\0025aa   ';
  }
  .lst-kix_list_3-1 > li:before {
    content: 'o  ';
  }
  .lst-kix_list_3-2 > li:before {
    content: '\0025aa   ';
  }
  .lst-kix_list_14-0 > li {
    counter-increment: lst-ctn-kix_list_14-0;
  }
  .lst-kix_list_8-1 > li:before {
    content: ' ';
  }
  ol.lst-kix_list_17-4.start {
    counter-reset: lst-ctn-kix_list_17-4 0;
  }
  .lst-kix_list_8-2 > li:before {
    content: ' ';
  }
  .lst-kix_list_6-0 > li {
    counter-increment: lst-ctn-kix_list_6-0;
  }
  .lst-kix_list_3-5 > li:before {
    content: '\0025aa   ';
  }
  .lst-kix_list_18-0 > li {
    counter-increment: lst-ctn-kix_list_18-0;
  }
  .lst-kix_list_12-0 > li {
    counter-increment: lst-ctn-kix_list_12-0;
  }
  .lst-kix_list_8-5 > li:before {
    content: ' ';
  }
  .lst-kix_list_8-6 > li:before {
    content: ' ';
  }
  .lst-kix_list_3-6 > li:before {
    content: '\0025aa   ';
  }
  ol.lst-kix_list_16-6.start {
    counter-reset: lst-ctn-kix_list_16-6 0;
  }
  .lst-kix_list_11-2 > li:before {
    content: ' ';
  }
  ol.lst-kix_list_16-0.start {
    counter-reset: lst-ctn-kix_list_16-0 0;
  }
  .lst-kix_list_16-6 > li:before {
    content: '' counter(lst-ctn-kix_list_16-6, decimal) '. ';
  }
  ol.lst-kix_list_6-4.start {
    counter-reset: lst-ctn-kix_list_6-4 0;
  }
  .lst-kix_list_17-1 > li:before {
    content: '' counter(lst-ctn-kix_list_17-1, lower-latin) '. ';
  }
  .lst-kix_list_16-1 > li:before {
    content: '' counter(lst-ctn-kix_list_16-1, lower-latin) '. ';
  }
  .lst-kix_list_16-2 > li:before {
    content: '' counter(lst-ctn-kix_list_16-2, lower-roman) '. ';
  }
  .lst-kix_list_16-5 > li:before {
    content: '(' counter(lst-ctn-kix_list_16-5, lower-roman) ') ';
  }
  .lst-kix_list_16-4 > li {
    counter-increment: lst-ctn-kix_list_16-4;
  }
  ol.lst-kix_list_6-3.start {
    counter-reset: lst-ctn-kix_list_6-3 0;
  }
  .lst-kix_list_17-2 > li:before {
    content: '' counter(lst-ctn-kix_list_17-2, lower-roman) '. ';
  }
  ol.lst-kix_list_16-5.start {
    counter-reset: lst-ctn-kix_list_16-5 0;
  }
  ol.lst-kix_list_17-3.start {
    counter-reset: lst-ctn-kix_list_17-3 0;
  }
  .lst-kix_list_17-6 > li:before {
    content: '' counter(lst-ctn-kix_list_17-6, decimal) '. ';
  }
  .lst-kix_list_17-5 > li:before {
    content: '(' counter(lst-ctn-kix_list_17-5, lower-roman) ') ';
  }
  .lst-kix_list_6-2 > li {
    counter-increment: lst-ctn-kix_list_6-2;
  }
  .lst-kix_list_2-6 > li:before {
    content: '\0025aa   ';
  }
  ol.lst-kix_list_16-2.start {
    counter-reset: lst-ctn-kix_list_16-2 0;
  }
  .lst-kix_list_7-1 > li:before {
    content: ' ';
  }
  .lst-kix_list_7-5 > li:before {
    content: ' ';
  }
  .lst-kix_list_18-5 > li:before {
    content: ' ';
  }
  .lst-kix_list_13-6 > li:before {
    content: ' ';
  }
  .lst-kix_list_6-7 > li {
    counter-increment: lst-ctn-kix_list_6-7;
  }
  .lst-kix_list_16-6 > li {
    counter-increment: lst-ctn-kix_list_16-6;
  }
  .lst-kix_list_15-6 > li:before {
    content: ' ';
  }
  ol.lst-kix_list_17-8.start {
    counter-reset: lst-ctn-kix_list_17-8 0;
  }
  ol.lst-kix_list_6-8.start {
    counter-reset: lst-ctn-kix_list_6-8 0;
  }
  .lst-kix_list_10-2 > li:before {
    content: ' ';
  }
  ol.lst-kix_list_17-5.start {
    counter-reset: lst-ctn-kix_list_17-5 0;
  }
  ol.lst-kix_list_6-5.start {
    counter-reset: lst-ctn-kix_list_6-5 0;
  }
  .lst-kix_list_4-2 > li:before {
    content: '\0025aa   ';
  }
  .lst-kix_list_4-6 > li:before {
    content: '\0025aa   ';
  }
  .lst-kix_list_17-4 > li {
    counter-increment: lst-ctn-kix_list_17-4;
  }
  .lst-kix_list_15-2 > li:before {
    content: ' ';
  }
  .lst-kix_list_10-6 > li:before {
    content: ' ';
  }
  .lst-kix_list_9-1 > li:before {
    content: ' ';
  }
  ol.lst-kix_list_6-7.start {
    counter-reset: lst-ctn-kix_list_6-7 0;
  }
  .lst-kix_list_9-5 > li:before {
    content: ' ';
  }
  .lst-kix_list_12-2 > li:before {
    content: ' ';
  }
  .lst-kix_list_11-6 > li:before {
    content: ' ';
  }
  .lst-kix_list_1-2 > li:before {
    content: '\0025aa   ';
  }
  ol.lst-kix_list_16-1.start {
    counter-reset: lst-ctn-kix_list_16-1 0;
  }
  ol.lst-kix_list_17-6.start {
    counter-reset: lst-ctn-kix_list_17-6 0;
  }
  .lst-kix_list_1-6 > li:before {
    content: '\0025aa   ';
  }
  .lst-kix_list_12-6 > li:before {
    content: ' ';
  }
  .lst-kix_list_2-2 > li:before {
    content: '\0025aa   ';
  }
  .lst-kix_list_13-2 > li:before {
    content: ' ';
  }
  ol {
    margin: 0;
    padding: 0;
  }
  table td,
  table th {
    padding: 0;
  }
  .c1 {
    margin-left: 0pt;
    padding-top: 0pt;
    list-style-position: inside;
    text-indent: 43.2pt;
    padding-bottom: 12pt;

    orphans: 2;
    widows: 2;
    text-align: left;
  }
  .c0 {
    margin-left: 0pt;
    padding-top: 0pt;
    list-style-position: inside;
    text-indent: 72pt;
    padding-bottom: 12pt;

    orphans: 2;
    widows: 2;
    text-align: left;
  }
  .c12 {
    -webkit-text-decoration-skip: none;
    color: #0000ff;
    font-weight: 400;
    text-decoration: underline;
    vertical-align: baseline;
    text-decoration-skip-ink: none;
    font-style: normal;
  }
  .c14 {
    -webkit-text-decoration-skip: none;
    color: #000000;
    font-weight: 400;
    text-decoration: underline;
    vertical-align: baseline;
    text-decoration-skip-ink: none;
    font-style: normal;
  }
  .c5 {
    margin-left: 0pt;
    padding-top: 0pt;
    list-style-position: inside;
    text-indent: 14.4pt;
    padding-bottom: 12pt;

    orphans: 2;
    widows: 2;
    text-align: left;
  }
  .c9 {
    -webkit-text-decoration-skip: none;
    color: #0000ff;
    font-weight: 600;
    text-transform: uppercase;
    -webkit-font-smoothing: antialiased;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    text-decoration: underline;
    vertical-align: baseline;
    text-decoration-skip-ink: none;
    font-style: normal;
  }
  .c15 {
    -webkit-text-decoration-skip: none;
    color: #000000;
    font-weight: 600;
    text-transform: uppercase;
    -webkit-font-smoothing: antialiased;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    text-decoration: underline;
    vertical-align: baseline;
    text-decoration-skip-ink: none;
    font-style: normal;
  }
  .c3 {
    color: #000000;
    font-weight: 600;
    text-transform: uppercase;
    -webkit-font-smoothing: antialiased;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    text-decoration: none;
    vertical-align: baseline;
    font-style: normal;
  }
  .c6 {
    color: #000000;
    font-weight: 400;
    text-decoration: none;
    vertical-align: baseline;
    font-style: normal;
  }
  .c10 {
    padding-top: 0pt;
    text-indent: 36pt;
    padding-bottom: 12pt;

    orphans: 2;
    widows: 2;
    text-align: left;
  }
  .c7 {
    color: #000000;
    font-weight: 600;
    text-transform: uppercase;
    -webkit-font-smoothing: antialiased;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    text-decoration: none;
    vertical-align: super;
    font-style: normal;
  }
  .c8 {
    padding-top: 0pt;
    padding-bottom: 12pt;

    orphans: 2;
    widows: 2;
    text-align: left;
    height: 12pt;
  }
  .c16 {
    padding-top: 0pt;
    padding-bottom: 0pt;

    orphans: 2;
    widows: 2;
    text-align: left;
    height: 12pt;
  }
  .c19 {
    padding-top: 0pt;
    padding-bottom: 0pt;

    orphans: 2;
    widows: 2;
    text-align: center;
  }
  .c17 {
    padding-top: 0pt;
    padding-bottom: 24pt;

    orphans: 2;
    widows: 2;
    text-align: center;
  }
  .c18 {
    padding-top: 0pt;
    padding-bottom: 0pt;

    orphans: 2;
    widows: 2;
    text-align: left;
  }
  .c11 {
    padding-top: 0pt;
    padding-bottom: 12pt;

    orphans: 2;
    widows: 2;
    text-align: left;
  }
  .c13 {
    max-width: 468pt;
    padding: 72pt 72pt 72pt 72pt;
  }
  .c4 {
    color: inherit;
    text-decoration: inherit;
  }
  .c2 {
    padding: 0;
    margin: 0;
  }
  .title {
    padding-top: 0pt;
    color: #000000;
    font-weight: 600;
    text-transform: uppercase;
    -webkit-font-smoothing: antialiased;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    font-size: 12pt;
    padding-bottom: 12pt;

    page-break-after: avoid;
    orphans: 2;
    widows: 2;
    text-align: center;
  }
  .subtitle {
    padding-top: 0pt;
    color: #000000;
    font-weight: 600;
    text-transform: uppercase;
    -webkit-font-smoothing: antialiased;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    padding-bottom: 12pt;

    page-break-after: avoid;
    orphans: 2;
    widows: 2;
    text-align: center;
  }
  li {
    color: #000000;
    font-size: 12pt;
  }
  p {
    margin: 0;
    color: #000000;
    font-size: 12pt;
  }
  h1 {
    padding-top: 0pt;
    color: #000000;
    padding-bottom: 12pt;

    page-break-after: avoid;
    orphans: 2;
    widows: 2;
    text-align: left;
  }
  h2 {
    padding-top: 0pt;
    color: #000000;
    padding-bottom: 12pt;

    orphans: 2;
    widows: 2;
    text-align: left;
  }
  h3 {
    padding-top: 0pt;
    color: #000000;
    padding-bottom: 12pt;

    orphans: 2;
    widows: 2;
    text-align: left;
  }
  h4 {
    padding-top: 0pt;
    color: #000000;
    font-size: 12pt;
    padding-bottom: 12pt;

    orphans: 2;
    widows: 2;
    text-align: left;
  }
  h5 {
    padding-top: 0pt;
    color: #000000;
    padding-bottom: 12pt;

    orphans: 2;
    widows: 2;
    text-align: left;
  }
  h6 {
    padding-top: 0pt;
    color: #000000;
    padding-bottom: 12pt;

    orphans: 2;
    widows: 2;
    text-align: left;
  }
  li::marker {
    content: '';
    font-size: initial;
    color: initial;
  }
}
