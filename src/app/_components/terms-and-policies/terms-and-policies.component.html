<ion-toolbar class="terms-title">
  <ion-title> Review and Accept </ion-title>
  <ion-buttons slot="end">
    <ion-button (click)="cancel()" [strong]="true">
      <i class="material-icons">close</i>
    </ion-button>
  </ion-buttons>
</ion-toolbar>
<ion-content [scrollEvents]="true" (ionScrollEnd)="handleScrollEnd()">
  <ion-item
    *ngIf="termsAndPolicies?.terms?.content"
    class="terms-of-services-content"
    lines="none"
    [innerHtml]="termsAndPolicies?.terms.content"
  >
  </ion-item>
  <ion-item
    *ngIf="termsAndPolicies?.policies?.content"
    class="privacy-policy-content"
    lines="none"
    [innerHTML]="termsAndPolicies?.policies?.content"
  >
  </ion-item>
</ion-content>
<ion-footer class="terms-footer">
  <ion-item lines="none" (ionChange)="onTermsChanged()">
    <ion-checkbox
      slot="start"
      [disabled]="!isScrolledToBottom"
      [checked]="accepted"
    ></ion-checkbox>
    <ion-label class="ion-text-wrap">
      I have read and agree to the Terms of Service, Arbitration Agreement, and
      Privacy Policy
    </ion-label>
  </ion-item>
  <ion-item>
    <ion-button
      [disabled]="!isScrolledToBottom || !accepted"
      (click)="confirm()"
    >
      Continue
    </ion-button>
  </ion-item>
</ion-footer>
