button {
  width: 280px;
  height: 42px;
  background-color: #00d0ff;
  border-radius: 6px;
  color: #fff;
  font-size: 14px;
  text-transform: uppercase;
  font-weight: 500;
}
.id-verification-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  width: 100%;
  background-color: var(--BlueGray1);
}

.size-container {
  margin-top: 40px;
  max-width: 85%;
  min-width: 300px;
  width: 400px;
  background-color: white;
  border-radius: 5px;
  -webkit-box-shadow: 0px 0px 6px 2px rgba(240, 235, 240, 1);
  -moz-box-shadow: 0px 0px 6px 2px rgba(240, 235, 240, 1);
  box-shadow: 0px 0px 6px 2px rgba(240, 235, 240, 1);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
}

.icons {
  display: flex;
  align-items: center;
}

.failed-icon {
  color: #ff5d26;
  font-size: 64px;
}

.view-results {
  width: 100%;
  .view-results-button {
    display: flex;
    width: 287px;
    min-height: 48px;
    padding: var(--button-padding-vertical-default, 6px)
      var(--button-padding-horizontal-default, 8px);
    justify-content: center;
    align-items: center;
    border-radius: var(--button-radius-radius-square, 8px);
    background: var(--poplin-pink, #ff6289);

    /* Light/Basic Drop Shadow */
    box-shadow: 0px 4px 24px 0px rgba(0, 0, 0, 0.04);
    color: var(--button-color-primary-alt, #fff);
    text-align: center;

    /* Aux Text/M */
    font-family: 'PitchSans-Medium', 'Helvetica Neue', Helvetica, Arial,
      sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 20px;
    letter-spacing: 1.6px;
    text-transform: uppercase;
    margin: auto;
  }
}

.failed-attempts {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 95%;
  text-align: center;
  font-size: 14px;
  color: var(--BlueGray5);
  h2 {
    display: flex;
    flex-direction: column;
    align-self: stretch;
    color: var(--content-primary, #000);
    text-align: center;

    /* Heading/Small */
    font-family: 'PitchSans-Medium', 'Helvetica Neue' sans-serif;
    font-size: 24px;
    font-style: normal;
    font-weight: 500;
    line-height: 120%;
    letter-spacing: -0.72px;
  }
  p {
    display: flex;
    flex-direction: column;
    align-self: stretch;
    color: var(--content-primary, #000);

    /* Body Text/Small */
    font-family: 'Fakt-Normal', 'Helvetica Neue', Helvetica, Arial sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 140%;
    text-align: left;
  }
  .contact-support-button {
    background-color: #fff;
    display: flex;
    padding: 0px var(--button-padding-inner-small, 12px);
    flex-direction: column;
    justify-content: center;
    align-items: center;
    align-self: stretch;
    color: var(--button-color-black-main, #000);
    text-align: center;
    margin-top: 10px;
    /* Aux Text/S */
    font-family: 'PitchSans-Medium', sans-serif;
    font-size: 13px;
    font-style: normal;
    font-weight: 700;
    line-height: 16px;
    letter-spacing: 1.3px;
    text-transform: uppercase;
    margin: auto;
  }
}
