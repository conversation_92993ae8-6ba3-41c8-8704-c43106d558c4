<div class="id-verification-container">
  <div class="size-container">
    <div class="icons">
      <div class="failed-container">
        <span class="material-icons-outlined failed-icon">error</span>
      </div>
    </div>

    <div class="failed-attempts">
      <h2 *ngIf="IdVerification">Something went wrong</h2>
      <h2 *ngIf="backgroundCheck">Your Background Check was Flagged</h2>
      <p *ngIf="IdVerification">
        Your ID verification has failed and you are out of attempts. Please
        contact our Concierge team if you think this is a mistake.
      </p>
      <p *ngIf="backgroundCheck">
        Something came up in your background check that was flagged for review
        by our team.<br /><br />
        You can view the results of your background check in Checkrs candidate
        portal. If you would like to review your results with our team, please
        contact our Concierge team.
      </p>
      <div class="view-results">
        <button
          *ngIf="backgroundCheck"
          id="view-results-button"
          class="view-results-button"
          [disabled]="clicked"
          expand="block"
          (click)="navToCheckrPortal()"
        >
          View Checkr results
        </button>
        <button
          [disabled]="clicked"
          (click)="contactSupport()"
          [ngClass]="
            IdVerification ? 'view-results-button' : 'contact-support-button'
          "
        >
          Contact Concierge
        </button>
      </div>
    </div>
  </div>
</div>
