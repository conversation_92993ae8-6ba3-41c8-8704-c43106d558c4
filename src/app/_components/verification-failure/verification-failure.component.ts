import { Component, Input, OnInit } from '@angular/core';
import { AngularFirestore } from '@angular/fire/firestore';
import { Router } from '@angular/router';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import {
  CheckrInformation,
  SudsterData,
} from 'src/app/_interfaces/sudster-data.interface';
import { AuthidService } from 'src/app/_services/authid.service';

enum CheckrResponse {
  complete = 'complete',
  event = 'report.engaged',
  engage = 'engaged',
}
@UntilDestroy()
@Component({
  selector: 'app-verification-failure',
  templateUrl: './verification-failure.component.html',
  styleUrls: ['./verification-failure.component.scss'],
})
export class VerificationFailureComponent implements OnInit {
  @Input() IdVerification: boolean;
  @Input() backgroundCheck: boolean;

  constructor(
    private firestore: AngularFirestore,
    private AuthId: AuthidService,
    private router: Router
  ) {}
  clicked = false;
  UserID = this.AuthId.getID();
  CheckrInformation: CheckrInformation;
  ngOnInit() {
    this.firestore
      .doc<SudsterData>(`Sudsters/${this.UserID}`)
      .valueChanges()
      .pipe(untilDestroyed(this))
      .subscribe((doc) => {
        if (this.backgroundCheck && doc.CheckrVerification === 'verified') {
          return this.router.navigate(['verification-success']);
        }
      });
  }

  navToCheckrPortal() {
    window.open('https://candidate.checkr.com/login');
  }
  contactSupport() {
    this.clicked = true;
    window.open('https://poplin-laundry-pro.zendesk.com/hc/en-us');
    this.clicked = false;
  }
}
