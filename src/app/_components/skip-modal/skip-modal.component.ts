import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { IonicModule, ModalController } from '@ionic/angular';
import { ButtonComponent } from 'src/app/shared/components/button/button.component';

@Component({
  selector: 'app-skip-modal',
  templateUrl: './skip-modal.component.html',
  styleUrls: ['./skip-modal.component.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule, ButtonComponent],
})
export class SkipModalComponent {
  constructor(private modalCtrl: ModalController) {}

  skip() {
    return this.modalCtrl.dismiss(null, 'skip');
  }

  continue() {
    return this.modalCtrl.dismiss(true, 'continue');
  }
}
