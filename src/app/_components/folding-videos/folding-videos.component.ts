import { Component, OnInit } from '@angular/core';
import { VIDEOREFS } from 'src/app/_interfaces/videoRefs';
import { PlayVideoService } from 'src/app/_services/play-video.service';

@Component({
  selector: 'app-folding-videos',
  templateUrl: './folding-videos.component.html',
  styleUrls: ['./folding-videos.component.scss'],
})
export class FoldingVideosComponent implements OnInit {
  videoRefs = VIDEOREFS;

  constructor(private PlayVideo: PlayVideoService) {}

  ngOnInit() {}

  click(videolink) {
    this.PlayVideo.open(videolink);
  }
}
