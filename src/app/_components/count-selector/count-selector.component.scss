span {
  color: var(--number-stepper-color-label-default, #000);

  /* Body Text/Medium */
  font-family: 'Fakt-Medium', sans-serif;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 140%; /* 22.4px */
  a {
    font-size: 14px;
    color: var(--content-link, #0065ad);
    text-decoration-line: underline;
  }
  vertical-align: center;
  text-transform: capitalize;
}
.bag-size-section {
  text-align: left;
}
.counter-section {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 4px;
  gap: 12px;
  border-radius: var(--border-radius-round, 1000px);
  border: 1px solid var(--number-stepper-color-border-default, #e6e6e6);
  background: var(--number-stepper-color-background-default, #fff);
  height: 38px;
  min-width: 114px;
  ion-icon {
    display: flex;
    padding: 6px;
    position: relative;
    align-items: flex-start;
    border-radius: var(--border-radius-round, 1000px);
    border: 1px solid var(--number-stepper-color-border-default, #e6e6e6);
    background: var(--number-stepper-color-background-default, #000);
    color: #fff;
  }
  .remove-bag-icon {
    opacity: 0.65;
    right: 7px;
  }
  .add-bag-icon {
    left: 7px;
  }
  strong {
    font-size: 20px;
    font-family: 'Fakt-Medium' sans-serif;
    color: var(--number-stepper-color-background-default, #000);
  }
}
