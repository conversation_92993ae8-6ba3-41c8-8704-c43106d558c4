import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { OrderData } from 'src/app/_interfaces/order-data.interface';
import { AdjustBagSizeComponent } from '../adjust-bag-size/adjust-bag-size.component';

@Component({
  selector: 'app-count-selector',
  templateUrl: './count-selector.component.html',
  styleUrls: ['./count-selector.component.scss'],
})
export class CountSelectorComponent implements OnInit {
  BagNumber = 0;
  @Input() userId: string;
  @Input() orderData: OrderData;
  @Input() orderNumber: string;
  @Input() isOverweightOrder: boolean;
  @Input() isBagDiscrepancy: boolean;
  @Output() CountChanged: EventEmitter<number> = new EventEmitter();

  constructor(private modalController: ModalController) {}

  ngOnInit() {}

  RTDOM_PlurelBagCount() {
    if (this.BagNumber === 1) {
      return '';
    } else {
      return 's';
    }
  }
  async presentModal() {
    const modal = await this.modalController.create({
      component: AdjustBagSizeComponent,
      cssClass: 'SmallPopupModal',
      componentProps: {
        orderData: this.orderData,
        orderNumber: this.orderNumber,
        isOverweightOrder: this.isOverweightOrder,
        isBagDiscrepancy: this.isBagDiscrepancy,
        userId: this.userId,
      },
    });
    await modal.present();
    return modal;
  }
}
