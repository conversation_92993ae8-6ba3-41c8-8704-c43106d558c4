<ion-row>
  <ion-col *ngIf="orderData.OrderStatusNumber < 2" size="8">
    <section id="bag-size-section" class="bag-size-section">
      <span class="bag-size-span">{{
        orderData.BagSize === 'oversized' ? 'Large' : orderData.BagSize
      }}</span>
      <br />
      <span><a (click)="presentModal()">Adjust Bag Size</a></span>
    </section>
  </ion-col>
  <ion-col [offset]="orderData.OrderStatusNumber > 2 ? 4 : 0" size="4">
    <section id="counter-section" class="counter-section">
      <ion-icon
        id="remove-bag-icon"
        class="remove-bag-icon"
        name="remove"
        (click)="
          BagNumber > 0 ? (BagNumber = BagNumber - 1) : null;
          CountChanged.emit(BagNumber)
        "
      ></ion-icon>

      <strong>{{ BagNumber }}</strong>
      <ion-icon
        id="add-bag-icon"
        class="add-bag-icon"
        name="add"
        (click)="BagNumber = BagNumber + 1; CountChanged.emit(BagNumber)"
      >
      </ion-icon>
    </section>
  </ion-col>
</ion-row>
