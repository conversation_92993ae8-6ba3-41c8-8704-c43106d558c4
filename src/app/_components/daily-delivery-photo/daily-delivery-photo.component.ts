import {
  Component,
  ElementRef,
  Input,
  OnDestroy,
  OnInit,
  Renderer2,
} from '@angular/core';
import { AngularFirestore } from '@angular/fire/firestore';
import { LoadingController, ModalController } from '@ionic/angular';
import * as confetti from 'canvas-confetti';

@Component({
  selector: 'app-daily-delivery-photo',
  templateUrl: './daily-delivery-photo.component.html',
  styleUrls: ['./daily-delivery-photo.component.scss'],
})
export class DailyDeliveryPhotoComponent implements OnInit, OnDestroy {
  constructor(
    public firestore: AngularFirestore,
    private loadingController: LoadingController,
    private modalController: ModalController,
    private renderer2: Renderer2,
    private elementRef: ElementRef
  ) {}
  @Input() Photo;
  Name = '';
  Location = '';
  Img = '';

  async ngOnInit() {
    const loading = await this.presentLoading();
    this.Name = this.Photo.SudsterName;
    this.Location = this.Photo.SudsterLocation;
    this.Img = this.Photo.ImgLink;

    setTimeout(() => {
      const canvas = this.renderer2.createElement('canvas');
      this.renderer2.appendChild(this.elementRef.nativeElement, canvas);
      const myConfetti = confetti.create(canvas, {
        resize: true,
      });
      myConfetti({
        particleCount: 200,
        spread: 70,
        origin: { y: 0.7 },
      });
    }, 500);
    loading.dismiss();
  }

  ngOnDestroy() {}

  async presentLoading() {
    const loading = await this.loadingController.create({});
    await loading.present();
    return loading;
  }

  Close() {
    localStorage.setItem(
      'DeliveryPhotoOfTheDayLastShown',
      new Date().getTime().toString()
    );
    this.modalController.dismiss();
  }
}
