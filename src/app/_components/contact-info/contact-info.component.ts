import { Component, Input } from '@angular/core';
import {
  AbstractControl,
  FormsModule,
  ReactiveFormsModule,
  UntypedFormGroup,
} from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { contactInfoRelationshipOptions } from 'src/app/_pages/rank/badges/constants';
import { InputComponent } from '../form/input/input.component';
import { SelectComponent } from '../form/select/select.component';

export interface RelationshipOption {
  value: string;
  label: string;
}

@Component({
  selector: 'app-contact-info',
  standalone: true,
  templateUrl: './contact-info.component.html',
  imports: [
    InputComponent,
    FormsModule,
    IonicModule,
    ReactiveFormsModule,
    SelectComponent,
  ],
})
export class ContactInfoComponent {
  @Input() public contactInfoForm: UntypedFormGroup;

  public relationshipOptions: RelationshipOption[] =
    contactInfoRelationshipOptions;

  get firstName(): AbstractControl {
    return this.contactInfoForm.get('iceFirstName');
  }

  get lastName(): AbstractControl {
    return this.contactInfoForm.get('iceLastName');
  }

  get email(): AbstractControl {
    return this.contactInfoForm.get('iceEmail');
  }

  get phoneNumber(): AbstractControl {
    return this.contactInfoForm.get('icePhone');
  }

  get relationship(): AbstractControl {
    return this.contactInfoForm.get('iceRelationship');
  }
}
