import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import moment from 'moment-timezone';

interface DayBox {
  label: string;
  value: Date;
  isSelected: boolean;
}

@Component({
  selector: 'app-short-day-selector',
  templateUrl: './short-day-selector.component.html',
  styleUrls: ['./short-day-selector.component.scss'],
})
export class ShortDaySelectorComponent implements OnInit {
  @Output() dateSelected = new EventEmitter<Date>();

  days: DayBox[] = [];
  selectedDayIndex: number = 0;

  ngOnInit(): void {
    const today = moment().startOf('day');

    this.days = Array.from({ length: 7 }, (_, index) => {
      const day = today.clone().add(index, 'days');

      return {
        label: index === 0 ? 'TODAY' : day.format('ddd').toUpperCase(),
        value: day.toDate(),
        isSelected: false,
      };
    });

    this.days[0].isSelected = true;
  }

  selectDay(index: number): void {
    this.days.forEach((day, i) => (day.isSelected = i === index));
    this.selectedDayIndex = index;

    this.dateSelected.emit(this.days[index].value);
  }
}
