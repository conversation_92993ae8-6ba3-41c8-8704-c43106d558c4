.horizontal-scroll-container {
  display: flex;
  overflow-x: auto;
  gap: 8px;
  padding: 8px;
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.day-box {
  width: 72px;
  min-width: 72px;
  height: 72px;
  border: 1px solid var(--gray-150);
  border-radius: 8px;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: border-color 0.3s, background-color 0.3s;
}

.checkbox {
  position: absolute;
  top: -6px;
  left: -6px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: var(--ion-color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: 16px;
  z-index: 10;
  ion-icon {
    font-size: 16px;
  }
}

.day-box:focus {
  outline: none;
  border-color: var(--gray-650);
}

.day-box.selected {
  border-color: var(--ion-color-primary);
  background-color: var(--ion-color-primary);
  color: var(--ion-color-primary-contrast);

  .label {
    color: var(--ion-color-primary-contrast);
  }
}

.day-box.today {
  background-color: var(--color-background-disabled);
  border: 1px solid var(--color-background-disabled);
  cursor: not-allowed;
}

.label {
  font-size: 12px;
  font-weight: bold;
  color: var(--BlueGray5);
}

.value {
  font-size: 24px;
  font-weight: bold;
}
