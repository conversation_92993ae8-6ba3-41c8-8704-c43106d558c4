<div class="horizontal-scroll-container">
  <div class="day-box today" tabindex="-1" role="button" aria-pressed="true">
    <div class="label">TODAY</div>
    <div class="value">
      {{ days[0].value | date : 'dd' }}
    </div>
  </div>

  <div
    *ngFor="let day of days.slice(1); let i = index"
    class="day-box"
    [class.selected]="day.isSelected"
    tabindex="0"
    role="button"
    aria-pressed="{{ day.isSelected }}"
    (click)="selectDay(i + 1)"
    (keydown.enter)="selectDay(i + 1)"
    (keydown.space)="selectDay(i + 1)"
  >
    <div class="checkbox" *ngIf="day.isSelected">
      <ion-icon name="checkmark-circle"></ion-icon>
    </div>
    <div class="label">{{ day.label }}</div>
    <div class="value">{{ day.value | date : 'dd' }}</div>
  </div>
</div>
