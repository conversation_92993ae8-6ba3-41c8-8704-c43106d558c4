import { Component, Input } from '@angular/core';
import { AbstractControl, UntypedFormGroup } from '@angular/forms';
import { InputComponent } from '../form/input/input.component';

@Component({
  selector: 'app-address-info',
  standalone: true,
  templateUrl: './address-info.component.html',
  styleUrls: ['./address-info.component.scss'],
  imports: [InputComponent],
})
export class AddressInfoComponent {
  @Input() public addressForm: UntypedFormGroup;

  get street(): AbstractControl {
    return this.addressForm.get('StreetAddress');
  }
  get addressLine(): AbstractControl {
    return this.addressForm.get('AddressLine2');
  }
  get city(): AbstractControl {
    return this.addressForm.get('City');
  }
  get state(): AbstractControl {
    return this.addressForm.get('State');
  }
  get zipCode(): AbstractControl {
    return this.addressForm.get('Zipcode');
  }
}
