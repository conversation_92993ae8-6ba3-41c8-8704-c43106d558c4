import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { NgxMaskModule } from 'ngx-mask';

import { IonicModule } from '@ionic/angular';

import { VerificationProcessingComponent } from './verification-processing.component';

const routes: Routes = [
  {
    path: '',
    component: VerificationProcessingComponent,
  },
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule.forChild(routes),
    ReactiveFormsModule,
    NgxMaskModule.forRoot(),
  ],
  exports: [RouterModule],
})
export class VerificationProcessingPageRoutingModule {}
