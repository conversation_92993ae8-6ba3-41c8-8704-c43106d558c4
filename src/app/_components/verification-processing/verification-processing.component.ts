import { Component, Input, OnInit } from '@angular/core';
import { AngularFirestore } from '@angular/fire/firestore';
import { Router } from '@angular/router';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import {
  CheckrInformation,
  SudsterData,
} from 'src/app/_interfaces/sudster-data.interface';
import { AuthidService } from 'src/app/_services/authid.service';

@UntilDestroy()
@Component({
  selector: 'app-verification-processing',
  templateUrl: './verification-processing.component.html',
  styleUrls: ['./verification-processing.component.scss'],
})
export class VerificationProcessingComponent implements OnInit {
  @Input() IdVerification: boolean;
  @Input() backgroundCheck: boolean;
  UserID = this.AuthID.getID();
  CheckrInformation: CheckrInformation;
  constructor(
    private firestore: AngularFirestore,
    private AuthID: AuthidService,
    private router: Router
  ) {}

  ngOnInit() {
    this.firestore
      .doc<SudsterData>(`Sudsters/${this.UserID}`)
      .valueChanges()
      .pipe(untilDestroyed(this))
      .subscribe((doc) => {
        if (this.backgroundCheck && doc.CheckrVerification) {
          if (doc.CheckrVerification === 'verified') {
            return this.router.navigate(['verification-success'], {
              replaceUrl: true,
            });
          }
        }
      });
  }
  navToCheckrPortal() {
    window.open('https://candidate.checkr.com/login');
  }
}
