.id-verification-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  width: 100%;
  background-color: var(--BlueGray1);
}

.size-container {
  margin-top: 40px;
  max-width: 85%;
  min-width: 300px;
  width: 400px;
  background-color: white;
  border-radius: 5px;
  -webkit-box-shadow: 0px 0px 6px 2px rgba(240, 235, 240, 1);
  -moz-box-shadow: 0px 0px 6px 2px rgba(240, 235, 240, 1);
  box-shadow: 0px 0px 6px 2px rgba(240, 235, 240, 1);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  height: 367px;
}

.icons {
  display: flex;
  align-items: center;
}

.pending-container {
  background-color: #54b8ff;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.pending-icon {
  color: white;
  font-size: 40px;
  transform: rotate(45deg);
}

.pending {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 95%;
  font-size: 14px;
  color: var(--content-primary, #000);
  h1 {
    color: var(--content-primary, #000);
    text-align: center;

    /* Heading/Small */
    font-family: 'PitchSans-Medium', 'Helvetica Neue', Helvetica,
      Arial sans-serif;
    font-size: 24px;
    font-style: normal;
    font-weight: 500;
    line-height: 120%;
    letter-spacing: -0.72px;
  }
  p {
    text-align: left;
  }
  /* Body Text/Small */
  font-family: 'Fakt-Normal' sans-serif;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 140%;
  ion-button {
    &::part(native) {
      margin-top: 10px;
      display: flex;
      width: 296px;
      min-height: 48px;
      padding: var(--button-padding-vertical-default, 6px)
        var(--button-padding-horizontal-default, 8px);
      justify-content: center;
      align-items: center;
      border-radius: var(--button-radius-radius-square, 8px);
      border: 2px solid var(--button-color-primary-main, #285652);

      /* Light/Basic Drop Shadow */
      box-shadow: 0px 4px 24px 0px rgba(0, 0, 0, 0.04);
      color: var(--button-color-primary-main, #285652);
      text-align: center;

      /* Aux Text/M */
      font-family: 'PitchSans-Medium', sans-serif;
      font-size: 16px;
      font-style: normal;
      font-weight: 700;
      line-height: 20px;
      letter-spacing: 1.6px;
      text-transform: uppercase;
    }
  }
}
