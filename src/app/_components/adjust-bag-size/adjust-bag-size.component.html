<ion-row>
  <ion-col size="2" offset="10">
    <ion-icon name="close" (click)="dismiss()" class="close-icon"></ion-icon>
  </ion-col>

  <h3>Adjust bag size</h3>
  <p>
    If your order doesn't match the average bag size, please adjust for
    accuracy.
  </p>

  <ion-col size="11">
    <ion-segment
      [value]="orderData.BagSize"
      mode="ios"
      (ionChange)="setBagSize($event)"
    >
      <ion-segment-button
        value="small"
        id="bag-bagsize-small-ion-segment-button"
      >
        <ion-label id="bag-bagsize-small-ion-label">Small</ion-label>
      </ion-segment-button>
      <ion-segment-button
        value="regular"
        id="bag-bagsize-medium-ion-segment-button"
      >
        <ion-label id="bag-bagsize-medium-ion-label">Regular</ion-label>
      </ion-segment-button>
      <ion-segment-button
        value="oversized"
        id="bag-bagsize-large-ion-segment-button"
      >
        <ion-label id="bag-bagsize-large-ion-label">Large</ion-label>
      </ion-segment-button>
    </ion-segment>
  </ion-col>
  <ion-col size="1">
    <ion-icon
      (click)="OnInfoClick()"
      name="information-circle-outline"
      id="bag-info-ion-icon"
      class="bag-info-ion-icon"
    ></ion-icon>
  </ion-col>

  <ion-button id="confirm-update-button" (click)="confirmUpdate()">
    Confirm Update
  </ion-button>
</ion-row>
