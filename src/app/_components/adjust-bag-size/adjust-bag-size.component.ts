import { Component, Input, OnInit } from '@angular/core';
import {
  <PERSON><PERSON><PERSON><PERSON>roller,
  LoadingController,
  ModalController,
} from '@ionic/angular';
import { OrderData } from 'src/app/_interfaces/order-data.interface';
import { AnalyticsLogService } from 'src/app/_services/analytics/analytics-log.service';
import { LegacyApiService } from 'src/app/_services/legacy-api.service';

@Component({
  selector: 'app-adjust-bag-size',
  templateUrl: './adjust-bag-size.component.html',
  styleUrls: ['./adjust-bag-size.component.scss'],
})
export class AdjustBagSizeComponent implements OnInit {
  @Input() userId: string;
  @Input() orderData: OrderData;
  @Input() orderNumber: string;
  @Input() isOverweightOrder: boolean;
  @Input() isBagDiscrepancy: boolean;
  originalBagSize: string;
  constructor(
    private modalController: <PERSON><PERSON><PERSON><PERSON>roll<PERSON>,
    private alertController: <PERSON><PERSON><PERSON>ontroller,
    private apiService: LegacyApiService,
    private analyticsService: AnalyticsLogService,
    private loadingController: LoadingController
  ) {}

  ngOnInit() {
    this.originalBagSize = this.orderData.BagSize;
  }

  dismiss(): Promise<boolean> {
    return this.modalController.dismiss();
  }
  setBagSize(e: any): string {
    return (this.orderData.BagSize = e.target.value);
  }
  async confirmUpdate() {
    const loading = this.presentLoading();
    await this.apiService
      .post('UpdateOrder/v1/minor-update', {
        type: 'TriggerOrderExtensionModal',
        orderNumber: this.orderNumber,
        orderPartial: {
          originalBagSize: this.originalBagSize,
          BagSize: this.orderData.BagSize,
        },
      })
      .toPromise()
      .then((res) => {
        if (res.status === '200') {
          this.analyticsService.logBagSizeDiscrepancy(
            this.userId,
            this.orderNumber,
            this.originalBagSize,
            this.orderData.BagSize
          );
          loading.then(function (ld) {
            ld.dismiss();
          });
          return this.modalController.dismiss();
        } else
          this.presentAlert(
            'Error',
            'Could not update Bag Size. Please try again or contact concierge.'
          );
      })
      .catch((err) => err);
  }
  async OnInfoClick() {
    const alert = await this.alertController.create({
      cssClass: 'bag-alert',
      mode: 'ios',
      header: 'Size Examples',
      message:
        '<div>' +
        '<p><span>Small:</span> Full grocery bag</p>' +
        '<p><span>Regular:</span> 13-gallon kitchen bag</p>' +
        '<p><span>Large:</span> 30-gallon lawn bag</p>' +
        '</div>',
      buttons: ['OK'],
    });

    await alert.present();
  }
  async presentAlert(
    Title: string,
    Message: string
  ): Promise<HTMLIonAlertElement> {
    const alert = await this.alertController.create({
      header: Title,
      message: Message,
      buttons: ['Try Again'],
    });
    await alert.present();
    return alert;
  }

  async presentLoading() {
    const loading = await this.loadingController.create({});
    await loading.present();
    return loading;
  }
}
