ion-row {
  max-width: 337px;
  margin-left: 25px;
  .close-icon {
    float: right;
    width: 32px;
    height: 32px;
    flex-shrink: 0;
    background-color: #000;
    color: #fff;
    border-radius: 100%;
    font-weight: 400;
    font-family: 'PitchSans-Medium', 'Helvetica Neue', sans-serif;
  }
  h3 {
    width: 285px;
    height: 29px;
    flex-shrink: 0;
    color: var(--themes-light-content, #000);

    /* Heading/Small */

    font-size: 24px;
    font-style: normal;
    font-weight: 500;
    line-height: 120%; /* 28.8px */
    letter-spacing: -0.72px;
    margin-left: 13px;
  }
  p {
    margin-left: 13px;
  }
  ion-button {
    &::part(native) {
      display: flex;
      width: 285px;
      min-height: 48px;
      padding: var(--button-padding-vertical-default, 6px)
        var(--button-padding-horizontal-default, 8px);
      justify-content: center;
      align-items: center;
      border-radius: var(--button-radius-radius-square, 8px);
      background: var(--button-color-primary-main, #285652);
      margin-top: 32px;
      font-family: 'PitchSans-Medium', 'Helvetica Neue', sans-serif;

      /* Light/Basic Drop Shadow */
      box-shadow: 0px 4px 24px 0px rgba(0, 0, 0, 0.04);
      font-size: 16px;
      font-style: normal;
      font-weight: 700;
      line-height: 20px; /* 125% */
      letter-spacing: 1.6px;
      text-transform: uppercase;
    }
  }
  ion-segment {
    margin-left: 8px;
  }
  .bag-info-ion-icon {
    height: 16px;
    width: 16px;
    margin-left: 3px;
  }
}
