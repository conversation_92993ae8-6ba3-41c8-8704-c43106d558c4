<!-- eslint-disable @angular-eslint/template/label-has-associated-control -->
<!-- eslint-disable @angular-eslint/template/interactive-supports-focus -->
<div
  class="photo-upload"
  (click)="uploadPhoto()"
  (keyup.enter)="uploadPhoto()"
  tabindex="0"
>
  <ion-icon name="camera-outline"></ion-icon>
  <label>TAP TO OPEN</label>
  <img
    *ngIf="photoSafeUrl !== ''"
    [src]="photoSafeUrl"
    alt="Photo Placeholder"
  />
</div>
