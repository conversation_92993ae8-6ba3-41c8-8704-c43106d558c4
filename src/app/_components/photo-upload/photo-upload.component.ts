import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  Output,
} from '@angular/core';
import { DomSanitizer, SafeUrl } from '@angular/platform-browser';
import { Geolocation } from '@capacitor/geolocation';
import { Network } from '@capacitor/network';
import { LoadingController } from '@ionic/angular';
import { PhotoService } from 'src/app/_services/photo.service';

export interface PhotoLocation {
  accuracy: number;
  latitude: number;
  longitude: number;
}

export interface PictureTakenEvent {
  photoLocalUrl: string;
  photoLocalPath: string;
  photoLocation: PhotoLocation;
}

export interface UploadStartedEvent {
  photoFileId: string;
}

@Component({
  selector: 'app-photo-upload',
  templateUrl: './photo-upload.component.html',
  styleUrls: ['./photo-upload.component.scss'],
})
export class PhotoUploadComponent implements OnChanges {
  @Output() pictureStored = new EventEmitter<PictureTakenEvent>();
  @Output() uploadStarted = new EventEmitter<UploadStartedEvent>();
  @Input() savePhotoPath: string;
  @Input() photoSafeUrl: SafeUrl = '';

  constructor(
    private photoService: PhotoService,
    private loadingController: LoadingController,
    private domSanitizer: DomSanitizer
  ) {}

  ngOnChanges({ photoSafeUrl }: { photoSafeUrl: any }) {
    if (photoSafeUrl.currentValue) {
      this.photoSafeUrl = this.domSanitizer.bypassSecurityTrustUrl(
        photoSafeUrl.currentValue
      );
    }
  }

  async uploadPhoto() {
    const loading = this.presentLoading('Preparing... Please wait.');

    await Network.getStatus()
      .then(async (status) => {
        const photoLocation = await this.getGPSLocation();

        const { localPhotoUrl, photoFilePath } =
          await this.photoService.takePhoto(this.savePhotoPath);

        const savedPhotoCompletePath = photoFilePath;

        loading.then((ld) => (ld.message = 'Uploading... Please wait.'));

        this.pictureStored.emit({
          photoLocalUrl: localPhotoUrl,
          photoLocalPath: savedPhotoCompletePath,
          photoLocation: photoLocation,
        });

        loading.then(function (ld) {
          ld.dismiss();
        });

        // If network is connected, upload the photo in the background
        if (status.connected) {
          const { fileId } = await this.photoService.uploadPhoto(
            savedPhotoCompletePath
          );

          if (fileId) {
            this.uploadStarted.emit({
              photoFileId: fileId,
            });
          }
        }
      })
      .catch((err) => {
        loading.then((ld) => ld.dismiss());
        return err;
      });
  }

  async getGPSLocation() {
    try {
      const geoLocation = await Geolocation.getCurrentPosition({
        enableHighAccuracy: false,
        timeout: 8000,
        maximumAge: 30000,
      });
      return {
        accuracy: geoLocation.coords.accuracy,
        latitude: geoLocation.coords.latitude,
        longitude: geoLocation.coords.longitude,
      };
    } catch (err) {
      console.error('Error getting location', err);
      return { accuracy: 0, latitude: 0, longitude: 0 };
    }
  }

  async presentLoading(message?: string) {
    const loading = await this.loadingController.create({
      spinner: 'dots',
      message: message,
    });
    await loading.present();
    return loading;
  }
}
