<ion-list class="menu-container">
  <ion-list-header> <img src="/assets/logo/black.png" /> </ion-list-header>
  <ion-item (click)="closeMenu()" [routerLink]="['/money']">
    <i class="material-icons" style="color: var(--Green)">attach_money</i>
    My Money
  </ion-item>
  <ion-item
    *ngIf="ReferralFeatureFlag"
    (click)="closeMenu(); referralOpened()"
    [routerLink]="['/refercode']"
  >
    <i class="material-icons" style="color: var(--viridian-825)">qr_code</i>
    My Referrals
  </ion-item>
  <ion-item (click)="closeMenu()" [routerLink]="['/rank']">
    <i class="material-icons" style="color: var(--Pink)">star_outline</i>
    My Rank
  </ion-item>
  <ion-item (click)="closeMenu()" [routerLink]="['/academy']">
    <i class="material-icons-outlined" style="color: var(--Orange)">school</i>
    Pro Tips
    <b slot="end" *ngIf="AcademyInboxCount > 0">{{ AcademyInboxCount }}</b>
  </ion-item>
  <ion-item (click)="closeMenu(); OpenShopPage()">
    <i class="material-icons" style="color: var(--Blue)">add_shopping_cart</i>
    Shop
  </ion-item>
  <ion-item (click)="closeMenu(); OpenCommunityPage()">
    <i class="material-icons" style="color: #3b5998">facebook</i>
    Community
  </ion-item>
  <ion-item
    (click)="closeMenu()"
    [routerLink]="['/help']"
    style="--border-style: none"
  >
    <i class="material-icons" style="color: var(--BlueGray4)">help_outline</i>
    Help
  </ion-item>
</ion-list>
