.menu-container {
  margin: 0 0 !important;
}

.material-icons,
.material-icons-outlined,
ion-icon {
  font-size: 20px;
  padding-right: 10px;
}

ion-item {
  --border-color: rgb(235, 235, 235);

  b {
    display: block;
    width: 20px;
    height: 20px;
    text-align: center;
    color: var(--BlueGray4);
    background: var(--BlueGray2);
    border-radius: 50%;
    font-size: 14px;
    line-height: 20px;
    font-weight: 800;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
  }
}

img {
  width: 60px;
  display: block;
  margin: 0 auto;
  margin-top: 8px;
  padding-bottom: 8px;
}

ion-list-header {
  padding: 0;
  border-bottom: solid 1px rgb(235, 235, 235);
}
