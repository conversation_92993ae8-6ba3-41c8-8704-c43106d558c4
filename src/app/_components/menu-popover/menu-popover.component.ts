import { Component, Input, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON> } from '@capacitor/browser';
import { PopoverController } from '@ionic/angular';
import { AnalyticsTrackEventType } from 'src/app/_services/analytics/analytics-event.entity';
import { LegacyApiService } from 'src/app/_services/legacy-api.service';
import { StatsigFactoryService } from 'src/app/_services/statsig-factory.service';
import { StatsigService } from 'src/app/_services/statsig.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-menu-popover',
  templateUrl: './menu-popover.component.html',
  styleUrls: ['./menu-popover.component.scss'],
})
export class MenuPopoverComponent implements OnInit {
  @Input() AcademyInboxCount: number;
  ReferralFeatureFlag = false;
  private statsigService: StatsigService;

  constructor(
    public popoverController: PopoverController,
    private statsigFactoryService: StatsigFactoryService,
    private apiService: LegacyApiService
  ) {
    this.statsigService = this.statsigFactoryService.getInstance();
  }

  ngOnInit() {
    this.statsigService
      .checkGate(environment.statsig.flags.SudsterReferralCode)
      .subscribe((value) => {
        this.ReferralFeatureFlag = value;
      });
  }

  OpenShopPage() {
    Browser.open({ url: 'https://store.poplin.co/' });
  }

  OpenCommunityPage() {
    Browser.open({ url: 'http://www.facebook.com/groups/poplinlaundrypros' });
  }

  closeMenu() {
    this.popoverController.dismiss();
  }
  referralOpened() {
    this.apiService
      .post('SegmentServiceApi/v1/TrackEvent', {
        source: 'Laundry Pro',
        eventCategory: 'Referral',
        eventData: {
          eventType: AnalyticsTrackEventType.ReferrealOpened,
          accessSource: 'menu',
        },
      })
      .subscribe();
  }
}
