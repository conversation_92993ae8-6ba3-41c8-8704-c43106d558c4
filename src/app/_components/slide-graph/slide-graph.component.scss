:host {
  width: 100%;
}

section {
  width: 100%;
  position: relative;
  padding-top: 35px;
  margin-top: 15px;
}

h1 {
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 2px;
  font-weight: 700;
  font-size: 18px;
  color: var(--BlueGray5);

  i {
    font-size: 16px;
    color: var(--BlueGray3);
    vertical-align: -1px;
  }
}

#BarDiv {
  width: 100%;
  height: 5px;
  overflow: hidden;

  b:not(:first-child) {
    margin-left: 3px;
  }

  b {
    display: block;
    float: left;
    height: 100%;
    border-radius: 5px;
  }
}

#LabelDiv {
  width: 100%;
  overflow: hidden;
  padding-top: 5px;

  small {
    display: block;
    float: left;
    text-align: center;
    text-transform: uppercase;
    font-size: 9px;
    font-weight: 700;
    color: var(--BlueGray3);
  }

  small:not(:first-child) {
    margin-left: 3px;
  }
}

#PointDiv {
  position: absolute;
  left: 0px;
  top: 0px;
  height: 43px;

  #Point {
    display: block;
    position: absolute;
    height: 12px;
    width: 12px;
    background: white;
    border-radius: 10px;
    border: solid 3px white;
    bottom: 0px;
    left: 10px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
  }

  #PointText {
    display: block;
    background-color: var(--Blue);
    border-radius: 5px;
    font-size: 14px;
    padding: 3px 5px;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    position: relative;
    white-space: nowrap;
    min-width: 47px;
    text-align: center;

    #PointArrow {
      display: block;
      position: absolute;
      font-size: 25px;
      color: var(--Blue);
      left: 3px;
      bottom: -14px;
      text-shadow: none;
    }
  }
}

.pending {
  filter: grayscale(1);
  -webkit-filter: grayscale(1);
  opacity: 0.5;

  #PointDiv {
    display: none !important;
  }

  section {
    padding-top: 10px !important;
  }
}
