import { Component, Input, OnInit } from '@angular/core';
import { AlertController } from '@ionic/angular';

@Component({
  selector: 'app-slide-graph',
  templateUrl: './slide-graph.component.html',
  styleUrls: ['./slide-graph.component.scss'],
})
export class SlideGraphComponent implements OnInit {
  constructor(private alertController: AlertController) {}

  @Input() Segments = [];
  @Input() PointTitle: string = '';
  @Input() PointNumber: string = '';
  @Input() PointLevel = 0;
  @Input() PopupInfo = '';
  @Input() pending = false;
  @Input() doubleBadge = false;

  ngOnInit() {}

  GetPointPosition() {
    return (
      'calc((' +
      ((this.PointLevel / 5) * 100 - 50 / this.Segments.length) +
      '%) - 16px)'
    );
  }
  GetArrowPosition() {
    if (this.PointLevel / 5 == 1) {
      return 'calc(50% + 3px)';
    } else {
      return '3px';
    }
  }

  GetPointLabelPosition() {
    if (this.PointLevel == 5) {
      return '-50%';
    } else {
      return '0px';
    }
  }

  GetSegmentWidth() {
    return 'calc(' + 100 / this.Segments.length + '%' + ' - 3px)';
  }
  GetSegmentColor(Grade) {
    if (Grade == 1) {
      return '#e74c3c';
    } else if (Grade == 2) {
      return '#e67e22';
    } else if (Grade == 3) {
      return '#f1c40f';
    } else if (Grade == 4) {
      return '#1abc9c';
    } else if (Grade == 5) {
      return '#2ecc71';
    }
  }

  async presentAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK'],
    });
    await alert.present();
  }

  isNumeric(num) {
    return !isNaN(num);
  }
}
