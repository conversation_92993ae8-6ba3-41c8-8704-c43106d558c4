<div [class.pending]="pending">
  <h1>
    {{ PointTitle }} {{ pending ? '(PENDING)' : ''
    }}<i
      *ngIf="PopupInfo !== ''"
      (click)="presentAlert(PointTitle, PopupInfo)"
      class="material-icons"
      >info_outline</i
    >
  </h1>
  <section>
    <div id="BarDiv">
      <b
        *ngFor="let segment of Segments"
        [style.width]="GetSegmentWidth()"
        [style.background]="GetSegmentColor(segment.ColorGrade)"
      ></b>
    </div>
    <div id="LabelDiv">
      <small
        [style.width]="GetSegmentWidth()"
        *ngFor="let segment of Segments"
        >{{ segment.Label === '' ? '&nbsp;' : segment.Label }}</small
      >
    </div>
    <div id="PointDiv" [style.left]="GetPointPosition()">
      <b id="Point" [style.background]="GetSegmentColor(PointLevel)"></b>
      <label
        id="PointText"
        [style.background]="GetSegmentColor(PointLevel)"
        [style.left]="GetPointLabelPosition()"
        ><b>{{ PointNumber }}{{ isNumeric(PointNumber) ? ' pts' : '' }}</b>
        <i
          class="material-icons"
          id="PointArrow"
          [style.left]="GetArrowPosition()"
          [style.color]="GetSegmentColor(PointLevel)"
          >arrow_drop_down</i
        ></label
      >
    </div>
  </section>
</div>
