import {
  Directive,
  ElementRef,
  HostListener,
  Input,
  Renderer2,
} from '@angular/core';

@Directive({
  selector: '[inputUpperCase]',
  standalone: true,
})
export class UpperCaseDirective {
  @Input() inputUpperCase: boolean = true;

  constructor(private el: ElementRef, private renderer: Renderer2) {}

  @HostListener('input', ['$event'])
  onInputChange(event: Event) {
    if (!this.inputUpperCase) return;

    const input = event.target as HTMLInputElement;
    const originalValue = input.value;
    const uppercaseValue = originalValue.toUpperCase();

    if (originalValue !== uppercaseValue) {
      const start = input.selectionStart;
      const end = input.selectionEnd;

      this.renderer.setProperty(input, 'value', uppercaseValue);
      input.setSelectionRange(start, end);
    }
  }
}
