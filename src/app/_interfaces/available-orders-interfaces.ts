import { Timestamp } from '@firebase/firestore-types';

export enum PreferredPickup {
  Flexible = 'flexible',
  ASAP = 'asap',
}

export interface NewOrders {
  UserID: string;
  OrderNumber: string;
  PrivateOrderNumber?: string;
  Name: string;
  City: string;
  BagCount: number;
  BagSize: string;
  Earnings: number;
  Bonus: number;
  BonusPoints?: number;
  BonusPointsMultiplier?: number;
  FirstOrder: boolean;
  Preferred: boolean;
  FromDualReferral: boolean;
  PreferredPickup: PreferredPickup;
  SameDayService: boolean;
  Detergent: string;
  LocationName: string;
  PriceBoost: boolean;
  SudsterRate: number;
}

export interface AvailableOrderData {
  UserID: string;
  OrderId: string;
  Address: {
    City: string;
    Lat: number;
    Long: number;
    State: string;
    ZipCode: string;
  };
  BagCount: number;
  BagSize: string;
  Bonus: number;
  BonusPoints?: number;
  BonusPointsMultiplier?: number;
  CreatedTimestamp: Timestamp;
  CustomerFirstName: string;
  CustomerLastName?: string;
  Delivery: string;
  DeliveryTimestamp: Timestamp;
  Detergent: string;
  EstimatedEarnings: number;
  FirstOrder?: boolean;
  OrderNumber?: string;
  Pickup: PreferredPickup;
  PickupTimestamp: Timestamp;
  Preferred?: boolean;
  SameDayService?: boolean;
  SameDay?: boolean;
  Timezone: string;
  IsCustomerFirstOrder: boolean;
  IsCommercial: boolean;
  IsNewOrderFlow?: boolean;
  Preferences: {
    HangDry: boolean;
    Hangers: boolean;
    Delicates: boolean;
    Instructions: string;
    LargeItems: boolean;
  };
  PriceSchedule: PriceSchedule;
}

export interface AvailableOrder {
  OrderNumber: string;
  AllowedSudster: string[];
  DeclinedSudster: string[];
  GeoHash: string[];
  Order: AvailableOrderData;
  PrioritySudsters: string[];
  ReferralSudster?: string;
  SameDayService?: boolean;
  LargeItems?: {
    CustomerSelected: boolean;
    Count: number;
  };
}

export interface PriceSchedule {
  ExpressRate: number;
  ExpressMinimum: number;
  StandardRate: number;
  StandardMinimum: number;
}
