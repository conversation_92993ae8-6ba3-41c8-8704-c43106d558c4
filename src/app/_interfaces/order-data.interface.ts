// Last updated: July 31, 2020
import { GeoPoint, Timestamp } from '@firebase/firestore-types';

export enum BagSize {
  Small = 'small',
  Regular = 'regular',
  OverSized = 'oversized',
  Large = 'large',
}

export enum PreferredPickup {
  Flexible = 'flexible',
  ASAP = 'asap',
}

export enum ExtensionReasons {
  Overweight = 'overweight',
  BagDifference = 'bagDiff',
}

export enum Stages {
  PickUp = 'PICKUP',
  Launder = 'LAUNDER',
  Weigh = 'WEIGH',
  Deliver = 'DELIVER',
  DoNotDeliver = 'DO NOT DELIVER',
  Hold = 'HOLD TIGHT',
}

export interface OrderData {
  OrderId: string;
  Accepted: boolean;
  Active: boolean;
  Address:
    | string
    | {
        Full: string;
        Line1: string;
        Line2: string;
        City: string;
        State: string;
        ZipCode: string;
        Country: string;
      };
  AddressLine2: boolean;
  AskForRate: boolean;
  AuthAmount: number;
  Bonus: number;
  BonusPointsMultiplier?: number;
  CardCharged: number;
  CardChargedString: string;
  CardUsed: string;
  ChargeID: string;
  CustomerFirstName: string;
  CustomerLastName?: string;
  CustomerID: string;
  CustomerMessageBadge: number;
  DeliveryDeadline: number;
  Detergent: string;
  ExactLocation: string;
  GeoHashes: string[];
  GeoLocation: GeoPoint;
  IsNewOrderFlow: boolean;
  IsSudsters: boolean;
  IsSudstersInWorkArea: boolean;
  Lat: number;
  Lng: number;
  Long: number;
  OrderSize: number;
  BagSize: BagSize;
  OrderStatusNumber: number;
  OrderStatus: string;
  PickupDeadline: number;
  Points: {
    LastUpdated: Timestamp;
    Delivery: number;
    Pickup: number;
    Messaging: number;
    Preferred: number;
    Tip: number;
    Rating: number;
    Review: number;
    BonusPoints?: number;
    BonusMultiplier?: number;
  };
  PickupSpot: { Instructions: string; SimpleSpot: string };
  Preferences: {
    Delicates: string;
    Detergent: string;
    HangDry: boolean;
    Hangers: boolean;
    Instructions: string;
  };
  PreWeight?: number;
  PriceSchedule: PriceSchedule;
  Rating: {
    Note: string;
    Number: number;
    Private: boolean;
    Time: Timestamp;
    Compliments: string[];
    Complaints: string[];
  };
  SameDayService: boolean;
  SentRateReminder: boolean;
  SpecificInstructions: string;
  StatusHistoryInfo: {
    Accepted: { UnixTime: number };
    Delivered: {
      BagCount: number;
      LocalTime: string;
      Photo: string;
      UnixTime: number;
    };
    Done: {
      BagCount: number;
      LocalTime: string;
      TotalWeight: number;
      UnixTime: number;
      WeightArray: string;
      PuffyItems: number;
    };
    Pickup: {
      GPS: { accuracy: number; latitude: number; longitude: number };
      BagCount: number;
      LocalTime: string;
      Photo: string;
      UnixTime: number;
    };
    Placed: { UnixTime: number };
  };
  SudsterID: string;
  SudsterMessageBadge: number;
  SudsterName: string;
  SurgeCharge: number;
  TimeStamp: Timestamp;
  TimezoneOffset: number;
  Timezone?: string; // set by Cloud Fn with lat,lng info.
  Tip: number;
  TipProfit: number;
  TotalEarned: number;
  TotalPrice: number;
  Unfulfilled: false;
  UnixTimeStamp: number;
  UserID: string;
  ViewTime: string;
  Zipcode: string;
  EstimatedEarnings: number;
  FirstOrder: boolean;
  OrderNumber: string;
  SameDay: boolean;
  PaymentDeclined: number;
  SudsterFirstOrder?: boolean;
  LargeItems?: {
    CustomerSelected: boolean;
    Count: number;
  };
  IsBusiness?: boolean;
  SudsterWeightChanged?: boolean;
  ContactAlert: boolean; // Customer or Sudster tried to direct contact
  PreferredPickup: PreferredPickup;
  PreferredSudsters: string[];
  Referral?: {
    HeroReferral?: boolean;
    ReferredBy?: string;
    ReferredBySudster?: string;
  };
  ReplyReminderNotSent?: boolean;
  // transient fields:
  Preferred?: boolean; // populated locally if order is preferred
  timeSincePreferredSudstersNotified?: number; // populated if order is preferred and we've notified all preferred sudsters
  OrderExtensionReasons?: string[];
  OrderWindow?: string;
  EstimatedDriveTime: string;
}
export interface PriceSchedule {
  ExpressRate: number;
  ExpressMinimum: number;
  StandardRate: number;
  StandardMinimum: number;
}
