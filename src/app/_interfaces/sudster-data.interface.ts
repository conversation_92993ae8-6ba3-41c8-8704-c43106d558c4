import { GeoPoint, Timestamp } from '@firebase/firestore-types';
import { VacationMode } from './vacation-mode.interface';

export interface SudsterData {
  Phone: string;
  SignupStepNumber: number;
  SinceMonth: string;
  AccountRejected: boolean;
  AccountRejectedDetails: string;
  AccountRejectedForterReason: string;
  AlertsOn: boolean;
  AlertsOnLp?: boolean;
  SignupUnixTime: number;
  FirstName: string;
  LastName: string;
  IDStatus: string;
  ContactEmail: string;
  GeoHash: string;
  WorkRadius: number;
  WorkAreaCenter: GeoPoint;
  Ranking: {
    Points: number;
    BadgeNumber: number;
  };
  BlockOrders: boolean;
  BlockPayouts: boolean;
  CheckrInformation: CheckrInformation;
  EmailVerified: boolean;
  PerformanceScore?: number;
  AverageRating: {
    Count: number;
    Current: number;
    Warning?: number;
    LastOrderWarned?: string;
    FeedbackCount?: number;
    Complaints?: Array<{ count: number; title: string }>;
    Compliments?: Array<{ count: number; title: string }>;
  };
  FirstMembershipTestOrder?: string;
  StreetAddress: string;
  AddressLine2: string;
  City: string;
  State: string;
  Zipcode: string;
  BlockedCustomers?: Array<string>;
  OrderCount: number;
  Academy?: {
    Completed?: Array<number>;
    LastUpdated?: Timestamp;
    Count: number;
  };
  StripeAccountID: string;
  SharedContactsDate: number; // last (timestamp) date that the contact hashes were sent to server.
  Retention: {
    Rate: number; // deprecated
    Score: number;
    LastUpdated: number;
  };
  ChatCredits?: number;
  ChatCreditsLastUpdate?: number;
  RequireAcceptTOS: boolean; // flag when stripe requires accept TOS again.
  FCM?: string;
  FCM2?: Array<FCM2>;
  LastTOSAcceptanceDate?: Timestamp;
  faceRecognition: string;
  standardVerification: string;
  appInfo: {
    appVersion: string;
    platform: string;
    timestamp: number;
    date: Date;
  };
  IdVerification?: string;
  IdVerificationDeadline?: number;
  IdVerificatonAttemptsLeft?: number;
  IdVerificationError?: string;
  extraAttemptAdded?: boolean;
  CheckrVerification?:
    | 'verified'
    | 'unverified'
    | 'pending'
    | 'failed'
    | 'consider'
    | 'canceled';
  ConfirmedVerification?: boolean;
  ReservedOrderId?: string;
  Referrals?: {
    LpInPersonQR?: string;
    LpDigitalRefCode?: string;
  };
  Vacation?: VacationMode;
  InboxData?: {
    lastMessageTime: number;
    inboxMessageCount: number;
    inAppMessageCount: number;
  };
}
export interface CheckrInformation {
  adjudication: string;
  candidate_id: string;
  event_type: string;
  result: string;
  status: string;
}
export enum ChatType {
  CHAT = 'chat',
  LIVE_CHAT = 'live chat',
}
export interface FCM2 {
  appInfo: appInfo;
  deviceId: string;
  timestamp: Timestamp;
  token: string;
}

export interface appInfo {
  appVersion: string;
  date: Timestamp;
  platform: 'android' | 'ios' | 'web';
  timestamp: number;
}

export interface WeighOrderDto {
  weightArray: string[];
  totalWeight: number;
  puffyNumber?: number;
}
