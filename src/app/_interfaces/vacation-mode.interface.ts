export interface VacationMode {
  isVacationEnabled: boolean;
  startDate: string; // ISO 8601
  endDate: string; // ISO 8601
  createDate: string; // ISO 8601
  updateDate: string; // ISO 8601
  isVacationModeActive?: boolean;
}
export interface VacationResponse {
  message: string; // Message describing the result
  data?: VacationData | any; // Optional vacation data
  error?: string; // Optional error message
}

export interface VacationUpdateData {
  startDate?: string;
  endDate?: string;
  isVacationEnabled?: boolean; // Optional for toggling state
  isVacationModeActive?: boolean;
}

export interface VacationData {
  startDate: string; // ISO 8601 format
  endDate: string; // ISO 8601 format
  createdAt?: string; // Optional; set when the record is created
  updatedAt?: string;
  isVacationModeActive?: boolean;
}
