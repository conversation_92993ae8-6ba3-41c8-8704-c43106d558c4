export interface MapkitRoute {
  polyline: any; // mapkit.PolylineOverlay;
  path: any; // DEPRECATED mapkit.Coordinate[][] ;
  steps: MapkitRouteStep[]; // RouteStep[];
  name: string;
  distance: number;
  expectedTravelTime: number;
  transportType: MapkitDirectionsTransport; // mapkit.Directions.Transport ;
}

export interface MapkitRouteStep {
  path: any; // mapkit.Coordinate[] ;
  instructions: string;
  distance: number;
  transportType: MapkitDirectionsTransport; // mapkit.Directions.Transport ;
}

export type MapkitDirectionsTransport = 'AUTOMOBILE' | 'WALKING' | any;
