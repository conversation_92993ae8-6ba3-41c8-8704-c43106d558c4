export interface AnnotationOptions {
  title: string;
  subtitle?: string;
  displayPriority?: number;
  data?: Object;
  clusteringIdentifier?: string;
}

export interface ImageAnnotationOptions extends AnnotationOptions {
  accessibilityLabel?: string;
  draggable?: boolean;
  visible?: boolean;
  enabled?: boolean;
  selected?: boolean;
  calloutEnabled?: boolean;
  animates?: boolean;
  appearanceAnimation?: string;
  anchorOffset?: DOMPoint;
  calloutOffset?: DOMPoint;
  callout?: any; // AnnotationCalloutDelegate;
  size?: Object;
  collisionMode?: string;
  padding?: any; // mapkit.Padding;
  url?: Object;
}

export interface MarkerAnnotationOptions extends AnnotationOptions {
  titleVisibility?: string;
  subtitleVisibility?: string;
  color?: string;
  glyphColor?: string;
  glyphText?: string;
  glyphImage?: Object;
  selectedGlyphImage?: Object;
}
