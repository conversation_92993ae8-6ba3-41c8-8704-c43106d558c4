// Constants
const EARTH_RADIUS_KM = 6371;
const KM_TO_MI = 0.621371;
const KM_PER_LAT_DEG = 111.32;

/**
 * Convert degrees to radians
 */
function toRadians(deg: number): number {
  return (deg * Math.PI) / 180;
}

export function getDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  const dLat = toRadians(lat2 - lat1);
  const dLon = toRadians(lon2 - lon1);

  const rLat1 = toRadians(lat1);
  const rLat2 = toRadians(lat2);

  const a =
    Math.sin(dLat / 2) ** 2 +
    Math.cos(rLat1) * Math.cos(rLat2) * Math.sin(dLon / 2) ** 2;
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return EARTH_RADIUS_KM * c;
}

export function getDistanceMi(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  return getDistance(lat1, lon1, lat2, lon2) * KM_TO_MI;
}

export function getCathetusDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  const latDiffDeg = Math.abs(lat2 - lat1);
  const lonDiffDeg = Math.abs(lon2 - lon1);

  const latDistanceKm = latDiffDeg * KM_PER_LAT_DEG;

  const avgLat = toRadians((lat1 + lat2) / 2);
  const lonDistanceKm = lonDiffDeg * KM_PER_LAT_DEG * Math.cos(avgLat);

  return latDistanceKm + lonDistanceKm;
}

export function getCathetusDistanceMi(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  return getCathetusDistance(lat1, lon1, lat2, lon2) * KM_TO_MI;
}
