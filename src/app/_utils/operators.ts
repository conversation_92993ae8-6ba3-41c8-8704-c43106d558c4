import { isEqual } from 'lodash';
import { Observable, OperatorFunction } from 'rxjs';
import { distinctUntilChanged, map } from 'rxjs/operators';

/**
 * Creates an operator that selects specific fields from a source object
 * and only emits when the selected state changes
 *
 * @param selector Function that extracts the desired state from the source
 * @param compare Optional comparison function to determine if state has changed
 */
export function selectDistinctBy<TSource, TResult>(
  selector: (source: TSource) => TResult,
  compare: (prev: TResult, curr: TResult) => boolean = isEqual
): OperatorFunction<TSource, TResult> {
  return (source: Observable<TSource>): Observable<TResult> => {
    return source.pipe(map(selector), distinctUntilChanged(compare));
  };
}
