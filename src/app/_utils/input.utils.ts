export const emailPattern = /^[A-Za-z0-9._%+-]+@[a-z0-9.-]+\.[A-Za-z]{2,4}$/;

/*
    US Phone Number Validation

    This validation ensures that phone numbers conform to standard US formats,
    including optional country code (+1), area codes, and common separators.

    ✅ Supports US phone number formats only.
    ✅ Enforces 10-digit numbers (excluding country code).
    ✅ Allows optional country code (+1) at the beginning.
    ✅ Allows area codes in parentheses.
    ✅ Accepts various separators: spaces, hyphens (-), and dots (.).
    ✅ Supports toll-free numbers (e.g., ************).
    ❌ Does NOT allow non-US numbers (e.g., +44 20 7946 0958).
    ❌ Does NOT allow numbers shorter than 10 digits.

    -------------------------------
    Examples of Valid US Phone Numbers (✅ Pass)
    -------------------------------
    - Standard 10-digit numbers:
        ************
        (*************
        (*************
        ************
        ************

    - With country code (+1):
        ******-456-7890
        +****************

    - Toll-Free Numbers:
        ************
        +****************

    -------------------------------
    Examples of Invalid Phone Numbers (❌ Fail)
    -------------------------------
    - Non-US numbers:
        +44 20 7946 0958  (UK)
        +91 98765 43210    (India)

    - Too short:
        456-7890
        123
        123456789

    - Incorrect formats:
        ****************  (Missing "+")
        *****-4567-890    (Incorrect structure)

*/

export const phoneNumberPattern =
  /^(?:\+1\s?)?(?:\(\d{3}\)|\d{3})[-.\s]?\d{3}[-.\s]?\d{4}$/;
