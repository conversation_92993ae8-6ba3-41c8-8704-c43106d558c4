import { DEFAULT_V4_HEADERS } from 'src/app/_constants';
import { environment } from '../../environments/environment';

export async function trackEvent(payload: any) {
  console.info('Track Event', JSON.stringify(payload));
  const GENERAL_API_URL = `${environment.apiPath}/general/v1`;
  try {
    await fetch(`${GENERAL_API_URL}/analytics/track-event`, {
      method: 'POST',
      headers: DEFAULT_V4_HEADERS,
      body: JSON.stringify(payload),
    });
    console.log('Event tracked Successfully');
  } catch (error) {
    console.error('Error tracking event:', error);
  }
}
