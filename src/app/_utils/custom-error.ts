export class CustomError extends Error {
  protected code: string;
  protected title: string;
  protected status: number;

  constructor({
    name = 'custom_error',
    code,
    message,
    title,
    status,
  }: {
    message: string;
    name?: string;
    code?: string;
    title?: string;
    status?: string;
  }) {
    super(message);
    this.name = name;
    this.code = code;
    this.message = message;
    this.title = title;
  }
}
