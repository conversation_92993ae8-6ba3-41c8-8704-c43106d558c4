export function resizeImageWeb(
  base64,
  maxWidth,
  maxHeight,
  outputFormat = 'image/jpeg',
  quality = 0.7
): Promise<string> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.src = base64;
    img.onload = () => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      let width = img.width;
      let height = img.height;

      if (width > maxWidth || height > maxHeight) {
        if (width > height) {
          height = Math.round((maxWidth / width) * height);
          width = maxWidth;
        } else {
          width = Math.round((maxHeight / height) * width);
          height = maxHeight;
        }
      }

      canvas.width = width;
      canvas.height = height;
      ctx.drawImage(img, 0, 0, width, height);

      const resizedBase64 = canvas.toDataURL(outputFormat, quality);
      resolve(resizedBase64);
    };
    img.onerror = (error) => reject(error);
  });
}

export function base64ToFile(base64URI, fileType): File {
  const [prefix, base64] = base64URI.split(',');
  const mimeType = prefix.match(/:(.*?);/)[1];

  try {
    const binaryData = Uint8Array.from(atob(base64), (char) =>
      char.charCodeAt(0)
    );
    return new File([binaryData], fileType, { type: mimeType });
  } catch (e) {
    console.log(e);
  }
}

export function convertBlobToBase64(blob: Blob): Promise<unknown> {
  return new Promise((resolve) => {
    const reader = new FileReader();
    // use loadend instead of onload to ensure
    // that the reader is closed before resolving
    reader.onloadend = () => resolve(reader.result);
    reader.readAsDataURL(blob);
  });
}
