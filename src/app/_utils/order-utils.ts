import moment from 'moment';
import { OrderData, Stages } from '../_interfaces/order-data.interface';
import { WeighStatus } from '../_services/polling.service';
import tz_lookup from 'tz-lookup';

export function getPickDeadlineStr(order: OrderData) {
  //pickup<PERSON><PERSON>lin knows if is today or next day from Fn.
  const pickupMoment = moment.tz(order.PickupDeadline, order.Timezone);
  const now = moment().tz(order.Timezone);

  return `${
    pickupMoment.isSame(now, 'day') ? 'Today' : 'Tomorrow'
  }, ${formatAMPM(pickupMoment)}`;
}

export function formatAMPM(date: Date | moment.Moment) {
  const dateMoment = moment(date);
  return dateMoment.format('h a');
}

export function getAddress(
  address:
    | string
    | {
        Full: string;
        Line1: string;
        Line2: string;
        City: string;
        State: string;
        ZipCode: string;
        Country: string;
      },
  fullAddress?: boolean
): string {
  if (address && typeof address === 'object' && address.Line1) {
    if (fullAddress) {
      return address.Full + address.ZipCode;
    } else {
      return address.Line1;
    }
  } else if (typeof address === 'string') {
    if (fullAddress) {
      return address;
    } else {
      return address.split(',')[0];
    }
  }
}

export function getStage(order: OrderData): string {
  const showHoldTightNotification =
    order?.OrderStatus === WeighStatus.processingPayment ||
    order?.OrderStatus === WeighStatus.updatingPayment;
  const showDoNotDeliverNotification =
    order?.OrderStatus === WeighStatus.paymentFailed ||
    order?.OrderStatus === WeighStatus.paymentUpdateFailed;

  if (showHoldTightNotification || showDoNotDeliverNotification) {
    if (showDoNotDeliverNotification) {
      return Stages.DoNotDeliver;
    }
    if (showHoldTightNotification) {
      return Stages.Hold;
    }
  } else {
    if (order.OrderStatusNumber == 2) {
      return Stages.Launder;
    } else if (order.OrderStatusNumber == 2.5) {
      return Stages.Weigh;
    } else if (order.OrderStatusNumber == 3) {
      return Stages.Deliver;
    }
  }

  return Stages.PickUp;
}

export function getDeadline(order: OrderData) {
  const timezone = order.Timezone
    ? order.Timezone
    : tz_lookup(order.Lat, order.Lng);

  const deadline =
    order.OrderStatusNumber >= 2 || order.OrderStatus !== WeighStatus.weighed
      ? order.DeliveryDeadline
      : order.PickupDeadline;

  return moment.tz(deadline, timezone);
}

export function formatDeadline(date): string {
  return date.calendar(null, {
    sameDay: '[today] [at] ha',
    nextDay: '[tomorrow] [at] ha',
    nextWeek: 'dddd [at] ha',
    lastDay: 'MMM D [at] h:mmA',
    lastWeek: 'MMM D [at] h:mmA',
    sameElse: 'MMM D [at] h:mmA',
  });
}
