export enum AuthErrors {
  WEAK_PASSWORD = 'auth/weak-password',
  INVALID_EMAIL = 'auth/invalid-email',
  EMAIL_IN_USE = 'auth/email-already-in-use',
  EMAIL_ALREADY_EXISTS = 'auth/email-already-exists',
  INVALID_PASSWORD = 'auth/invalid-password',
  NO_EMAIL_OR_PASSWORD = 'auth/argument-error',
  USER_NOT_FOUND = 'auth/user-not-found',
  WRONG_PASSWORD = 'auth/wrong-password',
  USER_DISABLED = 'auth/user-disabled',
}

export enum USER_TYPES {
  CUSTOMER = 'Customer',
  LAUNDRY_PRO = 'LaundryPro',
}

export enum ATTENTIVE_SUBSCRIPTION_TYPES {
  MARKETING = 'MARKETING',
  TRANSACTIONAL = 'TRANSACTIONAL',
}

export enum HTTP_METHOD {
  POST = 'POST',
}

export enum StatsigFlags {
  HIDE_FIRST_TIME_CUSTOMER_ORDER = 'hidefirsttimecustomerorder',
  CA_CONTRACT = 'cacontract',
}

export const DATE_FORMAT = 'MM/DD/yyyy';
export const DATETIME_FORMAT = 'MMM D [at] h:mmA';

export enum TimeRange {
  week = 'week',
  month = 'month',
  year = 'year',
  quarter = 'quarter',
  all = 'all',
}
