import moment from 'moment';

export abstract class Utils {
  static thousandComma(value: number | string): string {
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }

  static isUndefined(value: any) {
    return value === undefined;
  }
}

export const tokenReplace = (
  template: string,
  interpolations: Record<string, unknown>,
  throwErrors = false
): string => {
  return template.replace(/\{\{\s*(\w+)\s*\}\}/g, (marker, token) => {
    if (throwErrors && !Object.hasOwnProperty.call(interpolations, token)) {
      throw new Error(`Missing interpolation for '${token}'`);
    }
    return String(interpolations[token]);
  });
};

export const getOrdinalDay = (date: Date): string => {
  const dayNumber = date.getDate();
  return dayNumber + (dayNumber > 3 ? 'th' : ['st', 'nd', 'rd'][dayNumber - 1]);
};

export const parseDate = (
  input:
    | string
    | number
    | Date
    | { nanoseconds: number; seconds: number }
    | moment.Moment
): Date | null => {
  if (!input) return null;

  // Firestore Timestamp
  if (
    typeof input === 'object' &&
    'seconds' in input &&
    'nanoseconds' in input
  ) {
    return new Date(input.seconds * 1000);
  }

  // Moment.js Object
  if (moment.isMoment(input)) {
    return input.toDate();
  }

  // Native Date
  if (input instanceof Date) {
    return input;
  }

  // Number (timestamp)
  if (typeof input === 'number') {
    return new Date(input < 10000000000 ? input * 1000 : input); // Handle seconds vs milliseconds
  }

  // String Date
  if (typeof input === 'string') {
    const parsed = new Date(input);
    return isNaN(parsed.getTime()) ? null : parsed;
  }

  return null;
};

/**
 * Determines if the current date falls within the given vacation period.
 *
 * @param {string | number | Date | { nanoseconds: number; seconds: number }} startDate - The start date of the vacation.
 * @param {string | number | Date | { nanoseconds: number; seconds: number }} endDate - The end date of the vacation.
 * @returns {boolean} True if the current date is within the vacation period, otherwise false.
 */
export const isOnVacation = (
  startDate:
    | string
    | number
    | Date
    | { nanoseconds: number; seconds: number }
    | moment.Moment,
  endDate:
    | string
    | number
    | Date
    | { nanoseconds: number; seconds: number }
    | moment.Moment
): boolean => {
  const start = parseDate(startDate);
  const end = parseDate(endDate);
  const now = new Date();
  if (!start || !end) {
    console.error('[isOnVacation] Invalid date format.', {
      startDate,
      endDate,
      start,
      end,
    });
    return false;
  }

  if (start.getTime() > end.getTime()) {
    console.error('[isOnVacation] Start date is after end date.', {
      start,
      end,
    });
    return false;
  }

  return now.getTime() >= start.getTime() && now.getTime() <= end.getTime();
};

export const capitalizeFirstLetter = (val: string): string => {
  return String(val).charAt(0).toUpperCase() + String(val).slice(1);
};
