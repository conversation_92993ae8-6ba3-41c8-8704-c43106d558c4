import { WebPlugin } from '@capacitor/core';
import { SegmentPlugin } from '../plugin-definitions';

export class SegmentPluginWeb extends WebPlugin implements SegmentPlugin {
  private analytics = null;

  constructor(private window: Window) {
    super();
    this.analytics = this.window['analytics'];
  }

  async identify(data): Promise<{ result: boolean }> {
    if (!this.analytics) {
      return this.returnNoAnalytics();
    }

    this.analytics.identify(data.userId, data.traits);
    return { result: true };
  }

  async track(data): Promise<{ result: boolean }> {
    if (!this.analytics) {
      return this.returnNoAnalytics();
    }

    const eventName = data.eventName;
    const properties = data.properties;
    this.analytics.track(eventName, properties);

    return { result: true };
  }

  async reset(): Promise<{ result: boolean }> {
    if (!this.analytics) {
      return this.returnNoAnalytics();
    }

    this.analytics.user().traits({});
    this.analytics.group().traits({});

    return { result: true };
  }

  private returnNoAnalytics() {
    return { result: false };
  }
}
