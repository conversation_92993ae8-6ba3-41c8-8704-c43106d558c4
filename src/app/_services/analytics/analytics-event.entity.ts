import { ExtensionReasons } from 'src/app/_interfaces/order-data.interface';

export enum AnalyticsTrackEventType {
  Login = 'Account Logged In',
  IDVStart = 'Security Check Initiated',
  IDVCancel = 'Security Check Cancelled',
  AccountCreation = 'Account Creation',
  AddressChange = 'Address Change',
  OrderViewed = 'Order Viewed',
  OrderAccepted = 'Order Accepted',
  UpdatePayout = 'Payout Method Change',
  OrderPickup = 'Order Pickup',
  ReferrealOpened = 'Referral Opened',
  ReferralShared = 'Referral Shared',
  OrderExtensionRequested = 'Order Extension',
  bagSizeDiscrepancy = 'Bag Size Discrepancy',
}

export enum SignupSteps {
  SignedUp = 0,
  GetStarted = 1,
  AccountInfo = 2,
  ConnectBank = 3,
  IdVerification = 4,
  Training = 5,
  Test = 6,
  Completed = 7,
}

export class AnalyticEvent {
  userId: string;
  payload: Record<string, any> = new Object();

  constructor(userId: string) {
    this.userId = userId;
  }

  private setKey(k: string, v: any): AnalyticEvent {
    this.payload[k] = v;

    return this;
  }

  setSudsterId(sudsterId: string) {
    return this.setKey('sudsterId', sudsterId);
  }
  setFirstName(FirstName: string) {
    return this.setKey('firstName', FirstName);
  }
  setLastName(LastName: string) {
    return this.setKey('lastName', LastName);
  }
  setEmail(Email: string) {
    return this.setKey('email', Email);
  }
  setPhone(Phone: string) {
    return this.setKey('phone', Phone);
  }
  setPlatform(Platform: string) {
    return this.setKey('platform', Platform);
  }
  setTimestamp(Time: string) {
    return this.setKey('timestamp', Time);
  }
  isEmpty(): boolean {
    return Object.getOwnPropertyNames(this.payload).length > 0;
  }
  setLoginMethod(LoginMethod: string) {
    return this.setKey('loginMethod', LoginMethod);
  }
  setOrderId(orderId: string) {
    return this.setKey('orderId', orderId);
  }
  setIDVChecks(idvStatusFlags: {
    isCheckrEnabled: boolean;
    isStripeIDVEnabled: boolean;
  }) {
    return this.setKey(
      'isCheckrEnabled',
      idvStatusFlags.isCheckrEnabled
    ).setKey('isStripeIDVEnabled', idvStatusFlags.isStripeIDVEnabled);
  }
  setAccountCreationStepName(accountCreationStepName: string) {
    return this.setKey('accountCreationStepName', accountCreationStepName);
  }
  setAccountCreationStepNumber(accountCreationStepNumber: number) {
    return this.setKey('accountCreationStepNumber', accountCreationStepNumber);
  }
  setAppVersion(version: string) {
    return this.setKey('appVersion', version);
  }
  setCity(city: string) {
    return this.setKey('city', city);
  }
  setState(state: string) {
    return this.setKey('state', state);
  }
  setBadgeNumber(number: number) {
    return this.setKey('badgeNumber', number);
  }
  setBadgeLevel(badgeLevel: number) {
    return this.setKey('badgeLevel', badgeLevel);
  }
  setCurrentRating(rating: number) {
    return this.setKey('currentRating', rating);
  }
  setCurrentPointsTotal(pointsTotal: number) {
    return this.setKey('currentPointsTotal', pointsTotal);
  }
  setPreAcceptanceData(preAcceptanceData: boolean) {
    return this.setKey('preAcceptanceData', preAcceptanceData);
  }
  setPayoutType(payoutType: string) {
    return this.setKey('payoutType', payoutType);
  }
  setUserType(userType: string) {
    return this.setKey('userType', userType);
  }
  setOrderSize(orderSize: number) {
    return this.setKey('orderSize', orderSize);
  }
  setOrderSizePickedUp(orderSizePickedUp: number) {
    return this.setKey('orderSizePickedUp', orderSizePickedUp);
  }
  setOrderSizeDiscrepancy(orderSizeDiscrepancy: boolean) {
    return this.setKey('orderSizeDiscrepancy', orderSizeDiscrepancy);
  }
  setOrderSizeDiscrepancyAmount(orderSizeDiscrepancyAmount: number) {
    return this.setKey(
      'orderSizeDiscrepancyAmount',
      orderSizeDiscrepancyAmount
    );
  }
  setAccessSource(accessSource: string) {
    return this.setKey('accessSource', accessSource);
  }
  setPickedUpBeforeDeadline(pickedUpBeforeDeadline: boolean) {
    return this.setKey('pickedUpBeforeDeadline', pickedUpBeforeDeadline);
  }
  setOrderExtensionReason(extensionReason: ExtensionReasons) {
    return this.setKey('orderExtensionReason', extensionReason);
  }
  setOrderExtensionLogType(type: string) {
    return this.setKey('orderExtensionType', type);
  }

  setOriginalBagSize(size: string) {
    return this.setKey('originalBagSize', size);
  }
  setUpdatedBagSize(size: string) {
    return this.setKey('updatedBagSize', size);
  }
  setBagSizeDiscrepancy(discrepancy: boolean) {
    return this.setKey('bagSizeDiscrepancy', discrepancy);
  }
}
