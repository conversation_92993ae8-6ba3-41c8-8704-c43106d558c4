import { Injectable } from '@angular/core';
import { registerPlugin } from '@capacitor/core';
import { SegmentPlugin } from '../plugin-definitions';
import {
  AnalyticEvent,
  AnalyticsTrackEventType,
} from './analytics-event.entity';

const segmentPlugin = registerPlugin<SegmentPlugin>('Segment', {
  web: () =>
    import('./segment-web.plugin').then((m) => new m.SegmentPluginWeb(window)),
});

@Injectable({
  providedIn: 'root',
})
export class SegmentService {
  logIdentify(userId: string, traits: Record<string, any>) {
    segmentPlugin
      .identify({ userId, traits: traits })
      .then(() => Promise.resolve());
  }

  logTrack(type: AnalyticsTrackEventType, eventData: AnalyticEvent) {
    segmentPlugin
      .track({
        eventName: type.toString(),
        properties: eventData.payload,
      })
      .then(() => Promise.resolve());
  }

  reset() {
    segmentPlugin.reset();
  }
}
