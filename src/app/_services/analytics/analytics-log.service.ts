import { Injectable, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { <PERSON><PERSON> } from '@capacitor/device';
import { <PERSON><PERSON><PERSON><PERSON> } from '@ngneat/until-destroy';
import { ExtensionReasons } from 'src/app/_interfaces/order-data.interface';
import { SudsterData } from 'src/app/_interfaces/sudster-data.interface';
import { environment } from 'src/environments/environment';
import { AuthidService } from '../authid.service';
import { GetSudsterDataService } from '../get-sudster-data.service';
import {
  AnalyticEvent,
  AnalyticsTrackEventType,
} from './analytics-event.entity';
import { SegmentService } from './segment.service';

@UntilDestroy()
@Injectable({
  providedIn: 'root',
})
export class AnalyticsLogService implements OnDestroy {
  private platformType: string;
  private sudster: SudsterData;
  private lastEventTimestamp: number;
  private secondsBetweenEvents = 5;

  constructor(
    private segmentService: SegmentService,
    private sudsterService: GetSudsterDataService,
    private AuthID: AuthidService
  ) {
    this.sudsterService.listenForSudster().subscribe((sudster) => {
      if (sudster) {
        // if there is a sudster, log the identify
        this.logIdentifyForCurrentUser();
      } else if (this.sudster) {
        // for some reason the existing user logged out
        this.segmentService.reset();
      }
      this.sudster = sudster;
    });

    Device.getInfo().then((deviceInfo) => {
      this.platformType = deviceInfo.platform;
    });
  }

  public logIdentifyForCurrentUser() {
    if (!this.sudster) {
      return;
    }

    const event = this.buildEvent();

    if (this.platformType) {
      event.setPlatform(this.platformType);
    }
    event
      .setEmail(this.sudster.ContactEmail)
      .setFirstName(this.sudster.FirstName)
      .setLastName(this.sudster.LastName)
      .setPhone(this.sudster.Phone);

    this.identifyEvent(event);
  }

  logUICancelIDV(
    orderId: string,
    isCheckrEnabled: boolean,
    isStripeIDVEnabled: boolean
  ) {
    const evt = this.buildEvent();
    evt.setOrderId(orderId);
    // Steps Enabled: IDV, BG Check
    evt.setIDVChecks({ isCheckrEnabled, isStripeIDVEnabled });
    this.trackEvent(AnalyticsTrackEventType.IDVCancel, evt);
  }

  logIDVStart(
    orderId: string,
    isCheckrEnabled: boolean,
    isStripeIDVEnabled: boolean
  ) {
    const evt = this.buildEvent();
    evt.setOrderId(orderId);
    // Steps Enabled: IDV, BG Check
    evt.setIDVChecks({ isCheckrEnabled, isStripeIDVEnabled });
    this.trackEvent(AnalyticsTrackEventType.IDVStart, evt);
  }

  async logAccountCreation(
    sid: string,
    signupStepName: string,
    signupStepNumber: number,
    email: string
  ) {
    const event = await this.buildAccountCreationEvent(
      sid,
      signupStepName,
      signupStepNumber,
      email
    );

    this.trackEvent(AnalyticsTrackEventType.AccountCreation, event);
  }

  async logAccountCreationAccountInfo(
    sid: string,
    signupStepName: string,
    signupStepNumber: number,
    firstName?: string,
    lastName?: string,
    city?: string,
    state?: string
  ) {
    firstName = firstName ? firstName : this.sudster.FirstName;
    lastName = lastName ? lastName : this.sudster.LastName;
    city = city ? city : this.sudster.City;
    state = state ? state : this.sudster.State;
    const event = await this.buildAccountCreationEvent(
      sid,
      signupStepName,
      signupStepNumber,
      this.sudster.ContactEmail
    );
    event
      .setFirstName(firstName)
      .setLastName(lastName)
      .setCity(city)
      .setState(state);
    this.trackEvent(AnalyticsTrackEventType.AccountCreation, event);
  }
  logAddressChangeEvent(sid: string, email: string) {
    const event = this.buildAddressChangeEvent(sid, email);
    return this.trackEvent(AnalyticsTrackEventType.AddressChange, event);
  }

  public async logUserLoginEvent(
    sid: string,
    email: string,
    badgeNumber: number,
    ratingNumber: number,
    currentPointsTotal: number
  ) {
    const event = await this.buildLoginEvent(
      sid,
      email,
      badgeNumber,
      ratingNumber,
      currentPointsTotal
    );
    return this.trackEvent(AnalyticsTrackEventType.Login, event);
  }

  public async logOrderViewedEvent(
    sid: string,
    email: string,
    phone: string,
    orderNumber: string,
    preacceptanceData: boolean
  ) {
    const event = await this.buildOrderEvent(
      sid,
      email,
      phone,
      orderNumber,
      preacceptanceData
    );
    return this.trackEvent(AnalyticsTrackEventType.OrderViewed, event);
  }
  public async logOrderAcceptedEvent(
    sid: string,
    email: string,
    phone: string,
    orderNumber: string,
    preacceptanceData: boolean
  ) {
    const event = await this.buildOrderEvent(
      sid,
      email,
      phone,
      orderNumber,
      preacceptanceData
    );
    return this.trackEvent(AnalyticsTrackEventType.OrderAccepted, event);
  }

  async logPayoutMethodUpdate(payoutType: string): Promise<void> {
    const event = this.buildEvent();
    const now = new Date().getTime().toString();

    event
      .setPayoutType(payoutType)
      .setEmail(this.sudster.ContactEmail)
      .setBadgeLevel(this.sudster.Ranking.BadgeNumber)
      .setAppVersion(environment.version)
      .setTimestamp(now);
    await Device.getInfo().then((device) => {
      const platform =
        device && device.platform ? device.platform : 'not available';
      event.setPlatform(platform);
    });
    return this.trackEvent(AnalyticsTrackEventType.UpdatePayout, event);
  }

  async logOrderPickup(
    sid: string,
    orderNumber: string,
    orderSize: number,
    orderSizePickedUp: number,
    orderSizeDiscrepancy: boolean,
    pickupDeadline: number
  ) {
    const event = await this.buildOrderPickupEvent(
      sid,
      orderNumber,
      orderSize,
      orderSizePickedUp,
      orderSizeDiscrepancy,
      pickupDeadline
    );

    return this.trackEvent(AnalyticsTrackEventType.OrderPickup, event);
  }

  async logOrderExtension(
    sid: string,
    orderNumber: string,
    extensionReason: ExtensionReasons,
    extensionType: string
  ) {
    const event = await this.buildOrderExtensionEvent(
      sid,
      orderNumber,
      extensionReason,
      extensionType
    );

    return this.trackEvent(
      AnalyticsTrackEventType.OrderExtensionRequested,
      event
    );
  }

  async logBagSizeDiscrepancy(
    sid: string,
    orderId: string,
    origialBagSize: string,
    updatedBagSize: string
  ) {
    const event = await this.buildBagSizeDiscrepancyEvent(
      sid,
      orderId,
      origialBagSize,
      updatedBagSize
    );
    return this.trackEvent(AnalyticsTrackEventType.bagSizeDiscrepancy, event);
  }

  public trackEvent(type: AnalyticsTrackEventType, eventData: AnalyticEvent) {
    this.segmentService.logTrack(type, eventData);
  }

  /**
   * Builds an event associated to the current user in session
   * @returns and event initialized or null if there is still no user with active session
   */
  private buildEvent(): AnalyticEvent {
    if (!this.AuthID.getID()) {
      return null;
    }

    const e = new AnalyticEvent(this.AuthID.getID());
    // by default we set the sudsterId property on all events.
    e.setSudsterId(this.AuthID.getID());
    // by default we set the userType property on all events.
    e.setUserType('Laundry Pro');

    return e;
  }

  // Events for identify the user. Private method, logIdentifyForCurrentUser() is should be used if needed.
  private identifyEvent(eventData: AnalyticEvent) {
    // ignore event if previous identify event was withing some time (secondsBetweenEvents)
    if (this.limitEventTimeElapsed()) {
      return;
    }

    this.segmentService.logIdentify(eventData.userId, eventData.payload);
  }

  private limitEventTimeElapsed() {
    const nowTs = new Date().getTime();
    const ignoreEvent =
      (nowTs - this.lastEventTimestamp) / 1000 < this.secondsBetweenEvents;
    if (!ignoreEvent) {
      this.lastEventTimestamp = nowTs;

      return false;
    }

    return true;
  }

  private async buildAccountCreationEvent(
    sid: string,
    signupStepName: string,
    signupStepNumber: number,
    email: string
  ): Promise<AnalyticEvent> {
    email = email ? email : this.sudster.ContactEmail;
    const event = new AnalyticEvent(sid);
    const now = new Date().getTime().toString();
    event
      .setEmail(email)
      .setSudsterId(sid)
      .setAccountCreationStepName(signupStepName)
      .setAccountCreationStepNumber(signupStepNumber)
      .setAppVersion(environment.version)
      .setTimestamp(now);
    await Device.getInfo().then((device) => {
      const platform =
        device && device.platform ? device.platform : 'not available';
      event.setPlatform(platform);
    });
    return event;
  }

  private buildAddressChangeEvent(sid: string, email: string): AnalyticEvent {
    const event = new AnalyticEvent(sid);
    const now = new Date().getTime().toString();
    event.setSudsterId(sid).setEmail(email).setTimestamp(now);
    return event;
  }
  private async buildLoginEvent(
    sid: string,
    email: string,
    badgeNumber: number,
    ratingNumber: number,
    currentPointsTotal: number
  ): Promise<AnalyticEvent> {
    const event = this.buildEvent();
    const now = new Date().getTime().toString();

    event
      .setSudsterId(sid)
      .setEmail(email)
      .setTimestamp(now)
      .setAppVersion(environment.version)
      .setBadgeNumber(badgeNumber)
      .setCurrentRating(ratingNumber)
      .setCurrentPointsTotal(currentPointsTotal);
    await Device.getInfo().then((device) => {
      const platform =
        device && device.platform ? device.platform : 'not available';
      event.setPlatform(platform);
    });
    return event;
  }

  private async buildOrderEvent(
    sid: string,
    email: string,
    phone: string,
    orderNumber: string,
    preacceptanceData: boolean
  ): Promise<AnalyticEvent> {
    const event = this.buildEvent();
    const now = new Date().getTime().toString();
    event
      .setSudsterId(sid)
      .setEmail(email)
      .setPhone(phone)
      .setOrderId(orderNumber)
      .setAppVersion(environment.version)
      .setTimestamp(now)
      .setPreAcceptanceData(preacceptanceData);
    await Device.getInfo().then((device) => {
      const platform =
        device && device.platform ? device.platform : 'not available';
      event.setPlatform(platform);
    });
    return event;
  }

  private async buildOrderPickupEvent(
    sid: string,
    orderNumber: string,
    orderSize: number,
    orderSizePickedUp: number,
    orderSizeDiscrepancy: boolean,
    pickupDeadline: number
  ): Promise<AnalyticEvent> {
    const event = this.buildEvent();
    const now = new Date().getTime().toString();
    const nowUnixTime = new Date().getTime();
    const orderSizeArray = [orderSize, orderSizePickedUp];
    const sortedArray = orderSizeArray.sort((a, b) => a - b);
    const discrepancyAmount = sortedArray[1] - sortedArray[0];
    event
      .setSudsterId(sid)
      .setOrderId(orderNumber)
      .setAppVersion(environment.version)
      .setTimestamp(now)
      .setOrderSize(orderSize)
      .setOrderSizePickedUp(orderSizePickedUp)
      .setOrderSizeDiscrepancy(orderSizeDiscrepancy)
      .setOrderSizeDiscrepancyAmount(discrepancyAmount)
      .setPickedUpBeforeDeadline(pickupDeadline > nowUnixTime);
    await Device.getInfo().then((device) => {
      const platform =
        device && device.platform ? device.platform : 'not available';
      event.setPlatform(platform);
    });
    return event;
  }

  private async buildOrderExtensionEvent(
    sid: string,
    orderId: string,
    extensionReason: ExtensionReasons,
    extensionType: string
  ) {
    const event = new AnalyticEvent(sid);
    const now = new Date().getTime().toString();
    event
      .setSudsterId(sid)
      .setOrderId(orderId)
      .setOrderExtensionReason(extensionReason)
      .setOrderExtensionLogType(extensionType)
      .setAppVersion(environment.version)
      .setTimestamp(now);
    await Device.getInfo().then((device) => {
      const platform =
        device && device.platform ? device.platform : 'not available';
      event.setPlatform(platform);
    });
    return event;
  }

  private async buildBagSizeDiscrepancyEvent(
    sid: string,
    orderId: string,
    origialBagSize: string,
    updatedBagSize: string
  ): Promise<AnalyticEvent> {
    const event = this.buildEvent();
    const now = new Date().getTime().toString();
    event
      .setSudsterId(sid)
      .setOrderId(orderId)
      .setBagSizeDiscrepancy(true)
      .setOriginalBagSize(origialBagSize)
      .setUpdatedBagSize(updatedBagSize)
      .setTimestamp(now);
    await Device.getInfo().then((device) => {
      const platform =
        device && device.platform ? device.platform : 'not available';
      event.setPlatform(platform);
    });
    return event;
  }

  ngOnDestroy(): void {}
}
