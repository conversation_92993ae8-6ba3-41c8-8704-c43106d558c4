import { Injectable } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { VideoPopoverComponent } from '../_components/video-popover/video-popover.component';

@Injectable({
  providedIn: 'root',
})
export class PlayVideoService {
  constructor(public modalController: ModalController) {}

  async open(link) {
    const modal = await this.modalController.create({
      component: VideoPopoverComponent,
      componentProps: {
        link: link,
      },
    });
    return await modal.present();
  }
}
