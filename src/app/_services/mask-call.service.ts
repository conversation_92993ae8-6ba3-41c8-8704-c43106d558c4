import { Injectable } from '@angular/core';
import { AngularFireFunctions } from '@angular/fire/functions';
import { AlertController, LoadingController } from '@ionic/angular';

@Injectable({
  providedIn: 'root',
})
export class MaskCallService {
  constructor(
    private cloudFunctions: AngularFireFunctions,
    private alertController: Alert<PERSON>ontroller,
    private loadingController: LoadingController
  ) {}

  CallCustomer(OrderNumber) {
    this.alertController
      .create({
        header: 'Before Your Call',
        message:
          "We'll call your phone first, then connect you to the customer through a masked number. We recommend keeping the conversation professional and not sharing your personal contact information. This call may be recorded and monitored.",
        buttons: [
          {
            text: 'Connnect To Call',
            handler: () => {
              const loading = this.presentLoading();
              this.cloudFunctions
                .httpsCallable('SudsterV3_MaskCallSudsterToCustomer')({
                  OrderNumber: OrderNumber,
                })
                .toPromise()
                .then(() => {
                  loading.then(function (ld) {
                    ld.dismiss();
                  });
                  this.presentAlert(
                    'Connecting...',
                    "We're calling your phone now to connect you to your customer."
                  );
                })
                .catch(() => {
                  loading.then(function (ld) {
                    ld.dismiss();
                  });
                  this.presentAlert(
                    'Error',
                    'There was an error connecting your call.'
                  );
                });
            },
          },
          {
            text: 'Cancel',
            role: 'cancel',
          },
        ],
      })
      .then((alert) => {
        alert.present();
      });
  }

  async presentAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK'],
    });
    await alert.present();
  }

  async presentLoading() {
    const loading = await this.loadingController.create({});
    await loading.present();
    return loading;
  }
}
