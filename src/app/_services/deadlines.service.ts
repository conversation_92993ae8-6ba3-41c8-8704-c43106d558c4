import { Injectable } from '@angular/core';
import * as moment from 'moment';

@Injectable({
  providedIn: 'root',
})
export class DeadlinesService {
  constructor() {}

  CalcDeliveryDeadline(Timestamp: Date, ReturnUnix, SameDay?) {
    let DueString = 'Past Due';
    const DaysOfWeek = [
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
    ];

    const TimeDifference = new Date().getTime() - Timestamp.getTime();

    let TimeDiffInDays = TimeDifference / 86400000;

    let DueDateUnix = new Date(
      new Date().getTime() + 24 * 60 * 60 * 1000
    ).setHours(20, 0, 0);

    if (Timestamp.getHours() >= 15) {
      TimeDiffInDays--;
      DueDateUnix += 24 * 60 * 60 * 1000;
    }

    if (SameDay) {
      DueDateUnix = new Date().setHours(21, 0, 0);
    }

    if (TimeDiffInDays <= 0) {
      //In 2 days
      const DueWeekDay = DaysOfWeek[new Date().getDay() + 2];
      DueString = DueWeekDay + ', 8pm';
    }
    if (TimeDiffInDays >= 0) {
      //Tomorrow
      DueString = 'Tomorrow, 8pm';
    }
    if (TimeDiffInDays >= 1) {
      //Today
      DueString = 'Today, 8pm';
    }
    if (TimeDiffInDays >= 2) {
      //Today
      DueString = 'Past due';
    }

    if (ReturnUnix) {
      return DueDateUnix;
    } else {
      return DueString;
    }
  }

  ConvertUnixDeadlineToString(UnixTimestamp: number, timezone: string): string {
    if (moment.tz) {
      const now = moment.tz(timezone);
      const date = moment.tz(UnixTimestamp, timezone);

      if (date.isBefore(now)) {
        return 'PAST DUE';
      }

      return date.calendar(null, {
        sameDay: '[Today], h A',
        nextDay: '[Tomorrow], h A',
        nextWeek: 'dddd, h A',
        lastDay: '[Yesterday], h A',
        lastWeek: '[Last] dddd, h A',
        sameElse: 'ddd MMM D, h A',
      });
    }

    return '';
  }
}
