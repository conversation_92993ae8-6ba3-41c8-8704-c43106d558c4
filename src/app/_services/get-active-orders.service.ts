import { Injectable } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/auth';
import { AngularFirestore } from '@angular/fire/firestore';
import { BehaviorSubject, Subscription } from 'rxjs';
import { first } from 'rxjs/operators';
import { OrderData } from '../_interfaces/order-data.interface';
import { SudsterData } from '../_interfaces/sudster-data.interface';
import { AuthidService } from './authid.service';

@Injectable({
  providedIn: 'root',
})
export class GetActiveOrdersService {
  private OrderSource = new BehaviorSubject({});
  currentOrders = this.OrderSource.asObservable();
  private currentSubscription: Subscription = null;

  constructor(
    public firestore: AngularFirestore,
    public afAuth: AngularFireAuth,
    private AuthID: AuthidService
  ) {}

  UserID = this.AuthID.getID();

  LoadOrderList() {
    if (!this.currentSubscription?.closed) {
      this.currentSubscription?.unsubscribe();
    }

    this.firestore
      .doc<SudsterData>(`Sudsters/${this.UserID}`)
      .valueChanges()
      .pipe(first())
      .subscribe((SudsterDoc) => {
        this.currentSubscription = this.firestore
          .collection<OrderData>('Orders', (ref) =>
            ref
              .where('SudsterID', '==', this.UserID)
              .where('Active', '==', true)
              .orderBy('OrderStatusNumber', 'asc')
              .limit(100)
          )
          .valueChanges({ idField: 'OrderNumber' })
          .subscribe((collection) => {
            this.OrderSource.next({ Orders: collection, Sudster: SudsterDoc });
          });
      });
  }
}
