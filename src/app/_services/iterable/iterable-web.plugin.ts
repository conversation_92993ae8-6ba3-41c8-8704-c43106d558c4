import { WebPlugin } from '@capacitor/core';
import { IterablePlugin } from '../plugin-definitions';

export class IterablePluginWeb extends WebPlugin implements IterablePlugin {
  constructor(private window) {
    super();
    this.window = window;
  }
  trackPush(rq: {
    templateId: number;
    campaignId: number;
    msgId: string;
    appWasRunning: boolean;
  }): Promise<string> {
    return Promise.resolve('Not implemented in web');
  }
  initialize(identifyOptions) {
    return Promise.resolve();
  }
  registerUser(data) {
    return Promise.resolve();
  }
}
