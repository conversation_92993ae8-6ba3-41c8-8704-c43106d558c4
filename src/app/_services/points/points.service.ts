import { inject, Injectable } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON> } from '@ngneat/until-destroy';
import { ApiService } from '../../_services/api.service';
import { getAuthId } from '../../_utils/authId.injector';

/**
 * Service to modify the points of the Laundry Pro
 */

@UntilDestroy()
@Injectable({
  providedIn: 'root',
})
export class PointsService {
  private readonly _apiService = inject(ApiService);

  private readonly modifyPointsUrl =
    'Points/v1/sudster/first-order-videos-points';
  readonly userId = getAuthId();

  addFirstOrderPoints(orderId: string, quiz: number) {
    return this._apiService.patch(this.modifyPointsUrl, {
      orderId,
      quiz,
    });
  }
}
