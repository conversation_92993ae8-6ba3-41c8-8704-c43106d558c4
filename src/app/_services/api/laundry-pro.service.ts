import { Injectable } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON> } from '@ngneat/until-destroy';
import { PrivacyPolicy, TermsOfService } from '@sudshare/custom-node-package';
import { firstValueFrom } from 'rxjs';
import { DEFAULT_V4_HEADERS } from 'src/app/_constants';
import { CustomError } from 'src/app/_utils/custom-error';
import { trackEvent } from 'src/app/_utils/track-event';
import { environment } from 'src/environments/environment';
import { ApiResponse, LegacyApiService } from '../legacy-api.service';

export interface LaundryProFirstRegister {
  email: string;
  password: string;
  sinceMonth: string;
  signupUnixTime: number;
  lastTOSAcceptanceDate: Date;
  termsAndPoliciesAgreement: {
    policyVersion: string;
    termsVersion: string;
  };
}

export interface LaundryProTermsToAcceptResponse {
  terms: {
    needAccept: boolean;
    term: TermsOfService;
  };
  policies: {
    needAccept: boolean;
    privacyPolicy: PrivacyPolicy;
  };
}

@UntilDestroy()
@Injectable({
  providedIn: 'root',
})
export class LaundryProService {
  constructor(private apiService: LegacyApiService) {}

  public async createLaundryProUser(
    params: LaundryProFirstRegister
  ): Promise<void> {
    let response: Response;

    try {
      const GENERAL_API_URL = `${environment.apiPath}/general/v1`;
      response = await fetch(`${GENERAL_API_URL}/laundry-pro`, {
        method: 'POST',
        headers: DEFAULT_V4_HEADERS,
        body: JSON.stringify(params),
      });
    } catch {
      throw new Error(
        'Unexpected error, please contact concierge for assistance'
      );
    }

    if (!response.ok) {
      const data = await response.json();

      throw new CustomError({
        code: data.code,
        message:
          data.message ?? 'Unable to create the laundry pro internal data.',
        status: data.statusCode ?? response.status,
      });
    }
  }

  public async getTermsAndPoliciesToAccept(
    laundryProId: string
  ): Promise<LaundryProTermsToAcceptResponse> {
    const GENERAL_API_URL = `${environment.apiPath}/general/v1`;
    const response = await fetch(
      `${GENERAL_API_URL}/laundry-pro/${laundryProId}/terms-to-accept`,
      {
        method: 'GET',
        headers: DEFAULT_V4_HEADERS,
      }
    );

    if (!response.ok) {
      throw new Error('Unable to retrieve terms to accept.');
    }

    return response.json();
  }

  public async saveNewTermsAndPoliciesAccepted(
    laundryProId: string,
    params: {
      policyVersion: string;
      termsVersion: string;
    }
  ): Promise<void> {
    const GENERAL_API_URL = `${environment.apiPath}/general/v1`;
    const response = await fetch(
      `${GENERAL_API_URL}/laundry-pro/${laundryProId}/terms-accepted`,
      {
        method: 'POST',
        headers: DEFAULT_V4_HEADERS,
        body: JSON.stringify(params),
      }
    );

    if (!response.ok) {
      throw new Error('Unable to save terms acceptance data.');
    }
    const { policyVersion, termsVersion } = params;
    await trackEvent({
      eventData: {
        event: 'LPTermsAndConditionsAccepted',
        lpId: laundryProId,
        policyVersion,
        termsVersion,
      },
    });
  }

  async acceptOrder(
    orderNumber: string,
    body: Record<string, unknown>,
    isNewOrderFlow: boolean
  ): Promise<ApiResponse> {
    if (isNewOrderFlow) {
      return firstValueFrom(
        this.apiService.put(`/${orderNumber}/status/accept`, body, {
          baseUrl: `${environment.apiPathV2}/orders`,
        })
      );
    }

    return firstValueFrom(this.apiService.post('UpdateOrder/v1/accept', body));
  }
}
