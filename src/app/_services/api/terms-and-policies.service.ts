import { Injectable } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON> } from '@ngneat/until-destroy';
import { PrivacyPolicy, TermsOfService } from '@sudshare/custom-node-package';
import { DEFAULT_V4_HEADERS } from 'src/app/_constants';
import { environment } from 'src/environments/environment';

export interface TermsAndPoliciesResponse {
  terms: TermsOfService;
  policy: PrivacyPolicy;
}

@UntilDestroy()
@Injectable({
  providedIn: 'root',
})
export class TermsAndPoliciesService {
  public async getCurrentTermsAndPolicy(): Promise<TermsAndPoliciesResponse | null> {
    const GENERAL_API_URL = `${environment.apiPath}/general/v1`;
    const response = await fetch(`${GENERAL_API_URL}/term-and-policies`, {
      method: 'GET',
      headers: DEFAULT_V4_HEADERS,
    });

    if (response.ok) {
      return response.json();
    }

    return null;
  }
}
