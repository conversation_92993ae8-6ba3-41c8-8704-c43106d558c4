import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import {
  WorkRadiusData,
  WorkRadiusResponse,
} from '../_interfaces/work-radius.interface';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root',
})
export class WorkRadiusService {
  private readonly URL = 'WorkRadius/v1/sudster/work-radius';

  apiService = inject(ApiService);

  getWorkRadius(): Observable<number> {
    return this.apiService
      .get(this.URL)
      .pipe(map(({ data }) => (data as WorkRadiusResponse).data.WorkRadius));
  }

  updateWorkRadius(dataToUpdate: WorkRadiusData): Observable<number> {
    return this.apiService
      .put(this.URL, dataToUpdate)
      .pipe(map(({ data }) => (data as WorkRadiusResponse).data.WorkRadius));
  }
}
