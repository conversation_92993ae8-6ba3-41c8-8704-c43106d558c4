import { inject, Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { map, tap } from 'rxjs/operators';
import {
  VacationMode,
  VacationResponse,
  VacationUpdateData,
} from '../_interfaces/vacation-mode.interface';
import { DateRange } from '../_pages/availability/availability.model';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root',
})
export class VacationModeService {
  private readonly URL = 'VacationMode/v1/sudster/vacation';
  /**
   * Observable used to notify the Availability page that a vacationData
   * update is in progress. During this time, the "Save" functionality
   * is disabled to avoid a known issue where clicking it while saving
   * could result in an unintended property being deleted from the database.
   */
  public notifyVacationDataStartSaving$ = new BehaviorSubject<boolean>(false);

  apiService = inject(ApiService);
  public vacationModeModel: DateRange = {
    from: null,
    to: null,
  };

  getVacations(): Observable<VacationMode> {
    return this.apiService
      .get(this.URL)
      .pipe(map(({ data }) => (data as VacationResponse).data));
  }

  updateVacations(dataToUpdate: VacationUpdateData) {
    return this.apiService.put(this.URL, dataToUpdate).pipe(
      map(({ data }) => (data as VacationResponse).data),
      tap(() => {
        this.notifyVacationDataStartSaving$.next(false);
      })
    );
  }

  updateVacationsMode(isVacationEnabled: boolean) {
    return this.apiService.patch(this.URL, { isVacationEnabled }).pipe(
      map(({ data }) => (data as VacationResponse).data),
      tap(() => {
        this.notifyVacationDataStartSaving$.next(false);
      })
    );
  }
}
