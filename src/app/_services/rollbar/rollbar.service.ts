import { InjectionToken } from '@angular/core';

import Rollbar from 'rollbar';
import { environment } from 'src/environments/environment';
import packageJson from '../../../../package.json';

const rollbarConfig: Rollbar.Configuration = {
  accessToken: environment.rollbar.accessToken,
  captureUncaught: true,
  captureUnhandledRejections: true,
  captureEmail: true,
  captureIp: true,
  captureUsername: true,
  codeVersion: packageJson.version,
  ignoredMessages: ['Script error.'],
  enabled: !environment.local,
  payload: {
    environment: environment.production ? 'production' : 'development',
    client: {
      javascript: {
        code_version: packageJson.version,
        source_map_enabled: true,
        guess_uncaught_frames: true,
      },
      server: {
        root: 'webpack:///./',
      },
    },
  },
  transform: (payload: any) => {
    if (payload.body.trace || payload.body.trace_chain) {
      const frames =
        payload.body.trace?.frames || payload.body.trace_chain?.[0]?.frames;

      if (frames) {
        frames.forEach((frame) => {
          if (
            frame.filename &&
            frame.filename.startsWith('capacitor://localhost')
          ) {
            frame.filename = frame.filename.replace(
              'capacitor://localhost',
              `${environment.webApp}`
            );
          }

          if (frame.filename && frame.filename.startsWith('http://localhost')) {
            frame.filename = frame.filename.replace(
              'http://localhost',
              `${environment.webApp}`
            );
          }
        });
      }
    }
  },
};

export const RollbarService = new InjectionToken<Rollbar>('rollbar');

export function rollbarFactory() {
  return new Rollbar(rollbarConfig);
}
