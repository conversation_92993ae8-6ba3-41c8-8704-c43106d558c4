import { Injectable } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/auth';
import { Until<PERSON><PERSON>roy } from '@ngneat/until-destroy';
import { defer, from, Observable, OperatorFunction } from 'rxjs';
import { fromFetch } from 'rxjs/fetch';
import { concatMap, map, take } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { DEFAULT_HEADERS } from '../_constants';

export enum ApiResponseStatus {
  ok = '200',
  created = '201',
  noContent = '204',
  badRequest = '400',
  unauthenticated = '401',
  paymentRequired = '402',
  forbidden = '403',
  notFound = '404',
  methodNotAllowed = '405',
  notAcceptable = '406',
  proxyAuthRequired = '407',
  requestTimeout = '408',
  conflict = '409',
  gone = '410',
  lengthRequired = '411',
  preconditionFailed = '412',
  payloadTooLarge = '413',
  uriTooLong = '414',
  unsupportedMediaType = '415',
  rangeNotSatisfiable = '416',
  expectationFailed = '417',
  imATeapot = '418',
  misdirectedRequest = '421',
  unprocessableEntity = '422',
  locked = '423',
  failedDependency = '424',
  tooEarly = '425',
  upgradeRequired = '426',
  preconditionRequired = '428',
  tooManyRequests = '429',
  requestHeaderFieldsTooLarge = '431',
  unavailableForLegalReasons = '451',
  internalServerError = '500',
  notImplemented = '501',
  badGateway = '502',
  serviceUnavailable = '503',
  gatewayTimeout = '504',
  httpVersionNotSupported = '505',
  variantAlsoNegotiates = '506',
  insufficientStorage = '507',
  loopDetected = '508',
  notExtended = '510',
  networkAuthenticationRequired = '511',
}

export class ApiResponse {
  status: ApiResponseStatus;
  message: string;
  data: Record<string, any> | string | null;
  rawResponse: Response;

  constructor(status: ApiResponseStatus, data = null, message = '') {
    this.status = status;
    this.message = message;
    this.data = data;
  }
  setRawResponse(response: Response) {
    this.rawResponse = response;
  }
}

export class ApiResponseError extends ApiResponse implements Error {
  name: string;
  stack?: string;
  constructor(status: ApiResponseStatus, data, message) {
    super(status, data, message);
    this.name = status;
  }
}
/**
 * Helper operator that extracts the data from the ApiResponse with the option to
 * provide a type to the data, can be used to extract a single
 * ApiResponse or an array of ApiResponse
 *
 * @returns the data from the ApiResponse(s)
 */
export function extractData<T = any>(): OperatorFunction<
  ApiResponse | ApiResponse[],
  T | T[]
> {
  return (source: Observable<ApiResponse | ApiResponse[]>) => {
    return source.pipe(
      map((response) => {
        if (Array.isArray(response)) {
          return response.map((r) => {
            if (r instanceof ApiResponse && 'data' in r) {
              return r.data as T;
            }
            return r as T;
          });
        }
        if (response instanceof ApiResponse && 'data' in response) {
          return response.data as T;
        }
        return response as T;
      })
    );
  };
}

// Utility function to get the key of an enum by its value
export function getEnumKeyByValue<T extends { [key: string]: string | number }>(
  enumObj: T,
  value: string
): keyof T | undefined {
  return (Object.keys(enumObj) as Array<keyof T>).find(
    (key) => enumObj[key] === value
  );
}

// TODO: replace with ApiService
// api.service.ts

@UntilDestroy()
@Injectable({
  providedIn: 'root',
})
export class LegacyApiService {
  baseEndpoint: string;

  constructor(private afAuth: AngularFireAuth) {
    this.baseEndpoint = environment.baseEndpoint;
  }

  private handleResponse(response: Response): Observable<ApiResponse> {
    return defer(() =>
      from(response.json()).pipe(
        map((data) => {
          if (response.status >= 200 && response.status < 300) {
            const apiResponse = new ApiResponse(
              ApiResponseStatus.ok,
              data,
              data?.message || ''
            );
            apiResponse.setRawResponse(response);
            return apiResponse;
          } else {
            throw new ApiResponseError(
              ApiResponseStatus[
                getEnumKeyByValue(
                  ApiResponseStatus,
                  String(response.status)
                ) as keyof typeof ApiResponseStatus
              ],
              data,
              data?.message || ''
            );
          }
        })
      )
    );
  }

  public post(
    endpointPath: string,
    payload: Record<string, any> | FormData,
    { baseUrl, headers }: { baseUrl?: string; headers?: any } = {}
  ): Observable<ApiResponse> {
    const hostBaseUrl = baseUrl || this.baseEndpoint;
    const defaultHeaders = {
      ...DEFAULT_HEADERS,
      ...headers,
    };

    return this.afAuth.idToken.pipe(
      take(1),
      map((idToken) => {
        if (!idToken) {
          throw new ApiResponseError(
            ApiResponseStatus.unauthenticated,
            null,
            'User is not authenticated'
          );
        }
        return new Headers({
          ...defaultHeaders,
          Authorization: `Bearer ${idToken}`,
        });
      }),
      concatMap((headers) => {
        const isMultiPart = payload instanceof FormData;
        if (isMultiPart) {
          headers.delete('Content-Type');
        }
        return fromFetch(hostBaseUrl + endpointPath, {
          method: 'POST',
          headers,
          body: isMultiPart ? payload : JSON.stringify(payload),
        }).pipe(concatMap((response) => this.handleResponse(response)));
      })
    );
  }

  public get(
    endpointPath: string,
    { baseUrl, headers }: { baseUrl?: string; headers?: any } = {}
  ): Observable<ApiResponse> {
    const hostBaseUrl = baseUrl || this.baseEndpoint;
    const defaultHeaders = {
      'Content-Type': 'application/json',
      Authorization: '',
      ...headers,
    };

    return this.afAuth.idToken.pipe(
      take(1),
      map((idToken) => {
        if (!idToken) {
          throw new ApiResponseError(
            ApiResponseStatus.unauthenticated,
            null,
            'User is not authenticated'
          );
        }
        return new Headers({
          ...defaultHeaders,
          Authorization: `Bearer ${idToken}`,
        });
      }),
      concatMap((headers) => {
        return fromFetch(hostBaseUrl + endpointPath, {
          method: 'GET',
          headers,
        }).pipe(concatMap((response) => this.handleResponse(response)));
      })
    );
  }

  public patch(
    endpointPath: string,
    payload: Record<string, any>,
    { baseUrl, headers }: { baseUrl?: string; headers?: any } = {}
  ): Observable<ApiResponse> {
    const hostBaseUrl = baseUrl || this.baseEndpoint;
    const defaultHeaders = {
      ...DEFAULT_HEADERS,
      ...headers,
    };

    return this.afAuth.idToken.pipe(
      take(1),
      map((idToken) => {
        if (!idToken) {
          throw new ApiResponseError(
            ApiResponseStatus.unauthenticated,
            null,
            'Missing authentication token'
          );
        }
        return new Headers({
          ...defaultHeaders,
          Authorization: `Bearer ${idToken}`,
        });
      }),
      concatMap((headers) => {
        return fromFetch(hostBaseUrl + endpointPath, {
          method: 'PATCH',
          headers,
          body: JSON.stringify(payload),
        }).pipe(concatMap((response) => this.handleResponse(response)));
      })
    );
  }

  public put(
    endpointPath: string,
    payload: Record<string, any>,
    { baseUrl, headers }: { baseUrl?: string; headers?: any } = {}
  ): Observable<ApiResponse> {
    const hostBaseUrl = baseUrl || this.baseEndpoint;
    const defaultHeaders = {
      ...DEFAULT_HEADERS,
      ...headers,
    };

    return this.afAuth.idToken.pipe(
      take(1),
      map((idToken) => {
        if (!idToken) {
          throw new ApiResponseError(
            ApiResponseStatus.unauthenticated,
            null,
            'Missing authentication token'
          );
        }
        return new Headers({
          ...defaultHeaders,
          Authorization: `Bearer ${idToken}`,
        });
      }),
      concatMap((headers) =>
        fromFetch(hostBaseUrl + endpointPath, {
          method: 'PUT',
          headers,
          body: JSON.stringify(payload),
        })
      ),
      concatMap((response) => this.handleResponse(response))
    );
  }
}
