import { Injectable } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/auth';
import { Until<PERSON><PERSON>roy } from '@ngneat/until-destroy';
import { Observable, OperatorFunction, throwError } from 'rxjs';
import { fromFetch } from 'rxjs/fetch';
import { catchError, concatMap, map, take } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { DEFAULT_HEADERS } from '../_constants';

export enum ApiResponseStatus {
  ok = '200',
  created = '201',
  noContent = '204',
  badRequest = '400',
  unauthenticated = '401',
  notFound = '404',
  serverError = '500',
}

export class ApiResponse {
  status: ApiResponseStatus;
  message: string;
  data: Record<string, any> | string | null;
  rawResponse: Response;

  constructor(status: ApiResponseStatus, data = null, message = '') {
    this.status = status;
    this.message = message;
    this.data = data;
  }
  setRawResponse(response: Response) {
    this.rawResponse = response;
  }
}

export class ApiResponseError extends ApiResponse implements Error {
  name: string;
  stack?: string;
  constructor(status: ApiResponseStatus, data, message) {
    super(status, data, message);
    this.name = status;
  }
}
/**
 * Helper operator that extracts the data from the ApiResponse with the option to
 * provide a type to the data, can be used to extract a single
 * ApiResponse or an array of ApiResponse
 *
 * @returns the data from the ApiResponse(s)
 */
export function extractData<T = any>(): OperatorFunction<
  ApiResponse | ApiResponse[],
  T | T[]
> {
  return (source: Observable<ApiResponse | ApiResponse[]>) => {
    return source.pipe(
      map((response) => {
        if (Array.isArray(response)) {
          return response.map((r) => {
            if (r instanceof ApiResponse && 'data' in r) {
              return r.data as T;
            }
            return r as T;
          });
        }
        if (response instanceof ApiResponse && 'data' in response) {
          return response.data as T;
        }
        return response as T;
      })
    );
  };
}

// TODO: replace with ApiService
// api.service.ts

@UntilDestroy()
@Injectable({
  providedIn: 'root',
})
export class LegacyApiService {
  baseEndpoint: string;

  constructor(private afAuth: AngularFireAuth) {
    this.baseEndpoint = environment.baseEndpoint;
  }

  private handleResponse(response: Response): Observable<ApiResponse> {
    if (!response.ok) {
      return new Observable((subscriber) => {
        response.json().then((errorData) => {
          subscriber.error(
            new ApiResponseError(
              ApiResponseStatus[response.status] || ApiResponseStatus.notFound,
              errorData,
              errorData.message || 'Request failed'
            )
          );
        });
      });
    }
    return new Observable((subscriber) => {
      (response.status === 204 ? Promise.resolve(null) : response.json()).then(
        (data) => {
          const res = new ApiResponse(ApiResponseStatus.ok, data);
          res.setRawResponse(response);
          subscriber.next(res);
          subscriber.complete();
        }
      );
    });
  }

  public post(
    endpointPath: string,
    payload: Record<string, any> | FormData,
    { baseUrl, headers }: { baseUrl?: string; headers?: any } = {}
  ): Observable<ApiResponse> {
    const hostBaseUrl = baseUrl || this.baseEndpoint;
    const defaultHeaders = {
      ...DEFAULT_HEADERS,
      ...headers,
    };

    return this.afAuth.idToken.pipe(
      take(1),
      map((idToken) => {
        if (!idToken) {
          throw new ApiResponseError(
            ApiResponseStatus.unauthenticated,
            null,
            'User is not authenticated'
          );
        }
        return new Headers({
          ...defaultHeaders,
          Authorization: `Bearer ${idToken}`,
        });
      }),
      concatMap((headers) => {
        const isMultiPart = payload instanceof FormData;
        if (isMultiPart) {
          headers.delete('Content-Type');
        }
        return fromFetch(hostBaseUrl + endpointPath, {
          method: 'POST',
          headers,
          body: isMultiPart ? payload : JSON.stringify(payload),
        }).pipe(
          concatMap((response) => this.handleResponse(response)),
          catchError((err) => {
            if (err instanceof ApiResponseError) {
              return throwError(() => err);
            }
            return throwError(
              () => new ApiResponseError(err.status, null, err.message)
            );
          })
        );
      })
    );
  }

  public get(
    endpointPath: string,
    { baseUrl, headers }: { baseUrl?: string; headers?: any } = {}
  ): Observable<ApiResponse> {
    const hostBaseUrl = baseUrl || this.baseEndpoint;
    const defaultHeaders = {
      'Content-Type': 'application/json',
      Authorization: '',
      ...headers,
    };

    return this.afAuth.idToken.pipe(
      take(1),
      map((idToken) => {
        if (!idToken) {
          throw new ApiResponseError(
            ApiResponseStatus.unauthenticated,
            null,
            'User is not authenticated'
          );
        }
        return new Headers({
          ...defaultHeaders,
          Authorization: `Bearer ${idToken}`,
        });
      }),
      concatMap((headers) => {
        return fromFetch(hostBaseUrl + endpointPath, {
          method: 'GET',
          headers,
        }).pipe(
          concatMap((response) => this.handleResponse(response)),
          catchError((err) => {
            if (err instanceof ApiResponseError) {
              return throwError(() => err);
            }
            return throwError(
              () => new ApiResponseError(err.status, null, err.message)
            );
          })
        );
      })
    );
  }

  public patch(
    endpointPath: string,
    payload: Record<string, any>,
    { baseUrl, headers }: { baseUrl?: string; headers?: any } = {}
  ): Observable<ApiResponse> {
    const hostBaseUrl = baseUrl || this.baseEndpoint;
    const defaultHeaders = {
      ...DEFAULT_HEADERS,
      ...headers,
    };

    return this.afAuth.idToken.pipe(
      take(1),
      map((idToken) => {
        if (!idToken) {
          throw new ApiResponseError(
            ApiResponseStatus.unauthenticated,
            null,
            'Missing authentication token'
          );
        }
        return new Headers({
          ...defaultHeaders,
          Authorization: `Bearer ${idToken}`,
        });
      }),
      concatMap((headers) => {
        return fromFetch(hostBaseUrl + endpointPath, {
          method: 'PATCH',
          headers,
          body: JSON.stringify(payload),
        }).pipe(
          concatMap((response) => this.handleResponse(response)),
          catchError((err) => {
            if (err instanceof ApiResponseError) {
              return throwError(() => err);
            }
            return throwError(
              () =>
                new ApiResponseError(
                  ApiResponseStatus.serverError,
                  null,
                  err.message
                )
            );
          })
        );
      })
    );
  }

  public put(
    endpointPath: string,
    payload: Record<string, any>,
    { baseUrl, headers }: { baseUrl?: string; headers?: any } = {}
  ): Observable<ApiResponse> {
    const hostBaseUrl = baseUrl || this.baseEndpoint;
    const defaultHeaders = {
      ...DEFAULT_HEADERS,
      ...headers,
    };

    return this.afAuth.idToken.pipe(
      take(1),
      map((idToken) => {
        if (!idToken) {
          throw new ApiResponseError(
            ApiResponseStatus.unauthenticated,
            null,
            'Missing authentication token'
          );
        }
        return new Headers({
          ...defaultHeaders,
          Authorization: `Bearer ${idToken}`,
        });
      }),
      concatMap((headers) => {
        return fromFetch(hostBaseUrl + endpointPath, {
          method: 'PUT',
          headers,
          body: JSON.stringify(payload),
        }).pipe(
          concatMap((response) => this.handleResponse(response)),
          catchError((err) => {
            if (err instanceof ApiResponseError) {
              return throwError(() => err);
            }
            return throwError(
              () =>
                new ApiResponseError(
                  ApiResponseStatus.serverError,
                  null,
                  err.message
                )
            );
          })
        );
      })
    );
  }
}
