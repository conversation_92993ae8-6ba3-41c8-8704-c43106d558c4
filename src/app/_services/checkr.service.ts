import { Injectable } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/auth';
import axios from 'axios';
import { environment } from 'src/environments/environment';
import { SudsterData } from '../_interfaces/sudster-data.interface';

enum CheckrEvents {
  create = 'candidates',
  invite = 'invitations',
}
@Injectable({
  providedIn: 'root',
})
export class CheckrService {
  data: SudsterData;
  token: string;
  headers = {
    'Content-Type': 'application/json',
  };
  auth = {
    username: environment.checkr.apiKey,
    password: '',
  };

  constructor(private afAuth: AngularFireAuth) {
    this.afAuth.idToken.subscribe((token) => {
      if (token) {
        this.token = token;
      }
    });
  }

  async create(data: SudsterData, id: string): Promise<Response> {
    if (!this.token) {
      return;
    }
    const { FirstName, LastName, ContactEmail, Phone, Zipcode, State, City } =
      data;
    const body = {
      first_name: FirstName ? FirstName : '',
      last_name: LastName ? LastName : '',
      email: ContactEmail ? ContactEmail : '',
      phone: Phone ? Phone : '',
      zipcode: Zipcode ? Zipcode : 'Zipcode not found',
      work_locations: [
        {
          country: 'US',
          state: State ? State : '',
          city: City ? City : '',
        },
      ],
      metadata: {
        UserID: id,
        OrderID: data.ReservedOrderId ? data.ReservedOrderId : '',
      },
    };
    const url = `${environment.checkr.endpoint}/${CheckrEvents.create}`;
    const response = await axios
      .post(url, body, {
        headers: this.headers,
        auth: this.auth,
      })
      .then((res) => {
        return res.data;
      })
      .catch((err) => {
        return err;
      });

    return response;
  }

  async invite(data, sudster: SudsterData): Promise<Response> {
    if (!this.token) {
      return;
    }
    const { State, City } = sudster;
    const body = {
      package: environment.checkr.package,
      candidate_id: data.id,
      work_locations: [
        {
          country: 'US',
          state: State,
          city: City,
        },
      ],
    };
    const url = `${environment.checkr.endpoint}/${CheckrEvents.invite}`;
    const response = await axios
      .post(url, body, {
        headers: this.headers,
        auth: this.auth,
      })
      .then((res) => {
        return res.data;
      })
      .catch((err) => {
        return err;
      });

    return response;
  }
}
