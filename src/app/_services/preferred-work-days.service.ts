import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import {
  PreferredWorkDaysResponse,
  PreferredWorkingDays,
} from '../_interfaces/preferred-work-days.service';
import { ApiService } from './api.service';

@Injectable({
  providedIn: 'root',
})
export class PreferredWorkDaysService {
  private readonly URL = 'PreferredWorkDays/v1/sudster/preferred-work-days';

  apiService = inject(ApiService);

  getPreferredWorkDays(): Observable<PreferredWorkingDays> {
    return this.apiService
      .get(this.URL)
      .pipe(map(({ data }) => (data as PreferredWorkDaysResponse).data));
  }

  updatePreferredWorkDays(
    dataToUpdate: PreferredWorkingDays
  ): Observable<PreferredWorkingDays> {
    return this.apiService
      .put(this.URL, dataToUpdate)
      .pipe(map(({ data }) => (data as PreferredWorkDaysResponse).data));
  }
}
