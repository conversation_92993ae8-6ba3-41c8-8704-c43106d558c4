import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { PayoutMethodData } from '../../_interfaces/submit-payout.interface';
import { ApiResponse, ApiResponseStatus, ApiService } from '../api.service';

@Injectable({
  providedIn: 'root',
})
export class PayoutSetupService {
  private readonly ENDPOINT_PATH =
    'SubmitPayoutMethod/v1/sudster/submit-payout-method';

  constructor(private apiService: ApiService) {}

  submitPayoutMethod(data: PayoutMethodData): Observable<void> {
    return this.apiService.post(this.ENDPOINT_PATH, data).pipe(
      map((apiResponse: ApiResponse) => {
        if (
          apiResponse.status !== ApiResponseStatus.ok &&
          apiResponse.status !== ApiResponseStatus.created &&
          apiResponse.status !== ApiResponseStatus.accepted &&
          apiResponse.status !== ApiResponseStatus.noContent
        ) {
          throw new Error(apiResponse.message);
        }
        return;
      })
    );
  }
}
