import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ApiResponse, ApiResponseStatus, ApiService } from '../api.service';
import { SubmitIdData } from '../../_interfaces/submit-id.interface';

@Injectable({
  providedIn: 'root',
})
export class SubmitIdService {
  private readonly ENDPOINT_PATH = 'SubmitId/v1/sudster/submit-id';

  constructor(private apiService: ApiService) {}

  /**
   * Submits ID data to the backend.
   * Expects a 2xx response; otherwise throws an Error containing apiResponse.message.
   */
  submitId(data: SubmitIdData): Observable<void> {
    return this.apiService.post(this.ENDPOINT_PATH, data).pipe(
      map((apiResponse: ApiResponse) => {
        // Supondo que o ApiService retorne um objeto ApiResponse com status e message
        if (
          apiResponse.status !== ApiResponseStatus.ok &&
          apiResponse.status !== ApiResponseStatus.created &&
          apiResponse.status !== ApiResponseStatus.noContent
        ) {
          throw new Error(apiResponse.message);
        }
        return; // no return data needed, so we return void
      })
    );
  }
}
