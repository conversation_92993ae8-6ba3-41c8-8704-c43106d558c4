import { Injectable } from '@angular/core';
import { AngularFirestore } from '@angular/fire/firestore';
import { FCM } from '@capacitor-community/fcm';
import {
  ActionPerformed,
  PushNotifications,
  Token,
} from '@capacitor/push-notifications';
import { Al<PERSON><PERSON>ontroller, ToastController } from '@ionic/angular';
import firebase from 'firebase';
import moment from 'moment';
import { FCM2 } from '../_interfaces/sudster-data.interface';
import { DeepLinkService } from './deep-link.service';
import { IterableService } from './iterable/iterable.service';
import { trackEvent } from 'src/app/_utils/track-event';
import { AuthidService } from 'src/app/_services/authid.service';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { Router } from '@angular/router';

interface PoplinNotificationPayload {
  type: string;
  data: string;
}

interface IterableObjectParams {
  defaultAction: Record<string, any>;
  isGhostPush: boolean;
  messageId: string;
  templateId: number;
  campaignId: number;
}

type OrderTimestampedNotification = {
  timestamp: string;
  orderNumber: string;
};
type PushNotificationInternalPayload = {
  data: OrderTimestampedNotification;
  type: string;
};

interface IterableNotification {
  itbl: IterableObjectParams | string;
  url: string;
}

type PushNotificationPayload = PoplinNotificationPayload | IterableNotification;

const MONTH_IN_MILLIS = 1000 * 60 * 60 * 24 * 30;
const THREE_MONTHS_IN_MILLIS = 3 * MONTH_IN_MILLIS;
const TOKEN_LIFESPAN = THREE_MONTHS_IN_MILLIS;

enum NotificationTypes {
  firstOrder = 'firstOrder',
  newOrder = 'newOrder',
  bonusOrder = 'bonusOrder',
  orderStatus = 'orderStatus',
  unacceptedOrder = 'unacceptedOrder',
  rating = 'rating',
  tipping = 'tipping',
  customerReferrals = 'customerReferrals',
  chatMessages = 'chatMessages',
}

@Injectable({
  providedIn: 'root',
})
@UntilDestroy()
export class NotificationService {
  private sudsterId: string;
  private sudsterFCM2: Array<FCM2>;
  private platformType: 'android' | 'ios' | 'web';
  private mobileAppVersion: string;
  private mobileDeviceId: string;
  private userId: string;

  public setVariables(
    type: 'android' | 'ios' | 'web',
    version: string,
    deviceId: string,
    id: string,
    fcm: Array<FCM2>
  ) {
    this.platformType = type;
    this.mobileAppVersion = version;
    this.mobileDeviceId = deviceId;
    this.sudsterId = id;
    this.sudsterFCM2 = fcm;
  }

  constructor(
    private firestore: AngularFirestore,
    private alertController: AlertController,
    private toastController: ToastController,
    private deepLinkService: DeepLinkService,
    private iterableService: IterableService,
    private authIdService: AuthidService,
    private router: Router
  ) {
    authIdService.userID$.pipe(untilDestroyed(this)).subscribe((userId) => {
      this.userId = userId;
    });
  }

  init() {
    this.registerNotificationPermission();
  }

  private async registerNotificationPermission() {
    PushNotifications.requestPermissions().then((res) => {
      if (res.receive === 'granted') {
        this.processNotificationToken();
        this.processNotification();
      } else {
        this.presentAlertMessage(
          'Notification Error',
          'It looks like you have blocked notifications on your device. Please allow notifications from your device settings.'
        );
      }
    });
  }

  private async processNotification() {
    // this listener handles PN from iterable pn, iterable in-app messages, poplin
    await PushNotifications.addListener(
      'pushNotificationActionPerformed',
      (res: ActionPerformed) => {
        const payload = res.notification.data as PushNotificationPayload;
        const poplinPushNotificationPayload =
          payload as PoplinNotificationPayload;
        let customData: Record<string, any> = {
          type: '',
        };

        try {
          this.trackPushOpened(payload, false);

          customData = JSON.parse(poplinPushNotificationPayload.data);
          customData.type = poplinPushNotificationPayload.type;
        } catch (error) {
          customData = payload as unknown as Record<string, any>;
        }

        const type = customData.type;
        switch (type) {
          case NotificationTypes.newOrder:
          case NotificationTypes.bonusOrder:
            this.trackSegmentPushNotificationEvent(
              customData as PushNotificationInternalPayload,
              'PushNotificationOpened',
              type
            );
            break;
          default:
            this.handleDeepLink(customData);
        }
      }
    );

    await PushNotifications.addListener(
      'pushNotificationReceived',
      (notification) => {
        const { data } = notification;
        if (data) {
          this.trackPushOpened(data as PushNotificationPayload, true);
          const jsonData = data.data;
          if (jsonData) {
            let customData: PushNotificationInternalPayload;
            try {
              customData = JSON.parse(
                jsonData
              ) as PushNotificationInternalPayload;
            } catch (error) {
              console.error(
                'An error occurred while trying to parse push notification json',
                jsonData,
                error
              );
            }
            if (customData) {
              const type = customData.type;
              if (
                type === NotificationTypes.newOrder ||
                type === NotificationTypes.bonusOrder
              ) {
                this.trackSegmentPushNotificationEvent(
                  customData,
                  'PushNotificationReceived',
                  type as NotificationTypes
                );
              } else if (type === NotificationTypes.chatMessages) {
                this.displayCustomerMessageToast(
                  notification.title,
                  notification.body,
                  customData.data.orderNumber
                );
                return; // Return early to avoid showing the default toast
              }
            }
          }
        }

        this.toastController
          .create({
            message: 'Loading...', // Temporary placeholder
            duration: 5000,
            cssClass: 'dark-toast',
            position: 'bottom',
            buttons: [
              {
                text: 'Close',
                role: 'cancel',
              },
            ],
          })
          .then((toast) => {
            toast.present().then(() => {
              // Modify the innerHTML of the toast message content after it's presented
              const toastMessageEl = document.querySelector(
                '.dark-toast .toast-message'
              );
              if (toastMessageEl) {
                toastMessageEl.innerHTML = `
                  ${notification.title}<br>
                  ${this.truncateString(notification.body, 50)}
                `;
              }
            });
          });
      }
    );
  }

  private handleDeepLink(customData) {
    const url = customData['url'] || customData['uri'];
    if (url) {
      this.deepLinkService.handleUrl(url);
    }
  }

  trackPushOpened(payload: PushNotificationPayload, appWasRunning: boolean) {
    // android=string, ios=object
    const iterablePayloadNotification = payload as IterableNotification;
    if (iterablePayloadNotification.itbl) {
      const itbl = this.getAsObject(iterablePayloadNotification.itbl);
      const templateId = itbl.templateId;
      const campaignId = itbl.campaignId;
      const messageId = itbl.messageId;

      this.iterableService.trackPush(
        templateId,
        campaignId,
        messageId,
        appWasRunning
      );
    }
  }

  private truncateString(str: string, num: number) {
    if (str.length > num) {
      return str.slice(0, num) + '...';
    } else {
      return str;
    }
  }

  private processNotificationToken() {
    PushNotifications.register();
    const FCM2 = this.sudsterFCM2 ?? [];

    if (this.platformType === 'android') {
      PushNotifications.addListener('registration', (token: Token) => {
        this.storeNotificationToken(token.value, FCM2);
        this.createNewOrderChannel();
      });
    } else {
      FCM.getToken().then((token) => {
        this.storeNotificationToken(token.token, FCM2);
      });
    }
  }

  private storeNotificationToken(currentToken: string, deviceData: FCM2[]) {
    const now = moment().utc();
    const updateDocPath = `Sudsters/${this.sudsterId}`;

    const tokenExists = deviceData.some(
      (x) =>
        x.token === currentToken &&
        x.deviceId === this.mobileDeviceId &&
        x.appInfo.appVersion === this.mobileAppVersion
    );

    if (tokenExists) {
      return;
    }

    deviceData.push({
      token: currentToken,
      deviceId: this.mobileDeviceId,
      timestamp: firebase.firestore.Timestamp.fromMillis(now.valueOf()),
      appInfo: {
        appVersion: this.mobileAppVersion,
        platform: this.platformType,
        timestamp: now.valueOf(),
        date: firebase.firestore.Timestamp.fromMillis(now.valueOf()),
      },
    });

    const recordsPerDevice = deviceData.reduce((map, records: FCM2) => {
      if (!map.has(records.deviceId)) {
        map.set(records.deviceId, []);
      }
      map.get(records.deviceId).push(records);
      return map;
    }, new Map<string, FCM2[]>());

    const cleanedFCM: FCM2[] = [];
    recordsPerDevice.forEach((deviceFCM2) => {
      deviceFCM2.sort(
        (a, b) => b.timestamp.toMillis() - a.timestamp.toMillis()
      );

      const latestFCM2 = deviceFCM2[0];
      const latestFCM2Date = latestFCM2.timestamp.toMillis();
      const timeSinceTokenCreation = now.valueOf() - latestFCM2Date;
      if (timeSinceTokenCreation < TOKEN_LIFESPAN) {
        cleanedFCM.push(deviceFCM2[0]);
      }
    });

    this.firestore
      .doc(updateDocPath)
      .set({ FCM2: cleanedFCM, FCM: currentToken }, { merge: true });
  }

  private createNewOrderChannel() {
    PushNotifications.listChannels().then(async (res) => {
      // check for id: fcm_channel_neworders
      let channelIdx = res.channels.findIndex(
        (x) => x.id === 'fcm_channel_neworders'
      );
      // If its the channel with wrong audio file, remove it
      if (
        channelIdx > -1 &&
        res.channels[channelIdx].sound.indexOf('orderNotificationSound') > -1
      ) {
        await PushNotifications.deleteChannel({ id: 'fcm_channel_neworders' });
        channelIdx = -1; // to create again with correct value.
      }

      if (channelIdx < 0) {
        PushNotifications.createChannel({
          description: 'New orders notification channel',
          id: 'fcm_channel_neworders',
          importance: 5,
          lights: true,
          name: 'New Orders channel',
          sound: 'ordernotificationsound.wav',
          vibration: true,
          visibility: 1,
        }).catch((e) => {
          console.error('Failed to create new order channel', e);
        });
      }

      const channel2Idx = res.channels.findIndex(
        (x) => x.id === 'fcm_channel_default'
      );

      if (channel2Idx < 0) {
        PushNotifications.createChannel({
          description: 'In app messages and other system notifications',
          id: 'fcm_channel_default',
          importance: 5,
          lights: true,
          name: 'New Messages and System',
          sound: 'poplindefault.wav',
          vibration: true,
          visibility: 1,
        }).catch((e) => {
          console.error('Failed to create new order channel', e);
        });
      }
    });
  }

  private async presentAlertMessage(title: string, message: string) {
    const alert = await this.alertController.create({
      header: title,
      message: message,
      buttons: ['OK'],
    });

    await alert.present();
  }

  private getAsObject(data: string | object): IterableObjectParams {
    if (!data) {
      return {} as IterableObjectParams;
    }

    if (typeof data === 'string') {
      try {
        return JSON.parse(data);
      } catch (error) {
        console.error(`Failed parsing string payload`, error);
        return {} as IterableObjectParams;
      }
    }

    return data as IterableObjectParams;
  }

  private async trackSegmentPushNotificationEvent(
    customData: PushNotificationInternalPayload,
    notificationType: 'PushNotificationReceived' | 'PushNotificationOpened',
    type: NotificationTypes
  ) {
    try {
      const token = this.getToken(this.mobileDeviceId);
      const timestamp = customData.data?.timestamp;
      const orderId = customData.data?.orderNumber;
      const event = {
        eventData: {
          event: notificationType,
          userId: this.userId,
          deviceId: this.mobileDeviceId,
          deviceTimestamp: new Date().toISOString(),
          notificationTimestamp: timestamp,
          orderId,
          token,
          type,
        },
      };
      await trackEvent(event);
      console.info(
        `Tracking ${notificationType} notification segment event`,
        event
      );
    } catch (error) {
      console.error('Failed to create event on segment', error);
    }
  }

  private getToken(deviceId: string): string {
    const fcms = this.sudsterFCM2;
    return fcms.find((fcm) => fcm.deviceId === deviceId)?.token;
  }

  private displayCustomerMessageToast(
    title: string,
    message: string,
    orderNumber: string
  ): void {
    this.toastController
      .create({
        header: title,
        message: message,
        duration: 6000,
        cssClass: 'dark-toast',
        position: 'bottom',
        buttons: [
          {
            text: 'View',
            role: 'info',
            handler: () => {
              this.router.navigate([
                '/active',
                { id: orderNumber, showChat: true },
              ]);
            },
          },
          {
            text: 'Close',
            role: 'cancel',
          },
        ],
      })
      .then((toast) => {
        toast.present();
      });
  }
}
