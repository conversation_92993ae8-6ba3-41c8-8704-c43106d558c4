import { AngularFirestore } from '@angular/fire/firestore';
import { <PERSON><PERSON><PERSON>roy } from '@ngneat/until-destroy';
import { StatsigClient, StatsigOptions, StatsigUser } from '@statsig/js-client';
import { lastValueFrom, Observable, ReplaySubject } from 'rxjs';
import { filter, map, mergeMap, take } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { SudsterData } from '../_interfaces/sudster-data.interface';
import { AuthidService } from './authid.service';

@UntilDestroy()
export class StatsigService {
  public Initialize$ = new ReplaySubject<boolean>(1);
  private client: StatsigClient;
  private statsigUser: StatsigUser = null;
  private version: string;

  constructor(
    private firestore: AngularFirestore,
    private AuthID: AuthidService,
    version: string
  ) {
    this.version = version;
    this.Initialize$.next(false);
    this.AuthID.userID$
      .pipe(
        filter((userId) => !!userId),
        take(1),
        mergeMap((userId) =>
          this.firestore.doc<SudsterData>(`Sudsters/${userId}`).get()
        )
      )
      .subscribe((userDoc) => {
        if (userDoc.exists) {
          this.initializeStatsig(userDoc.id, userDoc.data())
            .then(() => this.setStatsigInitialized())
            .catch((e) => {
              console.error('Error initializing statsig', e);
            });
        }
      });
  }

  async initializeStatsig(uid: string, user: SudsterData): Promise<void> {
    let state = '';
    if (user?.State) {
      const stateRegex = /[^,]*[A-Z]{2}/;
      const stateResult = user.State.match(stateRegex);
      if (stateResult?.length > 0) {
        state = stateResult[0].trim();
      }
    }

    this.statsigUser = { userID: uid, custom: { state } };
    await this.initializeClient();
  }

  private async initializeClient() {
    const clientConfigs = environment.statsig.clients;
    const config = clientConfigs.find((c) => c.version === this.version);

    if (!config) {
      throw new Error(`No Statsig config found for version ${this.version}`);
    }

    const options: StatsigOptions = {
      environment: { tier: environment.production ? 'Prod' : 'NonProd' },
    };

    this.client = new StatsigClient(
      config.clientKey,
      this.statsigUser,
      options
    );
    await this.client.initializeAsync();
  }

  setStatsigInitialized() {
    this.Initialize$.next(true);
  }

  public checkGate(gateName: string): Observable<boolean> {
    return this.Initialize$.pipe(
      filter((initialized) => initialized === true),
      take(1),
      map(() => this.client.checkGate(gateName))
    );
  }

  public checkGates<T extends Record<string, boolean>>(
    flags: Record<string, string>
  ): Observable<T> {
    return this.Initialize$.pipe(
      filter((initialized) => initialized),
      take(1),
      map(() => {
        const rawResult: Record<string, boolean> = {};

        for (const key in flags) {
          const gateId = flags[key];
          rawResult[key] = this.client.checkGate(gateId);
        }

        return rawResult as T;
      })
    );
  }

  public getConfig(configName: string) {
    return this.client.getDynamicConfig(configName);
  }

  getConfigPromise(configName: string): Promise<any> {
    return lastValueFrom(
      this.Initialize$.pipe(
        filter((initialized) => initialized === true),
        take(1),
        map(() => this.client.getDynamicConfig(configName))
      )
    );
  }

  async shutdownPromisify(): Promise<void> {
    await lastValueFrom(
      this.Initialize$.pipe(
        take(1),
        map((value) => {
          if (value) {
            this.client.shutdown();
            this.Initialize$.next(false);
          }
        })
      )
    );
  }

  shutdown() {
    this.client?.shutdown();
    this.Initialize$.next(false);
  }

  public logEvent(eventName: string, value: any, metadata?: any): void {
    this.Initialize$.pipe(
      filter((initialized) => initialized === true),
      take(1),
      map(() => this.client.logEvent(eventName, value, metadata))
    ).subscribe();
  }
}
