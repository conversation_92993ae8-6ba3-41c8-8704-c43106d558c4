import { Injectable } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/auth';
import { AngularFirestore } from '@angular/fire/firestore';
import { AngularFireFunctions } from '@angular/fire/functions';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import moment from 'moment';
import { Observable, of, ReplaySubject } from 'rxjs';
import { distinct, first, mergeMap } from 'rxjs/operators';
import { SudsterData } from '../_interfaces/sudster-data.interface';
import { AuthidService } from './authid.service';

@UntilDestroy()
@Injectable({
  providedIn: 'root',
})
export class GetSudsterDataService {
  private SudsterDoc$: Observable<SudsterData> = new ReplaySubject<SudsterData>(
    1
  );
  UserID = this.AuthID.getID();

  constructor(
    public firestore: AngularFirestore,
    public afAuth: AngularFireAuth,
    private AuthID: AuthidService,
    private cloudFunctions: AngularFireFunctions
  ) {
    this.AuthID.userID$
      .pipe(
        untilDestroyed(this),
        distinct(),
        mergeMap((userId) => {
          if (!userId) {
            return of(null);
          }

          // mergeMap handles the subscription of the valueChanges() observable inside
          return this.firestore
            .doc<SudsterData>(`Sudsters/${userId}`)
            .valueChanges();
        })
      )
      .subscribe((sudster) => {
        (this.SudsterDoc$ as ReplaySubject<SudsterData>).next(sudster);
      });
  }

  listenForSudster() {
    return this.SudsterDoc$;
  }

  getOnce(): Promise<SudsterData> {
    return new Promise((result, reject) => {
      this.SudsterDoc$.pipe(first()).subscribe((doc) => {
        result(doc);
      });
    });
  }

  checkChatCredits() {
    this.getOnce().then((sudster) => {
      const lastCheckDate = sudster.ChatCreditsLastUpdate || 0;
      const lastMonth = moment().subtract(1, 'months');

      if (moment(lastCheckDate).isSameOrBefore(lastMonth)) {
        this.cloudFunctions
          .httpsCallable('SudsterV3_CheckChatCredits')({})
          .subscribe((res) => {});
      }
    });
  }
}
