import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class SignUpStepsService {
  private skippableStates = ['CA'];

  private state: string;
  private skipped: boolean;

  setState(state: string) {
    this.state = state;
  }

  canSkip(): boolean {
    return this.skippableStates.includes(this.state);
  }

  skip(): void {
    this.skipped = true;
  }

  undoSkip(): void {
    this.skipped = false;
  }

  isSkipped(): boolean {
    return this.skipped;
  }
}
