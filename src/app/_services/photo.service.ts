import { Injectable } from '@angular/core';
import {
  Camera,
  CameraDirection,
  CameraResultType,
  CameraSource,
  Photo,
} from '@capacitor/camera';
import { Capacitor } from '@capacitor/core';
import { Directory, Filesystem, WriteFileResult } from '@capacitor/filesystem';
import { Preferences } from '@capacitor/preferences';
import { Platform } from '@ionic/angular';
import { BehaviorSubject } from 'rxjs';
import {
  base64ToFile,
  convertBlobToBase64,
  resizeImageWeb,
} from '../_utils/files';
import { FileService, StartUploadResponse } from './file.service';

type PhotoUploadState = 'started' | 'uploading' | 'complete' | 'error';

@Injectable({
  providedIn: 'root',
})
export class PhotoService {
  private readonly isNativeDevice: boolean;
  private PHOTO_STORAGE: string = 'photos';
  private platform: Platform;

  photoUploadState$ = new BehaviorSubject<{
    photoFileId: string;
    uploadState: PhotoUploadState;
  } | null>(null);

  constructor(platform: Platform, private fileService: FileService) {
    this.platform = platform;
    this.isNativeDevice = this.platform.is('hybrid');
  }

  async takePhoto(path: string) {
    const image: Photo = await Camera.getPhoto({
      resultType: CameraResultType.Uri,
      source: CameraSource.Camera,
      width: 1000,
      height: 1000,
      quality: 70,
      allowEditing: false,
      direction: CameraDirection.Rear,
    }).catch((err: unknown) => {
      console.log('Camera issue ->', err);
      return undefined;
    });

    const fileName = new Date().getTime() + '.jpeg';

    const photoFilePath = `${path}/${fileName}`;
    const localPhotoUrl = await this.savePhotoToDeviceStorage(
      image,
      photoFilePath
    );

    return {
      localPhotoUrl,
      photoFilePath,
    };
  }

  private async readAsBase64(photo: Photo): Promise<string> {
    if (this.isNativeDevice) {
      const file = await Filesystem.readFile({
        path: photo.path,
      });

      return file.data.toString();
    }

    const response = await fetch(photo.webPath);
    const blob = await response.blob();
    const base64Image = await convertBlobToBase64(blob);

    return await resizeImageWeb(base64Image, 800, 800);
  }

  private async savePhotoToDeviceStorage(
    photo: Photo,
    path: string
  ): Promise<string> {
    const base64Data = await this.readAsBase64(photo);

    if (base64Data) {
      const savedFile: WriteFileResult = await Filesystem.writeFile({
        path: path,
        data: base64Data,
        directory: Directory.Data,
        recursive: true,
      }).catch((err) => err);

      await Preferences.set({
        key: `${this.PHOTO_STORAGE}/${path}`,
        value: JSON.stringify(photo),
      }).catch((err) => err);

      if (this.isNativeDevice) {
        const photoUrl = Capacitor.convertFileSrc(savedFile.uri);
        return photoUrl;
      }

      return photo.webPath;
    }
  }

  public async loadSavedPhoto(photoPath: string): Promise<File> {
    const readFile = await Filesystem.readFile({
      path: photoPath,
      directory: Directory.Data,
    }).catch((err) => {
      return err;
    });

    const webPath: string = `data:image/jpeg;base64,${readFile.data}`;

    return base64ToFile(webPath, 'photo.jpeg');
  }

  public async removeSavedPhoto(
    path: string,
    photoFileId: string
  ): Promise<void> {
    Preferences.remove({ key: `${this.PHOTO_STORAGE}/${path}` }).catch(
      (err) => {
        return err;
      }
    );

    if (photoFileId) {
      this.fileService.clearFileUpload(photoFileId);
    }

    return await Filesystem.deleteFile({
      path,
      directory: Directory.Data,
    }).catch((err) => {
      return err;
    });
  }

  public async uploadPhoto(
    localPath: string
  ): Promise<{ photoFile: File; fileId: string }> {
    const photoFile = await this.loadSavedPhoto(localPath);

    const startUploadResponse = await (
      await this.fileService.startUpload(photoFile.name, photoFile.size)
    ).toPromise();

    const { fileId } = (await startUploadResponse.data) as StartUploadResponse;

    // Do not await this, as we want to return the fileId immediately
    this.fileService.uploadFile(fileId, photoFile);

    this.photoUploadState$.next({
      photoFileId: fileId,
      uploadState: 'started',
    });

    this.subscribeFileUploadState(fileId);

    return { photoFile, fileId };
  }

  public subscribeFileUploadState(uploadingFileId: string) {
    let completed = false;

    this.fileService.uploadComplete$.subscribe((fileId) => {
      if (fileId === uploadingFileId) {
        this.photoUploadState$.next({
          photoFileId: uploadingFileId,
          uploadState: 'complete',
        });

        completed = true;
      }
    });

    setTimeout(() => {
      if (!completed) {
        this.fileService.cancelUpload(uploadingFileId);
      }
    }, 15000);

    this.fileService.uploadError$.subscribe((error) => {
      if (
        error &&
        error.fileId === uploadingFileId &&
        error.type === 'upload-failed'
      ) {
        this.photoUploadState$.next({
          photoFileId: uploadingFileId,
          uploadState: 'error',
        });
      }
    });
  }

  public photoHasAlreadyBeenUploaded(photoId: string): boolean {
    return this.fileService.isFileUploadComplete(photoId);
  }
}
