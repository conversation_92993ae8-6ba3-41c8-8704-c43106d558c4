import { <PERSON>rror<PERSON><PERSON><PERSON>, Inject, Injectable } from '@angular/core';
import Rollbar from 'rollbar';
import { AuthidService } from '../authid.service';
import { GetSudsterDataService } from '../get-sudster-data.service';
import { RollbarService } from '../rollbar/rollbar.service';

// overlay error message when using toast
const ignoreErrors = new Set<string>(['overlay does not exist']);

@Injectable({
  providedIn: 'root',
})
export class ErrorHandlingService implements ErrorHandler {
  constructor(
    @Inject(RollbarService) private rollbar: Rollbar,
    @Inject(GetSudsterDataService)
    private sudsterService: GetSudsterDataService,
    @Inject(AuthidService) private AuthID: AuthidService
  ) {}

  handleError(err: any): void {
    if (ignoreErrors.has(err)) {
      return;
    }
    console.error('err', err);
    this.sudsterService.getOnce().then((sudster) => {
      this.rollbar.configure({
        payload: {
          person: {
            phone: sudster?.Phone || 'N/A',
            fullname: `${sudster?.FirstName || 'N/A'} ${
              sudster?.LastName || 'N/A'
            }`,
            id: this.AuthID.getID() ?? 'N/A',
            email: sudster?.ContactEmail || 'N/A',
            username: sudster?.ContactEmail || 'N/A',
          },
        },
      });
      this.rollbar.error(err);
    });
  }
}
