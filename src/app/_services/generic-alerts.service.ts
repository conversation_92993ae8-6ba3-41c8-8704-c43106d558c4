import { inject, Injectable } from '@angular/core';
import { from, Observable } from 'rxjs';
import { AlertController } from '@ionic/angular';

@Injectable({
  providedIn: 'root',
})
export class GenericAlertsService {
  currentFormData = null;
  private alertController = inject(AlertController);

  private alertButtons = [
    {
      text: 'SAVE CHANGES',
      role: 'submit',
      handler: async () => {
        await this.alertController.dismiss({
          data: this.currentFormData,
          role: 'submit',
        });
      },
    },
    {
      text: 'LEAVE WITHOUT SAVING',
      role: 'cancel',
      handler: async () => {
        await this.alertController.dismiss({ data: undefined, role: 'cancel' });
      },
    },
  ];

  constructor() {}

  showUnsavedChangesAlert(data: any): Observable<{ data: any; role: string }> {
    this.currentFormData = data;

    this.alertController.dismiss().then();

    return from(
      this.alertController
        .create({
          cssClass: 'poplin-alert',
          header: 'You have unsaved changes',
          message:
            'You have unsaved changes on this page. <br>If you leave now, your changes will be lost.',
          buttons: this.alertButtons,
        })
        .then(async (alert) => {
          await alert.present();
          return (await alert.onDidDismiss()) as any;
        })
    );
  }
}
