import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { BagSize, OrderData } from '../_interfaces/order-data.interface';
import { StatsigFactoryService } from './statsig-factory.service';
import { StatsigService } from './statsig.service';

@Injectable({
  providedIn: 'root',
})
export class OrderExtensionService {
  isEnabled = true;
  baseOffDeadline = true;
  largeBags = 3;
  regularBags = 7;
  smallBags = 9;
  bagDiff = 2;
  orderExtensionLimit = 4;

  private statsigService: StatsigService;

  constructor(private statsigFactoryService: StatsigFactoryService) {
    this.statsigService = this.statsigFactoryService.getInstance();
    this.statsigService.Initialize$.asObservable().subscribe((val) => {
      if (val) {
        this.setConfigValues();
      }
    });
  }

  public async canExtendOrder(order: OrderData): Promise<boolean[]> {
    return new Promise((resolve) => {
      if (!this.isEnabled || order.SameDayService) {
        resolve([false, false]);
      }

      const timeLimit = this.orderExtensionLimit * 3600000; //milliseconds in an hour
      const currentTime = Date.now();
      const deadline =
        this.baseOffDeadline ||
        order.PickupDeadline < order.StatusHistoryInfo.Pickup.UnixTime
          ? order.PickupDeadline
          : order.StatusHistoryInfo.Pickup.UnixTime;

      if (currentTime > timeLimit + deadline && !order.OrderExtensionReasons) {
        resolve([false, false]);
      }

      const isOverweightOrder =
        (order.BagSize === BagSize.Small &&
          order.OrderSize >= this.smallBags) ||
        (order.BagSize === BagSize.Regular &&
          order.OrderSize >= this.regularBags) ||
        (order.BagSize === BagSize.OverSized &&
          order.OrderSize >= this.largeBags) ||
        (order.BagSize === BagSize.Large && order.OrderSize >= this.largeBags);

      const isBagDiscrepancy =
        order.OrderSize - order.StatusHistoryInfo.Pickup.BagCount <=
        -this.bagDiff;

      resolve([isOverweightOrder, isBagDiscrepancy]);
    });
  }

  public async canExtendAvailableOrder(
    bagSize: string,
    bagCount: number,
    sameDayService: boolean
  ): Promise<boolean> {
    return new Promise((resolve) => {
      if (!this.isEnabled || sameDayService) {
        resolve(false);
      }

      const isOverweightOrder =
        (bagSize === BagSize.Small && bagCount >= this.smallBags) ||
        (bagSize === BagSize.Regular && bagCount >= this.regularBags) ||
        (bagSize === BagSize.OverSized && bagCount >= this.largeBags) ||
        (bagSize === BagSize.Large && bagCount >= this.largeBags);

      resolve(isOverweightOrder);
    });
  }

  private setConfigValues(): void {
    this.statsigService
      .checkGate(environment.statsig.flags.overweightOrderExtension)
      .subscribe((val) => {
        this.isEnabled = val;
      });
    const config = this.statsigService.getConfig(
      environment.statsig.dynamic.orderExtensionBagCount
    );
    this.largeBags = config.get('largeBags', 3);
    this.regularBags = config.get('regularBags', 7);
    this.smallBags = config.get('smallBags', 9);
    this.bagDiff = config.get('bagDiff', 2);
    this.orderExtensionLimit = config.get('orderExtensionLimit', 4);
    this.baseOffDeadline = config.get('baseOffDeadline', true);
  }
}
