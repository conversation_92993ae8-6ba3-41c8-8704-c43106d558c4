import { Injectable } from '@angular/core';
import { AngularFireFunctions } from '@angular/fire/functions';
import {
  ImageAnnotationOptions,
  MarkerAnnotationOptions,
} from 'src/app/_interfaces/mapkit/annotation.interface';
import { MapkitRoute } from '../_interfaces/mapkit/route.interface';

declare const mapkit: any;

@Injectable({
  providedIn: 'root',
})
export class MapService {
  mapData = {
    CenterLat: 0,
    CenterLng: 0,

    TotalLat: 0,
    TotalLng: 0,
    AvgLatCount: 0,
    AvgLngCount: 0,

    LongestLon: 0.01,
    LongestLat: 0.01,
  };

  map: any;

  constructor(private cloudFunctions: AngularFireFunctions) {
    this.initMapkit();
  }

  initMapkit() {
    mapkit.init({
      authorizationCallback: (done) => {
        return this.cloudFunctions
          .httpsCallable('SudsterV3_GenerateMapKitKey')({})
          .toPromise()
          .then((res) => {
            done(res);
          });
      },
      language: 'en',
    });
  }

  createMap(
    mapId = 'map',
    center,
    showsUserLocation = true,
    tracksUserLocation = true,
    mapType = mapkit.Map.MapTypes.MutedStandard,
    showsPointsOfInterest = false
  ) {
    const map = new mapkit.Map(mapId, {
      center,
      showsUserLocation,
      tracksUserLocation,
      region: new mapkit.CoordinateRegion(
        center,
        new mapkit.CoordinateSpan(0.1, 0.1)
      ),
      mapType,
      showsPointsOfInterest,
    });

    map.annotationForCluster = this.annotationForCluster;

    return map;
  }

  addImageAnnotation(
    center: any,
    map = this.map,
    annotationOptions: ImageAnnotationOptions = { title: '' }
  ) {
    const annotation = new mapkit.ImageAnnotation(center, annotationOptions);
    map.addAnnotation(annotation);
  }

  addMarkerAnnotation(
    center: any,
    map = this.map,
    annotationOptions: MarkerAnnotationOptions = { title: '' }
  ) {
    const annotation = new mapkit.MarkerAnnotation(center, annotationOptions);
    return map.addAnnotation(annotation);
  }

  clearMapAnnotations(map = this.map, filterList: string[]) {
    map.annotations.forEach((annotation) => {
      if (filterList.indexOf(annotation.clusteringIdentifier) !== -1) {
        map.removeAnnotation(annotation);
      }
    });
  }

  getDirections(originLat, originLng, destLat, destLng) {
    const origin = new mapkit.Coordinate(originLat, originLng);
    const destination = new mapkit.Coordinate(destLat, destLng);

    return new Promise((resolve, reject) => {
      const myDirections = new mapkit.Directions();
      myDirections.route(
        {
          origin,
          destination,
          transportType: mapkit.Directions.Transport.Automobile,
        },
        (error, data) => {
          if (Array.isArray(data['routes'])) {
            if (data['routes'].length > 0) {
              const route: MapkitRoute = data['routes'][0];
              resolve(route);
            }
          } else {
            console.error('Directions Error', { error });
            reject({ error, message: 'Error when getting directions' });
          }
        }
      );
    });
  }

  annotationForCluster(clusterAnnotation, center) {
    clusterAnnotation.addEventListener('select', (event) => {
      let LongestClusterLat = 0.01;
      let LongestClusterLon = 0.01;
      const cluster = event.target;
      cluster.memberAnnotations.forEach((element) => {
        const DistDiff = this.getCoordDistance(
          cluster.coordinate.latitude,
          cluster.coordinate.longitude,
          element.coordinate.latitude,
          element.coordinate.longitude
        );

        if (DistDiff.lon > LongestClusterLon) {
          LongestClusterLon = DistDiff.lon;
        }
        if (DistDiff.lat > LongestClusterLat) {
          LongestClusterLat = DistDiff.lat;
        }
      });
      this.CenterMap(this.map, this.mapData);
    });

    if (clusterAnnotation.clusteringIdentifier === 'Orders') {
      clusterAnnotation.glyphText =
        '+' + clusterAnnotation.memberAnnotations.length;
      let OrderNames = '';
      let TotalBonus = 0;
      clusterAnnotation.memberAnnotations.forEach((Order) => {
        OrderNames += Order.title + ', ';
        if (Order.subtitle?.includes('$')) {
          const Bonus = Order.subtitle.replace(/^\D+/g, '');
          TotalBonus += parseInt(Bonus, 10);
        }
      });
      clusterAnnotation.subtitle = OrderNames.slice(0, -2);
      if (TotalBonus > 0) {
        clusterAnnotation.title = `$${TotalBonus} BOOST`;
        clusterAnnotation.color = '#ff00d0';
      } else {
        clusterAnnotation.title = '';
        clusterAnnotation.color = '#00d0ff';
      }
    } else if (clusterAnnotation.clusteringIdentifier === 'Active') {
      clusterAnnotation.glyphText =
        '+' + clusterAnnotation.memberAnnotations.length;
      let OrderNames = '';
      clusterAnnotation.memberAnnotations.forEach((Order) => {
        OrderNames += Order.title + ', ';
      });
      clusterAnnotation.subtitle = OrderNames.slice(0, -2);
      clusterAnnotation.title = '';
      clusterAnnotation.color = '#ffb700';
    }
  }

  getCoordDistance(lat1, lon1, lat2, lon2) {
    return { lat: Math.abs(lat1 - lat2), lon: Math.abs(lon1 - lon2) };
  }

  SetAvgCenter(mapData, lat, lng) {
    mapData.TotalLat += lat;
    mapData.TotalLng += lng;
    mapData.AvgLatCount++;
    mapData.AvgLngCount++;
    mapData.CenterLat = mapData.TotalLat / mapData.AvgLatCount;
    mapData.CenterLng = mapData.TotalLng / mapData.AvgLngCount;
  }

  CenterMap(map, mapData) {
    const MapCenter = new mapkit.Coordinate(
      mapData.CenterLat,
      mapData.CenterLng
    );
    map.center = MapCenter;
    map.region = new mapkit.CoordinateRegion(
      MapCenter,
      new mapkit.CoordinateSpan(mapData.LongestLat * 2, mapData.LongestLon * 2)
    );
  }

  AddWorkAreaOverlay(
    map,
    lat,
    lng,
    workMiles,
    overlayStyle = {
      lineWidth: 1,
      strokeColor: '#78828c',
      fillColor: '',
    }
  ) {
    const overlay = new mapkit.CircleOverlay(
      new mapkit.Coordinate(lat, lng),
      workMiles * 1609.34
    );
    overlay.style = new mapkit.Style(overlayStyle);
    map.addOverlay(overlay);
  }

  static getDistanceinMiles(lat1, lon1, lat2, lon2) {
    function deg2rad(deg) {
      return deg * (Math.PI / 180);
    }
    const R = 6371; // Radius of the earth in km
    const dLat = deg2rad(lat2 - lat1); // deg2rad below
    const dLon = deg2rad(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(deg2rad(lat1)) *
        Math.cos(deg2rad(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = Math.atan2(Math.sqrt(a), Math.sqrt(1 - a)) * 2;
    const d = R * c; // Distance in km
    const dm = d * 0.621371; // Distance in miles
    return dm;
  }
}
