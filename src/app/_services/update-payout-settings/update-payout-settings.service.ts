import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ApiResponse, ApiResponseStatus, ApiService } from '../api.service';
import { UpdatePayoutSettingsData } from '../../_interfaces/update-payout-settings.interface';

@Injectable({
  providedIn: 'root',
})
export class UpdatePayoutSettingsService {
  // Use the new endpoint path defined in your route constants.
  private readonly ENDPOINT_PATH =
    'UpdatePayoutSettings/v1/sudster/update-payout-settings';

  constructor(private apiService: ApiService) {}

  updatePayoutSettings(data: UpdatePayoutSettingsData): Observable<void> {
    return this.apiService.post(this.ENDPOINT_PATH, data).pipe(
      map((apiResponse: ApiResponse) => {
        if (
          apiResponse.status !== ApiResponseStatus.ok &&
          apiResponse.status !== ApiResponseStatus.created &&
          apiResponse.status !== ApiResponseStatus.noContent
        ) {
          throw new Error(apiResponse.message);
        }
        return;
      })
    );
  }
}
