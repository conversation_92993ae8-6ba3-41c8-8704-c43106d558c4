import { Injectable } from '@angular/core';
import { Capacitor } from '@capacitor/core';
import { ZendeskMessaging } from '@sudshare/capacitor-zendesk';
import { BehaviorSubject } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { LegacyApiService } from './legacy-api.service';

const ZENDESK_ENDPOINT = 'ThirdPartyAuth/v1/zendesk';

@Injectable({
  providedIn: 'root',
})
export class ZendeskService {
  private isLoggedIn = new BehaviorSubject<boolean>(false);
  public getIsLoggedIn = this.isLoggedIn.asObservable();

  constructor(private readonly apiService: LegacyApiService) {}

  init(): void {
    switch (Capacitor.getPlatform()) {
      case 'ios':
        ZendeskMessaging.initialize({
          channelKey: environment.zendesk.iosKey,
          enableLogs: false,
        })
          .then((result) => {
            return this.getLPToken().subscribe(async (data) => {
              const jwt = (await data) as any;
              if (jwt?.data?.jwt) {
                this.login(jwt.data.jwt);
              } else {
                this.isLoggedIn.next(true);
              }
            });
          })
          .catch(() => {
            this.isLoggedIn.next(true);
          });
        break;
      case 'android':
        ZendeskMessaging.initialize({
          channelKey: environment.zendesk.androidKey,
          enableLogs: false,
        })
          .then(() =>
            this.getLPToken().subscribe(async (data) => {
              const jwt = (await data) as any;
              if (jwt?.data?.jwt) {
                this.login(jwt.data.jwt);
              } else {
                this.isLoggedIn.next(true);
              }
            })
          )
          .catch(() => {
            this.isLoggedIn.next(true);
          });
        break;
      default:
        ZendeskMessaging.initialize({
          channelKey: environment.zendesk.webKey,
        })
          .then(() =>
            this.getLPToken().subscribe(async (data) => {
              const jwt = (await data) as any;
              if (jwt?.data?.jwt) {
                setTimeout(() => {
                  this.login(jwt.data.jwt);
                }, 3000);
              } else {
                this.isLoggedIn.next(true);
              }
            })
          )
          .catch(() => {
            this.isLoggedIn.next(true);
          });
        break;
    }
  }

  login(userToken: string): void {
    ZendeskMessaging.userLogin({ userJwt: userToken });
    this.isLoggedIn.next(true);
  }

  logout(): void {
    ZendeskMessaging.userLogout();
  }

  show(): void {
    ZendeskMessaging.showMessaging();
  }

  getUnreadCount(): Promise<number> {
    return ZendeskMessaging.getUnreadCount();
  }

  private getLPToken() {
    return this.apiService.get(ZENDESK_ENDPOINT).pipe(map((res) => res.data));
  }
}
