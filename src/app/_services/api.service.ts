import { Injectable } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/auth';
import { UntilDestroy } from '@ngneat/until-destroy';
import { from, Observable, of } from 'rxjs';
import { fromFetch } from 'rxjs/fetch';
import { catchError, concatMap, map, switchMap, take } from 'rxjs/operators';
import { environment } from '../../environments/environment';

export enum ApiResponseStatus {
  ok = '200',
  created = '201',
  accepted = '202',
  noContent = '204',
  badRequest = '400',
  unauthenticated = '401',
  notFound = '404',
  serverError = '500',
}

export const validStatus = [
  ApiResponseStatus.ok,
  ApiResponseStatus.noContent,
  ApiResponseStatus.created,
  ApiResponseStatus,
];

export class ApiResponse {
  constructor(
    public status: ApiResponseStatus,
    public data: Record<string, any> | string | null = null,
    public message: string = '',
    public rawResponse?: Response
  ) {}
}

export class ApiResponseError extends ApiResponse implements Error {
  name: string;
  stack?: string;

  constructor(status: ApiResponseStatus, data: any, message: string) {
    super(status, data, message);
    this.name = status;
  }
}

@UntilDestroy()
@Injectable({
  providedIn: 'root',
})
export class ApiService {
  private readonly baseEndpoint: string = environment.baseEndpoint;

  constructor(private afAuth: AngularFireAuth) {}

  private getHeaders(): Observable<Headers> {
    return this.afAuth.idToken.pipe(
      take(1),
      map((idToken) => {
        if (!idToken) {
          throw new ApiResponseError(
            ApiResponseStatus.unauthenticated,
            null,
            'User is not authenticated'
          );
        }
        return new Headers({
          'Content-Type': 'application/json',
          Authorization: `Bearer ${idToken}`,
        });
      })
    );
  }

  private handleResponse(response: Response): Observable<ApiResponse> {
    return from(response.json().catch(() => null)).pipe(
      map((data) => {
        const { status } = response;

        const message = data?.message || response.statusText;
        const errorMessage =
          data?.error || response.statusText || 'An error occurred';
        const responseStatus = ApiResponseStatus[status] || status.toString();
        const isValidStatus =
          responseStatus in validStatus || (status >= 200 && status < 300);

        return new ApiResponse(
          responseStatus,
          data,
          isValidStatus ? message : errorMessage,
          response
        );
      })
    );
  }

  private handleError(err: any): Observable<ApiResponse> {
    if (err instanceof ApiResponseError) {
      return of(err);
    }

    // just in case
    // network errors
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      return of(
        new ApiResponseError(
          ApiResponseStatus.serverError,
          null,
          'Network error: Unable to connect to the server'
        )
      );
    }

    // timeout errors
    if (err.name === 'TimeoutError') {
      return of(
        new ApiResponseError(
          ApiResponseStatus.serverError,
          null,
          'Request timed out'
        )
      );
    }

    return of(
      new ApiResponseError(
        ApiResponseStatus.serverError,
        null,
        err.message || 'An unexpected error occurred'
      )
    );
  }

  private request(
    method: 'GET' | 'POST' | 'PATCH' | 'PUT' | 'DELETE',
    endpointPath: string,
    payload?: Record<string, any> | FormData
  ): Observable<ApiResponse> {
    return this.getHeaders().pipe(
      concatMap((headers) => {
        const options: RequestInit = {
          method,
          headers,
        };

        if (payload) {
          if (payload instanceof FormData) {
            // remove header in formData to let the browser set it
            headers.delete('Content-Type');
            options.body = payload;
          } else {
            options.body = JSON.stringify(payload);
          }
        }

        return fromFetch(this.baseEndpoint + endpointPath, options);
      }),
      switchMap((response) => this.handleResponse(response)),
      catchError((err) => this.handleError(err))
    );
  }

  public post(
    endpointPath: string,
    payload: Record<string, any> | FormData
  ): Observable<ApiResponse> {
    return this.request('POST', endpointPath, payload);
  }

  public get(endpointPath: string): Observable<ApiResponse> {
    return this.request('GET', endpointPath);
  }

  public patch(
    endpointPath: string,
    payload: Record<string, any>
  ): Observable<ApiResponse> {
    return this.request('PATCH', endpointPath, payload);
  }

  public put(
    endpointPath: string,
    payload: Record<string, any>
  ): Observable<ApiResponse> {
    return this.request('PUT', endpointPath, payload);
  }

  public delete(endpointPath: string): Observable<ApiResponse> {
    return this.request('DELETE', endpointPath);
  }
}
