import { Injectable } from '@angular/core';
import { AngularFirestore } from '@angular/fire/firestore';
import { AuthidService } from './authid.service';
import { StatsigService } from './statsig.service';

@Injectable({
  providedIn: 'root',
})
export class StatsigFactoryService {
  private instances: Map<string, StatsigService> = new Map();
  private defaultVersion = 'default';

  constructor(
    private firestore: AngularFirestore,
    private AuthID: AuthidService
  ) {}

  getInstance(version?: string): StatsigService {
    const clientVersion = version || this.defaultVersion;

    if (!this.instances.has(clientVersion)) {
      const instance = new StatsigService(
        this.firestore,
        this.AuthID,
        clientVersion
      );
      this.instances.set(clientVersion, instance);
    }

    return this.instances.get(clientVersion);
  }

  async shutdownAll(): Promise<void> {
    for (const [_, instance] of this.instances) {
      await instance.shutdownPromisify();
    }
    this.instances.clear();
  }
}
