import { inject, Injectable } from '@angular/core';
import { ToastController } from '@ionic/angular';
import { from, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class ToastService {
  private toastCtrl = inject(ToastController);

  successToast(
    header: string,
    message: string
  ): Observable<HTMLIonToastElement> {
    this.dismiss().subscribe();

    return from(
      this.toastCtrl
        .create({
          header: header,
          message: message,
          duration: 3000,
          cssClass: 'success-toast',
          icon: 'checkmark-circle',
          position: 'top',
        })
        .then(async (toast) => {
          await toast.present();
          return toast;
        })
    );
  }

  errorToast(header: string, message: string): Observable<HTMLIonToastElement> {
    this.dismiss().subscribe();

    return from(
      this.toastCtrl
        .create({
          header: header,
          message: message,
          duration: 5000,
          icon: 'checkmark-circle',
          position: 'top',
          cssClass: 'error-toast',
        })
        .then(async (toast) => {
          await toast.present();
          return toast;
        })
    );
  }

  dismiss() {
    return from(this.toastCtrl.dismiss());
  }
}
