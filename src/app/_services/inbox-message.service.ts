import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import {
  ActionSheetButton,
  ActionSheetController,
  ActionSheetOptions,
  ModalController,
} from '@ionic/angular';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { InboxMessageWithId } from '@sudshare/custom-node-package';
import { debounceTime, Subject } from 'rxjs';
import { SudsterData } from '../_interfaces/sudster-data.interface';
import { ApiService } from './api.service';
import { OrderReadyNotificationComponent } from '../_components/order-ready-notification/order-ready-notification.component';

const DUPLICATE_MESSAGE_COOLDOWN_MS = 60 * 1000;

@UntilDestroy()
@Injectable()
export class InboxMessageService {
  private handlerRq$ = new Subject<SudsterData['InboxData']>();

  // the same message should not be shown multiple times within at least a minute
  private lastTimeMessagePresentedMap = new Map<string, number>();

  constructor(
    private actionSheetCtrl: ActionSheetController,
    private apiService: ApiService,
    private router: Router,
    private modalController: ModalController
  ) {
    this.handlerRq$
      .pipe(untilDestroyed(this), debounceTime(400))
      .subscribe(async (data) => {
        await this.handleInboxMessage(data);
      });
  }

  updateInboxMessage(data?: SudsterData['InboxData']) {
    if (!data) {
      return;
    }

    this.handlerRq$.next(data);
  }

  private async handleInboxMessage(data: SudsterData['InboxData']) {
    if (data.inAppMessageCount > 0) {
      this.apiService
        .get('LpInboxMessages/v1/sudster/inbox')
        .subscribe(async (response) => {
          if (!response.data['inApp']?.length) {
            return;
          }

          if (response.data['inApp'][0].type === 'paymentV2') {
            const modal = await this.modalController.create({
              component: OrderReadyNotificationComponent,
              componentProps: {
                notificationData: response.data['inApp'][0],
              },
              cssClass: 'poplin-theme-dynamic-height',
              backdropDismiss: false,
            });

            await modal.present();
          } else {
            const messages = response.data['inApp'] as InboxMessageWithId[];
            await this.showModals(messages.concat(messages));
          }
        });
    }
  }

  private async showModals(messages: InboxMessageWithId[]) {
    for (const message of messages) {
      const actions = message.metadata?.actions || [];
      if (actions.length === 0) {
        continue;
      }

      if (this.checkIfMessagePresentedRecently(message.id)) {
        continue;
      }

      const buttons = actions.map((action, index) => {
        return {
          text: action.label,
          role: action.role,
          data: action,
          cssClass: index == 0 ? 'inbox-message-button-primary' : '',
        } as ActionSheetButton;
      });

      const actionSheet = await this.actionSheetCtrl.create({
        header: message.title,
        subHeader: message.body,
        buttons: buttons,
        backdropDismiss: false,
        cssClass: 'inbox-message-action-sheet',
      } as ActionSheetOptions);

      this.lastTimeMessagePresentedMap.set(message.id, Date.now());

      actionSheet.present();

      const { role, data } = await actionSheet.onDidDismiss().then((role) => {
        return role;
      });

      if (role === 'navigate' && data.route) {
        const route = data.route;
        const params = data.routeParams || {};
        this.router.navigate([route, params], {
          replaceUrl: true,
        });
      }

      if (data.delete) {
        this.deleteMessage(message.id);
      } else if (data.markAsRead) {
        this.markInboxMessageAsRead(message.id);
      }
    }
  }

  markInboxMessageAsRead(id: string) {
    this.apiService
      .patch(`LpInboxMessages/v1/sudster/inbox/${id}`, {})
      .subscribe(() => {});
  }

  deleteMessage(id: string) {
    this.apiService
      .delete(`LpInboxMessages/v1/sudster/inbox/${id}`)
      .subscribe(() => {});
  }

  private checkIfMessagePresentedRecently(messageId: string) {
    const lastTimePresented = this.lastTimeMessagePresentedMap.get(messageId);
    if (!lastTimePresented) {
      return false;
    }

    const currentTime = Date.now();
    const diff = currentTime - lastTimePresented;
    return diff < DUPLICATE_MESSAGE_COOLDOWN_MS;
  }
}
