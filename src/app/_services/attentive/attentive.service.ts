import { Injectable } from '@angular/core';

import { DEFAULT_V4_HEADERS } from 'src/app/_constants';
import { environment } from 'src/environments/environment';
import {
  ATTENTIVE_SUBSCRIPTION_TYPES,
  HTTP_METHOD,
  USER_TYPES,
} from '../../_utils/enum';

const ATTENTIVE_LAUNDRY_PRO_SOURCE_ID = '1007432';
const ATTENTIVE_SUBSCRIPTION_API_URL =
  'https://api.attentivemobile.com/v1/subscriptions';
const ATTENTIVE_SUBSCRIPTION_API_KEY =
  'c2w0M3JNaHJ5eWZuUkpPQlI2R3FGdkQxRTZXWDRWcW9EclRJ';

@Injectable({
  providedIn: 'root',
})
export class AttentiveService {
  /**
   * Subscribes a user to Attentive service
   * @param phone - User's phone number
   * @param email - User's email address
   * @param newUserId - User's document Id in Firebase
   */
  public async subscribeUser(
    phone: string,
    email: string,
    newUserId: string
  ): Promise<void> {
    try {
      await this.makeRequest(phone, email, newUserId);
    } catch (error) {
      // Log an error if the subscription attempt fails
      console.error(
        `Failed to subscribe user: ${error.message}
       ${error.stack}`
      );
    }
  }

  /**
   * Makes a request to Attentive API to subscribe a user.
   * @param phone - User's phone number
   * @param email - User's email address
   * @param newUserId - User's document Id in Firebase
   * @returns Promise with the response from the API
   */
  private async makeRequest(
    phone: string,
    email: string,
    newUserId: string
  ): Promise<unknown> {
    const GENERAL_API_URL = `${environment.apiPath}/general/v1`;

    try {
      console.info(
        `Sending subscription request to Attentive for ${ATTENTIVE_SUBSCRIPTION_TYPES.TRANSACTIONAL} SMS messages`
      );
      // Make a request to Attentive API
      const options = {
        method: HTTP_METHOD.POST,
        headers: {
          'Access-Control-Allow-Origin': '*',
          ...DEFAULT_V4_HEADERS,
        },
        body: JSON.stringify({
          phone: phone,
          email: email,
          userType: USER_TYPES.LAUNDRY_PRO,
          userId: newUserId,
          subscriptionType: ATTENTIVE_SUBSCRIPTION_TYPES.TRANSACTIONAL,
        }),
      };
      const response = await fetch(
        `${GENERAL_API_URL}/marketing/attentive`,
        options
      );
      console.log(response, 'response');
      // Check if the response is not ok
      if (!response.ok) {
        // Log error if subscription request fails
        const errorText = await response.text();
        console.error(
          `Subscription request failed with status ${response.status}: ${errorText}`
        );
        return;
      }
      // Log success if subscription request is successful
      console.info(`Subscription request successful: ${response.status}`);
      return await response.json();
    } catch (error) {
      // Log error if there is an issue making the request to Attentive API
      console.error(
        `Error making request to Attentive API: ${error.message},
        ${error.stack}`
      );
    }
  }
}
