import { Injectable } from '@angular/core';
import { Network } from '@capacitor/network';
import { Platform } from '@ionic/angular';
import { BehaviorSubject } from 'rxjs';
import { environment } from 'src/environments/environment';
import { base64ToFile, convertBlobToBase64 } from '../_utils/files';
import { ApiResponseStatus } from './api.service';
import { LegacyApiService } from './legacy-api.service';

export interface StartUploadResponse {
  fileId: string;
}

export const MAX_CHUNK_SIZE = 1024 * 256; // 256KB

export type FileErrorType = 'connection-lost' | 'upload-failed';

export interface UploadError {
  fileId?: string;
  type: FileErrorType;
  message: string;
}

export interface UploadProgress {
  fileId: string;
  progress: number;
}

export interface UploadState {
  fileId: string;
  storedChunks: number[];
  retryCount: number;
  isCancelled: boolean;
  isCompleted: boolean;
}

@Injectable({
  providedIn: 'root',
})
export class FileService {
  private readonly fileServiceBaseUrl = `${environment.apiPathV2}/files`;
  private readonly localStorageKey = 'uploadState';
  private readonly fileKeyPrefix = 'file-';
  private platform: Platform;
  private readonly isNativeDevice: boolean;

  uploadComplete$ = new BehaviorSubject<string | null>(null);
  uploadProgress$ = new BehaviorSubject<UploadProgress | null>(null);
  uploadError$ = new BehaviorSubject<UploadError | null>(null);
  isPaused = false;

  constructor(private apiService: LegacyApiService, platform: Platform) {
    this.platform = platform;
    this.isNativeDevice = this.platform.is('hybrid');

    if (this.isNativeDevice) {
      Network.addListener('networkStatusChange', (status) => {
        if (status.connected) {
          this.resumeUploads();
        } else {
          this.pauseUploads();
          this.uploadError$.next({
            type: 'connection-lost',
            message: 'Network disconnection detected',
          });
        }
      });
    } else {
      window.addEventListener('online', () => {
        this.resumeUploads();
      });

      window.addEventListener('offline', () => {
        this.pauseUploads();
        this.uploadError$.next({
          type: 'connection-lost',
          message: 'Network disconnection detected',
        });
      });
    }
  }

  async startUpload(fileName: string, fileSize: number) {
    const totalChunks = Math.ceil(fileSize / MAX_CHUNK_SIZE);

    return await this.apiService.post(
      `/start-upload`,
      {
        fileName,
        fileSize,
        totalChunks,
      },
      { baseUrl: this.fileServiceBaseUrl }
    );
  }

  saveUploadState({
    fileId,
    storedChunks,
    retryCount = 0,
    isCancelled = false,
    isCompleted = false,
  }: {
    fileId: string;
    storedChunks: number[];
    retryCount?: number;
    isCancelled?: boolean;
    isCompleted?: boolean;
  }) {
    localStorage.setItem(
      `${this.localStorageKey}-${fileId}`,
      JSON.stringify({
        fileId,
        storedChunks,
        retryCount,
        isCancelled,
        isCompleted,
      })
    );
  }

  async saveCurrentUpload(fileId: string, file: File) {
    const base64File = await convertBlobToBase64(file);
    localStorage.setItem(
      `${this.fileKeyPrefix}-${fileId}`,
      base64File as string
    );
  }

  getUploadState(fileId: string): UploadState {
    const state = localStorage.getItem(`${this.localStorageKey}-${fileId}`);
    return state
      ? JSON.parse(state)
      : { fileId: undefined, storedChunks: [], retryCount: 0 };
  }

  getAllUploadStates(): UploadState[] {
    const keys = Object.keys(localStorage).filter((key) =>
      key.startsWith(this.localStorageKey)
    );

    return keys.map((key) => {
      const state = localStorage.getItem(key);
      return state
        ? JSON.parse(state)
        : { fileId: undefined, storedChunks: [], retryCount: 0 };
    });
  }

  getCurrentUpload(fileId: string) {
    const uploadState = this.getUploadState(fileId);

    const fileData = localStorage.getItem(
      `${this.fileKeyPrefix}-${uploadState.fileId}`
    );

    const fileType = fileData?.split(';')[0].split(':')[1];

    return fileData ? base64ToFile(fileData, fileType) : null;
  }

  clearUploadState(fileId: string) {
    localStorage.removeItem(`${this.localStorageKey}-${fileId}`);
  }

  clearCurrentUpload(fileId: string) {
    localStorage.removeItem(`${this.fileKeyPrefix}-${fileId}`);
  }

  async uploadFile(
    fileId: string,
    file: File,
    chunkSize: number = MAX_CHUNK_SIZE
  ) {
    if (!file) {
      this.uploadError$.next({
        fileId,
        type: 'upload-failed',
        message: 'File not found in localStorage',
      });
      return;
    }

    this.saveCurrentUpload(fileId, file);

    const totalChunks = Math.ceil(file.size / chunkSize);

    try {
      for (let i = 0; i < totalChunks; i++) {
        if (this.isPaused) {
          return;
        }

        const uploadState = this.getUploadState(fileId);
        const storedChunks = new Set<number>(uploadState.storedChunks);

        if (uploadState.isCancelled) {
          return;
        }

        if (storedChunks.has(i)) continue;

        const chunk = file.slice(i * chunkSize, (i + 1) * chunkSize);
        await this.uploadChunk(fileId, chunk, i);

        storedChunks.add(i);
        this.saveUploadState({
          ...uploadState,
          fileId,
          storedChunks: Array.from(storedChunks),
        });
        this.uploadProgress$.next({
          fileId,
          progress: ((i + 1) / totalChunks) * 100,
        });
      }

      const uploadState = this.getUploadState(fileId);

      this.saveUploadState({
        ...uploadState,
        isCompleted: true,
      });
      this.uploadComplete$.next(fileId);
      this.uploadProgress$.next({
        fileId,
        progress: 100,
      });
    } catch (error) {
      console.error('Chunk upload failed:', error);
      const uploadState = this.getUploadState(fileId);

      if (uploadState.retryCount < 3 && !uploadState.isCancelled) {
        this.saveUploadState({
          ...uploadState,
          fileId,
          retryCount: uploadState.retryCount + 1,
        });
        await this.uploadFile(fileId, file, chunkSize);
      } else {
        this.uploadError$.next({
          fileId,
          type: 'upload-failed',
          message: 'Chunk upload failed',
        });
        this.clearFileUpload(fileId);
      }
    }
  }

  async uploadChunk(fileId: string, chunk: Blob, chunkIndex: number) {
    const formData = new FormData();
    formData.append('fileId', fileId);
    formData.append('chunk', chunk);
    formData.append('chunkId', chunkIndex.toString());

    const res = await this.apiService
      .post('/upload-chunk', formData, {
        baseUrl: this.fileServiceBaseUrl,
      })
      .toPromise();

    if (res.status !== ApiResponseStatus.ok) {
      throw new Error(res.message);
    }

    return res.data;
  }

  pauseUploads() {
    this.isPaused = true;
    console.warn('Upload paused. Waiting for network reconnection.');
  }

  async resumeUploads() {
    if (this.isPaused) {
      this.isPaused = false;
      const uploadStates = this.getAllUploadStates();

      for (let i = 0; i < uploadStates.length; i++) {
        const uploadState = uploadStates[i];

        if (
          uploadState &&
          uploadState.fileId &&
          uploadState.retryCount < 3 &&
          uploadState.storedChunks.length > 0 &&
          !uploadState.isCancelled
        ) {
          const file = this.getCurrentUpload(uploadState.fileId);

          if (file) {
            this.uploadFile(uploadState.fileId, file);
          }
        }
      }
    }
  }

  cancelUpload(fileId: string) {
    this.pauseUploads();

    const uploadState = this.getUploadState(fileId);
    this.saveUploadState({ ...uploadState, isCancelled: true });
    this.uploadError$.next({
      fileId,
      type: 'upload-failed',
      message: 'File upload cancelled',
    });

    this.resumeUploads();
  }

  clearFileUpload(fileId: string) {
    this.clearUploadState(fileId);
    this.clearCurrentUpload(fileId);
  }

  isFileUploadComplete(fileId: string): boolean {
    const uploadState = this.getUploadState(fileId);
    return uploadState.isCompleted;
  }
}
