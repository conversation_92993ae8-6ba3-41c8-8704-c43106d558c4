import { Inject, Injectable, OnDestroy } from '@angular/core';
import { Network } from '@capacitor/network';
import Rollbar from 'rollbar';
import { BehaviorSubject, from, interval, of, Subject } from 'rxjs';
import {
  catchError,
  filter,
  finalize,
  mergeMap,
  switchMap,
  takeUntil,
  tap,
} from 'rxjs/operators';
import { WeightService } from '../service/weight.service';
import { RollbarService } from './rollbar/rollbar.service';

export enum WeighStatus {
  weighed = 'weighed',
  processingPayment = 'processingPayment',
  paymentFailed = 'paymentFailed',
  updatingPayment = 'updatingPayment',
  paymentUpdateFailed = 'paymentUpdateFailed',
  editingWeight = 'editingWeight',
}

@Injectable({
  providedIn: 'root',
})
export class PollingService implements OnDestroy {
  private stopPolling = new Subject<void>();
  private networkStatus$ = new BehaviorSubject<boolean>(true);
  private isPolling = false;
  private networkListener: any;
  private onlineHandler: () => void;
  private offlineHandler: () => void;

  constructor(
    private weightService: WeightService,
    @Inject(RollbarService) private rollbar: Rollbar
  ) {
    this.setupNetworkListeners();
  }

  ngOnDestroy() {
    this.cleanupNetworkListeners();
    this.stopPolling?.next();
    this.stopPolling?.complete();
    this.networkStatus$.complete();
  }

  private setupNetworkListeners() {
    this.networkListener = Network.addListener(
      'networkStatusChange',
      (status) => {
        this.handleNetworkChange(status.connected);
      }
    );

    this.onlineHandler = () => this.handleNetworkChange(true);
    this.offlineHandler = () => this.handleNetworkChange(false);

    window.addEventListener('online', this.onlineHandler);
    window.addEventListener('offline', this.offlineHandler);

    from(Network.getStatus())
      .pipe(
        catchError((error) => {
          this.rollbar.error('Error getting initial network status', error);
          return of({ connected: false });
        })
      )
      .subscribe((status) => {
        this.handleNetworkChange(status.connected);
      });
  }

  private cleanupNetworkListeners() {
    if (this.networkListener) {
      this.networkListener.remove();
    }
    window.removeEventListener('online', this.onlineHandler);
    window.removeEventListener('offline', this.offlineHandler);
  }

  private handleNetworkChange(isConnected: boolean) {
    this.networkStatus$.next(isConnected);

    if (!isConnected) {
      // if network comes back, and we were polling before, restart the poll
      this.stopPolling.next();
      this.stopPolling.complete();
      this.stopPolling = new Subject<void>();
    }
  }

  pollWeightStatus(orderNumber: string) {
    this.isPolling = true;

    return this.networkStatus$.pipe(
      filter((keepPolling) => !!keepPolling),
      switchMap(() =>
        interval(1000).pipe(
          takeUntil(this.stopPolling),
          switchMap(() => {
            if (!this.networkStatus$.value) {
              return of([]);
            }
            return this.weightService.getWeighStatus(orderNumber).pipe(
              catchError((error) => {
                this.rollbar.error('Error getting weight status', error);
                return of([]);
              })
            );
          }),
          mergeMap((response: any) => {
            if (!response?.data) {
              this.rollbar.error('Invalid weight status response', {
                response,
              });
              return of([]);
            }
            return from(of(response.data)).pipe(
              catchError((error) => {
                this.rollbar.error(
                  'Error processing weight status response',
                  error
                );
                return of([]);
              })
            );
          }),
          tap({
            next: (resolvedData) => console.log('Weigh data:', resolvedData),
            error: (error) => {
              this.rollbar.error('Error in weight status polling', error);
            },
          }),
          finalize(() => {
            this.isPolling = false;
          })
        )
      )
    );
  }

  stop() {
    this.isPolling = false;
    this.stopPolling.next();
    this.stopPolling.complete();
    this.stopPolling = new Subject<void>();
  }
}
