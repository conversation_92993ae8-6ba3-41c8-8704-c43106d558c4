import type { Plugin } from '@capacitor/core';

export interface SegmentPlugin {
  identify(data: {
    userId: string;
    traits: Record<string, any>;
  }): Promise<{ result: boolean }>;

  track(data: {
    eventName: string;
    properties: Record<string, any>;
  }): Promise<{ result: boolean }>;

  reset(): Promise<{ result: boolean }>;
}

export interface SudSharePlugin {
  setup(options: { userId: string }): Promise<{ value: string }>;
}

export interface IterablePlugin extends Plugin {
  initialize(identifyOptions: {
    apiKey: string;
    packageName: string;
    userId: string;
  }): Promise<void>;

  trackPush(rq: {
    templateId: number;
    campaignId: number;
    msgId: string;
    appWasRunning: boolean;
  }): Promise<string>;

  registerUser(data: { userId: string }): Promise<void>;
}
