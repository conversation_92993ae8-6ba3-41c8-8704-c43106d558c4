import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { LegacyApiService } from '../_services/legacy-api.service';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class WeightService {
  private apiService = inject(LegacyApiService);

  private readonly baseUrl = `${environment.apiPathV2}/orders`;

  patchEditWeight(id: string, canEdit: boolean): Observable<any> {
    const WEIGHT_UPDATE_URL = `/${id}/status/edit-weight`;

    return this.apiService.patch(
      WEIGHT_UPDATE_URL,
      { canEdit },
      { baseUrl: this.baseUrl }
    );
  }

  getWeighStatus(id: string) {
    const WEIGHT_UPDATE_URL = `/${id}/status`;

    return this.apiService.get(WEIGHT_UPDATE_URL, {
      baseUrl: this.baseUrl,
    });
  }
}
