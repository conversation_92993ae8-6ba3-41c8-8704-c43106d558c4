import { Injectable } from '@angular/core';
import { AngularFireAuth } from '@angular/fire/auth';
import { AngularFirestore } from '@angular/fire/firestore';
import { BehaviorSubject, Subscription } from 'rxjs';
import { filter, take } from 'rxjs/operators';
import { SudsterData } from '../_interfaces/sudster-data.interface';
import { AuthidService } from '../_services/authid.service';

@Injectable({
  providedIn: 'root',
})
export class GetNewOrdersService {
  private OrderSource = new BehaviorSubject({});
  private availableOrdersSubscription: Subscription;
  currentOrders = this.OrderSource.asObservable();

  constructor(
    public firestore: AngularFirestore,
    public afAuth: AngularFireAuth,
    private AuthID: AuthidService
  ) {}

  UserID = this.AuthID.getID();

  LoadOrderList() {
    return new Promise((resolve) => {
      this.firestore
        .doc<SudsterData>(`Sudsters/${this.UserID}`)
        .valueChanges()
        .pipe(
          filter((SudsterDoc) => !!SudsterDoc),
          take(1)
        )
        .subscribe((SudsterDoc) => {
          if (SudsterDoc.BlockOrders == true) {
            this.OrderSource.next({
              AvailableOrders: [],
              Sudster: SudsterDoc,
            });
            resolve(null);
          } else {
            if (!this.availableOrdersSubscription?.closed) {
              this.availableOrdersSubscription?.unsubscribe();
            }

            this.availableOrdersSubscription = this.firestore
              .collection<any>('AvailableOrders', (ref) =>
                ref
                  .where('AllowedSudster', 'array-contains', this.UserID)
                  .orderBy('Order.CreatedTimestamp', 'desc')
                  .limit(100)
              )
              .valueChanges({ idField: 'OrderNumber' })
              .subscribe((collection) => {
                this.OrderSource.next({
                  AvailableOrders: collection,
                  Sudster: SudsterDoc,
                });

                resolve(null);
              });
          }
        });
    });
  }
}
