import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { VerificationFailureComponent } from './_components/verification-failure/verification-failure.component';
import { VerificationProcessingComponent } from './_components/verification-processing/verification-processing.component';
@NgModule({
  imports: [CommonModule],
  exports: [
    CommonModule,
    IonicModule,
    VerificationFailureComponent,
    VerificationProcessingComponent,
  ],
  declarations: [VerificationFailureComponent, VerificationProcessingComponent],
})
export class SharedModule {}
