import { BannerConfigMap } from '../_interfaces/banner.interface';

export enum BannerTypeEnum {
  ALERT = 'alert',
  VACATION = 'vacation',
}

export const BANNER_CONFIG: BannerConfigMap = {
  [BannerTypeEnum.ALERT]: {
    icon: 'bell_custom',
    iconColor: '--ratings-color-poor',
    title: 'NEW ORDER ALERTS OFF',
    message:
      'Enable alerts in Availability Settings to stay updated on jobs in your area.',
  },
  [BannerTypeEnum.VACATION]: {
    icon: 'calendar_filled',
    iconColor: '--blue-550',
    title: 'ENJOY YOUR VACATION!',
    message: "You'll start receiving new order alerts again on ",
  },
};
