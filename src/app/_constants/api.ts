import { Capacitor } from '@capacitor/core';
import { environment } from '../../environments/environment';

export const DEFAULT_APP_INFO_HEADERS = {
  'x-app-version': `${environment.version}`,
  'x-app-platform': `${Capacitor.getPlatform()}`,
} as const;

export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  Authorization: '',
  ...DEFAULT_APP_INFO_HEADERS,
} as const;

export const DEFAULT_V4_HEADERS = {
  ...DEFAULT_APP_INFO_HEADERS,
  'Content-Type': 'application/json',
  'x-api-key': `${environment.apiToken}`,
} as const;
