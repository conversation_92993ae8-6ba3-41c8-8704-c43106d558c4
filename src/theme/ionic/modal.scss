@import 'src/theme/type-mixins.scss';

ion-modal.poplin-theme,
ion-modal.poplin-theme-dynamic-height {
  --border-radius: var(--border-radius-default);
  --box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.04);

  ion-toolbar {
    padding-top: 0;
  }

  ion-header {
    box-shadow: none;

    ion-title {
      @include l-alt-head;

      &.slide-up-title {
        text-align: left;
        padding: 0;
      }
    }
  }

  ion-content {
    --padding-end: 10px;
    --padding-start: 10px;
  }
}

ion-modal.poplin-theme-dynamic-height {
  --height: auto;
  align-items: end;
}

ion-modal.confirmation-modal {
  --height: auto;
  --border-radius: var(--border-radius-default);
  padding: 0 30px;

  &::part(content) {
    max-width: 440px;
  }

  confirmation-modal {
    max-width: 440px;
  }

  &.modal-default {
    top: 0;
    bottom: 0;
  }
}

@media only screen and (max-device-width: 812px) {
  ion-modal.poplin-theme-dynamic-height {
    top: 0;
    bottom: 0;
  }
}

// Misc
ion-modal.datepicker-modal {
  --height: 670px;
  --width: 95%;
  --border-radius: var(--border-radius-default);
  --box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.04);
  max-width: 440px;
  margin: 0 auto;

  &.modal-default {
    top: 0;
    bottom: 0;
  }
}
