@import 'type-mixins';

ion-button.poplin-theme {
  --box-shadow: none;
  --border-radius: var(--border-radius-default);

  font-size: 13px;
  font-style: normal;
  font-weight: 700;
  line-height: 16px;
  letter-spacing: 1.3px;

  &.button-disabled {
    opacity: 1;
  }

  &.h-large {
    --border-radius: 8px;
    height: 48px;
    font-size: 16px;
    line-height: 20px;
  }

  span.title {
    margin-right: 10px;
  }

  &.v2 {
    --border-radius: 8px;
    height: 48px;
    @include m-aux;
    line-height: 20px;
    margin: 0 0 12px;
    max-width: 440px;
  }
}

ion-button[disabled].poplin-theme {
  --background: var(--color-button-primary-disabled) !important;
  --ion-color-base: var(--gray-650) !important;
}
