{"name": "laundry-pro", "version": "3.59.0", "author": "Poplin Engineering", "homepage": "http://poplin.co", "private": true, "scripts": {"start": "ionic serve --port 4300 --lab --lab-port 8200 --no-open --configuration=local", "emulator": "ionic serve --port 4300 --lab --lab-port 8200 --no-open --configuration=emulator", "prod": "ionic serve --port 4300 --lab --lab-port 8200 --no-open --configuration=production", "build": "ionic build", "build:local": "ionic build --configuration=local", "sync": "cap sync --deployment", "test": "ng test", "android": "ionic cap run android --no-build --open", "ios": "ionic cap run ios --no-build --open", "build:sync": "ionic build && cap sync --deployment", "build:prod": "ionic build --prod --minifyjs --minifycss --optimizejs", "build:softprod": "ionic build --configuration=soft-prod --minifyjs --minifycss --optimizejs", "build:prod:sync": "ionic build --prod --minifyjs --minifycss --optimizejs && SUDSHARE_ENV=prod cap sync --deployment", "build:prod:syncIos": "ionic build --prod --minifyjs --minifycss --optimizejs && SUDSHARE_ENV=prod cap sync --deployment ios", "postinstall": "jetifier", "env:nonProd": "firebase use nonProd", "env:prod": "firebase use prod", "test:actions:ios": "npm run build && cap sync --deployment ios", "local:actions:android": "npm run build:local && cap sync android --no-build", "local:actions:ios": "npm run build:local && cap sync ios --no-build", "deploy:website:nonProd": "npm run build && firebase deploy --only hosting:nonProd", "deploy:website:softProd": "npm run build:softprod && firebase deploy --only hosting:softProd", "deploy:website:prod": "npm run build:prod && firebase deploy --only hosting:prod", "deploy:preview-website:nonProd": "npm run build && firebase hosting:channel:deploy preview_test --only nonProd --expires 1h", "deploy:preview-website:prod": "npm run build:prod && firebase hosting:channel:deploy preview_prod --only prod --expires 1h", "prettier:check": "prettier 'src/**/*.{js,jsx,ts,tsx,html,css,scss}' --check .", "prettier:fix": "prettier 'src/**/*.{js,jsx,ts,tsx,html,css,scss}' --write .", "lint:check": "eslint 'src/**/*.{js,jsx,ts,tsx,html,css,scss}' .", "lint:fix": "eslint 'src/**/*.{js,jsx,ts,tsx,html,css,scss}' --fix .", "prepare": "husky install", "get:config": "firebase functions:config:get > .runtimeconfig.json", "upload:sourcemaps": "rollbar-cli upload-sourcemaps ./www --access-token \"$ROLLBAR_ACCESS_TOKEN\" --url-prefix \"$MINIFIED_URL\" --code-version \"$npm_package_version\" --next", "delete:sourcemaps": "del-cli www/*.js.map", "lint": "ng lint"}, "engines": {"node": ">=16.10.0"}, "dependencies": {"@angular/animations": "^14.3.0", "@angular/common": "^14.3.0", "@angular/core": "^14.3.0", "@angular/fire": "^6.1.5", "@angular/forms": "^14.3.0", "@angular/platform-browser": "^14.3.0", "@angular/platform-browser-dynamic": "^14.3.0", "@angular/router": "^14.3.0", "@awesome-cordova-plugins/core": "^5.46.0", "@awesome-cordova-plugins/smartlook": "^6.8.0", "@awesome-cordova-plugins/sms": "^5.46.0", "@capacitor-community/contacts": "^5.0.4", "@capacitor-community/fcm": "^2.0.2", "@capacitor/android": "^5.7.7", "@capacitor/app": "^5.0.0", "@capacitor/browser": "^5.0.0", "@capacitor/camera": "^5.0.10", "@capacitor/clipboard": "^5.0.0", "@capacitor/core": "^5.0.0", "@capacitor/device": "^5.0.0", "@capacitor/filesystem": "^5.0.0", "@capacitor/geolocation": "^5.0.0", "@capacitor/haptics": "^5.0.0", "@capacitor/ios": "^5.7.7", "@capacitor/keyboard": "^5.0.0", "@capacitor/network": "^5.0.0", "@capacitor/preferences": "^5.0.0", "@capacitor/push-notifications": "^5.0.0", "@capacitor/share": "^6.0.2", "@capacitor/splash-screen": "^5.0.0", "@capacitor/status-bar": "^5.0.0", "@capgo/capacitor-updater": "^5.0.0", "@checkr/web-sdk": "0.0.32", "@date-fns/tz": "^1.2.0", "@googlproxer/ion-range-calendar": "^1.4.0", "@ionic-native/call-number": "^5.36.0", "@ionic-native/core": "^5.36.0", "@ionic-native/firebase": "^5.36.0", "@ionic-native/mobile-accessibility": "^5.36.0", "@ionic-native/sms": "^5.36.0", "@ionic-native/splash-screen": "^5.36.0", "@ionic-native/status-bar": "^5.36.0", "@ionic/angular": "^7.8.6", "@ionic/angular-server": "^7.8.6", "@ionic/pwa-elements": "^3.1.1", "@ngneat/until-destroy": "^9.2.2", "@statsig/js-client": "^3.12.0", "@sudshare/capacitor-zendesk": "1.1.0", "@sudshare/custom-node-package": "^1.10.50", "@typescript-eslint/parser": "^8.17.0", "appsflyer-capacitor-plugin": "^6.14.3", "axios": "^1.3.4", "call-number": "^1.0.1", "canvas-confetti": "^1.5.1", "chart.js": "^2.9.4", "cordova-plugin-idfa": "^2.1.0", "cordova-plugin-ionic": "5.5.3", "cordova-plugin-smartlook": "^2.0.2", "cordova-sms-plugin": "^1.0.2", "core-js": "^2.6.12", "date-fns": "^4.1.0", "firebase": "^8.10.1", "forter-cordova-plugin": "git+https://forter-mobile-git:<EMAIL>/forter-mobile/forter-cordova-plugin.git", "jetifier": "^1.6.8", "latlon-geohash": "^2.0.0", "lodash": "^4.17.21", "mailcheck": "^1.1.1", "material-design-icons": "^3.0.1", "moment": "^2.29.4", "moment-timezone": "^0.5.38", "ngx-cookie-service": "^14.0.1", "ngx-mask": "^9", "ngx-qrcode-styling": "1.2.3", "rollbar": "^2.26.4", "rxjs": "^7.5.0", "safe-pipe": "^2.0.4", "smartlook-client": "^8.3.0", "swiper": "^8.4.7", "tslib": "^2.6.3", "tz-lookup": "git+https://github.com/leonardosudshare/tz-lookup-oss.git", "zone.js": "^0.11.8"}, "devDependencies": {"@angular-devkit/architect": "^0.1402.13", "@angular-devkit/build-angular": "^14.2.13", "@angular-devkit/core": "^14.2.13", "@angular-devkit/schematics": "^14.2.13", "@angular-eslint/builder": "19.0.0", "@angular-eslint/eslint-plugin": "19.0.0", "@angular-eslint/eslint-plugin-template": "19.0.0", "@angular-eslint/schematics": "19.0.0", "@angular-eslint/template-parser": "19.0.0", "@angular/cli": "^14.2.13", "@angular/compiler": "^14.3.0", "@angular/compiler-cli": "^14.3.0", "@angular/language-service": "^14.3.0", "@capacitor/cli": "^5.0.0", "@capgo/cli": "^5.0.0", "@ionic/angular-toolkit": "^8.0.0", "@ionic/cli": "^6.20.8", "@ionic/lab": "3.2.15", "@types/jasmine": "^2.8.19", "@types/jasminewd2": "^2.0.10", "@types/node": "^16.10.0", "@types/tz-lookup": "^6.1.0", "@typescript-eslint/eslint-plugin": "7.11.0", "@typescript-eslint/parser": "7.11.0", "cordova-support-android-plugin": "^2.0.4", "del-cli": "^6.0.0", "eslint": "8.57.0", "eslint-config-prettier": "^8.5.0", "firebase-tools": "^9.20.0", "husky": "^8.0.0", "ionic": "^5.2.4", "jasmine-core": "~2.99.1", "jasmine-spec-reporter": "~4.2.1", "karma": "^6.4.1", "karma-chrome-launcher": "~2.2.0", "karma-coverage-istanbul-reporter": "^2.1.1", "karma-jasmine": "~1.1.2", "karma-jasmine-html-reporter": "^0.2.2", "prettier": "^2.7.1", "protractor": "^7.0.0", "rollbar-cli": "^0.2.1", "ts-node": "~10.9.2", "tslint": "~6.1.3", "typescript": "^4.8.4"}, "cordova": {"plugins": {"cordova-sms-plugin": {}, "forter-cordova-plugin": {}, "cordova-plugin-ionic": {}, "cordova-support-android-plugin": {}, "cordova-plugin-idfa": {"ANDROID_PLAY_ADID_VERSION": "16.+"}}, "platforms": ["browser", "ios", "android"]}}