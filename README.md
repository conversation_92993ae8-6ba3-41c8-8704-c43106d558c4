# Laundry Pros

This repository holds the main application used by the customers (does laundry). It's a single code base for the WebApp, Android, and iOS which is possible by using the [Ionic Framework](https://ionicframework.com/docs)

## Local Development

The following frameworks and tools are required on your machine:

- Node v10+
- NPM v6+
- Visual Studio Code
- Xcode
  - After install, an update to the emulator and tools might be needed
- Android Studio
  - After install, open to install android sdk and other tools prior to opening this project

### Install Packages

This step must be run prior to any of the below options

- `nvm use`
- `npm install`
- `npm run build:sync`

### Multiple Platforms

Once the above requirements are satisfied, run the below commands in a terminal from the root folder to access:

1. `npm run start`

Now you have the WebApp running locally and it can be accessed here [Localhost](http://localhost:4300/) and you can access iOS and Android version in the browseer at [Localhost](http://localhost:8200)

### Android

Once the above requirements are satisfied, run the below commands in a terminal from the root folder to access:

1. `npm run android`
2. Select `Build` -> `Select Build Variant` -> `app` -> `qaDebug`

This will build the android application and will open Android Studio after the build. You can also access the WebApp simultaneously at [Localhost](http://localhost:8100/)

### iOS

Once the above requirements are satisfied, run the below commands in a terminal from the root folder to access:

1. `npm run ios`

This will build the ios application and will open xcode after the build. You can also access the WebApp simultaneously at [Localhost](http://localhost:8100/)

## Local Dependencies

> You can use the `functions-v3` repository to use firebase emulator alongside this application for an e2e local development (partial e2e).
> Comment out the lines in [app.module.ts](src/app/app.module.ts) that say _for using local cloud functions_

## Debug

There is a VS code configuration in place that will either attach or launch chrome for this application. In order to use it, you can select `Run and Debug` on left panel for VS code and select the profile of your choice.

**Note** Currently profiles require Google Chrome to be installed

# Test Deployment

These are the necessary steps to publish a test build of the WebApp, Android, and iOS. You will need to have the appropriate signing certificates installed on your machine to continue with the process.

**Important** Follow these steps in order to ensure proper test assets are generated in the ned

1. Clone `master` branch in a new folder
2. Run `npm install`
3. Run `npm run build`

### Preview Mode

1. Run `npm run env:nonProd`
2. Run `npm run build && firebase hosting:channel:deploy preview_test --only nonProd --expires 1h`

### WebApp

1. Run `npm run env:nonProd`
2. Run `npm run deploy:website:nonProd`

### Android

1. Run `npm run android` (Open's Android Studio)
2. Select `Build` -> `Select Build Variant` -> `app` -> `qaDebug`
3. Select `Build` -> `Build Bundle(s)/ APK(s)` -> `Build APK(s)`
4. This will output a file `app-qa-debug.apk` in the project folder
5. Upload the file to [App Distribution](https://console.firebase.google.com/project/sudsharetest/appdistribution/app/android:com.sudshare.sudster.test/releases) and add relevant release notes
6. Notify users that test build is available

### iOS

**Note** Must have `SudShare Inc.` certificate on local machine

1. Run `npm run ios` (Open's Xcode)
2. Select `Product` -> `Schema` -> `Test`
3. Select `Product` -> `Build` -> `Any iOS Device (arm64)`
4. Select `Product` -> `Archive` (Select the completed build above)
5. Select `Window` -> `Organizer` -> `Distribute App`
6. Build will show up in `TestFlight` section in `Apple Connect` ~10 minutes after upload

# Production Deployment

These are the necessary steps to publish a production build of the WebApp, Android, and iOS. You will need to have the appropriate signing certificates installed on your machine to continue with the process.

**Important** Follow these steps in order to ensure proper production assets are generated in the ned

1. Clone `master` branch in a new folder
2. Run `npm install`
3. Run `npm run build:prod`

### WebApp

1. Run `npm run env:prod`
1. Run `npm run deploy:website:prod`

### Android

1. Run `npm run android` (Open's Android Studio)
2. Select `Build` -> `Select Build Variant` -> `app` -> `prodDebug` (App Distribution) OR `prodRelease` (Play Store)
3. For `prodDebug`:
   - Select `Build` -> `Build Bundle(s)/ APK(s)` -> `Build APK(s)`
   - This will output a file `app-prod-debug.apk`
   - Upload the file to [App Distribution](https://console.firebase.google.com/project/laundry-llama/appdistribution/app/android:com.sudshare.sudster/releases) and add relevant release notes
4. For `prodRelease`:
   - Select `Build` -> `Generate Signed APK Bundle` -> `prodRelease`
   - This will output a file `app-prod-release.aab`
   - Upload the file to Google Play Console (Engineering Leads or Admins have access)

### iOS

**Note** Must have `SudShare Inc.` certificate on local machine

1. Run `npm run ios` (Open's Xcode)
2. Select `Product` -> `Schema` -> `Prod`
3. Select `Product` -> `Build` -> `Any iOS Device (arm64)`
4. Select `Product` -> `Archive` (Select the completed build above)
5. Select `Window` -> `Organizer` -> `Distribute App`
6. Build will show up in `TestFlight` section in `Apple Connect` ~10 minutes after upload

### CapGo (Live Updates)

The app uses CapGo for live updates of web assets without requiring a full native app update. The deployment process is handled through a GitHub Actions workflow that can be triggered:

1. **Manually** through GitHub Actions UI with the following options:

   - Environment: `dev` or `production`
   - Channel: `dev` or `production`
   - Custom Version: Optional version override
   - Commit Changes: Whether to commit version updates back to the repository

2. **Programmatically** from other workflows by calling the `deploy-capgo.yml` workflow

The bundle version follows this pattern:

- Dev: `{package.version}-dev.{timestamp}` (e.g., "1.0.0-dev.20240320123456")
- Production: `{package.version}` (e.g., "1.0.0")

To manually trigger a CapGo update:

1. Go to GitHub Actions
2. Select "CapGo Bundle Upload" workflow
3. Click "Run workflow"
4. Select the desired environment and channel
5. Click "Run workflow"

Note: CapGo updates can be globally disabled by setting the `DISABLE_CAPGO_UPDATES` repository variable to `true`.

Version tracking is maintained in `src/environments/app.version.ts` with two exports:

- `currentAppVersion`: Tracks the package.json version
- `bundleVersion`: Tracks the CapGo bundle version
