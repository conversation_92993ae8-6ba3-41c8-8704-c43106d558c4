#!/bin/bash

# This script updates the version of the SudsterSupportedVersions field in the RemoteConfig document in Firestore.
# It checks if the version already exists in the array and skips the update if it does.
# Otherwise, it adds the new version to the array.

# Check if required parameters are provided
if [ $# -ne 2 ]; then
  echo "Usage: $0 <project_id> <app_version>"
  echo "Example: $0 laundry-llama 10.0.0"
  exit 1
fi

PROJECT_ID="$1"                       # First parameter: project ID
APP_VERSION="$2"                      # Second parameter: app version
FIELD_NAME="SudsterSupportedVersions" # field name in Firestore

# Validate parameters
if [ -z "$PROJECT_ID" ]; then
  echo "Error: Project ID cannot be empty"
  exit 1
fi

if [ -z "$APP_VERSION" ]; then
  echo "Error: App version cannot be empty"
  exit 1
fi

# Set your access token and project details
ACCESS_TOKEN=$(gcloud auth application-default print-access-token)
COLLECTION="Code"
DOCUMENT_ID="RemoteConfig"

# Construct the document path
DOCUMENT_PATH="projects/${PROJECT_ID}/databases/(default)/documents/${COLLECTION}/${DOCUMENT_ID}"

# First, get the current document
CURRENT_DOC=$(curl -s -X GET \
  -H "Authorization: Bearer ${ACCESS_TOKEN}" \
  "https://firestore.googleapis.com/v1/${DOCUMENT_PATH}")

# Check if version already exists
VERSION_EXISTS=$(echo $CURRENT_DOC | jq -r '.fields.'${FIELD_NAME}'.arrayValue.values[] | if .stringValue == "'${APP_VERSION}'" then true else false end' | grep -q "true" && echo "true" || echo "false")

if [ "$VERSION_EXISTS" = "true" ]; then
  echo "Version ${APP_VERSION} already exists in the array. Skipping update."
  exit 0
fi

# Extract current array values and ensure they're properly formatted
CURRENT_VALUES=$(echo $CURRENT_DOC | jq -r '.fields.'${FIELD_NAME}'.arrayValue.values[] | if .stringValue then "{\"stringValue\": \"" + .stringValue + "\"}" else "{\"numberValue\": " + (.numberValue | tostring) + "}" end' | tr '\n' ',' | sed 's/,$//')

# Create new array with current values and new version
NEW_VALUES="["
if [ ! -z "$CURRENT_VALUES" ]; then
  NEW_VALUES="${NEW_VALUES}${CURRENT_VALUES},"
fi
NEW_VALUES="${NEW_VALUES}{\"stringValue\": \"${APP_VERSION}\"}]"

# JSON payload with the updated fields
JSON_PAYLOAD='{
  "fields": {
    "'${FIELD_NAME}'": {
      "arrayValue": {
        "values": '${NEW_VALUES}'
      }
    }
  }
}'

# Send the PATCH request to update the document
curl -X PATCH \
  -H "Authorization: Bearer ${ACCESS_TOKEN}" \
  -H "Content-Type: application/json" \
  -d "${JSON_PAYLOAD}" \
  "https://firestore.googleapis.com/v1/${DOCUMENT_PATH}?updateMask.fieldPaths=${FIELD_NAME}"

echo "Updated RemoteConfig document with version ${APP_VERSION}"
